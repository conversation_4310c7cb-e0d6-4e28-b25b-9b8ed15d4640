{"abi": [{"type": "function", "name": "emergencyPauseAll", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "emergencyPauseMarket", "inputs": [{"name": "_market", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "emergencyPauseMarketFor", "inputs": [{"name": "_market", "type": "address", "internalType": "address"}, {"name": "_pauseType", "type": "uint8", "internalType": "enum ImTokenOperationTypes.OperationType"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "MarketAdded", "inputs": [{"name": "market", "type": "address", "indexed": true, "internalType": "address"}, {"name": "marketType", "type": "uint8", "indexed": false, "internalType": "enum IPauser.PausableType"}], "anonymous": false}, {"type": "event", "name": "MarketPaused", "inputs": [{"name": "market", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "MarketPausedFor", "inputs": [{"name": "market", "type": "address", "indexed": true, "internalType": "address"}, {"name": "pauseType", "type": "uint8", "indexed": false, "internalType": "enum ImTokenOperationTypes.OperationType"}], "anonymous": false}, {"type": "event", "name": "MarketRemoved", "inputs": [{"name": "market", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "PauseAll", "inputs": [], "anonymous": false}, {"type": "error", "name": "Pauser_AddressNotValid", "inputs": []}, {"type": "error", "name": "Pauser_AlreadyRegistered", "inputs": []}, {"type": "error", "name": "Pauser_ContractNotEnabled", "inputs": []}, {"type": "error", "name": "Pauser_EntryNotFound", "inputs": []}, {"type": "error", "name": "Pauser_NotAuthorized", "inputs": []}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"emergencyPauseAll()": "0775ef0f", "emergencyPauseMarket(address)": "c63ca1f7", "emergencyPauseMarketFor(address,uint8)": "a5d9b634"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"Pauser_AddressNotValid\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Pauser_AlreadyRegistered\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Pauser_ContractNotEnabled\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Pauser_EntryNotFound\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Pauser_NotAuthorized\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"market\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"enum IPauser.PausableType\",\"name\":\"marketType\",\"type\":\"uint8\"}],\"name\":\"MarketAdded\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"market\",\"type\":\"address\"}],\"name\":\"MarketPaused\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"market\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"enum ImTokenOperationTypes.OperationType\",\"name\":\"pauseType\",\"type\":\"uint8\"}],\"name\":\"MarketPausedFor\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"market\",\"type\":\"address\"}],\"name\":\"MarketRemoved\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[],\"name\":\"PauseAll\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"emergencyPauseAll\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_market\",\"type\":\"address\"}],\"name\":\"emergencyPauseMarket\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_market\",\"type\":\"address\"},{\"internalType\":\"enum ImTokenOperationTypes.OperationType\",\"name\":\"_pauseType\",\"type\":\"uint8\"}],\"name\":\"emergencyPauseMarketFor\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{\"emergencyPauseMarket(address)\":{\"params\":{\"_market\":\"the mToken address\"}},\"emergencyPauseMarketFor(address,uint8)\":{\"params\":{\"_market\":\"the mToken address\",\"_pauseType\":\"the operation type\"}}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"emergencyPauseAll()\":{\"notice\":\"pauses all operations for all registered markets\"},\"emergencyPauseMarket(address)\":{\"notice\":\"pauses all operations for a market\"},\"emergencyPauseMarketFor(address,uint8)\":{\"notice\":\"pauses a specific operation for a market\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/IPauser.sol\":\"IPauser\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"src/interfaces/IPauser.sol\":{\"keccak256\":\"0x8c72972618419ab401a13bd0ca2ecaf299ee91e2462b704d87bd6e99e933234c\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://9055675921383a76b4659a1b364a355618480963254b847d870697b829e2e31c\",\"dweb:/ipfs/Qma2xdDddgxjp8qs13WfU8aCFjoVMyJNxvBmo5Zgr87yGZ\"]},\"src/interfaces/IRoles.sol\":{\"keccak256\":\"0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c\",\"dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj\"]},\"src/interfaces/ImToken.sol\":{\"keccak256\":\"0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36\",\"dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "Pauser_AddressNotValid"}, {"inputs": [], "type": "error", "name": "Pauser_AlreadyRegistered"}, {"inputs": [], "type": "error", "name": "Pauser_ContractNotEnabled"}, {"inputs": [], "type": "error", "name": "Pauser_EntryNotFound"}, {"inputs": [], "type": "error", "name": "Pauser_NotAuthorized"}, {"inputs": [{"internalType": "address", "name": "market", "type": "address", "indexed": true}, {"internalType": "enum IPauser.PausableType", "name": "marketType", "type": "uint8", "indexed": false}], "type": "event", "name": "MarketAdded", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "market", "type": "address", "indexed": true}], "type": "event", "name": "MarketPaused", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "market", "type": "address", "indexed": true}, {"internalType": "enum ImTokenOperationTypes.OperationType", "name": "pauseType", "type": "uint8", "indexed": false}], "type": "event", "name": "MarketPausedFor", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "market", "type": "address", "indexed": true}], "type": "event", "name": "MarketRemoved", "anonymous": false}, {"inputs": [], "type": "event", "name": "PauseAll", "anonymous": false}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "emergencyPauseAll"}, {"inputs": [{"internalType": "address", "name": "_market", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "emergencyPauseMarket"}, {"inputs": [{"internalType": "address", "name": "_market", "type": "address"}, {"internalType": "enum ImTokenOperationTypes.OperationType", "name": "_pauseType", "type": "uint8"}], "stateMutability": "nonpayable", "type": "function", "name": "emergencyPauseMarketFor"}], "devdoc": {"kind": "dev", "methods": {"emergencyPauseMarket(address)": {"params": {"_market": "the mToken address"}}, "emergencyPauseMarketFor(address,uint8)": {"params": {"_market": "the mToken address", "_pauseType": "the operation type"}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"emergencyPauseAll()": {"notice": "pauses all operations for all registered markets"}, "emergencyPauseMarket(address)": {"notice": "pauses all operations for a market"}, "emergencyPauseMarketFor(address,uint8)": {"notice": "pauses a specific operation for a market"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/interfaces/IPauser.sol": "<PERSON><PERSON><PERSON>"}, "evmVersion": "london", "libraries": {}}, "sources": {"src/interfaces/IPauser.sol": {"keccak256": "0x8c72972618419ab401a13bd0ca2ecaf299ee91e2462b704d87bd6e99e933234c", "urls": ["bzz-raw://9055675921383a76b4659a1b364a355618480963254b847d870697b829e2e31c", "dweb:/ipfs/Qma2xdDddgxjp8qs13WfU8aCFjoVMyJNxvBmo5Zgr87yGZ"], "license": "BSL-1.1"}, "src/interfaces/IRoles.sol": {"keccak256": "0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365", "urls": ["bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c", "dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj"], "license": "BSL-1.1"}, "src/interfaces/ImToken.sol": {"keccak256": "0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f", "urls": ["bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36", "dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL"], "license": "BSL-1.1"}}, "version": 1}, "id": 140}