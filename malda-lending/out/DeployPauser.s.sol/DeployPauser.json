{"abi": [{"type": "function", "name": "IS_SCRIPT", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "run", "inputs": [{"name": "deployer", "type": "address", "internalType": "contract Deployer"}, {"name": "roles", "type": "address", "internalType": "address"}, {"name": "operator", "type": "address", "internalType": "address"}, {"name": "owner", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}], "bytecode": {"object": "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", "sourceMap": "394:1137:95:-:0;;;3126:44:3;;;-1:-1:-1;;849:28:1;;;;;394:1137:95;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "394:1137:95:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;432:866;;;;;;:::i;:::-;;:::i;:::-;;;-1:-1:-1;;;;;1058:32:242;;;1040:51;;1028:2;1013:18;432:866:95;;;;;;;;849:28:1;;;;;;;;;;;;;;;1267:14:242;;1260:22;1242:41;;1230:2;1215:18;849:28:1;1102:187:242;432:866:95;561:25;;-1:-1:-1;;;561:25:95;;1496:2:242;561:25:95;;;1478:21:242;1535:2;1515:18;;;1508:30;-1:-1:-1;;;1554:18:242;;;1547:41;528:7:95;;;;336:42:0;;561:10:95;;1605:18:242;;561:25:95;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;547:39;;597:12;612:23;;;;;;;;;;;;;;-1:-1:-1;;;612:23:95;;;:7;:23::i;:::-;597:38;;646:31;;;;;;;;;;;;;;-1:-1:-1;;;646:31:95;;;:11;:31::i;:::-;706:25;;-1:-1:-1;;;706:25:95;;;;;1969::242;;;688:15:95;;-1:-1:-1;;;;;706:19:95;;;;;1942:18:242;;706:25:95;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;688:43;;793:7;-1:-1:-1;;;;;793:19:95;;816:1;793:24;789:394;;833:22;;-1:-1:-1;;;833:22:95;;;;;1969:25:242;;;336:42:0;;833:17:95;;1942:18:242;;833:22:95;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;895:8;-1:-1:-1;;;;;895:15:95;;911:4;934:25;;;;;;;;:::i;:::-;-1:-1:-1;;934:25:95;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2673:32:242;;;934:25:95;961:34;;2655:51:242;2742:32;;;2722:18;;;2715:60;;;;2811:32;;2791:18;;;2784:60;2628:18;;961:34:95;;;-1:-1:-1;;961:34:95;;;;;;;;;;917:79;;;961:34;917:79;;:::i;:::-;;;;;;;;;;;;;895:102;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;869:128;;336:42:0;-1:-1:-1;;;;;1011:16:95;;:18;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1043:46;;;;;;;;;;;;;;-1:-1:-1;;;1043:46:95;;;1081:7;1043:11;:46::i;:::-;789:394;;;1120:52;;;;;;;;;;;;;;;;;;1164:7;1120:11;:52::i;:::-;1284:7;432:866;-1:-1:-1;;;;;;;432:866:95:o;1304:225::-;1448:27;;-1:-1:-1;;;1448:27:95;;4377:2:242;1448:27:95;;;4359:21:242;4416:2;4396:18;;;4389:30;-1:-1:-1;;;4435:18:242;;;4428:41;1364:7:95;;1430:10;;336:42:0;;1448:12:95;;4486:18:242;;1448:27:95;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;1448:27:95;;;;;;;;;;;;:::i;:::-;1498:4;1484:26;;;;;;;;:::i;:::-;;;;-1:-1:-1;;1484:26:95;;;;;;;;;;1413:99;;;;1484:26;1413:99;;:::i;:::-;;;;;;;;;;;;;1390:132;;;;;;1383:139;;1304:225;;;:::o;6191:121:16:-;6246:59;6301:2;6262:42;;;;;;;;:::i;:::-;;;;-1:-1:-1;;6262:42:16;;;;;;;;;;;;;;-1:-1:-1;;;;;6262:42:16;-1:-1:-1;;;6262:42:16;;;6246:15;:59::i;:::-;6191:121;:::o;7740:145::-;7807:71;7870:2;7874;7823:54;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;7823:54:16;;;;;;;;;;;;;;-1:-1:-1;;;;;7823:54:16;-1:-1:-1;;;7823:54:16;;;7807:15;:71::i;:::-;7740:145;;:::o;851:129::-;922:51;965:7;265:22;131:42;265:40;;594:1;571;541:7;535:14;510:2;501:7;497:16;461:14;434:5;402:211;381:246;367:270;180:463;:::o;-1:-1:-1:-;;;;;;;;:::o;14:141:242:-;-1:-1:-1;;;;;99:31:242;;89:42;;79:70;;145:1;142;135:12;160:729;264:6;272;280;288;341:3;329:9;320:7;316:23;312:33;309:53;;;358:1;355;348:12;309:53;397:9;384:23;416:41;451:5;416:41;:::i;:::-;476:5;-1:-1:-1;533:2:242;518:18;;505:32;546:43;505:32;546:43;:::i;:::-;608:7;-1:-1:-1;667:2:242;652:18;;639:32;680:43;639:32;680:43;:::i;:::-;742:7;-1:-1:-1;801:2:242;786:18;;773:32;814:43;773:32;814:43;:::i;:::-;160:729;;;;-1:-1:-1;160:729:242;;-1:-1:-1;;160:729:242:o;1634:184::-;1704:6;1757:2;1745:9;1736:7;1732:23;1728:32;1725:52;;;1773:1;1770;1763:12;1725:52;-1:-1:-1;1796:16:242;;1634:184;-1:-1:-1;1634:184:242:o;2005:261::-;2075:6;2128:2;2116:9;2107:7;2103:23;2099:32;2096:52;;;2144:1;2141;2134:12;2096:52;2176:9;2170:16;2195:41;2230:5;2195:41;:::i;:::-;2255:5;2005:261;-1:-1:-1;;;2005:261:242:o;2855:250::-;2940:1;2950:113;2964:6;2961:1;2958:13;2950:113;;;3040:11;;;3034:18;3021:11;;;3014:39;2986:2;2979:10;2950:113;;;-1:-1:-1;;3097:1:242;3079:16;;3072:27;2855:250::o;3110:492::-;3285:3;3323:6;3317:13;3339:66;3398:6;3393:3;3386:4;3378:6;3374:17;3339:66;:::i;:::-;3468:13;;3427:16;;;;3490:70;3468:13;3427:16;3537:4;3525:17;;3490:70;:::i;:::-;3576:20;;3110:492;-1:-1:-1;;;;3110:492:242:o;3607:270::-;3648:3;3686:5;3680:12;3713:6;3708:3;3701:19;3729:76;3798:6;3791:4;3786:3;3782:14;3775:4;3768:5;3764:16;3729:76;:::i;:::-;3859:2;3838:15;-1:-1:-1;;3834:29:242;3825:39;;;;3866:4;3821:50;;3607:270;-1:-1:-1;;3607:270:242:o;3882:288::-;4057:6;4046:9;4039:25;4100:2;4095;4084:9;4080:18;4073:30;4020:4;4120:44;4160:2;4149:9;4145:18;4137:6;4120:44;:::i;:::-;4112:52;3882:288;-1:-1:-1;;;;3882:288:242:o;4515:127::-;4576:10;4571:3;4567:20;4564:1;4557:31;4607:4;4604:1;4597:15;4631:4;4628:1;4621:15;4647:916;4727:6;4780:2;4768:9;4759:7;4755:23;4751:32;4748:52;;;4796:1;4793;4786:12;4748:52;4829:9;4823:16;4862:18;4854:6;4851:30;4848:50;;;4894:1;4891;4884:12;4848:50;4917:22;;4970:4;4962:13;;4958:27;-1:-1:-1;4948:55:242;;4999:1;4996;4989:12;4948:55;5032:2;5026:9;5058:18;5050:6;5047:30;5044:56;;;5080:18;;:::i;:::-;5129:2;5123:9;5221:2;5183:17;;-1:-1:-1;;5179:31:242;;;5212:2;5175:40;5171:54;5159:67;;5256:18;5241:34;;5277:22;;;5238:62;5235:88;;;5303:18;;:::i;:::-;5339:2;5332:22;5363;;;5404:15;;;5421:2;5400:24;5397:37;-1:-1:-1;5394:57:242;;;5447:1;5444;5437:12;5394:57;5460:72;5525:6;5520:2;5512:6;5508:15;5503:2;5499;5495:11;5460:72;:::i;:::-;5551:6;4647:916;-1:-1:-1;;;;;4647:916:242:o;5568:443::-;5789:3;5827:6;5821:13;5843:66;5902:6;5897:3;5890:4;5882:6;5878:17;5843:66;:::i;:::-;-1:-1:-1;;;5931:16:242;;5956:20;;;-1:-1:-1;6003:1:242;5992:13;;5568:443;-1:-1:-1;5568:443:242:o;6016:613::-;6274:26;6270:31;6261:6;6257:2;6253:15;6249:53;6244:3;6237:66;6219:3;6332:6;6326:13;6348:75;6416:6;6411:2;6406:3;6402:12;6395:4;6387:6;6383:17;6348:75;:::i;:::-;6483:13;;6442:16;;;;6505:76;6483:13;6567:2;6559:11;;6552:4;6540:17;;6505:76;:::i;:::-;6601:17;6620:2;6597:26;;6016:613;-1:-1:-1;;;;;6016:613:242:o;6634:219::-;6783:2;6772:9;6765:21;6746:4;6803:44;6843:2;6832:9;6828:18;6820:6;6803:44;:::i;6858:316::-;7035:2;7024:9;7017:21;6998:4;7055:44;7095:2;7084:9;7080:18;7072:6;7055:44;:::i;:::-;7047:52;;7164:1;7160;7155:3;7151:11;7147:19;7139:6;7135:32;7130:2;7119:9;7115:18;7108:60;6858:316;;;;;:::o", "linkReferences": {}}, "methodIdentifiers": {"IS_SCRIPT()": "f8ccbf47", "run(address,address,address,address)": "37e26c9d"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"IS_SCRIPT\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"contract Deployer\",\"name\":\"deployer\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"roles\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"run\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"notice\":\"forge script Deploy<PERSON>auser \\\\     --slow \\\\     --verify \\\\     --verifier-url <url> \\\\     --rpc-url <url> \\\\     --etherscan-api-key <key> \\\\     --broadcast\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"script/deployment/generic/DeployPauser.s.sol\":\"DeployPauser\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"lib/forge-std/src/Base.sol\":{\"keccak256\":\"0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d\",\"dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z\"]},\"lib/forge-std/src/Script.sol\":{\"keccak256\":\"0xc942e27c7baae499beb01afbbae99f24d42af9a6e4aae675bc6901b704aa8e9b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0456008adf68947247f358b62863af4a8e349549d2260f2ff9569ff0e3cf5c98\",\"dweb:/ipfs/QmdviSUj2i7o3TPN5vd2xocqGMFVqjUzaiJTZRYyPxyHPx\"]},\"lib/forge-std/src/StdChains.sol\":{\"keccak256\":\"0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351\",\"dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm\"]},\"lib/forge-std/src/StdCheats.sol\":{\"keccak256\":\"0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41\",\"dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK\"]},\"lib/forge-std/src/StdConstants.sol\":{\"keccak256\":\"0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc\",\"dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r\"]},\"lib/forge-std/src/StdJson.sol\":{\"keccak256\":\"0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974\",\"dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3\"]},\"lib/forge-std/src/StdMath.sol\":{\"keccak256\":\"0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92\",\"dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC\"]},\"lib/forge-std/src/StdStorage.sol\":{\"keccak256\":\"0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57\",\"dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ\"]},\"lib/forge-std/src/StdStyle.sol\":{\"keccak256\":\"0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8\",\"dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK\"]},\"lib/forge-std/src/StdUtils.sol\":{\"keccak256\":\"0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138\",\"dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776\"]},\"lib/forge-std/src/Vm.sol\":{\"keccak256\":\"0xaae88142a348064677cf81169ea0ca706be3e99e2a0f13a01878ff8d52cf6b25\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://da4fb0ed1b31381c7e60ee386acc72796e8a8325c83739ec9a3678e2f841073c\",\"dweb:/ipfs/Qmb4MU6FpAi8Wt5kUQ3wa7ooHnfMVbdd9zuvnqbTehLmxJ\"]},\"lib/forge-std/src/console.sol\":{\"keccak256\":\"0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57\",\"dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP\"]},\"lib/forge-std/src/console2.sol\":{\"keccak256\":\"0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d\",\"dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ\"]},\"lib/forge-std/src/interfaces/IMulticall3.sol\":{\"keccak256\":\"0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0\",\"dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2\"]},\"lib/forge-std/src/safeconsole.sol\":{\"keccak256\":\"0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab\",\"dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3\"]},\"lib/openzeppelin-contracts/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"script/deployment/generic/DeployPauser.s.sol\":{\"keccak256\":\"0x046fe2f5a84d5362d5bca057c90dd3fcfcb00967b45cedb5342bf227707bd076\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://aa6d0d28d2579a1a9ed1e3bb91d3940dbe6a3ffffd3c7bfe897513dd68153634\",\"dweb:/ipfs/QmPBjpe9zr57hDdpe3q7w2SswbSBUrwV3vzpzCh5rgEiR5\"]},\"src/interfaces/IBlacklister.sol\":{\"keccak256\":\"0x47ed20135507a9d16473027cbee18de5d952846bada761c6248b91c75cd88be1\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://91a886b95dd578156f9b6cec3236e4751aae8a38e5d15e0d6045217c6fbd3d63\",\"dweb:/ipfs/QmRNjz7sGTuMkQNZhwwcg5DNN2eeqTZSAdZmxWxbx66CHU\"]},\"src/interfaces/IOperator.sol\":{\"keccak256\":\"0x0ae3d63eb07518da5a4fd6de7b013b6f55c87defb31900548378cffc70a51f72\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://1d9710c4ac43cb0563449aa519501b4ed18a331c90d0bc7eb79cdc56407bad8a\",\"dweb:/ipfs/Qmafe9Wup2GF9pD83CLWUrRait93vwvReqU1C7EHQhY7WV\"]},\"src/interfaces/IPauser.sol\":{\"keccak256\":\"0x8c72972618419ab401a13bd0ca2ecaf299ee91e2462b704d87bd6e99e933234c\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://9055675921383a76b4659a1b364a355618480963254b847d870697b829e2e31c\",\"dweb:/ipfs/Qma2xdDddgxjp8qs13WfU8aCFjoVMyJNxvBmo5Zgr87yGZ\"]},\"src/interfaces/IRoles.sol\":{\"keccak256\":\"0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c\",\"dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj\"]},\"src/interfaces/ImToken.sol\":{\"keccak256\":\"0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36\",\"dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL\"]},\"src/interfaces/ImTokenGateway.sol\":{\"keccak256\":\"0x7b47f5229cd07de60af49eb473eddc27e429abd846df81001ef05af2c3d47634\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://04d8c52462eeb19a4c28980ac5c81a901fd35982dc47c0ab95a4ee6466b150c1\",\"dweb:/ipfs/QmfTyZrLWMnWLU6zo85RVwjv5W2qXVQPkLWzru5zcBarYm\"]},\"src/libraries/Bytes32AddressLib.sol\":{\"keccak256\":\"0xd2c90ee900d4cfe4b48254363cfbada176cd8b130ddee62ede7f9c8ec2a6b8bd\",\"license\":\"AGPL-3.0-only\",\"urls\":[\"bzz-raw://e864869c9c87f0cb20b898c0435f9585db5e1f52ba0d235af76c8e6a8b4fbffd\",\"dweb:/ipfs/QmRCLJpFCGsra4VFGDNf2hEuEJP7sMs6PQxZz6CV3sGcxa\"]},\"src/libraries/CREATE3.sol\":{\"keccak256\":\"0x71104c800a3f8588df1011b4c03d6c206e4738b6b9f0f2b2c5910754e9e6c975\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://4b1a6e66b89698a8d8d236da1eaa2be8728cb3cd872393b3b4ff00dc79fba6f2\",\"dweb:/ipfs/QmW78LNN4W5h19G9qQsdMd8ffc2kVBmCwT3Dqs28R6ZyPC\"]},\"src/pauser/Pauser.sol\":{\"keccak256\":\"0xa72ca5608e352415254971a1dd694faeb40488832e202d9162f95af7d41ddeb0\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://b3f14fa24b998c229e7751bdbdba8f82f7893100b836afefdc24bc2cfd6dfb99\",\"dweb:/ipfs/QmfCxQwg1Tf3wWsaG6FcMSV3X7zLf8Cfs3UKgxQcLGzJmY\"]},\"src/utils/Deployer.sol\":{\"keccak256\":\"0xfb7f1e6a2c171b5d0e3f864383774bee1d97e29c510b0623b357c808c5f36489\",\"license\":\"AGPL-3.0\",\"urls\":[\"bzz-raw://cf252f86bb47a8c766d39602cf345c24d408e536b49053541b942878de57a64d\",\"dweb:/ipfs/QmNsfFKPtXaEG9yX9A7JV9MGQTZ2C8E7nxzZtjpvEzQyQc\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "view", "type": "function", "name": "IS_SCRIPT", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "contract Deployer", "name": "deployer", "type": "address"}, {"internalType": "address", "name": "roles", "type": "address"}, {"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "address", "name": "owner", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "run", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"script/deployment/generic/DeployPauser.s.sol": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "evmVersion": "london", "libraries": {}}, "sources": {"lib/forge-std/src/Base.sol": {"keccak256": "0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd", "urls": ["bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d", "dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z"], "license": "MIT"}, "lib/forge-std/src/Script.sol": {"keccak256": "0xc942e27c7baae499beb01afbbae99f24d42af9a6e4aae675bc6901b704aa8e9b", "urls": ["bzz-raw://0456008adf68947247f358b62863af4a8e349549d2260f2ff9569ff0e3cf5c98", "dweb:/ipfs/QmdviSUj2i7o3TPN5vd2xocqGMFVqjUzaiJTZRYyPxyHPx"], "license": "MIT"}, "lib/forge-std/src/StdChains.sol": {"keccak256": "0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e", "urls": ["bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351", "dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm"], "license": "MIT"}, "lib/forge-std/src/StdCheats.sol": {"keccak256": "0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746", "urls": ["bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41", "dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK"], "license": "MIT"}, "lib/forge-std/src/StdConstants.sol": {"keccak256": "0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534", "urls": ["bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc", "dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r"], "license": "MIT"}, "lib/forge-std/src/StdJson.sol": {"keccak256": "0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500", "urls": ["bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974", "dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3"], "license": "MIT"}, "lib/forge-std/src/StdMath.sol": {"keccak256": "0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2", "urls": ["bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92", "dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC"], "license": "MIT"}, "lib/forge-std/src/StdStorage.sol": {"keccak256": "0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc", "urls": ["bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57", "dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ"], "license": "MIT"}, "lib/forge-std/src/StdStyle.sol": {"keccak256": "0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d", "urls": ["bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8", "dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK"], "license": "MIT"}, "lib/forge-std/src/StdUtils.sol": {"keccak256": "0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737", "urls": ["bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138", "dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776"], "license": "MIT"}, "lib/forge-std/src/Vm.sol": {"keccak256": "0xaae88142a348064677cf81169ea0ca706be3e99e2a0f13a01878ff8d52cf6b25", "urls": ["bzz-raw://da4fb0ed1b31381c7e60ee386acc72796e8a8325c83739ec9a3678e2f841073c", "dweb:/ipfs/Qmb4MU6FpAi8Wt5kUQ3wa7ooHnfMVbdd9zuvnqbTehLmxJ"], "license": "MIT OR Apache-2.0"}, "lib/forge-std/src/console.sol": {"keccak256": "0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5", "urls": ["bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57", "dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP"], "license": "MIT"}, "lib/forge-std/src/console2.sol": {"keccak256": "0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f", "urls": ["bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d", "dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ"], "license": "MIT"}, "lib/forge-std/src/interfaces/IMulticall3.sol": {"keccak256": "0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a", "urls": ["bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0", "dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2"], "license": "MIT"}, "lib/forge-std/src/safeconsole.sol": {"keccak256": "0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11", "urls": ["bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab", "dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/access/Ownable.sol": {"keccak256": "0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb", "urls": ["bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6", "dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "script/deployment/generic/DeployPauser.s.sol": {"keccak256": "0x046fe2f5a84d5362d5bca057c90dd3fcfcb00967b45cedb5342bf227707bd076", "urls": ["bzz-raw://aa6d0d28d2579a1a9ed1e3bb91d3940dbe6a3ffffd3c7bfe897513dd68153634", "dweb:/ipfs/QmPBjpe9zr57hDdpe3q7w2SswbSBUrwV3vzpzCh5rgEiR5"], "license": "BSL-1.1"}, "src/interfaces/IBlacklister.sol": {"keccak256": "0x47ed20135507a9d16473027cbee18de5d952846bada761c6248b91c75cd88be1", "urls": ["bzz-raw://91a886b95dd578156f9b6cec3236e4751aae8a38e5d15e0d6045217c6fbd3d63", "dweb:/ipfs/QmRNjz7sGTuMkQNZhwwcg5DNN2eeqTZSAdZmxWxbx66CHU"], "license": "BSL-1.1"}, "src/interfaces/IOperator.sol": {"keccak256": "0x0ae3d63eb07518da5a4fd6de7b013b6f55c87defb31900548378cffc70a51f72", "urls": ["bzz-raw://1d9710c4ac43cb0563449aa519501b4ed18a331c90d0bc7eb79cdc56407bad8a", "dweb:/ipfs/Qmafe9Wup2GF9pD83CLWUrRait93vwvReqU1C7EHQhY7WV"], "license": "BSL-1.1"}, "src/interfaces/IPauser.sol": {"keccak256": "0x8c72972618419ab401a13bd0ca2ecaf299ee91e2462b704d87bd6e99e933234c", "urls": ["bzz-raw://9055675921383a76b4659a1b364a355618480963254b847d870697b829e2e31c", "dweb:/ipfs/Qma2xdDddgxjp8qs13WfU8aCFjoVMyJNxvBmo5Zgr87yGZ"], "license": "BSL-1.1"}, "src/interfaces/IRoles.sol": {"keccak256": "0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365", "urls": ["bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c", "dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj"], "license": "BSL-1.1"}, "src/interfaces/ImToken.sol": {"keccak256": "0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f", "urls": ["bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36", "dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL"], "license": "BSL-1.1"}, "src/interfaces/ImTokenGateway.sol": {"keccak256": "0x7b47f5229cd07de60af49eb473eddc27e429abd846df81001ef05af2c3d47634", "urls": ["bzz-raw://04d8c52462eeb19a4c28980ac5c81a901fd35982dc47c0ab95a4ee6466b150c1", "dweb:/ipfs/QmfTyZrLWMnWLU6zo85RVwjv5W2qXVQPkLWzru5zcBarYm"], "license": "BSL-1.1"}, "src/libraries/Bytes32AddressLib.sol": {"keccak256": "0xd2c90ee900d4cfe4b48254363cfbada176cd8b130ddee62ede7f9c8ec2a6b8bd", "urls": ["bzz-raw://e864869c9c87f0cb20b898c0435f9585db5e1f52ba0d235af76c8e6a8b4fbffd", "dweb:/ipfs/QmRCLJpFCGsra4VFGDNf2hEuEJP7sMs6PQxZz6CV3sGcxa"], "license": "AGPL-3.0-only"}, "src/libraries/CREATE3.sol": {"keccak256": "0x71104c800a3f8588df1011b4c03d6c206e4738b6b9f0f2b2c5910754e9e6c975", "urls": ["bzz-raw://4b1a6e66b89698a8d8d236da1eaa2be8728cb3cd872393b3b4ff00dc79fba6f2", "dweb:/ipfs/QmW78LNN4W5h19G9qQsdMd8ffc2kVBmCwT3Dqs28R6ZyPC"], "license": "BSL-1.1"}, "src/pauser/Pauser.sol": {"keccak256": "0xa72ca5608e352415254971a1dd694faeb40488832e202d9162f95af7d41ddeb0", "urls": ["bzz-raw://b3f14fa24b998c229e7751bdbdba8f82f7893100b836afefdc24bc2cfd6dfb99", "dweb:/ipfs/QmfCxQwg1Tf3wWsaG6FcMSV3X7zLf8Cfs3UKgxQcLGzJmY"], "license": "BSL-1.1"}, "src/utils/Deployer.sol": {"keccak256": "0xfb7f1e6a2c171b5d0e3f864383774bee1d97e29c510b0623b357c808c5f36489", "urls": ["bzz-raw://cf252f86bb47a8c766d39602cf345c24d408e536b49053541b942878de57a64d", "dweb:/ipfs/QmNsfFKPtXaEG9yX9A7JV9MGQTZ2C8E7nxzZtjpvEzQyQc"], "license": "AGPL-3.0"}}, "version": 1}, "id": 95}