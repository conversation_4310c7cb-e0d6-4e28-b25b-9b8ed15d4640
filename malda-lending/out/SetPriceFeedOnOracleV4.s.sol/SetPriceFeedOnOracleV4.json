{"abi": [{"type": "function", "name": "IS_SCRIPT", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "run", "inputs": [{"name": "oracle", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "runTestnet", "inputs": [{"name": "oracle", "type": "address", "internalType": "address"}, {"name": "symbol", "type": "string", "internalType": "string"}, {"name": "priceFeed", "type": "address", "internalType": "address"}, {"name": "underlyingDecimals", "type": "uint8", "internalType": "uint8"}], "outputs": [], "stateMutability": "nonpayable"}], "bytecode": {"object": "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", "sourceMap": "553:6192:69:-:0;;;3126:44:3;;;-1:-1:-1;;849:28:1;;;;;553:6192:69;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "553:6192:69:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1244:5499;;;;;;:::i;:::-;;:::i;:::-;;601:637;;;;;;:::i;:::-;;:::i;849:28:1:-;;;;;;;;;;;;;;;2010:14:242;;2003:22;1985:41;;1973:2;1958:18;849:28:1;;;;;;;1244:5499:69;1304:25;;-1:-1:-1;;;1304:25:69;;2239:2:242;1304:25:69;;;2221:21:242;2278:2;2258:18;;;2251:30;-1:-1:-1;;;2297:18:242;;;2290:41;1290:11:69;;336:42:0;;1304:10:69;;2348:18:242;;1304:25:69;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1370:22;;;1389:2;1370:22;;;;;;;;;1290:39;;-1:-1:-1;1340:27:69;;1370:22;;;;;-1:-1:-1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1370:22:69;;;;;;;;;;;;-1:-1:-1;;1429:253:69;;;;;;;;;;;;;;;-1:-1:-1;;;1429:253:69;;;;;;1496:42;1429:253;;;;;;;;1565:42;1429:253;;;;;;;;;;;;;;;-1:-1:-1;;;1429:253:69;;;;-1:-1:-1;;;1429:253:69;;;;1670:1;-1:-1:-1;;;1429:253:69;1418:8;;;;-1:-1:-1;1429:253:69;1418:8;;-1:-1:-1;;1418:8:69;;;;:::i;:::-;;;;;;;;;;;:264;;;;1703:252;;;;;;;;;;;;;;;-1:-1:-1;;;1703:252:69;;;;;;1769:42;1703:252;;;;1838:42;1703:252;;;;;;;;;;;;;;;-1:-1:-1;;;1703:252:69;;;;;;;-1:-1:-1;1703:252:69;;;1943:1;-1:-1:-1;;;1703:252:69;1692:8;;:5;;1698:1;;1692:8;;;;;;:::i;:::-;;;;;;;;;;;:263;;;;1991:253;;;;;;;;;;;;;;;-1:-1:-1;;;1991:253:69;;;;;;2058:42;1991:253;;;;2127:42;1991:253;;;;;;;;;;;;;;;-1:-1:-1;;;1991:253:69;;;;;;;-1:-1:-1;1991:253:69;;;2232:1;-1:-1:-1;;;1991:253:69;1980:8;;:5;;1986:1;;1980:8;;;;;;:::i;:::-;;;;;;:264;;;;2265:252;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;2265:252:69;;;;;;;2331:42;-1:-1:-1;;;;;2265:252:69;;;;;2400:42;-1:-1:-1;;;;;2265:252:69;;;;;;;;;;;;;;;;;;-1:-1:-1;;;2265:252:69;;;;;;;2505:1;2265:252;;;;;2254:5;2260:1;2254:8;;;;;;;;:::i;:::-;;;;;;;;;;;:263;;;;2553:253;;;;;;;;;;;;;;;-1:-1:-1;;;2553:253:69;;;;;;2620:42;2553:253;;;;2689:42;2553:253;;;;;;;;;;;;;;;-1:-1:-1;;;2553:253:69;;;;;;;-1:-1:-1;2553:253:69;;;2794:1;-1:-1:-1;;;2553:253:69;2542:8;;:5;;2548:1;;2542:8;;;;;;:::i;:::-;;;;;;;;;;;:264;;;;2827:252;;;;;;;;;;;;;;;-1:-1:-1;;;2827:252:69;;;;;;2893:42;2827:252;;;;2962:42;2827:252;;;;;;;;;;;;;;;-1:-1:-1;;;2827:252:69;;;;;;;-1:-1:-1;2827:252:69;;;3067:1;-1:-1:-1;;;2827:252:69;2816:8;;:5;;2822:1;;2816:8;;;;;;:::i;:::-;;;;;;;;;;;:263;;;;3115:254;;;;;;;;;;;;;;;-1:-1:-1;;;3115:254:69;;;;;;3182:42;3115:254;;;;3251:42;3115:254;;;;;;;;;;;;;;;-1:-1:-1;;;3115:254:69;;;;;;;-1:-1:-1;3115:254:69;;;3356:2;-1:-1:-1;;;3115:254:69;3104:8;;:5;;3110:1;;3104:8;;;;;;:::i;:::-;;;;;;;;;;;:265;;;;3390:253;;;;;;;;;;;;;;;-1:-1:-1;;;3390:253:69;;;;;;3456:42;3390:253;;;;3525:42;3390:253;;;;;;;;;;;;;;;-1:-1:-1;;;3390:253:69;;;;;;;-1:-1:-1;3390:253:69;;;3630:2;-1:-1:-1;;;3390:253:69;3379:8;;:5;;3385:1;;3379:8;;;;;;:::i;:::-;;;;;;;;;;;:264;;;;3680:255;;;;;;;;;;;;;;;-1:-1:-1;;;3680:255:69;;;;;;3748:42;3680:255;;;;3817:42;3680:255;;;;;;;;;;;;;;;-1:-1:-1;;;3680:255:69;;;;;;;-1:-1:-1;3680:255:69;;;3922:2;-1:-1:-1;;;3680:255:69;3669:8;;:5;;3675:1;;3669:8;;;;;;:::i;:::-;;;;;;;;;;;:266;;;;3956:254;;;;;;;;;;;;;;;-1:-1:-1;;;3956:254:69;;;;;;4023:42;3956:254;;;;4092:42;3956:254;;;;;;;;;;;;;;;-1:-1:-1;;;3956:254:69;;;;;;;-1:-1:-1;3956:254:69;;;4197:2;-1:-1:-1;;;3956:254:69;3945:8;;:5;;3951:1;;3945:8;;;;;;:::i;:::-;;;;;;;;;;;:265;;;;4248:255;;;;;;;;;;;;;;;-1:-1:-1;;;4248:255:69;;;;;;4316:42;4248:255;;;;4385:42;4248:255;;;;;;;;;;;;;;;-1:-1:-1;;;4248:255:69;;;;;;;-1:-1:-1;4248:255:69;;;4490:2;-1:-1:-1;;;4248:255:69;4236:9;;:5;;4242:2;;4236:9;;;;;;:::i;:::-;;;;;;;;;;;:267;;;;4525:254;;;;;;;;;;;;;;;-1:-1:-1;;;4525:254:69;;;;;;4592:42;4525:254;;;;4661:42;4525:254;;;;;;;;;;;;;;;-1:-1:-1;;;4525:254:69;;;;;;;-1:-1:-1;4525:254:69;;;4766:2;-1:-1:-1;;;4525:254:69;4513:9;;:5;;4519:2;;4513:9;;;;;;:::i;:::-;;;;;;;;;;;:266;;;;4817:256;;;;;;;;;;;;;;;-1:-1:-1;;;4817:256:69;;;;;;4886:42;4817:256;;;;4955:42;4817:256;;;;;;;;;;;;;;;-1:-1:-1;;;4817:256:69;;;;;;;-1:-1:-1;4817:256:69;;;5060:2;-1:-1:-1;;;4817:256:69;4805:9;;:5;;4811:2;;4805:9;;;;;;:::i;:::-;;;;;;;;;;;:268;;;;5095:255;;;;;;;;;;;;;;;-1:-1:-1;;;5095:255:69;;;;;;5163:42;5095:255;;;;5232:42;5095:255;;;;;;;;;;;;;;;-1:-1:-1;;;5095:255:69;;;;;;;-1:-1:-1;5095:255:69;;;5337:2;-1:-1:-1;;;5095:255:69;5083:9;;:5;;5089:2;;5083:9;;;;;;:::i;:::-;;;;;;;;;;;:267;;;;5389:256;;;;;;;;;;;;;;;-1:-1:-1;;;5389:256:69;;;;;;5458:42;5389:256;;;;5527:42;5389:256;;;;;;;;;;;;;;;-1:-1:-1;;;5389:256:69;;;;;;;-1:-1:-1;5389:256:69;;;5632:2;-1:-1:-1;;;5389:256:69;5377:9;;:5;;5383:2;;5377:9;;;;;;:::i;:::-;;;;;;;;;;;:268;;;;5667:255;;;;;;;;;;;;;;;-1:-1:-1;;;5667:255:69;;;;;;5735:42;5667:255;;;;5804:42;5667:255;;;;;;;;;;;;;;;-1:-1:-1;;;5667:255:69;;;;;;;-1:-1:-1;5667:255:69;;;5909:2;-1:-1:-1;;;5667:255:69;5655:9;;:5;;5661:2;;5655:9;;;;;;:::i;:::-;;;;;;;;;;:267;5947:12;;5933:11;5947:12;5995:17;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;5969:43;;6022:47;6109:3;6072:41;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6072:41:69;;;;;;;;;;;;;;;;;6022:91;;6128:9;6123:418;6143:3;6139:1;:7;6123:418;;;6176:5;6182:1;6176:8;;;;;;;;:::i;:::-;;;;;;;:15;;;6163:7;6171:1;6163:10;;;;;;;;:::i;:::-;;;;;;:28;;;;6218:253;;;;;;;;6277:5;6283:1;6277:8;;;;;;;;:::i;:::-;;;;;;;:18;;;-1:-1:-1;;;;;6218:253:69;;;;;6326:5;6332:1;6326:8;;;;;;;;:::i;:::-;;;;;;;:20;;;-1:-1:-1;;;;;6218:253:69;;;;;6374:5;6380:1;6374:8;;;;;;;;:::i;:::-;;;;;;;:17;;;6218:253;;;;6429:5;6435:1;6429:8;;;;;;;;:::i;:::-;;;;;;;:27;;;6218:253;;;;;6205:7;6213:1;6205:10;;;;;;;;:::i;:::-;;;;;;;;;;:266;6513:3;;6123:418;;;-1:-1:-1;6551:22:69;;-1:-1:-1;;;6551:22:69;;;;;2844:25:242;;;336:42:0;;6551:17:69;;2817:18:242;;6551:22:69;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6588:9;6583:126;6603:7;:14;6599:1;:18;6583:126;;;6657:6;-1:-1:-1;;;;;6638:36:69;;6675:7;6683:1;6675:10;;;;;;;;:::i;:::-;;;;;;;6687:7;6695:1;6687:10;;;;;;;;:::i;:::-;;;;;;;6638:60;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6619:3;;;;;6583:126;;;;336:42:0;-1:-1:-1;;;;;6718:16:69;;:18;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1280:5463;;;;;1244:5499;:::o;601:637::-;735:25;;-1:-1:-1;;;735:25:69;;2239:2:242;735:25:69;;;2221:21:242;2278:2;2258:18;;;2251:30;-1:-1:-1;;;2297:18:242;;;2290:41;721:11:69;;336:42:0;;735:10:69;;2348:18:242;;735:25:69;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;817:192;;;;;;;;-1:-1:-1;;;;;817:192:69;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;817:192:69;;;;;;;;;;;;;;;1020:49;;;;;;;;;;;;;;;;721:39;;-1:-1:-1;1020:49:69;;1062:6;1020:11;:49::i;:::-;1079:22;;-1:-1:-1;;;1079:22:69;;;;;2844:25:242;;;336:42:0;;1079:17:69;;2817:18:242;;1079:22:69;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;1111:52:69;;-1:-1:-1;;;1111:52:69;;-1:-1:-1;;;;;1111:36:69;;;-1:-1:-1;1111:36:69;;-1:-1:-1;1111:52:69;;1148:6;;1156;;1111:52;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;336:42:0;-1:-1:-1;;;;;1173:16:69;;:18;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1201:30;;;;;;;;;;;;;;-1:-1:-1;;;1201:30:69;;;:11;:30::i;:::-;711:527;;601:637;;;;:::o;7439:150:16:-;7512:70;7574:2;7578;7528:53;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;7528:53:16;;;;;;;;;;;;;;-1:-1:-1;;;;;7528:53:16;-1:-1:-1;;;7528:53:16;;;7512:15;:70::i;:::-;7439:150;;:::o;6191:121::-;6246:59;6301:2;6262:42;;;;;;;;:::i;:::-;;;;-1:-1:-1;;6262:42:16;;;;;;;;;;;;;;-1:-1:-1;;;;;6262:42:16;-1:-1:-1;;;6262:42:16;;;6246:15;:59::i;:::-;6191:121;:::o;851:129::-;922:51;965:7;265:22;131:42;265:40;;594:1;571;541:7;535:14;510:2;501:7;497:16;461:14;434:5;402:211;381:246;367:270;180:463;:::o;14:173:242:-;82:20;;-1:-1:-1;;;;;131:31:242;;121:42;;111:70;;177:1;174;167:12;111:70;14:173;;;:::o;192:186::-;251:6;304:2;292:9;283:7;279:23;275:32;272:52;;;320:1;317;310:12;272:52;343:29;362:9;343:29;:::i;:::-;333:39;192:186;-1:-1:-1;;;192:186:242:o;383:127::-;444:10;439:3;435:20;432:1;425:31;475:4;472:1;465:15;499:4;496:1;489:15;515:156;581:20;;641:4;630:16;;620:27;;610:55;;661:1;658;651:12;676:1164;770:6;778;786;794;847:3;835:9;826:7;822:23;818:33;815:53;;;864:1;861;854:12;815:53;887:29;906:9;887:29;:::i;:::-;877:39;;967:2;956:9;952:18;939:32;994:18;986:6;983:30;980:50;;;1026:1;1023;1016:12;980:50;1049:22;;1102:4;1094:13;;1090:27;-1:-1:-1;1080:55:242;;1131:1;1128;1121:12;1080:55;1171:2;1158:16;1197:18;1189:6;1186:30;1183:56;;;1219:18;;:::i;:::-;1268:2;1262:9;1360:2;1322:17;;-1:-1:-1;;1318:31:242;;;1351:2;1314:40;1310:54;1298:67;;1395:18;1380:34;;1416:22;;;1377:62;1374:88;;;1442:18;;:::i;:::-;1478:2;1471:22;1502;;;1543:15;;;1560:2;1539:24;1536:37;-1:-1:-1;1533:57:242;;;1586:1;1583;1576:12;1533:57;1642:6;1637:2;1633;1629:11;1624:2;1616:6;1612:15;1599:50;1695:1;1690:2;1681:6;1673;1669:19;1665:28;1658:39;1716:6;1706:16;;;;;1741:38;1775:2;1764:9;1760:18;1741:38;:::i;:::-;1731:48;;1798:36;1830:2;1819:9;1815:18;1798:36;:::i;:::-;1788:46;;676:1164;;;;;;;:::o;2377:184::-;2447:6;2500:2;2488:9;2479:7;2475:23;2471:32;2468:52;;;2516:1;2513;2506:12;2468:52;-1:-1:-1;2539:16:242;;2377:184;-1:-1:-1;2377:184:242:o;2566:127::-;2627:10;2622:3;2618:20;2615:1;2608:31;2658:4;2655:1;2648:15;2682:4;2679:1;2672:15;2880:400;2922:3;2960:5;2954:12;2987:6;2982:3;2975:19;3012:1;3022:139;3036:6;3033:1;3030:13;3022:139;;;3144:4;3129:13;;;3125:24;;3119:31;3099:11;;;3095:22;;3088:63;3051:12;3022:139;;;3026:3;3206:1;3199:4;3190:6;3185:3;3181:16;3177:27;3170:38;3269:4;3262:2;3258:7;3253:2;3245:6;3241:15;3237:29;3232:3;3228:39;3224:50;3217:57;;;2880:400;;;;:::o;3285:764::-;3522:2;3511:9;3504:21;3485:4;3548:45;3589:2;3578:9;3574:18;3566:6;3548:45;:::i;:::-;3641:9;3633:6;3629:22;3624:2;3613:9;3609:18;3602:50;3712:1;3708;3703:3;3699:11;3695:19;3686:6;3680:13;3676:39;3668:6;3661:55;3794:1;3790;3785:3;3781:11;3777:19;3771:2;3763:6;3759:15;3753:22;3749:48;3744:2;3736:6;3732:15;3725:73;3845:2;3837:6;3833:15;3827:22;3882:4;3877:2;3869:6;3865:15;3858:29;3910:50;3954:4;3946:6;3942:17;3928:12;3910:50;:::i;:::-;3896:64;;4013:4;4005:6;4001:17;3995:24;3988:4;3980:6;3976:17;3969:51;4037:6;4029:14;;;;3285:764;;;;;:::o;4054:383::-;4251:2;4240:9;4233:21;4214:4;4277:45;4318:2;4307:9;4303:18;4295:6;4277:45;:::i;:::-;4370:9;4362:6;4358:22;4353:2;4342:9;4338:18;4331:50;4398:33;4424:6;4416;4398:33;:::i;:::-;4390:41;4054:383;-1:-1:-1;;;;;4054:383:242:o;4442:220::-;4591:2;4580:9;4573:21;4554:4;4611:45;4652:2;4641:9;4637:18;4629:6;4611:45;:::i", "linkReferences": {}}, "methodIdentifiers": {"IS_SCRIPT()": "f8ccbf47", "run(address)": "522bb704", "runTestnet(address,string,address,uint8)": "eba1c881"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"IS_SCRIPT\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"oracle\",\"type\":\"address\"}],\"name\":\"run\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"oracle\",\"type\":\"address\"},{\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"},{\"internalType\":\"address\",\"name\":\"priceFeed\",\"type\":\"address\"},{\"internalType\":\"uint8\",\"name\":\"underlyingDecimals\",\"type\":\"uint8\"}],\"name\":\"runTestnet\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"notice\":\"forge script SetPriceFeedOnOracleV4.  \\\\     --slow \\\\     --verify \\\\     --verifier-url <url> \\\\     --rpc-url <url> \\\\     --etherscan-api-key <key> \\\\     --sig \\\"run(string,address,string,uint8)\\\" \\\"WETHUSD\\\" \\\"******************************************\\\" \\\"USD\\\" 18 \\\\     --broadcast\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"script/configuration/SetPriceFeedOnOracleV4.s.sol\":\"SetPriceFeedOnOracleV4\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"lib/forge-std/src/Base.sol\":{\"keccak256\":\"0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d\",\"dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z\"]},\"lib/forge-std/src/Script.sol\":{\"keccak256\":\"0xc942e27c7baae499beb01afbbae99f24d42af9a6e4aae675bc6901b704aa8e9b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0456008adf68947247f358b62863af4a8e349549d2260f2ff9569ff0e3cf5c98\",\"dweb:/ipfs/QmdviSUj2i7o3TPN5vd2xocqGMFVqjUzaiJTZRYyPxyHPx\"]},\"lib/forge-std/src/StdChains.sol\":{\"keccak256\":\"0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351\",\"dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm\"]},\"lib/forge-std/src/StdCheats.sol\":{\"keccak256\":\"0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41\",\"dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK\"]},\"lib/forge-std/src/StdConstants.sol\":{\"keccak256\":\"0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc\",\"dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r\"]},\"lib/forge-std/src/StdJson.sol\":{\"keccak256\":\"0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974\",\"dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3\"]},\"lib/forge-std/src/StdMath.sol\":{\"keccak256\":\"0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92\",\"dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC\"]},\"lib/forge-std/src/StdStorage.sol\":{\"keccak256\":\"0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57\",\"dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ\"]},\"lib/forge-std/src/StdStyle.sol\":{\"keccak256\":\"0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8\",\"dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK\"]},\"lib/forge-std/src/StdUtils.sol\":{\"keccak256\":\"0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138\",\"dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776\"]},\"lib/forge-std/src/Vm.sol\":{\"keccak256\":\"0xaae88142a348064677cf81169ea0ca706be3e99e2a0f13a01878ff8d52cf6b25\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://da4fb0ed1b31381c7e60ee386acc72796e8a8325c83739ec9a3678e2f841073c\",\"dweb:/ipfs/Qmb4MU6FpAi8Wt5kUQ3wa7ooHnfMVbdd9zuvnqbTehLmxJ\"]},\"lib/forge-std/src/console.sol\":{\"keccak256\":\"0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57\",\"dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP\"]},\"lib/forge-std/src/console2.sol\":{\"keccak256\":\"0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d\",\"dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ\"]},\"lib/forge-std/src/interfaces/IMulticall3.sol\":{\"keccak256\":\"0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0\",\"dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2\"]},\"lib/forge-std/src/safeconsole.sol\":{\"keccak256\":\"0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab\",\"dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3\"]},\"script/configuration/SetPriceFeedOnOracleV4.s.sol\":{\"keccak256\":\"0x9c1e8073b2ab772ac333b229d496738d974d15f1161bd8eca8342548359a62f4\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://6ec885d4a115609594f1b26456cb595b994d9018284bf2b359ca4dea0c3b6ca2\",\"dweb:/ipfs/QmeYhq1pJRQLujiH22j7yCBbEKNnjpFK6kW8A7MgSBAyEs\"]},\"script/deployers/Types.sol\":{\"keccak256\":\"0xdfe4dc54c46c9b5fcd959a17ac33580f659d4d4b1bbf262f80c13963e5c4aad7\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://33209fe74e0e8982c2ea6502da587fcb8a2f3092dbeef205ea66a5d4a6208f43\",\"dweb:/ipfs/Qme6EWBu4Q2T8bbKKPAJh31dimpkcuLi4eh3fN4E6XYqmw\"]},\"src/interfaces/IDefaultAdapter.sol\":{\"keccak256\":\"0xbf7e882eeb81776c7be55110bb171c65d166bafeb71d828c085b139bed5735c8\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://7e139fb3ddd0623189493679e73fd42c4e505531502d55e1e699789fa3c1451a\",\"dweb:/ipfs/Qma3XsUVPffiGXZ7epTqMyNJKuh87xrFhqCTwQXznEccU6\"]},\"src/interfaces/IOracleOperator.sol\":{\"keccak256\":\"0xcc2848c75dd5e1531c2b958746cdd76a3b2b329be98fdca4311219a12a6b409e\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://337bdabf03ac279d95ba96aca60105932c7af5c15fdb4b21ab1c369509310fbc\",\"dweb:/ipfs/QmNYrNYNUwWWSrPwHdarhxt1ShdH1dy8B94M49TZ7XvmK1\"]},\"src/interfaces/IRoles.sol\":{\"keccak256\":\"0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c\",\"dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj\"]},\"src/interfaces/ImToken.sol\":{\"keccak256\":\"0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36\",\"dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL\"]},\"src/oracles/MixedPriceOracleV4.sol\":{\"keccak256\":\"0x5bc6345422528b3c76c4c7b4485bbc16dba36e91995a730f4a8c29e28a3b7ad2\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://e3c5c9006e2d3b6a5b8288a771e23831620e757e463fd54aa0827adc72066a94\",\"dweb:/ipfs/QmPETu2S7ZGWDuvMrF7jWAg2MQKUhDwDSw5KMPh4Jc5DGN\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "view", "type": "function", "name": "IS_SCRIPT", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "oracle", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "run"}, {"inputs": [{"internalType": "address", "name": "oracle", "type": "address"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "address", "name": "priceFeed", "type": "address"}, {"internalType": "uint8", "name": "underlyingDecimals", "type": "uint8"}], "stateMutability": "nonpayable", "type": "function", "name": "runTestnet"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"script/configuration/SetPriceFeedOnOracleV4.s.sol": "SetPriceFeedOnOracleV4"}, "evmVersion": "london", "libraries": {}}, "sources": {"lib/forge-std/src/Base.sol": {"keccak256": "0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd", "urls": ["bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d", "dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z"], "license": "MIT"}, "lib/forge-std/src/Script.sol": {"keccak256": "0xc942e27c7baae499beb01afbbae99f24d42af9a6e4aae675bc6901b704aa8e9b", "urls": ["bzz-raw://0456008adf68947247f358b62863af4a8e349549d2260f2ff9569ff0e3cf5c98", "dweb:/ipfs/QmdviSUj2i7o3TPN5vd2xocqGMFVqjUzaiJTZRYyPxyHPx"], "license": "MIT"}, "lib/forge-std/src/StdChains.sol": {"keccak256": "0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e", "urls": ["bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351", "dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm"], "license": "MIT"}, "lib/forge-std/src/StdCheats.sol": {"keccak256": "0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746", "urls": ["bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41", "dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK"], "license": "MIT"}, "lib/forge-std/src/StdConstants.sol": {"keccak256": "0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534", "urls": ["bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc", "dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r"], "license": "MIT"}, "lib/forge-std/src/StdJson.sol": {"keccak256": "0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500", "urls": ["bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974", "dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3"], "license": "MIT"}, "lib/forge-std/src/StdMath.sol": {"keccak256": "0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2", "urls": ["bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92", "dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC"], "license": "MIT"}, "lib/forge-std/src/StdStorage.sol": {"keccak256": "0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc", "urls": ["bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57", "dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ"], "license": "MIT"}, "lib/forge-std/src/StdStyle.sol": {"keccak256": "0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d", "urls": ["bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8", "dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK"], "license": "MIT"}, "lib/forge-std/src/StdUtils.sol": {"keccak256": "0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737", "urls": ["bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138", "dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776"], "license": "MIT"}, "lib/forge-std/src/Vm.sol": {"keccak256": "0xaae88142a348064677cf81169ea0ca706be3e99e2a0f13a01878ff8d52cf6b25", "urls": ["bzz-raw://da4fb0ed1b31381c7e60ee386acc72796e8a8325c83739ec9a3678e2f841073c", "dweb:/ipfs/Qmb4MU6FpAi8Wt5kUQ3wa7ooHnfMVbdd9zuvnqbTehLmxJ"], "license": "MIT OR Apache-2.0"}, "lib/forge-std/src/console.sol": {"keccak256": "0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5", "urls": ["bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57", "dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP"], "license": "MIT"}, "lib/forge-std/src/console2.sol": {"keccak256": "0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f", "urls": ["bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d", "dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ"], "license": "MIT"}, "lib/forge-std/src/interfaces/IMulticall3.sol": {"keccak256": "0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a", "urls": ["bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0", "dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2"], "license": "MIT"}, "lib/forge-std/src/safeconsole.sol": {"keccak256": "0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11", "urls": ["bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab", "dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3"], "license": "MIT"}, "script/configuration/SetPriceFeedOnOracleV4.s.sol": {"keccak256": "0x9c1e8073b2ab772ac333b229d496738d974d15f1161bd8eca8342548359a62f4", "urls": ["bzz-raw://6ec885d4a115609594f1b26456cb595b994d9018284bf2b359ca4dea0c3b6ca2", "dweb:/ipfs/QmeYhq1pJRQLujiH22j7yCBbEKNnjpFK6kW8A7MgSBAyEs"], "license": "BSL-1.1"}, "script/deployers/Types.sol": {"keccak256": "0xdfe4dc54c46c9b5fcd959a17ac33580f659d4d4b1bbf262f80c13963e5c4aad7", "urls": ["bzz-raw://33209fe74e0e8982c2ea6502da587fcb8a2f3092dbeef205ea66a5d4a6208f43", "dweb:/ipfs/Qme6EWBu4Q2T8bbKKPAJh31dimpkcuLi4eh3fN4E6XYqmw"], "license": "BSL-1.1"}, "src/interfaces/IDefaultAdapter.sol": {"keccak256": "0xbf7e882eeb81776c7be55110bb171c65d166bafeb71d828c085b139bed5735c8", "urls": ["bzz-raw://7e139fb3ddd0623189493679e73fd42c4e505531502d55e1e699789fa3c1451a", "dweb:/ipfs/Qma3XsUVPffiGXZ7epTqMyNJKuh87xrFhqCTwQXznEccU6"], "license": "BSL-1.1"}, "src/interfaces/IOracleOperator.sol": {"keccak256": "0xcc2848c75dd5e1531c2b958746cdd76a3b2b329be98fdca4311219a12a6b409e", "urls": ["bzz-raw://337bdabf03ac279d95ba96aca60105932c7af5c15fdb4b21ab1c369509310fbc", "dweb:/ipfs/QmNYrNYNUwWWSrPwHdarhxt1ShdH1dy8B94M49TZ7XvmK1"], "license": "BSL-1.1"}, "src/interfaces/IRoles.sol": {"keccak256": "0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365", "urls": ["bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c", "dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj"], "license": "BSL-1.1"}, "src/interfaces/ImToken.sol": {"keccak256": "0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f", "urls": ["bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36", "dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL"], "license": "BSL-1.1"}, "src/oracles/MixedPriceOracleV4.sol": {"keccak256": "0x5bc6345422528b3c76c4c7b4485bbc16dba36e91995a730f4a8c29e28a3b7ad2", "urls": ["bzz-raw://e3c5c9006e2d3b6a5b8288a771e23831620e757e463fd54aa0827adc72066a94", "dweb:/ipfs/QmPETu2S7ZGWDuvMrF7jWAg2MQKUhDwDSw5KMPh4Jc5DGN"], "license": "BSL-1.1"}}, "version": 1}, "id": 69}