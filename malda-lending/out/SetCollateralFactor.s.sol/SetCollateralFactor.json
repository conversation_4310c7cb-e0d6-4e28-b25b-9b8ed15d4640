{"abi": [{"type": "function", "name": "IS_SCRIPT", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "run", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "market", "type": "address", "internalType": "address"}, {"name": "factor", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}], "bytecode": {"object": "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", "sourceMap": "169:644:63:-:0;;;3126:44:3;;;-1:-1:-1;;849:28:1;;;;;169:644:63;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "169:644:63:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;214:597;;;;;;:::i;:::-;;:::i;:::-;;849:28:1;;;;;;;;;;;;;;;690:14:242;;683:22;665:41;;653:2;638:18;849:28:1;;;;;;;214:597:63;316:25;;-1:-1:-1;;;316:25:63;;919:2:242;316:25:63;;;901:21:242;958:2;938:18;;;931:30;-1:-1:-1;;;977:18:242;;;970:41;302:11:63;;336:42:0;;316:10:63;;1028:18:242;;316:25:63;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;302:39;;352:59;;;;;;;;;;;;;;;;;;404:6;352:11;:59::i;:::-;451:34;;-1:-1:-1;;;451:34:63;;-1:-1:-1;;;;;1456:32:242;;;451:34:63;;;1438:51:242;425:21:63;;451:26;;;;;;1411:18:242;;451:34:63;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;422:63;;;;517:6;500:13;:23;496:118;;539:44;;;;;;;;;;;;;;;;;;:11;:44::i;:::-;597:7;;214:597;;;:::o;496:118::-;624:22;;-1:-1:-1;;;624:22:63;;;;;2208:25:242;;;336:42:0;;624:17:63;;2181:18:242;;624:22:63;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;656:54:63;;-1:-1:-1;;;656:54:63;;-1:-1:-1;;;;;2436:32:242;;;656:54:63;;;2418:51:242;2485:18;;;2478:34;;;656:38:63;;;-1:-1:-1;656:38:63;;-1:-1:-1;2391:18:242;;656:54:63;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;336:42:0;-1:-1:-1;;;;;720:16:63;;:18;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;749:55;;;;;;;;;;;;;;;;;;797:6;7740:145:16;7807:71;7870:2;7874;7823:54;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;7823:54:16;;;;;;;;;;;;;;-1:-1:-1;;;;;7823:54:16;-1:-1:-1;;;7823:54:16;;;7807:15;:71::i;:::-;7740:145;;:::o;6191:121::-;6246:59;6301:2;6262:42;;;;;;;;:::i;:::-;;;;-1:-1:-1;;6262:42:16;;;;;;;;;;;;;;-1:-1:-1;;;;;6262:42:16;-1:-1:-1;;;6262:42:16;;;6246:15;:59::i;:::-;6191:121;:::o;851:129::-;922:51;965:7;265:22;131:42;265:40;;594:1;571;541:7;535:14;510:2;501:7;497:16;461:14;434:5;402:211;381:246;367:270;180:463;:::o;14:173:242:-;82:20;;-1:-1:-1;;;;;131:31:242;;121:42;;111:70;;177:1;174;167:12;111:70;14:173;;;:::o;192:328::-;269:6;277;285;338:2;326:9;317:7;313:23;309:32;306:52;;;354:1;351;344:12;306:52;377:29;396:9;377:29;:::i;:::-;367:39;;425:38;459:2;448:9;444:18;425:38;:::i;:::-;415:48;;510:2;499:9;495:18;482:32;472:42;;192:328;;;;;:::o;1057:230::-;1127:6;1180:2;1168:9;1159:7;1155:23;1151:32;1148:52;;;1196:1;1193;1186:12;1148:52;-1:-1:-1;1241:16:242;;1057:230;-1:-1:-1;1057:230:242:o;1500:164::-;1576:13;;1625;;1618:21;1608:32;;1598:60;;1654:1;1651;1644:12;1669:388;1751:6;1759;1767;1820:2;1808:9;1799:7;1795:23;1791:32;1788:52;;;1836:1;1833;1826:12;1788:52;1859:37;1886:9;1859:37;:::i;:::-;1958:2;1943:18;;1937:25;1849:47;;-1:-1:-1;1937:25:242;-1:-1:-1;2005:46:242;2047:2;2032:18;;2005:46;:::i;:::-;1995:56;;1669:388;;;;;:::o;2523:400::-;2565:3;2603:5;2597:12;2630:6;2625:3;2618:19;2655:1;2665:139;2679:6;2676:1;2673:13;2665:139;;;2787:4;2772:13;;;2768:24;;2762:31;2742:11;;;2738:22;;2731:63;2694:12;2665:139;;;2669:3;2849:1;2842:4;2833:6;2828:3;2824:16;2820:27;2813:38;2912:4;2905:2;2901:7;2896:2;2888:6;2884:15;2880:29;2875:3;2871:39;2867:50;2860:57;;;2523:400;;;;:::o;2928:317::-;3105:2;3094:9;3087:21;3068:4;3125:45;3166:2;3155:9;3151:18;3143:6;3125:45;:::i;:::-;3117:53;;3235:1;3231;3226:3;3222:11;3218:19;3210:6;3206:32;3201:2;3190:9;3186:18;3179:60;2928:317;;;;;:::o;3250:220::-;3399:2;3388:9;3381:21;3362:4;3419:45;3460:2;3449:9;3445:18;3437:6;3419:45;:::i;:::-;3411:53;3250:220;-1:-1:-1;;;3250:220:242:o", "linkReferences": {}}, "methodIdentifiers": {"IS_SCRIPT()": "f8ccbf47", "run(address,address,uint256)": "8892170a"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"IS_SCRIPT\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"market\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"factor\",\"type\":\"uint256\"}],\"name\":\"run\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"script/configuration/SetCollateralFactor.s.sol\":\"SetCollateralFactor\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"lib/forge-std/src/Base.sol\":{\"keccak256\":\"0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d\",\"dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z\"]},\"lib/forge-std/src/Script.sol\":{\"keccak256\":\"0xc942e27c7baae499beb01afbbae99f24d42af9a6e4aae675bc6901b704aa8e9b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0456008adf68947247f358b62863af4a8e349549d2260f2ff9569ff0e3cf5c98\",\"dweb:/ipfs/QmdviSUj2i7o3TPN5vd2xocqGMFVqjUzaiJTZRYyPxyHPx\"]},\"lib/forge-std/src/StdChains.sol\":{\"keccak256\":\"0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351\",\"dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm\"]},\"lib/forge-std/src/StdCheats.sol\":{\"keccak256\":\"0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41\",\"dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK\"]},\"lib/forge-std/src/StdConstants.sol\":{\"keccak256\":\"0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc\",\"dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r\"]},\"lib/forge-std/src/StdJson.sol\":{\"keccak256\":\"0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974\",\"dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3\"]},\"lib/forge-std/src/StdMath.sol\":{\"keccak256\":\"0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92\",\"dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC\"]},\"lib/forge-std/src/StdStorage.sol\":{\"keccak256\":\"0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57\",\"dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ\"]},\"lib/forge-std/src/StdStyle.sol\":{\"keccak256\":\"0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8\",\"dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK\"]},\"lib/forge-std/src/StdUtils.sol\":{\"keccak256\":\"0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138\",\"dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776\"]},\"lib/forge-std/src/Vm.sol\":{\"keccak256\":\"0xaae88142a348064677cf81169ea0ca706be3e99e2a0f13a01878ff8d52cf6b25\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://da4fb0ed1b31381c7e60ee386acc72796e8a8325c83739ec9a3678e2f841073c\",\"dweb:/ipfs/Qmb4MU6FpAi8Wt5kUQ3wa7ooHnfMVbdd9zuvnqbTehLmxJ\"]},\"lib/forge-std/src/console.sol\":{\"keccak256\":\"0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57\",\"dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP\"]},\"lib/forge-std/src/console2.sol\":{\"keccak256\":\"0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d\",\"dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ\"]},\"lib/forge-std/src/interfaces/IMulticall3.sol\":{\"keccak256\":\"0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0\",\"dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2\"]},\"lib/forge-std/src/safeconsole.sol\":{\"keccak256\":\"0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab\",\"dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol\":{\"keccak256\":\"0xc163fcf9bb10138631a9ba5564df1fa25db9adff73bd9ee868a8ae1858fe093a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9706d43a0124053d9880f6e31a59f31bc0a6a3dc1acd66ce0a16e1111658c5f6\",\"dweb:/ipfs/QmUFmfowzkRwGtDu36cXV9SPTBHJ3n7dG9xQiK5B28jTf2\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol\":{\"keccak256\":\"0x631188737069917d2f909d29ce62c4d48611d326686ba6683e26b72a23bfac0b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7a61054ae84cd6c4d04c0c4450ba1d6de41e27e0a2c4f1bcdf58f796b401c609\",\"dweb:/ipfs/QmUvtdp7X1mRVyC3CsHrtPbgoqWaXHp3S1ZR24tpAQYJWM\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol\":{\"keccak256\":\"0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9\",\"dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV\"]},\"script/configuration/SetCollateralFactor.s.sol\":{\"keccak256\":\"0x02af1601cbaccd8bce2aedb6c3eaae393b77707b7287cd0f1a56510ddc48df54\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://c9ce749bd03259f2feeb423772fdc6cac52a7328ec6de4c180f39e0d2b8fb06e\",\"dweb:/ipfs/QmZt7CTBFhVADk5DMvsZyssEYiDrCkCa3PGNgetxx9gPxF\"]},\"src/Operator/Operator.sol\":{\"keccak256\":\"0x1486ed6af6f40f0e045403ac9e35bc045ae7031d9ecac7d1f0358f78ff83b469\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://e136048d46ae9b4384f561e16dfd4638e68db987007d2059aaf19011dcfc8f65\",\"dweb:/ipfs/QmcyrVa45W2qYtd9UuJz9C89M3xKzKzACDQqVMAk4hvPeq\"]},\"src/Operator/OperatorStorage.sol\":{\"keccak256\":\"0x3d9e7f2bc64ab9b5de39d4a58f0a72ac0f86c3b95a6318dd03a7b8acde166783\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://dc0d5e026e00f5f5f92cded7704bb602b06f387370ddd7ee0d75111099b01f3a\",\"dweb:/ipfs/QmQfZzADQpBuvTHJpbHz6P4fGhaGWvQgkU8qMkKULCLdjQ\"]},\"src/interfaces/IBlacklister.sol\":{\"keccak256\":\"0x47ed20135507a9d16473027cbee18de5d952846bada761c6248b91c75cd88be1\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://91a886b95dd578156f9b6cec3236e4751aae8a38e5d15e0d6045217c6fbd3d63\",\"dweb:/ipfs/QmRNjz7sGTuMkQNZhwwcg5DNN2eeqTZSAdZmxWxbx66CHU\"]},\"src/interfaces/IOperator.sol\":{\"keccak256\":\"0x0ae3d63eb07518da5a4fd6de7b013b6f55c87defb31900548378cffc70a51f72\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://1d9710c4ac43cb0563449aa519501b4ed18a331c90d0bc7eb79cdc56407bad8a\",\"dweb:/ipfs/Qmafe9Wup2GF9pD83CLWUrRait93vwvReqU1C7EHQhY7WV\"]},\"src/interfaces/IOracleOperator.sol\":{\"keccak256\":\"0xcc2848c75dd5e1531c2b958746cdd76a3b2b329be98fdca4311219a12a6b409e\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://337bdabf03ac279d95ba96aca60105932c7af5c15fdb4b21ab1c369509310fbc\",\"dweb:/ipfs/QmNYrNYNUwWWSrPwHdarhxt1ShdH1dy8B94M49TZ7XvmK1\"]},\"src/interfaces/IRewardDistributor.sol\":{\"keccak256\":\"0xd39c4db6e2a85e90db82e20685d4b235966d993c3407e1c0fb52add6421b86df\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://e865fe1569ec1ac79cd924a9aa2e2b43754555e510526f6cd030395e9923f01d\",\"dweb:/ipfs/QmXcshgzCxLKBZJYGuQviEamruhmwXWNJ3ZYLxazWBahuc\"]},\"src/interfaces/IRoles.sol\":{\"keccak256\":\"0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c\",\"dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj\"]},\"src/interfaces/ImToken.sol\":{\"keccak256\":\"0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36\",\"dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL\"]},\"src/utils/ExponentialNoError.sol\":{\"keccak256\":\"0xa97178117d8c32059d55dd2224e5514c54b31f896e565c3834cab3d7502b1bb6\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://1cecc0713709f57a5bb7f64d2fa06b9437a42752fa3fed5d3c0b19d82aa56ef4\",\"dweb:/ipfs/QmQqBCZkzmZxomXLr3b1tHUV55n8EPk3wAUsXgojYSpR41\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "view", "type": "function", "name": "IS_SCRIPT", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "address", "name": "market", "type": "address"}, {"internalType": "uint256", "name": "factor", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "run"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"script/configuration/SetCollateralFactor.s.sol": "SetCollateralFactor"}, "evmVersion": "london", "libraries": {}}, "sources": {"lib/forge-std/src/Base.sol": {"keccak256": "0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd", "urls": ["bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d", "dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z"], "license": "MIT"}, "lib/forge-std/src/Script.sol": {"keccak256": "0xc942e27c7baae499beb01afbbae99f24d42af9a6e4aae675bc6901b704aa8e9b", "urls": ["bzz-raw://0456008adf68947247f358b62863af4a8e349549d2260f2ff9569ff0e3cf5c98", "dweb:/ipfs/QmdviSUj2i7o3TPN5vd2xocqGMFVqjUzaiJTZRYyPxyHPx"], "license": "MIT"}, "lib/forge-std/src/StdChains.sol": {"keccak256": "0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e", "urls": ["bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351", "dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm"], "license": "MIT"}, "lib/forge-std/src/StdCheats.sol": {"keccak256": "0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746", "urls": ["bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41", "dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK"], "license": "MIT"}, "lib/forge-std/src/StdConstants.sol": {"keccak256": "0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534", "urls": ["bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc", "dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r"], "license": "MIT"}, "lib/forge-std/src/StdJson.sol": {"keccak256": "0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500", "urls": ["bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974", "dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3"], "license": "MIT"}, "lib/forge-std/src/StdMath.sol": {"keccak256": "0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2", "urls": ["bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92", "dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC"], "license": "MIT"}, "lib/forge-std/src/StdStorage.sol": {"keccak256": "0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc", "urls": ["bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57", "dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ"], "license": "MIT"}, "lib/forge-std/src/StdStyle.sol": {"keccak256": "0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d", "urls": ["bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8", "dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK"], "license": "MIT"}, "lib/forge-std/src/StdUtils.sol": {"keccak256": "0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737", "urls": ["bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138", "dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776"], "license": "MIT"}, "lib/forge-std/src/Vm.sol": {"keccak256": "0xaae88142a348064677cf81169ea0ca706be3e99e2a0f13a01878ff8d52cf6b25", "urls": ["bzz-raw://da4fb0ed1b31381c7e60ee386acc72796e8a8325c83739ec9a3678e2f841073c", "dweb:/ipfs/Qmb4MU6FpAi8Wt5kUQ3wa7ooHnfMVbdd9zuvnqbTehLmxJ"], "license": "MIT OR Apache-2.0"}, "lib/forge-std/src/console.sol": {"keccak256": "0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5", "urls": ["bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57", "dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP"], "license": "MIT"}, "lib/forge-std/src/console2.sol": {"keccak256": "0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f", "urls": ["bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d", "dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ"], "license": "MIT"}, "lib/forge-std/src/interfaces/IMulticall3.sol": {"keccak256": "0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a", "urls": ["bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0", "dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2"], "license": "MIT"}, "lib/forge-std/src/safeconsole.sol": {"keccak256": "0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11", "urls": ["bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab", "dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol": {"keccak256": "0xc163fcf9bb10138631a9ba5564df1fa25db9adff73bd9ee868a8ae1858fe093a", "urls": ["bzz-raw://9706d43a0124053d9880f6e31a59f31bc0a6a3dc1acd66ce0a16e1111658c5f6", "dweb:/ipfs/QmUFmfowzkRwGtDu36cXV9SPTBHJ3n7dG9xQiK5B28jTf2"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol": {"keccak256": "0x631188737069917d2f909d29ce62c4d48611d326686ba6683e26b72a23bfac0b", "urls": ["bzz-raw://7a61054ae84cd6c4d04c0c4450ba1d6de41e27e0a2c4f1bcdf58f796b401c609", "dweb:/ipfs/QmUvtdp7X1mRVyC3CsHrtPbgoqWaXHp3S1ZR24tpAQYJWM"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol": {"keccak256": "0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397", "urls": ["bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9", "dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV"], "license": "MIT"}, "script/configuration/SetCollateralFactor.s.sol": {"keccak256": "0x02af1601cbaccd8bce2aedb6c3eaae393b77707b7287cd0f1a56510ddc48df54", "urls": ["bzz-raw://c9ce749bd03259f2feeb423772fdc6cac52a7328ec6de4c180f39e0d2b8fb06e", "dweb:/ipfs/QmZt7CTBFhVADk5DMvsZyssEYiDrCkCa3PGNgetxx9gPxF"], "license": "BSL-1.1"}, "src/Operator/Operator.sol": {"keccak256": "0x1486ed6af6f40f0e045403ac9e35bc045ae7031d9ecac7d1f0358f78ff83b469", "urls": ["bzz-raw://e136048d46ae9b4384f561e16dfd4638e68db987007d2059aaf19011dcfc8f65", "dweb:/ipfs/QmcyrVa45W2qYtd9UuJz9C89M3xKzKzACDQqVMAk4hvPeq"], "license": "BSL-1.1"}, "src/Operator/OperatorStorage.sol": {"keccak256": "0x3d9e7f2bc64ab9b5de39d4a58f0a72ac0f86c3b95a6318dd03a7b8acde166783", "urls": ["bzz-raw://dc0d5e026e00f5f5f92cded7704bb602b06f387370ddd7ee0d75111099b01f3a", "dweb:/ipfs/QmQfZzADQpBuvTHJpbHz6P4fGhaGWvQgkU8qMkKULCLdjQ"], "license": "BSL-1.1"}, "src/interfaces/IBlacklister.sol": {"keccak256": "0x47ed20135507a9d16473027cbee18de5d952846bada761c6248b91c75cd88be1", "urls": ["bzz-raw://91a886b95dd578156f9b6cec3236e4751aae8a38e5d15e0d6045217c6fbd3d63", "dweb:/ipfs/QmRNjz7sGTuMkQNZhwwcg5DNN2eeqTZSAdZmxWxbx66CHU"], "license": "BSL-1.1"}, "src/interfaces/IOperator.sol": {"keccak256": "0x0ae3d63eb07518da5a4fd6de7b013b6f55c87defb31900548378cffc70a51f72", "urls": ["bzz-raw://1d9710c4ac43cb0563449aa519501b4ed18a331c90d0bc7eb79cdc56407bad8a", "dweb:/ipfs/Qmafe9Wup2GF9pD83CLWUrRait93vwvReqU1C7EHQhY7WV"], "license": "BSL-1.1"}, "src/interfaces/IOracleOperator.sol": {"keccak256": "0xcc2848c75dd5e1531c2b958746cdd76a3b2b329be98fdca4311219a12a6b409e", "urls": ["bzz-raw://337bdabf03ac279d95ba96aca60105932c7af5c15fdb4b21ab1c369509310fbc", "dweb:/ipfs/QmNYrNYNUwWWSrPwHdarhxt1ShdH1dy8B94M49TZ7XvmK1"], "license": "BSL-1.1"}, "src/interfaces/IRewardDistributor.sol": {"keccak256": "0xd39c4db6e2a85e90db82e20685d4b235966d993c3407e1c0fb52add6421b86df", "urls": ["bzz-raw://e865fe1569ec1ac79cd924a9aa2e2b43754555e510526f6cd030395e9923f01d", "dweb:/ipfs/QmXcshgzCxLKBZJYGuQviEamruhmwXWNJ3ZYLxazWBahuc"], "license": "BSL-1.1"}, "src/interfaces/IRoles.sol": {"keccak256": "0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365", "urls": ["bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c", "dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj"], "license": "BSL-1.1"}, "src/interfaces/ImToken.sol": {"keccak256": "0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f", "urls": ["bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36", "dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL"], "license": "BSL-1.1"}, "src/utils/ExponentialNoError.sol": {"keccak256": "0xa97178117d8c32059d55dd2224e5514c54b31f896e565c3834cab3d7502b1bb6", "urls": ["bzz-raw://1cecc0713709f57a5bb7f64d2fa06b9437a42752fa3fed5d3c0b19d82aa56ef4", "dweb:/ipfs/QmQqBCZkzmZxomXLr3b1tHUV55n8EPk3wAUsXgojYSpR41"], "license": "BSL-1.1"}}, "version": 1}, "id": 63}