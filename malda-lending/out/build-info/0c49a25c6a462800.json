{"id": "0c49a25c6a462800", "source_id_to_path": {"0": "lib/forge-std/src/Base.sol", "1": "lib/forge-std/src/Script.sol", "2": "lib/forge-std/src/StdAssertions.sol", "3": "lib/forge-std/src/StdChains.sol", "4": "lib/forge-std/src/StdCheats.sol", "5": "lib/forge-std/src/StdConstants.sol", "6": "lib/forge-std/src/StdError.sol", "7": "lib/forge-std/src/StdInvariant.sol", "8": "lib/forge-std/src/StdJson.sol", "9": "lib/forge-std/src/StdMath.sol", "10": "lib/forge-std/src/StdStorage.sol", "11": "lib/forge-std/src/StdStyle.sol", "12": "lib/forge-std/src/StdToml.sol", "13": "lib/forge-std/src/StdUtils.sol", "14": "lib/forge-std/src/Test.sol", "15": "lib/forge-std/src/Vm.sol", "16": "lib/forge-std/src/console.sol", "17": "lib/forge-std/src/console2.sol", "18": "lib/forge-std/src/interfaces/IERC20.sol", "19": "lib/forge-std/src/interfaces/IMulticall3.sol", "20": "lib/forge-std/src/safeconsole.sol", "21": "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "22": "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "23": "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "24": "lib/openzeppelin-contracts-upgradeable/contracts/utils/ReentrancyGuardUpgradeable.sol", "25": "lib/openzeppelin-contracts/contracts/access/AccessControl.sol", "26": "lib/openzeppelin-contracts/contracts/access/IAccessControl.sol", "27": "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "28": "lib/openzeppelin-contracts/contracts/governance/TimelockController.sol", "29": "lib/openzeppelin-contracts/contracts/interfaces/IERC1967.sol", "30": "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol", "31": "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "32": "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Utils.sol", "33": "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "34": "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "35": "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "36": "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "37": "lib/openzeppelin-contracts/contracts/token/ERC1155/IERC1155Receiver.sol", "38": "lib/openzeppelin-contracts/contracts/token/ERC1155/utils/ERC1155Holder.sol", "39": "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "40": "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "41": "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "42": "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol", "43": "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "44": "lib/openzeppelin-contracts/contracts/token/ERC721/IERC721Receiver.sol", "45": "lib/openzeppelin-contracts/contracts/token/ERC721/utils/ERC721Holder.sol", "46": "lib/openzeppelin-contracts/contracts/utils/Address.sol", "47": "lib/openzeppelin-contracts/contracts/utils/Context.sol", "48": "lib/openzeppelin-contracts/contracts/utils/ReentrancyGuard.sol", "49": "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "50": "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "51": "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "52": "lib/openzeppelin-contracts/contracts/utils/cryptography/MessageHashUtils.sol", "53": "lib/openzeppelin-contracts/contracts/utils/introspection/ERC165.sol", "54": "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol", "55": "lib/openzeppelin-contracts/contracts/utils/math/Math.sol", "56": "lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol", "57": "lib/risc0-ethereum/contracts/src/IRiscZeroVerifier.sol", "58": "lib/risc0-ethereum/contracts/src/Util.sol", "59": "lib/risc0-ethereum/contracts/src/steel/Steel.sol", "60": "script/configuration/AddPausable.s.sol", "61": "script/configuration/SetBorrowCap.s.sol", "62": "script/configuration/SetBorrowRateMaxMantissa.s.sol", "63": "script/configuration/SetCollateralFactor.s.sol", "64": "script/configuration/SetGasFees.s.sol", "65": "script/configuration/SetGasHelper.s.sol", "66": "script/configuration/SetLiquidationBonus.s.sol", "67": "script/configuration/SetOperatorInRewardDistributor.s.sol", "68": "script/configuration/SetPriceFeedOnOracle.s.sol", "69": "script/configuration/SetPriceFeedOnOracleV4.s.sol", "70": "script/configuration/SetPriceOracleOnOperator.s.sol", "71": "script/configuration/SetReserveFactor.s.sol", "72": "script/configuration/SetRole.s.sol", "73": "script/configuration/SetSupplyCap.s.sol", "74": "script/configuration/SetWhitelistDisabled.s.sol", "75": "script/configuration/SetWhitelistEnabled.s.sol", "76": "script/configuration/SetWhitelistedUsersOnGateway.s.sol", "77": "script/configuration/SetZkImageId.s.sol", "78": "script/configuration/SupportMarket.s.sol", "79": "script/configuration/TransferOwnership.s.sol", "80": "script/configuration/UpdateAllAllowedChains.s.sol", "81": "script/configuration/UpdateAllowedChains.s.sol", "82": "script/configuration/rebalancers/CreateEverclearIntent.sol", "83": "script/configuration/rebalancers/RebalanceWithEverclear.s.sol", "84": "script/deployers/DeployBase.sol", "85": "script/deployers/DeployBaseRelease.sol", "86": "script/deployers/DeployDeployer.s.sol", "87": "script/deployers/Types.sol", "88": "script/deployment/DeployProtocol.s.sol", "89": "script/deployment/DeployProtocolUpdated.s.sol", "90": "script/deployment/DeployRebalancers.s.sol", "91": "script/deployment/generic/DeployBatchSubmitter.s.sol", "92": "script/deployment/generic/DeployGasHelper.s.sol", "93": "script/deployment/generic/DeployLiquidationHelper.s.sol", "94": "script/deployment/generic/DeployMockToken.s.sol", "95": "script/deployment/generic/DeployPauser.s.sol", "96": "script/deployment/generic/DeployRbac.s.sol", "97": "script/deployment/generic/DeployReferralSigning.s.sol", "98": "script/deployment/generic/DeployTimelockController.s.sol", "99": "script/deployment/generic/DeployZkVerifier.s.sol", "100": "script/deployment/interest/DeployJumpRateModelV4.s.sol", "101": "script/deployment/mainnet/ConfigureRelease.s.sol", "102": "script/deployment/mainnet/DeployCoreRelease.s.sol", "103": "script/deployment/mainnet/DeployMarketsRelease.s.sol", "104": "script/deployment/mainnet/DeployProtocolRelease.s.sol", "105": "script/deployment/mainnet/TransferReleaseOwnership.s.sol", "106": "script/deployment/markets/BaseMarketDeploy.s.sol", "107": "script/deployment/markets/DeployOperator.s.sol", "108": "script/deployment/markets/extension/DeployExtensionMarket.s.sol", "109": "script/deployment/markets/host/DeployHostMarket.s.sol", "110": "script/deployment/migrator/DeployMigrator.s.sol", "111": "script/deployment/oracles/DeployChainlinkOracle.s.sol", "112": "script/deployment/oracles/DeployMixedPriceOracleV3.s.sol", "113": "script/deployment/oracles/DeployMixedPriceOracleV4.s.sol", "114": "script/deployment/oracles/DeployMockOracle.s.sol", "115": "script/deployment/rebalancer/DeployAcrossBridge.s.sol", "116": "script/deployment/rebalancer/DeployEverclearBridge.s.sol", "117": "script/deployment/rebalancer/DeployRebalancer.s.sol", "118": "script/deployment/rebalancer/RedeployRebalancer.s.sol", "119": "script/deployment/rewards/DeployRewardDistributor.s.sol", "120": "script/deployment/testnet/ConfigureTestnet.s.sol", "121": "script/deployment/testnet/DeployCoreTestnet.s.sol", "122": "script/deployment/testnet/DeployMarketsTestnet.s.sol", "123": "script/deployment/testnet/DeployProtocolTestnet.s.sol", "124": "script/deployment/upgrades/UpgradeMarket.s.sol", "125": "script/verifiers/SimpleInteractionsCheck.s.sol", "126": "src/Operator/EmptyOperator.sol", "127": "src/Operator/Operator.sol", "128": "src/Operator/OperatorStorage.sol", "129": "src/Roles.sol", "130": "src/blacklister/Blacklister.sol", "131": "src/interest/JumpRateModelV4.sol", "132": "src/interfaces/IBlacklister.sol", "133": "src/interfaces/IBridge.sol", "134": "src/interfaces/IDefaultAdapter.sol", "135": "src/interfaces/IGasFeesHelper.sol", "136": "src/interfaces/IInterestRateModel.sol", "137": "src/interfaces/IOperator.sol", "138": "src/interfaces/IOracleOperator.sol", "139": "src/interfaces/IOwnable.sol", "140": "src/interfaces/IPauser.sol", "141": "src/interfaces/IRebalancer.sol", "142": "src/interfaces/IRewardDistributor.sol", "143": "src/interfaces/IRoles.sol", "144": "src/interfaces/ImErc20.sol", "145": "src/interfaces/ImErc20Host.sol", "146": "src/interfaces/ImToken.sol", "147": "src/interfaces/ImTokenGateway.sol", "148": "src/interfaces/external/across/IAcrossReceiverV3.sol", "149": "src/interfaces/external/across/IAcrossSpokePoolV3.sol", "150": "src/interfaces/external/chainlink/IAggregatorV3.sol", "151": "src/interfaces/external/connext/IConnext.sol", "152": "src/interfaces/external/everclear/IEverclearSpoke.sol", "153": "src/interfaces/external/everclear/IFeeAdapter.sol", "154": "src/interfaces/external/layerzero/ILayerZeroEndpoint.sol", "155": "src/interfaces/external/layerzero/ILayerZeroReceiver.sol", "156": "src/interfaces/external/layerzero/ILayerZeroUserApplicationConfig.sol", "157": "src/interfaces/external/layerzero/v2/ILayerZeroEndpointV2.sol", "158": "src/interfaces/external/layerzero/v2/ILayerZeroOFT.sol", "159": "src/interfaces/external/layerzero/v2/ILayerZeroReceiverV2.sol", "160": "src/interfaces/external/layerzero/v2/IMessageLib.sol", "161": "src/interfaces/external/layerzero/v2/IMessageLibManager.sol", "162": "src/interfaces/external/layerzero/v2/IMessagingChannel.sol", "163": "src/interfaces/external/layerzero/v2/IMessagingComposer.sol", "164": "src/interfaces/external/layerzero/v2/IMessagingContext.sol", "165": "src/interfaces/external/poh/IPohVerifier.sol", "166": "src/libraries/Bytes32AddressLib.sol", "167": "src/libraries/BytesLib.sol", "168": "src/libraries/CREATE3.sol", "169": "src/libraries/CommonLib.sol", "170": "src/libraries/SafeApprove.sol", "171": "src/libraries/mTokenProofDecoderLib.sol", "172": "src/mToken/BatchSubmitter.sol", "173": "src/mToken/extension/mTokenGateway.sol", "174": "src/mToken/host/mErc20Host.sol", "175": "src/mToken/mErc20.sol", "176": "src/mToken/mErc20Immutable.sol", "177": "src/mToken/mErc20Upgradable.sol", "178": "src/mToken/mToken.sol", "179": "src/mToken/mTokenConfiguration.sol", "180": "src/mToken/mTokenStorage.sol", "181": "src/migration/IMigrator.sol", "182": "src/migration/Migrator.sol", "183": "src/oracles/ChainlinkOracle.sol", "184": "src/oracles/MixedPriceOracleV3.sol", "185": "src/oracles/MixedPriceOracleV4.sol", "186": "src/oracles/gas/DefaultGasHelper.sol", "187": "src/pauser/Pauser.sol", "188": "src/rebalancer/Rebalancer.sol", "189": "src/rebalancer/bridges/AcrossBridge.sol", "190": "src/rebalancer/bridges/BaseBridge.sol", "191": "src/rebalancer/bridges/EverclearBridge.sol", "192": "src/referral/ReferralSigning.sol", "193": "src/rewards/RewardDistributor.sol", "194": "src/utils/Deployer.sol", "195": "src/utils/ExponentialNoError.sol", "196": "src/utils/LiquidationHelper.sol", "197": "src/utils/WrapAndSupply.sol", "198": "src/verifier/ZkVerifier.sol", "199": "test/Base_Unit_Test.t.sol", "200": "test/integration/Base_Integration_Test.t.sol", "201": "test/integration/migration/Migrator.Integration.t.sol", "202": "test/integration/shared/Rebalancer_Integration_Shared.t.sol", "203": "test/mocks/BridgeMock.sol", "204": "test/mocks/ERC20Mock.sol", "205": "test/mocks/LendingProtocolMock.sol", "206": "test/mocks/MockRoles.sol", "207": "test/mocks/OracleMock.sol", "208": "test/mocks/Risc0VerifierMock.sol", "209": "test/mocks/WrappedMock.sol", "210": "test/referrals/ReferralSigning.t.sol", "211": "test/unit/BatchSubmitter/BatchSubmitter_methods.t.sol", "212": "test/unit/LendingProtocolMock/LendingProtocolMock.t.sol", "213": "test/unit/Pauser/Pauser_addPausableMarket.t.sol", "214": "test/unit/Pauser/Pauser_pause.t.sol", "215": "test/unit/Pauser/Pauser_removePausableMarket.t.sol", "216": "test/unit/Rebalancer/Rebalancer_methods.t.sol", "217": "test/unit/blacklist/Blacklister.t.sol", "218": "test/unit/mErc20/mErc20_borrow.t.sol", "219": "test/unit/mErc20/mErc20_liquidateHelper.t.sol", "220": "test/unit/mErc20/mErc20_mint.t.sol", "221": "test/unit/mErc20/mErc20_redeemAndRedeemUnderlying.t.sol", "222": "test/unit/mErc20/mErc20_repay.t.sol", "223": "test/unit/mErc20/mErc20_repayBehalf.t.sol", "224": "test/unit/mErc20Host/mErc20Host_borrow.t.sol", "225": "test/unit/mErc20Host/mErc20Host_liquidate.t.sol", "226": "test/unit/mErc20Host/mErc20Host_mint.t.sol", "227": "test/unit/mErc20Host/mErc20Host_redeemAndRedeemUnderlying.t.sol", "228": "test/unit/mErc20Host/mErc20Host_repay.t.sol", "229": "test/unit/mTokenGateway/mTokenGateway_outHere.t.sol", "230": "test/unit/mTokenGateway/mTokenGateway_supplyOnHost.t.sol", "231": "test/unit/mocks/ERC20MockTests.t.sol", "232": "test/unit/oracle/MixedPriceOracleV4.t.sol", "233": "test/unit/oracle/OracleUnderlying.t.sol", "234": "test/unit/shared/BatchSubmitter_Unit_Shared.t.sol", "235": "test/unit/shared/Pauser_Unit_Shared.t.sol", "236": "test/unit/shared/Rebalancer_Unit_Shared.t.sol", "237": "test/unit/shared/mToken_Unit_Shared.t.sol", "238": "test/utils/Constants.sol", "239": "test/utils/Events.sol", "240": "test/utils/Helpers.sol", "241": "test/utils/Types.sol"}, "language": "Solidity"}