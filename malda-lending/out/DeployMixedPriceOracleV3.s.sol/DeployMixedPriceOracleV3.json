{"abi": [{"type": "function", "name": "IS_SCRIPT", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "run", "inputs": [{"name": "deployer", "type": "address", "internalType": "contract Deployer"}, {"name": "usdcFeed", "type": "address", "internalType": "address"}, {"name": "wethFeed", "type": "address", "internalType": "address"}, {"name": "roles", "type": "address", "internalType": "address"}, {"name": "stalenessPeriod", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "runWithFeeds", "inputs": [{"name": "deployer", "type": "address", "internalType": "contract Deployer"}, {"name": "feeds", "type": "tuple[]", "internalType": "struct OracleFeed[]", "components": [{"name": "symbol", "type": "string", "internalType": "string"}, {"name": "defaultFeed", "type": "address", "internalType": "address"}, {"name": "toSymbol", "type": "string", "internalType": "string"}, {"name": "underlyingDecimals", "type": "uint8", "internalType": "uint8"}]}, {"name": "roles", "type": "address", "internalType": "address"}, {"name": "stalenessPeriod", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}], "bytecode": {"object": "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", "sourceMap": "555:3029:112:-:0;;;3126:44:3;;;-1:-1:-1;;849:28:1;;;;;555:3029:112;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "555:3029:112:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2045:1306;;;;;;:::i;:::-;;:::i;:::-;;;-1:-1:-1;;;;;1437:32:242;;;1419:51;;1407:2;1392:18;2045:1306:112;;;;;;;;605:1434;;;;;;:::i;:::-;;:::i;849:28:1:-;;;;;;;;;;;;;;;5343:14:242;;5336:22;5318:41;;5306:2;5291:18;849:28:1;5178:187:242;2045:1306:112;2222:25;;-1:-1:-1;;;2222:25:112;;5572:2:242;2222:25:112;;;5554:21:242;5611:2;5591:18;;;5584:30;-1:-1:-1;;;5630:18:242;;;5623:41;2185:7:112;;;;336:42:0;;2222:10:112;;5681:18:242;;2222:25:112;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2284:15;;;2297:1;2284:15;;;;;;;;;2208:39;;-1:-1:-1;2258:23:112;;2284:15;;;;;;;;;;;;;;;;;;;;;2258:41;;2309:20;;;;;;;;;;;;;-1:-1:-1;;;2309:20:112;;;:7;2317:1;2309:10;;;;;;;;:::i;:::-;;;;;;:20;;;;2339;;;;;;;;;;;;;-1:-1:-1;;;2339:20:112;;;:7;2347:1;2339:10;;;;;;;;:::i;:::-;;;;;;;;;;:20;2417:36;;;2451:1;2417:36;;;;;;;;;2370:44;;2417:36;;;;;-1:-1:-1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2417:36:112;;;;;;;;;;;;;;;;2370:83;;2476:92;;;;;;;;2518:8;-1:-1:-1;;;;;2476:92:112;;;;;;;;;;;;;;;;;;-1:-1:-1;;;2476:92:112;;;;;;;2565:1;2476:92;;;2463:7;2471:1;2463:10;;;;;;;;:::i;:::-;;;;;;:105;;;;2592:93;;;;;;;;2634:8;-1:-1:-1;;;;;2592:93:112;;;;;;;;;;;;;;;;;;-1:-1:-1;;;2592:93:112;;;;;;;2681:2;2592:93;;;2579:7;2587:1;2579:10;;;;;;;;:::i;:::-;;;;;;:106;;;;2696:12;2711:29;;;;;;;;;;;;;;-1:-1:-1;;;2711:29:112;;;:7;:29::i;:::-;2768:25;;-1:-1:-1;;;2768:25:112;;;;;6177::242;;;2696:44:112;;-1:-1:-1;2750:15:112;;-1:-1:-1;;;;;2768:19:112;;;;;6150:18:242;;2768:25:112;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2750:43;-1:-1:-1;;;;;;2807:19:112;;;:23;2803:517;;2846:66;;;;;;;;;;;;;;;;;;2904:7;2846:11;:66::i;:::-;2803:517;;;2943:22;;-1:-1:-1;;;2943:22:112;;;;;6177:25:242;;;336:42:0;;2943:17:112;;6150:18:242;;2943:22:112;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2989:8;-1:-1:-1;;;;;2989:15:112;;3022:4;3082:37;;;;;;;;:::i;:::-;-1:-1:-1;;3082:37:112;;;;;;;;;;;;;;;;3121:52;;3132:7;;3141;;3150:5;;3157:15;;3082:37;3121:52;;:::i;:::-;;;;-1:-1:-1;;3121:52:112;;;;;;;;;;3044:147;;;3121:52;3044:147;;:::i;:::-;;;;;;;;;;;;;2989:216;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2979:226;;336:42:0;-1:-1:-1;;;;;3219:16:112;;:18;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3251:58;;;;;;;;;;;;;;;;;;3301:7;3251:11;:58::i;:::-;3337:7;2045:1306;-1:-1:-1;;;;;;;;;;2045:1306:112:o;605:1434::-;782:25;;-1:-1:-1;;;782:25:112;;5572:2:242;782:25:112;;;5554:21:242;5611:2;5591:18;;;5584:30;-1:-1:-1;;;5630:18:242;;;5623:41;745:7:112;;;;336:42:0;;782:10:112;;5681:18:242;;782:25:112;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;832:12;;768:39;;-1:-1:-1;818:11:112;832:12;880:17;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;854:43;;907:44;988:3;954:38;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;954:38:112;;;;;;;;;;;;;;;;;907:85;;1007:9;1002:369;1022:3;1018:1;:7;1002:369;;;1055:5;1061:1;1055:8;;;;;;;;:::i;:::-;;;;;;;:15;;;1042:7;1050:1;1042:10;;;;;;;;:::i;:::-;;;;;;:28;;;;1097:204;;;;;;;;1156:5;1162:1;1156:8;;;;;;;;:::i;:::-;;;;;;;:20;;;-1:-1:-1;;;;;1097:204:112;;;;;1204:5;1210:1;1204:8;;;;;;;;:::i;:::-;;;;;;;:17;;;1097:204;;;;1259:5;1265:1;1259:8;;;;;;;;:::i;:::-;;;;;;;:27;;;1097:204;;;;;1084:7;1092:1;1084:10;;;;;;;;:::i;:::-;;;;;;;;;;:217;1343:3;;1002:369;;;;1380:12;1395:33;;;;;;;;;;;;;;-1:-1:-1;;;1395:33:112;;;:7;:33::i;:::-;1456:25;;-1:-1:-1;;;1456:25:112;;;;;6177::242;;;1380:48:112;;-1:-1:-1;1438:15:112;;-1:-1:-1;;;;;1456:19:112;;;;;6150:18:242;;1456:25:112;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1438:43;-1:-1:-1;;;;;;1495:19:112;;;:23;1491:517;;1534:66;;;;;;;;;;;;;;;;;;1592:7;1534:11;:66::i;1491:517::-;1631:22;;-1:-1:-1;;;1631:22:112;;;;;6177:25:242;;;336:42:0;;1631:17:112;;6150:18:242;;1631:22:112;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1677:8;-1:-1:-1;;;;;1677:15:112;;1710:4;1770:37;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;1820:7;1829;1838:5;1845:15;1809:52;;;;;;;;;;;:::i;3357:225::-;3501:27;;-1:-1:-1;;;3501:27:112;;10185:2:242;3501:27:112;;;10167:21:242;10224:2;10204:18;;;10197:30;-1:-1:-1;;;10243:18:242;;;10236:41;3417:7:112;;3483:10;;336:42:0;;3501:12:112;;10294:18:242;;3501:27:112;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;3501:27:112;;;;;;;;;;;;:::i;:::-;3551:4;3537:26;;;;;;;;:::i;:::-;;;;-1:-1:-1;;3537:26:112;;;;;;;;;;3466:99;;;;3537:26;3466:99;;:::i;:::-;;;;;;;;;;;;;3443:132;;;;;;3436:139;;3357:225;;;:::o;7740:145:16:-;7807:71;7870:2;7874;7823:54;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;7823:54:16;;;;;;;;;;;;;;-1:-1:-1;;;;;7823:54:16;-1:-1:-1;;;7823:54:16;;;7807:15;:71::i;:::-;7740:145;;:::o;851:129::-;922:51;965:7;934:29;922:51::i;:::-;851:129;:::o;180:463::-;265:22;131:42;265:40;;594:1;571;541:7;535:14;510:2;501:7;497:16;461:14;434:5;402:211;381:246;367:270;180:463;:::o;-1:-1:-1:-;;;;;;;;:::o;14:141:242:-;-1:-1:-1;;;;;99:31:242;;89:42;;79:70;;145:1;142;135:12;160:144;228:20;;257:41;228:20;257:41;:::i;:::-;160:144;;;:::o;309:850::-;422:6;430;438;446;454;507:3;495:9;486:7;482:23;478:33;475:53;;;524:1;521;514:12;475:53;563:9;550:23;582:41;617:5;582:41;:::i;:::-;642:5;-1:-1:-1;699:2:242;684:18;;671:32;712:43;671:32;712:43;:::i;:::-;774:7;-1:-1:-1;833:2:242;818:18;;805:32;846:43;805:32;846:43;:::i;:::-;908:7;-1:-1:-1;967:2:242;952:18;;939:32;980:43;939:32;980:43;:::i;:::-;309:850;;;;-1:-1:-1;309:850:242;;1122:3;1107:19;1094:33;;309:850;-1:-1:-1;;309:850:242:o;1481:127::-;1542:10;1537:3;1533:20;1530:1;1523:31;1573:4;1570:1;1563:15;1597:4;1594:1;1587:15;1613:252;1685:2;1679:9;1727:3;1715:16;;1761:18;1746:34;;1782:22;;;1743:62;1740:88;;;1808:18;;:::i;:::-;1844:2;1837:22;1613:252;:::o;1870:275::-;1941:2;1935:9;2006:2;1987:13;;-1:-1:-1;;1983:27:242;1971:40;;2041:18;2026:34;;2062:22;;;2023:62;2020:88;;;2088:18;;:::i;:::-;2124:2;2117:22;1870:275;;-1:-1:-1;1870:275:242:o;2150:187::-;2199:4;2232:18;2224:6;2221:30;2218:56;;;2254:18;;:::i;:::-;-1:-1:-1;2320:2:242;2299:15;-1:-1:-1;;2295:29:242;2326:4;2291:40;;2150:187::o;2342:488::-;2385:5;2438:3;2431:4;2423:6;2419:17;2415:27;2405:55;;2456:1;2453;2446:12;2405:55;2496:6;2483:20;2527:53;2543:36;2572:6;2543:36;:::i;:::-;2527:53;:::i;:::-;2605:6;2596:7;2589:23;2659:3;2652:4;2643:6;2635;2631:19;2627:30;2624:39;2621:59;;;2676:1;2673;2666:12;2621:59;2741:6;2734:4;2726:6;2722:17;2715:4;2706:7;2702:18;2689:59;2797:1;2768:20;;;2790:4;2764:31;2757:42;;;;2772:7;2342:488;-1:-1:-1;;;2342:488:242:o;2835:2338::-;2993:6;3001;3009;3017;3070:3;3058:9;3049:7;3045:23;3041:33;3038:53;;;3087:1;3084;3077:12;3038:53;3126:9;3113:23;3145:41;3180:5;3145:41;:::i;:::-;3205:5;-1:-1:-1;3261:2:242;3246:18;;3233:32;3288:18;3277:30;;3274:50;;;3320:1;3317;3310:12;3274:50;3343:22;;3396:4;3388:13;;3384:27;-1:-1:-1;3374:55:242;;3425:1;3422;3415:12;3374:55;3465:2;3452:16;3491:18;3483:6;3480:30;3477:56;;;3513:18;;:::i;:::-;3559:6;3556:1;3552:14;3586:28;3610:2;3606;3602:11;3586:28;:::i;:::-;3648:19;;;3692:2;3722:11;;;3718:20;;;3683:12;;;;3750:19;;;3747:39;;;3782:1;3779;3772:12;3747:39;3814:2;3810;3806:11;3795:22;;3826:1157;3842:6;3837:3;3834:15;3826:1157;;;3928:3;3915:17;3964:18;3951:11;3948:35;3945:55;;;3996:1;3993;3986:12;3945:55;4023:20;;4095:3;4067:16;;;-1:-1:-1;;4063:30:242;4059:40;4056:60;;;4112:1;4109;4102:12;4056:60;4144:22;;:::i;:::-;4216:2;4212;4208:11;4195:25;4249:18;4239:8;4236:32;4233:52;;;4281:1;4278;4271:12;4233:52;4314:54;4360:7;4355:2;4344:8;4340:2;4336:17;4332:26;4314:54;:::i;:::-;4305:7;4298:71;;4418:2;4414;4410:11;4397:25;4435:43;4470:7;4435:43;:::i;:::-;4511:2;4498:16;;4491:33;4574:2;4566:11;;4553:25;4607:18;4594:32;;4591:52;;;4639:1;4636;4629:12;4591:52;4681:54;4727:7;4722:2;4711:8;4707:2;4703:17;4699:26;4681:54;:::i;:::-;4676:2;4667:7;4663:16;4656:80;;4785:3;4781:2;4777:12;4764:26;4749:41;;4838:4;4829:7;4825:18;4816:7;4813:31;4803:59;;4858:1;4855;4848:12;4803:59;4895:2;4882:16;;4875:33;;;;4921:20;;4970:2;3859:12;;;;4961;;;;3826:1157;;;5002:5;4992:15;;;;;;;5026:38;5060:2;5049:9;5045:18;5026:38;:::i;:::-;2835:2338;;;;-1:-1:-1;5016:48:242;;5137:2;5122:18;5109:32;;-1:-1:-1;;2835:2338:242:o;5710:184::-;5780:6;5833:2;5821:9;5812:7;5808:23;5804:32;5801:52;;;5849:1;5846;5839:12;5801:52;-1:-1:-1;5872:16:242;;5710:184;-1:-1:-1;5710:184:242:o;5899:127::-;5960:10;5955:3;5951:20;5948:1;5941:31;5991:4;5988:1;5981:15;6015:4;6012:1;6005:15;6213:261;6283:6;6336:2;6324:9;6315:7;6311:23;6307:32;6304:52;;;6352:1;6349;6342:12;6304:52;6384:9;6378:16;6403:41;6438:5;6403:41;:::i;:::-;6463:5;6213:261;-1:-1:-1;;;6213:261:242:o;6661:250::-;6746:1;6756:113;6770:6;6767:1;6764:13;6756:113;;;6846:11;;;6840:18;6827:11;;;6820:39;6792:2;6785:10;6756:113;;;-1:-1:-1;;6903:1:242;6885:16;;6878:27;6661:250::o;6916:271::-;6958:3;6996:5;6990:12;7023:6;7018:3;7011:19;7039:76;7108:6;7101:4;7096:3;7092:14;7085:4;7078:5;7074:16;7039:76;:::i;:::-;7169:2;7148:15;-1:-1:-1;;7144:29:242;7135:39;;;;7176:4;7131:50;;6916:271;-1:-1:-1;;6916:271:242:o;7192:1995::-;7548:4;7596:3;7585:9;7581:19;7627:3;7616:9;7609:22;7651:6;7686;7680:13;7717:6;7709;7702:22;7755:3;7744:9;7740:19;7733:26;;7818:3;7808:6;7805:1;7801:14;7790:9;7786:30;7782:40;7768:54;;7857:4;7849:6;7845:17;7880:1;7890:260;7904:6;7901:1;7898:13;7890:260;;;7997:3;7993:8;7981:9;7973:6;7969:22;7965:37;7960:3;7953:50;8026:40;8059:6;8050;8044:13;8026:40;:::i;:::-;8016:50;-1:-1:-1;8101:4:242;8126:14;;;;8089:17;;;;;7926:1;7919:9;7890:260;;;7894:3;;;;8200:9;8192:6;8188:22;8181:4;8170:9;8166:20;8159:52;8233:6;8270;8264:13;8301:8;8293:6;8286:24;8340:4;8332:6;8328:17;8319:26;;8403:4;8391:8;8388:1;8384:16;8376:6;8372:29;8368:40;8445:4;8437:6;8433:17;8470:1;8480:576;8496:8;8491:3;8488:17;8480:576;;;8569:19;;;-1:-1:-1;;8565:33:242;8551:48;;8622:15;;8669:9;;-1:-1:-1;;;;;8665:35:242;8650:51;;8748:4;8740:13;;;8734:20;8793:4;8774:17;;;8767:31;;;8734:20;8825:50;;8857:17;;8734:20;8825:50;:::i;:::-;8928:4;8920:13;;;8914:20;8895:17;;;;8888:47;;;;9003:4;9030:16;;;;8811:64;;-1:-1:-1;8989:19:242;;;;;8524:1;8515:11;8480:576;;;-1:-1:-1;;;;;;;1230:31:242;;9130:4;9115:20;;1218:44;9073:6;-1:-1:-1;9088:48:242;;-1:-1:-1;;;1164:104:242;9088:48;9174:6;9167:4;9156:9;9152:20;9145:36;7192:1995;;;;;;;:::o;9192:492::-;9367:3;9405:6;9399:13;9421:66;9480:6;9475:3;9468:4;9460:6;9456:17;9421:66;:::i;:::-;9550:13;;9509:16;;;;9572:70;9550:13;9509:16;9619:4;9607:17;;9572:70;:::i;:::-;9658:20;;9192:492;-1:-1:-1;;;;9192:492:242:o;9689:289::-;9864:6;9853:9;9846:25;9907:2;9902;9891:9;9887:18;9880:30;9827:4;9927:45;9968:2;9957:9;9953:18;9945:6;9927:45;:::i;:::-;9919:53;9689:289;-1:-1:-1;;;;9689:289:242:o;10323:669::-;10403:6;10456:2;10444:9;10435:7;10431:23;10427:32;10424:52;;;10472:1;10469;10462:12;10424:52;10505:9;10499:16;10538:18;10530:6;10527:30;10524:50;;;10570:1;10567;10560:12;10524:50;10593:22;;10646:4;10638:13;;10634:27;-1:-1:-1;10624:55:242;;10675:1;10672;10665:12;10624:55;10708:2;10702:9;10733:53;10749:36;10778:6;10749:36;:::i;10733:53::-;10809:6;10802:5;10795:21;10857:7;10852:2;10843:6;10839:2;10835:15;10831:24;10828:37;10825:57;;;10878:1;10875;10868:12;10825:57;10891:71;10955:6;10950:2;10943:5;10939:14;10934:2;10930;10926:11;10891:71;:::i;:::-;10981:5;10323:669;-1:-1:-1;;;;;10323:669:242:o;10997:443::-;11218:3;11256:6;11250:13;11272:66;11331:6;11326:3;11319:4;11311:6;11307:17;11272:66;:::i;:::-;-1:-1:-1;;;11360:16:242;;11385:20;;;-1:-1:-1;11432:1:242;11421:13;;10997:443;-1:-1:-1;10997:443:242:o;11445:613::-;11703:26;11699:31;11690:6;11686:2;11682:15;11678:53;11673:3;11666:66;11648:3;11761:6;11755:13;11777:75;11845:6;11840:2;11835:3;11831:12;11824:4;11816:6;11812:17;11777:75;:::i;:::-;11912:13;;11871:16;;;;11934:76;11912:13;11996:2;11988:11;;11981:4;11969:17;;11934:76;:::i;:::-;12030:17;12049:2;12026:26;;11445:613;-1:-1:-1;;;;;11445:613:242:o;12063:317::-;12240:2;12229:9;12222:21;12203:4;12260:45;12301:2;12290:9;12286:18;12278:6;12260:45;:::i;:::-;12252:53;;12370:1;12366;12361:3;12357:11;12353:19;12345:6;12341:32;12336:2;12325:9;12321:18;12314:60;12063:317;;;;;:::o", "linkReferences": {}}, "methodIdentifiers": {"IS_SCRIPT()": "f8ccbf47", "run(address,address,address,address,uint256)": "8d7deeae", "runWithFeeds(address,(string,address,string,uint8)[],address,uint256)": "ed6fb1dd"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"IS_SCRIPT\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"contract Deployer\",\"name\":\"deployer\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"usdcFeed\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"wethFeed\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"roles\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"stalenessPeriod\",\"type\":\"uint256\"}],\"name\":\"run\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"contract Deployer\",\"name\":\"deployer\",\"type\":\"address\"},{\"components\":[{\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"},{\"internalType\":\"address\",\"name\":\"defaultFeed\",\"type\":\"address\"},{\"internalType\":\"string\",\"name\":\"toSymbol\",\"type\":\"string\"},{\"internalType\":\"uint8\",\"name\":\"underlyingDecimals\",\"type\":\"uint8\"}],\"internalType\":\"struct OracleFeed[]\",\"name\":\"feeds\",\"type\":\"tuple[]\"},{\"internalType\":\"address\",\"name\":\"roles\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"stalenessPeriod\",\"type\":\"uint256\"}],\"name\":\"runWithFeeds\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"notice\":\"forge script DeployMixedPriceOracleV3  \\\\     --slow \\\\     --verify \\\\     --verifier-url <url> \\\\     --rpc-url <url> \\\\     --etherscan-api-key <key> \\\\     --broadcast\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"script/deployment/oracles/DeployMixedPriceOracleV3.s.sol\":\"DeployMixedPriceOracleV3\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"lib/forge-std/src/Base.sol\":{\"keccak256\":\"0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d\",\"dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z\"]},\"lib/forge-std/src/Script.sol\":{\"keccak256\":\"0xc942e27c7baae499beb01afbbae99f24d42af9a6e4aae675bc6901b704aa8e9b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0456008adf68947247f358b62863af4a8e349549d2260f2ff9569ff0e3cf5c98\",\"dweb:/ipfs/QmdviSUj2i7o3TPN5vd2xocqGMFVqjUzaiJTZRYyPxyHPx\"]},\"lib/forge-std/src/StdChains.sol\":{\"keccak256\":\"0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351\",\"dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm\"]},\"lib/forge-std/src/StdCheats.sol\":{\"keccak256\":\"0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41\",\"dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK\"]},\"lib/forge-std/src/StdConstants.sol\":{\"keccak256\":\"0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc\",\"dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r\"]},\"lib/forge-std/src/StdJson.sol\":{\"keccak256\":\"0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974\",\"dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3\"]},\"lib/forge-std/src/StdMath.sol\":{\"keccak256\":\"0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92\",\"dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC\"]},\"lib/forge-std/src/StdStorage.sol\":{\"keccak256\":\"0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57\",\"dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ\"]},\"lib/forge-std/src/StdStyle.sol\":{\"keccak256\":\"0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8\",\"dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK\"]},\"lib/forge-std/src/StdUtils.sol\":{\"keccak256\":\"0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138\",\"dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776\"]},\"lib/forge-std/src/Vm.sol\":{\"keccak256\":\"0xaae88142a348064677cf81169ea0ca706be3e99e2a0f13a01878ff8d52cf6b25\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://da4fb0ed1b31381c7e60ee386acc72796e8a8325c83739ec9a3678e2f841073c\",\"dweb:/ipfs/Qmb4MU6FpAi8Wt5kUQ3wa7ooHnfMVbdd9zuvnqbTehLmxJ\"]},\"lib/forge-std/src/console.sol\":{\"keccak256\":\"0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57\",\"dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP\"]},\"lib/forge-std/src/console2.sol\":{\"keccak256\":\"0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d\",\"dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ\"]},\"lib/forge-std/src/interfaces/IMulticall3.sol\":{\"keccak256\":\"0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0\",\"dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2\"]},\"lib/forge-std/src/safeconsole.sol\":{\"keccak256\":\"0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab\",\"dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3\"]},\"script/deployers/Types.sol\":{\"keccak256\":\"0xdfe4dc54c46c9b5fcd959a17ac33580f659d4d4b1bbf262f80c13963e5c4aad7\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://33209fe74e0e8982c2ea6502da587fcb8a2f3092dbeef205ea66a5d4a6208f43\",\"dweb:/ipfs/Qme6EWBu4Q2T8bbKKPAJh31dimpkcuLi4eh3fN4E6XYqmw\"]},\"script/deployment/oracles/DeployMixedPriceOracleV3.s.sol\":{\"keccak256\":\"0x6a323395bc5f8ce315f262691cf33d91cd9c8af27c0dc2d00748c905c71f697b\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://cadc46a64ba08de59c627e5b81b9024d9defa181d000aa8cb25819eda3ed8a34\",\"dweb:/ipfs/QmRNJR52povLWc6izjNzB3HjadNsyrAazDKZTLrg6ojTyV\"]},\"src/interfaces/IDefaultAdapter.sol\":{\"keccak256\":\"0xbf7e882eeb81776c7be55110bb171c65d166bafeb71d828c085b139bed5735c8\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://7e139fb3ddd0623189493679e73fd42c4e505531502d55e1e699789fa3c1451a\",\"dweb:/ipfs/Qma3XsUVPffiGXZ7epTqMyNJKuh87xrFhqCTwQXznEccU6\"]},\"src/interfaces/IOracleOperator.sol\":{\"keccak256\":\"0xcc2848c75dd5e1531c2b958746cdd76a3b2b329be98fdca4311219a12a6b409e\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://337bdabf03ac279d95ba96aca60105932c7af5c15fdb4b21ab1c369509310fbc\",\"dweb:/ipfs/QmNYrNYNUwWWSrPwHdarhxt1ShdH1dy8B94M49TZ7XvmK1\"]},\"src/interfaces/IRoles.sol\":{\"keccak256\":\"0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c\",\"dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj\"]},\"src/interfaces/ImToken.sol\":{\"keccak256\":\"0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36\",\"dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL\"]},\"src/libraries/Bytes32AddressLib.sol\":{\"keccak256\":\"0xd2c90ee900d4cfe4b48254363cfbada176cd8b130ddee62ede7f9c8ec2a6b8bd\",\"license\":\"AGPL-3.0-only\",\"urls\":[\"bzz-raw://e864869c9c87f0cb20b898c0435f9585db5e1f52ba0d235af76c8e6a8b4fbffd\",\"dweb:/ipfs/QmRCLJpFCGsra4VFGDNf2hEuEJP7sMs6PQxZz6CV3sGcxa\"]},\"src/libraries/CREATE3.sol\":{\"keccak256\":\"0x71104c800a3f8588df1011b4c03d6c206e4738b6b9f0f2b2c5910754e9e6c975\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://4b1a6e66b89698a8d8d236da1eaa2be8728cb3cd872393b3b4ff00dc79fba6f2\",\"dweb:/ipfs/QmW78LNN4W5h19G9qQsdMd8ffc2kVBmCwT3Dqs28R6ZyPC\"]},\"src/oracles/MixedPriceOracleV3.sol\":{\"keccak256\":\"0x2a12b509ba518e116b6c1136575e04a2c2bfdd5402730f6746cfabbbe5e6549e\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://2417f80a6dfc814e73df5362226cd89db15ea2314effd80dd16bcb87595ea755\",\"dweb:/ipfs/QmcRPazSFBvLKUqo3SCYhNkzuDuQZHWcWjwTzxV88E6own\"]},\"src/utils/Deployer.sol\":{\"keccak256\":\"0xfb7f1e6a2c171b5d0e3f864383774bee1d97e29c510b0623b357c808c5f36489\",\"license\":\"AGPL-3.0\",\"urls\":[\"bzz-raw://cf252f86bb47a8c766d39602cf345c24d408e536b49053541b942878de57a64d\",\"dweb:/ipfs/QmNsfFKPtXaEG9yX9A7JV9MGQTZ2C8E7nxzZtjpvEzQyQc\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "view", "type": "function", "name": "IS_SCRIPT", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "contract Deployer", "name": "deployer", "type": "address"}, {"internalType": "address", "name": "usdcFeed", "type": "address"}, {"internalType": "address", "name": "wethFeed", "type": "address"}, {"internalType": "address", "name": "roles", "type": "address"}, {"internalType": "uint256", "name": "stalenessPeriod", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "run", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "contract Deployer", "name": "deployer", "type": "address"}, {"internalType": "struct OracleFeed[]", "name": "feeds", "type": "tuple[]", "components": [{"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "address", "name": "defaultFeed", "type": "address"}, {"internalType": "string", "name": "toSymbol", "type": "string"}, {"internalType": "uint8", "name": "underlyingDecimals", "type": "uint8"}]}, {"internalType": "address", "name": "roles", "type": "address"}, {"internalType": "uint256", "name": "stalenessPeriod", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "runWithFeeds", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"script/deployment/oracles/DeployMixedPriceOracleV3.s.sol": "DeployMixedPriceOracleV3"}, "evmVersion": "london", "libraries": {}}, "sources": {"lib/forge-std/src/Base.sol": {"keccak256": "0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd", "urls": ["bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d", "dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z"], "license": "MIT"}, "lib/forge-std/src/Script.sol": {"keccak256": "0xc942e27c7baae499beb01afbbae99f24d42af9a6e4aae675bc6901b704aa8e9b", "urls": ["bzz-raw://0456008adf68947247f358b62863af4a8e349549d2260f2ff9569ff0e3cf5c98", "dweb:/ipfs/QmdviSUj2i7o3TPN5vd2xocqGMFVqjUzaiJTZRYyPxyHPx"], "license": "MIT"}, "lib/forge-std/src/StdChains.sol": {"keccak256": "0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e", "urls": ["bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351", "dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm"], "license": "MIT"}, "lib/forge-std/src/StdCheats.sol": {"keccak256": "0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746", "urls": ["bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41", "dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK"], "license": "MIT"}, "lib/forge-std/src/StdConstants.sol": {"keccak256": "0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534", "urls": ["bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc", "dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r"], "license": "MIT"}, "lib/forge-std/src/StdJson.sol": {"keccak256": "0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500", "urls": ["bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974", "dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3"], "license": "MIT"}, "lib/forge-std/src/StdMath.sol": {"keccak256": "0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2", "urls": ["bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92", "dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC"], "license": "MIT"}, "lib/forge-std/src/StdStorage.sol": {"keccak256": "0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc", "urls": ["bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57", "dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ"], "license": "MIT"}, "lib/forge-std/src/StdStyle.sol": {"keccak256": "0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d", "urls": ["bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8", "dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK"], "license": "MIT"}, "lib/forge-std/src/StdUtils.sol": {"keccak256": "0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737", "urls": ["bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138", "dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776"], "license": "MIT"}, "lib/forge-std/src/Vm.sol": {"keccak256": "0xaae88142a348064677cf81169ea0ca706be3e99e2a0f13a01878ff8d52cf6b25", "urls": ["bzz-raw://da4fb0ed1b31381c7e60ee386acc72796e8a8325c83739ec9a3678e2f841073c", "dweb:/ipfs/Qmb4MU6FpAi8Wt5kUQ3wa7ooHnfMVbdd9zuvnqbTehLmxJ"], "license": "MIT OR Apache-2.0"}, "lib/forge-std/src/console.sol": {"keccak256": "0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5", "urls": ["bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57", "dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP"], "license": "MIT"}, "lib/forge-std/src/console2.sol": {"keccak256": "0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f", "urls": ["bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d", "dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ"], "license": "MIT"}, "lib/forge-std/src/interfaces/IMulticall3.sol": {"keccak256": "0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a", "urls": ["bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0", "dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2"], "license": "MIT"}, "lib/forge-std/src/safeconsole.sol": {"keccak256": "0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11", "urls": ["bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab", "dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3"], "license": "MIT"}, "script/deployers/Types.sol": {"keccak256": "0xdfe4dc54c46c9b5fcd959a17ac33580f659d4d4b1bbf262f80c13963e5c4aad7", "urls": ["bzz-raw://33209fe74e0e8982c2ea6502da587fcb8a2f3092dbeef205ea66a5d4a6208f43", "dweb:/ipfs/Qme6EWBu4Q2T8bbKKPAJh31dimpkcuLi4eh3fN4E6XYqmw"], "license": "BSL-1.1"}, "script/deployment/oracles/DeployMixedPriceOracleV3.s.sol": {"keccak256": "0x6a323395bc5f8ce315f262691cf33d91cd9c8af27c0dc2d00748c905c71f697b", "urls": ["bzz-raw://cadc46a64ba08de59c627e5b81b9024d9defa181d000aa8cb25819eda3ed8a34", "dweb:/ipfs/QmRNJR52povLWc6izjNzB3HjadNsyrAazDKZTLrg6ojTyV"], "license": "BSL-1.1"}, "src/interfaces/IDefaultAdapter.sol": {"keccak256": "0xbf7e882eeb81776c7be55110bb171c65d166bafeb71d828c085b139bed5735c8", "urls": ["bzz-raw://7e139fb3ddd0623189493679e73fd42c4e505531502d55e1e699789fa3c1451a", "dweb:/ipfs/Qma3XsUVPffiGXZ7epTqMyNJKuh87xrFhqCTwQXznEccU6"], "license": "BSL-1.1"}, "src/interfaces/IOracleOperator.sol": {"keccak256": "0xcc2848c75dd5e1531c2b958746cdd76a3b2b329be98fdca4311219a12a6b409e", "urls": ["bzz-raw://337bdabf03ac279d95ba96aca60105932c7af5c15fdb4b21ab1c369509310fbc", "dweb:/ipfs/QmNYrNYNUwWWSrPwHdarhxt1ShdH1dy8B94M49TZ7XvmK1"], "license": "BSL-1.1"}, "src/interfaces/IRoles.sol": {"keccak256": "0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365", "urls": ["bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c", "dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj"], "license": "BSL-1.1"}, "src/interfaces/ImToken.sol": {"keccak256": "0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f", "urls": ["bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36", "dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL"], "license": "BSL-1.1"}, "src/libraries/Bytes32AddressLib.sol": {"keccak256": "0xd2c90ee900d4cfe4b48254363cfbada176cd8b130ddee62ede7f9c8ec2a6b8bd", "urls": ["bzz-raw://e864869c9c87f0cb20b898c0435f9585db5e1f52ba0d235af76c8e6a8b4fbffd", "dweb:/ipfs/QmRCLJpFCGsra4VFGDNf2hEuEJP7sMs6PQxZz6CV3sGcxa"], "license": "AGPL-3.0-only"}, "src/libraries/CREATE3.sol": {"keccak256": "0x71104c800a3f8588df1011b4c03d6c206e4738b6b9f0f2b2c5910754e9e6c975", "urls": ["bzz-raw://4b1a6e66b89698a8d8d236da1eaa2be8728cb3cd872393b3b4ff00dc79fba6f2", "dweb:/ipfs/QmW78LNN4W5h19G9qQsdMd8ffc2kVBmCwT3Dqs28R6ZyPC"], "license": "BSL-1.1"}, "src/oracles/MixedPriceOracleV3.sol": {"keccak256": "0x2a12b509ba518e116b6c1136575e04a2c2bfdd5402730f6746cfabbbe5e6549e", "urls": ["bzz-raw://2417f80a6dfc814e73df5362226cd89db15ea2314effd80dd16bcb87595ea755", "dweb:/ipfs/QmcRPazSFBvLKUqo3SCYhNkzuDuQZHWcWjwTzxV88E6own"], "license": "BSL-1.1"}, "src/utils/Deployer.sol": {"keccak256": "0xfb7f1e6a2c171b5d0e3f864383774bee1d97e29c510b0623b357c808c5f36489", "urls": ["bzz-raw://cf252f86bb47a8c766d39602cf345c24d408e536b49053541b942878de57a64d", "dweb:/ipfs/QmNsfFKPtXaEG9yX9A7JV9MGQTZ2C8E7nxzZtjpvEzQyQc"], "license": "AGPL-3.0"}}, "version": 1}, "id": 112}