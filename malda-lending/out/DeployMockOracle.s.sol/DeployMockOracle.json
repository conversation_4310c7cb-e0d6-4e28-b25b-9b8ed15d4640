{"abi": [{"type": "function", "name": "IS_SCRIPT", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "run", "inputs": [{"name": "deployer", "type": "address", "internalType": "contract Deployer"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}], "bytecode": {"object": "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", "sourceMap": "410:873:114:-:0;;;3126:44:3;;;-1:-1:-1;;849:28:1;;;;;410:873:114;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "410:873:114:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;526:755;;;;;;:::i;:::-;;:::i;:::-;;;-1:-1:-1;;;;;604:32:242;;;586:51;;574:2;559:18;526:755:114;;;;;;;;849:28:1;;;;;;;;;;;;;;;813:14:242;;806:22;788:41;;776:2;761:18;849:28:1;648:187:242;526:755:114;665:27;;-1:-1:-1;;;665:27:114;;1042:2:242;665:27:114;;;1024:21:242;1081:2;1061:18;;;1054:30;-1:-1:-1;;;1100:18:242;;;1093:41;574:7:114;;;;647:10;;336:42:0;;665:12:114;;1151:18:242;;665:27:114;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;665:27:114;;;;;;;;;;;;:::i;:::-;695:23;;;;;;;;;;;;;-1:-1:-1;;;695:23:114;;;630:89;;;;;;;;;;:::i;:::-;;;;;;;-1:-1:-1;;630:89:114;;;;;;;620:100;;630:89;620:100;;;;-1:-1:-1;;;745:25:114;;;;;3290:21:242;;;;3347:2;3327:18;;;3320:30;-1:-1:-1;;;3366:18:242;;;3359:41;620:100:114;-1:-1:-1;731:11:114;;336:42:0;;745:10:114;;3417:18:242;;745:25:114;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;780:22;;-1:-1:-1;;;780:22:114;;;;;3781:25:242;;;731:39:114;;-1:-1:-1;336:42:0;;780:17:114;;3754:18:242;;780:22:114;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;812:15;830:8;-1:-1:-1;;;;;830:15:114;;846:4;869:29;;;;;;;;:::i;:::-;-1:-1:-1;;869:29:114;;;;;;;;;;;;;;;;477:42;869:29;900:17;;586:51:242;559:18;900:17:114;;;-1:-1:-1;;900:17:114;;;;;;;;;;852:66;;;900:17;852:66;;:::i;:::-;;;;;;;;;;;;;830:89;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;812:107;;336:42:0;-1:-1:-1;;;;;929:16:114;;:18;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;957:51;;;;;;;;;;;;;;;;;;1000:7;957:11;:51::i;:::-;1019:33;;;;;;;;;;;;;;-1:-1:-1;;;1019:33:114;;;:11;:33::i;:::-;1062:22;;-1:-1:-1;;;1062:22:114;;;;;3781:25:242;;;336:42:0;;1062:17:114;;3754:18:242;;1062:22:114;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;1094:34:114;;-1:-1:-1;;;1094:34:114;;1123:4;1094:34;;;3781:25:242;-1:-1:-1;;;;;1094:28:114;;;-1:-1:-1;1094:28:114;;-1:-1:-1;3754:18:242;;1094:34:114;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;1138:44:114;;-1:-1:-1;;;1138:44:114;;1177:4;1138:44;;;3781:25:242;-1:-1:-1;;;;;1138:38:114;;;-1:-1:-1;1138:38:114;;-1:-1:-1;3754:18:242;;1138:44:114;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;336:42:0;-1:-1:-1;;;;;1192:16:114;;:18;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1220:30;;;;;;;;;;;;;;-1:-1:-1;;;1220:30:114;;;:11;:30::i;:::-;1267:7;526:755;-1:-1:-1;;;;526:755:114:o;7740:145:16:-;7807:71;7870:2;7874;7823:54;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;7823:54:16;;;;;;;;;;;;;;-1:-1:-1;;;;;7823:54:16;-1:-1:-1;;;7823:54:16;;;7807:15;:71::i;:::-;7740:145;;:::o;6191:121::-;6246:59;6301:2;6262:42;;;;;;;;:::i;:::-;;;;-1:-1:-1;;6262:42:16;;;;;;;;;;;;;;-1:-1:-1;;;;;6262:42:16;-1:-1:-1;;;6262:42:16;;;6246:15;:59::i;:::-;6191:121;:::o;851:129::-;922:51;965:7;265:22;131:42;265:40;;594:1;571;541:7;535:14;510:2;501:7;497:16;461:14;434:5;402:211;381:246;367:270;180:463;:::o;-1:-1:-1:-;;;;;;;;:::o;14:141:242:-;-1:-1:-1;;;;;99:31:242;;89:42;;79:70;;145:1;142;135:12;160:275;237:6;290:2;278:9;269:7;265:23;261:32;258:52;;;306:1;303;296:12;258:52;345:9;332:23;364:41;399:5;364:41;:::i;:::-;424:5;160:275;-1:-1:-1;;;160:275:242:o;1180:127::-;1241:10;1236:3;1232:20;1229:1;1222:31;1272:4;1269:1;1262:15;1296:4;1293:1;1286:15;1312:250;1397:1;1407:113;1421:6;1418:1;1415:13;1407:113;;;1497:11;;;1491:18;1478:11;;;1471:39;1443:2;1436:10;1407:113;;;-1:-1:-1;;1554:1:242;1536:16;;1529:27;1312:250::o;1567:916::-;1647:6;1700:2;1688:9;1679:7;1675:23;1671:32;1668:52;;;1716:1;1713;1706:12;1668:52;1749:9;1743:16;1782:18;1774:6;1771:30;1768:50;;;1814:1;1811;1804:12;1768:50;1837:22;;1890:4;1882:13;;1878:27;-1:-1:-1;1868:55:242;;1919:1;1916;1909:12;1868:55;1952:2;1946:9;1978:18;1970:6;1967:30;1964:56;;;2000:18;;:::i;:::-;2049:2;2043:9;2141:2;2103:17;;-1:-1:-1;;2099:31:242;;;2132:2;2095:40;2091:54;2079:67;;2176:18;2161:34;;2197:22;;;2158:62;2155:88;;;2223:18;;:::i;:::-;2259:2;2252:22;2283;;;2324:15;;;2341:2;2320:24;2317:37;-1:-1:-1;2314:57:242;;;2367:1;2364;2357:12;2314:57;2380:72;2445:6;2440:2;2432:6;2428:15;2423:2;2419;2415:11;2380:72;:::i;:::-;2471:6;1567:916;-1:-1:-1;;;;;1567:916:242:o;2488:613::-;2746:26;2742:31;2733:6;2729:2;2725:15;2721:53;2716:3;2709:66;2691:3;2804:6;2798:13;2820:75;2888:6;2883:2;2878:3;2874:12;2867:4;2859:6;2855:17;2820:75;:::i;:::-;2955:13;;2914:16;;;;2977:76;2955:13;3039:2;3031:11;;3024:4;3012:17;;2977:76;:::i;:::-;3073:17;3092:2;3069:26;;2488:613;-1:-1:-1;;;;;2488:613:242:o;3446:184::-;3516:6;3569:2;3557:9;3548:7;3544:23;3540:32;3537:52;;;3585:1;3582;3575:12;3537:52;-1:-1:-1;3608:16:242;;3446:184;-1:-1:-1;3446:184:242:o;3817:492::-;3992:3;4030:6;4024:13;4046:66;4105:6;4100:3;4093:4;4085:6;4081:17;4046:66;:::i;:::-;4175:13;;4134:16;;;;4197:70;4175:13;4134:16;4244:4;4232:17;;4197:70;:::i;:::-;4283:20;;3817:492;-1:-1:-1;;;;3817:492:242:o;4314:270::-;4355:3;4393:5;4387:12;4420:6;4415:3;4408:19;4436:76;4505:6;4498:4;4493:3;4489:14;4482:4;4475:5;4471:16;4436:76;:::i;:::-;4566:2;4545:15;-1:-1:-1;;4541:29:242;4532:39;;;;4573:4;4528:50;;4314:270;-1:-1:-1;;4314:270:242:o;4589:288::-;4764:6;4753:9;4746:25;4807:2;4802;4791:9;4787:18;4780:30;4727:4;4827:44;4867:2;4856:9;4852:18;4844:6;4827:44;:::i;4882:261::-;4952:6;5005:2;4993:9;4984:7;4980:23;4976:32;4973:52;;;5021:1;5018;5011:12;4973:52;5053:9;5047:16;5072:41;5107:5;5072:41;:::i;5356:316::-;5533:2;5522:9;5515:21;5496:4;5553:44;5593:2;5582:9;5578:18;5570:6;5553:44;:::i;:::-;5545:52;;5662:1;5658;5653:3;5649:11;5645:19;5637:6;5633:32;5628:2;5617:9;5613:18;5606:60;5356:316;;;;;:::o;5677:219::-;5826:2;5815:9;5808:21;5789:4;5846:44;5886:2;5875:9;5871:18;5863:6;5846:44;:::i", "linkReferences": {}}, "methodIdentifiers": {"IS_SCRIPT()": "f8ccbf47", "run(address)": "522bb704"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"IS_SCRIPT\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"contract Deployer\",\"name\":\"deployer\",\"type\":\"address\"}],\"name\":\"run\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"notice\":\"forge script DeployMockOracle  \\\\     --slow \\\\     --verify \\\\     --verifier-url <url> \\\\     --rpc-url <url> \\\\     --etherscan-api-key <key> \\\\     --broadcast\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"script/deployment/oracles/DeployMockOracle.s.sol\":\"DeployMockOracle\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"lib/forge-std/src/Base.sol\":{\"keccak256\":\"0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d\",\"dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z\"]},\"lib/forge-std/src/Script.sol\":{\"keccak256\":\"0xc942e27c7baae499beb01afbbae99f24d42af9a6e4aae675bc6901b704aa8e9b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0456008adf68947247f358b62863af4a8e349549d2260f2ff9569ff0e3cf5c98\",\"dweb:/ipfs/QmdviSUj2i7o3TPN5vd2xocqGMFVqjUzaiJTZRYyPxyHPx\"]},\"lib/forge-std/src/StdChains.sol\":{\"keccak256\":\"0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351\",\"dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm\"]},\"lib/forge-std/src/StdCheats.sol\":{\"keccak256\":\"0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41\",\"dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK\"]},\"lib/forge-std/src/StdConstants.sol\":{\"keccak256\":\"0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc\",\"dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r\"]},\"lib/forge-std/src/StdJson.sol\":{\"keccak256\":\"0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974\",\"dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3\"]},\"lib/forge-std/src/StdMath.sol\":{\"keccak256\":\"0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92\",\"dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC\"]},\"lib/forge-std/src/StdStorage.sol\":{\"keccak256\":\"0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57\",\"dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ\"]},\"lib/forge-std/src/StdStyle.sol\":{\"keccak256\":\"0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8\",\"dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK\"]},\"lib/forge-std/src/StdUtils.sol\":{\"keccak256\":\"0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138\",\"dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776\"]},\"lib/forge-std/src/Vm.sol\":{\"keccak256\":\"0xaae88142a348064677cf81169ea0ca706be3e99e2a0f13a01878ff8d52cf6b25\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://da4fb0ed1b31381c7e60ee386acc72796e8a8325c83739ec9a3678e2f841073c\",\"dweb:/ipfs/Qmb4MU6FpAi8Wt5kUQ3wa7ooHnfMVbdd9zuvnqbTehLmxJ\"]},\"lib/forge-std/src/console.sol\":{\"keccak256\":\"0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57\",\"dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP\"]},\"lib/forge-std/src/console2.sol\":{\"keccak256\":\"0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d\",\"dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ\"]},\"lib/forge-std/src/interfaces/IMulticall3.sol\":{\"keccak256\":\"0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0\",\"dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2\"]},\"lib/forge-std/src/safeconsole.sol\":{\"keccak256\":\"0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab\",\"dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3\"]},\"script/deployment/oracles/DeployMockOracle.s.sol\":{\"keccak256\":\"0x9282174f82284306d36614c8ac13f115dd5056153371171ba31dc5c6d65cdf16\",\"license\":\"UNLICENSED\",\"urls\":[\"bzz-raw://d35402a889d21cffcd4c098a21cfe00206ce3d977f96dc7bded1f3a954975561\",\"dweb:/ipfs/QmYpu2Wjh82u6GYf7M7oVT2bdULR9eVMJUwBzz6iQF96ZH\"]},\"src/libraries/Bytes32AddressLib.sol\":{\"keccak256\":\"0xd2c90ee900d4cfe4b48254363cfbada176cd8b130ddee62ede7f9c8ec2a6b8bd\",\"license\":\"AGPL-3.0-only\",\"urls\":[\"bzz-raw://e864869c9c87f0cb20b898c0435f9585db5e1f52ba0d235af76c8e6a8b4fbffd\",\"dweb:/ipfs/QmRCLJpFCGsra4VFGDNf2hEuEJP7sMs6PQxZz6CV3sGcxa\"]},\"src/libraries/CREATE3.sol\":{\"keccak256\":\"0x71104c800a3f8588df1011b4c03d6c206e4738b6b9f0f2b2c5910754e9e6c975\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://4b1a6e66b89698a8d8d236da1eaa2be8728cb3cd872393b3b4ff00dc79fba6f2\",\"dweb:/ipfs/QmW78LNN4W5h19G9qQsdMd8ffc2kVBmCwT3Dqs28R6ZyPC\"]},\"src/utils/Deployer.sol\":{\"keccak256\":\"0xfb7f1e6a2c171b5d0e3f864383774bee1d97e29c510b0623b357c808c5f36489\",\"license\":\"AGPL-3.0\",\"urls\":[\"bzz-raw://cf252f86bb47a8c766d39602cf345c24d408e536b49053541b942878de57a64d\",\"dweb:/ipfs/QmNsfFKPtXaEG9yX9A7JV9MGQTZ2C8E7nxzZtjpvEzQyQc\"]},\"test/mocks/OracleMock.sol\":{\"keccak256\":\"0xb749ba9d217b9aedc9010690022b31672eeacf0927f43efe9769ae886f4ce855\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://5167ca5dcd31bc0ae6dcc22e43276e8f746b018d7d9c2edc332d1160024f6328\",\"dweb:/ipfs/QmVdgBmaBntj9TMV4z65bga1uuN2YkfUbLNN9Hsmvu4x6f\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "view", "type": "function", "name": "IS_SCRIPT", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "contract Deployer", "name": "deployer", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "run", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"script/deployment/oracles/DeployMockOracle.s.sol": "DeployMockOracle"}, "evmVersion": "london", "libraries": {}}, "sources": {"lib/forge-std/src/Base.sol": {"keccak256": "0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd", "urls": ["bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d", "dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z"], "license": "MIT"}, "lib/forge-std/src/Script.sol": {"keccak256": "0xc942e27c7baae499beb01afbbae99f24d42af9a6e4aae675bc6901b704aa8e9b", "urls": ["bzz-raw://0456008adf68947247f358b62863af4a8e349549d2260f2ff9569ff0e3cf5c98", "dweb:/ipfs/QmdviSUj2i7o3TPN5vd2xocqGMFVqjUzaiJTZRYyPxyHPx"], "license": "MIT"}, "lib/forge-std/src/StdChains.sol": {"keccak256": "0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e", "urls": ["bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351", "dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm"], "license": "MIT"}, "lib/forge-std/src/StdCheats.sol": {"keccak256": "0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746", "urls": ["bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41", "dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK"], "license": "MIT"}, "lib/forge-std/src/StdConstants.sol": {"keccak256": "0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534", "urls": ["bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc", "dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r"], "license": "MIT"}, "lib/forge-std/src/StdJson.sol": {"keccak256": "0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500", "urls": ["bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974", "dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3"], "license": "MIT"}, "lib/forge-std/src/StdMath.sol": {"keccak256": "0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2", "urls": ["bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92", "dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC"], "license": "MIT"}, "lib/forge-std/src/StdStorage.sol": {"keccak256": "0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc", "urls": ["bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57", "dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ"], "license": "MIT"}, "lib/forge-std/src/StdStyle.sol": {"keccak256": "0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d", "urls": ["bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8", "dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK"], "license": "MIT"}, "lib/forge-std/src/StdUtils.sol": {"keccak256": "0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737", "urls": ["bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138", "dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776"], "license": "MIT"}, "lib/forge-std/src/Vm.sol": {"keccak256": "0xaae88142a348064677cf81169ea0ca706be3e99e2a0f13a01878ff8d52cf6b25", "urls": ["bzz-raw://da4fb0ed1b31381c7e60ee386acc72796e8a8325c83739ec9a3678e2f841073c", "dweb:/ipfs/Qmb4MU6FpAi8Wt5kUQ3wa7ooHnfMVbdd9zuvnqbTehLmxJ"], "license": "MIT OR Apache-2.0"}, "lib/forge-std/src/console.sol": {"keccak256": "0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5", "urls": ["bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57", "dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP"], "license": "MIT"}, "lib/forge-std/src/console2.sol": {"keccak256": "0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f", "urls": ["bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d", "dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ"], "license": "MIT"}, "lib/forge-std/src/interfaces/IMulticall3.sol": {"keccak256": "0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a", "urls": ["bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0", "dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2"], "license": "MIT"}, "lib/forge-std/src/safeconsole.sol": {"keccak256": "0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11", "urls": ["bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab", "dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3"], "license": "MIT"}, "script/deployment/oracles/DeployMockOracle.s.sol": {"keccak256": "0x9282174f82284306d36614c8ac13f115dd5056153371171ba31dc5c6d65cdf16", "urls": ["bzz-raw://d35402a889d21cffcd4c098a21cfe00206ce3d977f96dc7bded1f3a954975561", "dweb:/ipfs/QmYpu2Wjh82u6GYf7M7oVT2bdULR9eVMJUwBzz6iQF96ZH"], "license": "UNLICENSED"}, "src/libraries/Bytes32AddressLib.sol": {"keccak256": "0xd2c90ee900d4cfe4b48254363cfbada176cd8b130ddee62ede7f9c8ec2a6b8bd", "urls": ["bzz-raw://e864869c9c87f0cb20b898c0435f9585db5e1f52ba0d235af76c8e6a8b4fbffd", "dweb:/ipfs/QmRCLJpFCGsra4VFGDNf2hEuEJP7sMs6PQxZz6CV3sGcxa"], "license": "AGPL-3.0-only"}, "src/libraries/CREATE3.sol": {"keccak256": "0x71104c800a3f8588df1011b4c03d6c206e4738b6b9f0f2b2c5910754e9e6c975", "urls": ["bzz-raw://4b1a6e66b89698a8d8d236da1eaa2be8728cb3cd872393b3b4ff00dc79fba6f2", "dweb:/ipfs/QmW78LNN4W5h19G9qQsdMd8ffc2kVBmCwT3Dqs28R6ZyPC"], "license": "BSL-1.1"}, "src/utils/Deployer.sol": {"keccak256": "0xfb7f1e6a2c171b5d0e3f864383774bee1d97e29c510b0623b357c808c5f36489", "urls": ["bzz-raw://cf252f86bb47a8c766d39602cf345c24d408e536b49053541b942878de57a64d", "dweb:/ipfs/QmNsfFKPtXaEG9yX9A7JV9MGQTZ2C8E7nxzZtjpvEzQyQc"], "license": "AGPL-3.0"}, "test/mocks/OracleMock.sol": {"keccak256": "0xb749ba9d217b9aedc9010690022b31672eeacf0927f43efe9769ae886f4ce855", "urls": ["bzz-raw://5167ca5dcd31bc0ae6dcc22e43276e8f746b018d7d9c2edc332d1160024f6328", "dweb:/ipfs/QmVdgBmaBntj9TMV4z65bga1uuN2YkfUbLNN9Hsmvu4x6f"], "license": "BSL-1.1"}}, "version": 1}, "id": 114}