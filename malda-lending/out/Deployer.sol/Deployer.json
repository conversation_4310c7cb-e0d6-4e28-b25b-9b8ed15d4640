{"abi": [{"type": "constructor", "inputs": [{"name": "_admin", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "receive", "stateMutability": "payable"}, {"type": "function", "name": "acceptAdmin", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "admin", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "create", "inputs": [{"name": "salt", "type": "bytes32", "internalType": "bytes32"}, {"name": "code", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "payable"}, {"type": "function", "name": "pendingAdmin", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "precompute", "inputs": [{"name": "salt", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "saveEth", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "_addr", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setPendingAdmin", "inputs": [{"name": "newAdmin", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "AdminAccepted", "inputs": [{"name": "_admin", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "AdminSet", "inputs": [{"name": "_admin", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "PendingAdminSet", "inputs": [{"name": "_admin", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "NotAuthorized", "inputs": [{"name": "admin", "type": "address", "internalType": "address"}, {"name": "sender", "type": "address", "internalType": "address"}]}], "bytecode": {"object": "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", "sourceMap": "894:1671:194:-:0;;;1297:59;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1335:5;:14;;-1:-1:-1;;;;;;1335:14:194;-1:-1:-1;;;;;1335:14:194;;;;;;;;;;894:1671;;14:290:242;84:6;137:2;125:9;116:7;112:23;108:32;105:52;;;153:1;150;143:12;105:52;179:16;;-1:-1:-1;;;;;224:31:242;;214:42;;204:70;;270:1;267;260:12;204:70;293:5;14:290;-1:-1:-1;;;14:290:242:o;:::-;894:1671:194;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "894:1671:194:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2312:251;;;;;;;;;;;;;:::i;:::-;;944:27;;;;;;;;;;-1:-1:-1;944:27:194;;;;-1:-1:-1;;;;;944:27:194;;;;;;-1:-1:-1;;;;;178:32:242;;;160:51;;148:2;133:18;944:27:194;;;;;;;1435:143;;;;;;;;;;-1:-1:-1;1435:143:194;;;;;:::i;:::-;;:::i;2151:155::-;;;;;;:::i;:::-;;:::i;1991:115::-;;;;;;;;;;-1:-1:-1;1991:115:194;;;;;:::i;:::-;;:::i;1788:159::-;;;;;;;;;;-1:-1:-1;1788:159:194;;;;;:::i;:::-;;:::i;1584:198::-;;;;;;;;;;;;;:::i;918:20::-;;;;;;;;;;-1:-1:-1;918:20:194;;;;-1:-1:-1;;;;;918:20:194;;;2312:251;2372:12;;-1:-1:-1;;;;;2372:12:194;2358:10;:26;2354:103;;2421:12;;2407:39;;-1:-1:-1;;;2407:39:194;;-1:-1:-1;;;;;2421:12:194;;;2407:39;;;2113:51:242;2435:10:194;2180:18:242;;;2173:60;2086:18;;2407:39:194;;;;;;;;2354:103;2474:12;;;;2466:20;;-1:-1:-1;;;;;2474:12:194;;-1:-1:-1;;;;;;2466:20:194;;;;;;;2496:25;;;;;2536:20;;2474:12;;2536:20;;;2312:251::o;1435:143::-;1233:5;;-1:-1:-1;;;;;1233:5:194;1219:10;:19;;;1211:62;;;;-1:-1:-1;;;1211:62:194;;-1:-1:-1;;;;;2131:32:242;;;1211:62:194;;;2113:51:242;2200:32;;2180:18;;;2173:60;2086:18;;1211:62:194;1939:300:242;1211:62:194;-1:-1:-1;;1507:12:194::1;:23:::0;;-1:-1:-1;;;;;;1507:23:194::1;-1:-1:-1::0;;;;;1507:23:194;::::1;::::0;;::::1;::::0;;;1546:25:::1;::::0;::::1;::::0;-1:-1:-1;;1546:25:194::1;1435:143:::0;:::o;2151:155::-;2236:7;1233:5;;-1:-1:-1;;;;;1233:5:194;1219:10;:19;;;1211:62;;;;-1:-1:-1;;;1211:62:194;;-1:-1:-1;;;;;2131:32:242;;;1211:62:194;;;2113:51:242;2200:32;;2180:18;;;2173:60;2086:18;;1211:62:194;1939:300:242;1211:62:194;;;2262:37:::1;2277:4;2283;2289:9;2262:14;:37::i;:::-;2255:44:::0;2151:155;-1:-1:-1;;;2151:155:194:o;1991:115::-;2048:7;2074:25;2094:4;2074:19;:25::i;:::-;2067:32;1991:115;-1:-1:-1;;1991:115:194:o;1788:159::-;1847:5;;1856:10;-1:-1:-1;;;;;1847:5:194;;;:19;1843:98;;1882:5;:13;;-1:-1:-1;;;;;;1882:13:194;-1:-1:-1;;;;;1882:13:194;;;;;;;1915:15;;1882:13;;1915:15;;;1843:98;1788:159;:::o;1584:198::-;1626:5;;1635:10;-1:-1:-1;;;;;1626:5:194;;;:19;1622:154;;1679:49;;1662:12;;1679:10;;1702:21;;1662:12;1679:49;1662:12;1679:49;1702:21;1679:10;:49;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1661:67;;;1750:7;1742:23;;;;-1:-1:-1;;;1742:23:194;;2656:2:242;1742:23:194;;;2638:21:242;2695:1;2675:18;;;2668:29;-1:-1:-1;;;2713:18:242;;;2706:33;2756:18;;1742:23:194;2454:326:242;1622:154:194;1584:198::o;2502:722:168:-;2592:16;2620:31;2654:14;;;;;;;;;;;;;-1:-1:-1;;;2654:14:168;;;2620:48;;2679:13;2959:4;2938:18;2932:25;2927:2;2907:18;2903:27;2900:1;2892:72;2883:81;-1:-1:-1;;;;;;2991:19:168;;2983:49;;;;-1:-1:-1;;;2983:49:168;;2987:2:242;2983:49:168;;;2969:21:242;3026:2;3006:18;;;2999:30;-1:-1:-1;;;3045:18:242;;;3038:47;3102:18;;2983:49:168;2785:341:242;2983:49:168;3054:17;3066:4;3054:11;:17::i;:::-;3043:28;;3082:12;3099:5;-1:-1:-1;;;;;3099:10:168;3117:5;3124:12;3099:38;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3081:56;;;3155:7;:36;;;;-1:-1:-1;;;;;;3166:20:168;;;:25;;3155:36;3147:70;;;;-1:-1:-1;;;3147:70:168;;3750:2:242;3147:70:168;;;3732:21:242;3789:2;3769:18;;;3762:30;-1:-1:-1;;;3808:18:242;;;3801:51;3869:18;;3147:70:168;3548:345:242;3147:70:168;2610:614;;;2502:722;;;;;:::o;3230:624::-;2480:14;;;;;;;;;;;-1:-1:-1;;;2480:14:168;;;;;3333:72;;-1:-1:-1;;;;;;3333:72:168;;;4109:39:242;;;;-1:-1:-1;;3372:4:168;4185:2:242;4181:15;4177:53;4164:11;;;4157:74;4247:12;;;4240:28;;;2470:25:168;4284:12:242;;;4277:28;-1:-1:-1;;;;3323:212:168;;4321:12:242;;3333:72:168;;;;;;;;;;;;;3323:83;;;;;;398:10:166;280:138;3323:212:168;3563:43;;-1:-1:-1;;;3563:43:168;;;4675:28:242;-1:-1:-1;;4740:2:242;4736:15;;;4732:53;4719:11;;;4712:74;-1:-1:-1;;;4802:12:242;;;4795:33;3307:228:168;;-1:-1:-1;3553:294:168;;4844:12:242;;3563:43:168;4344:518:242;222:286;281:6;334:2;322:9;313:7;309:23;305:32;302:52;;;350:1;347;340:12;302:52;376:23;;-1:-1:-1;;;;;428:31:242;;418:42;;408:70;;474:1;471;464:12;513:127;574:10;569:3;565:20;562:1;555:31;605:4;602:1;595:15;629:4;626:1;619:15;645:1058;722:6;730;783:2;771:9;762:7;758:23;754:32;751:52;;;799:1;796;789:12;751:52;844:23;;;-1:-1:-1;942:2:242;927:18;;914:32;969:18;958:30;;955:50;;;1001:1;998;991:12;955:50;1024:22;;1077:4;1069:13;;1065:27;-1:-1:-1;1055:55:242;;1106:1;1103;1096:12;1055:55;1146:2;1133:16;1172:18;1164:6;1161:30;1158:56;;;1194:18;;:::i;:::-;1243:2;1237:9;1335:2;1297:17;;-1:-1:-1;;1293:31:242;;;1326:2;1289:40;1285:54;1273:67;;1370:18;1355:34;;1391:22;;;1352:62;1349:88;;;1417:18;;:::i;:::-;1453:2;1446:22;1477;;;1518:15;;;1535:2;1514:24;1511:37;-1:-1:-1;1508:57:242;;;1561:1;1558;1551:12;1508:57;1617:6;1612:2;1608;1604:11;1599:2;1591:6;1587:15;1574:50;1670:1;1665:2;1656:6;1648;1644:19;1640:28;1633:39;1691:6;1681:16;;;;;645:1058;;;;;:::o;1708:226::-;1767:6;1820:2;1808:9;1799:7;1795:23;1791:32;1788:52;;;1836:1;1833;1826:12;1788:52;-1:-1:-1;1881:23:242;;1708:226;-1:-1:-1;1708:226:242:o;3131:412::-;3260:3;3298:6;3292:13;3323:1;3333:129;3347:6;3344:1;3341:13;3333:129;;;3445:4;3429:14;;;3425:25;;3419:32;3406:11;;;3399:53;3362:12;3333:129;;;-1:-1:-1;3517:1:242;3481:16;;3506:13;;;-1:-1:-1;3481:16:242;3131:412;-1:-1:-1;3131:412:242:o", "linkReferences": {}}, "methodIdentifiers": {"acceptAdmin()": "0e18b681", "admin()": "f851a440", "create(bytes32,bytes)": "5b37e150", "pendingAdmin()": "26782247", "precompute(bytes32)": "7587ea9f", "saveEth()": "9528432b", "setNewAdmin(address)": "8eec99c8", "setPendingAdmin(address)": "4dd18bf5"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_admin\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"admin\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"NotAuthorized\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_admin\",\"type\":\"address\"}],\"name\":\"AdminAccepted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_admin\",\"type\":\"address\"}],\"name\":\"AdminSet\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_admin\",\"type\":\"address\"}],\"name\":\"PendingAdminSet\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"acceptAdmin\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"admin\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"salt\",\"type\":\"bytes32\"},{\"internalType\":\"bytes\",\"name\":\"code\",\"type\":\"bytes\"}],\"name\":\"create\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"pendingAdmin\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"salt\",\"type\":\"bytes32\"}],\"name\":\"precompute\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"saveEth\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_addr\",\"type\":\"address\"}],\"name\":\"setNewAdmin\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newAdmin\",\"type\":\"address\"}],\"name\":\"setPendingAdmin\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"stateMutability\":\"payable\",\"type\":\"receive\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/utils/Deployer.sol\":\"Deployer\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"src/libraries/Bytes32AddressLib.sol\":{\"keccak256\":\"0xd2c90ee900d4cfe4b48254363cfbada176cd8b130ddee62ede7f9c8ec2a6b8bd\",\"license\":\"AGPL-3.0-only\",\"urls\":[\"bzz-raw://e864869c9c87f0cb20b898c0435f9585db5e1f52ba0d235af76c8e6a8b4fbffd\",\"dweb:/ipfs/QmRCLJpFCGsra4VFGDNf2hEuEJP7sMs6PQxZz6CV3sGcxa\"]},\"src/libraries/CREATE3.sol\":{\"keccak256\":\"0x71104c800a3f8588df1011b4c03d6c206e4738b6b9f0f2b2c5910754e9e6c975\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://4b1a6e66b89698a8d8d236da1eaa2be8728cb3cd872393b3b4ff00dc79fba6f2\",\"dweb:/ipfs/QmW78LNN4W5h19G9qQsdMd8ffc2kVBmCwT3Dqs28R6ZyPC\"]},\"src/utils/Deployer.sol\":{\"keccak256\":\"0xfb7f1e6a2c171b5d0e3f864383774bee1d97e29c510b0623b357c808c5f36489\",\"license\":\"AGPL-3.0\",\"urls\":[\"bzz-raw://cf252f86bb47a8c766d39602cf345c24d408e536b49053541b942878de57a64d\",\"dweb:/ipfs/QmNsfFKPtXaEG9yX9A7JV9MGQTZ2C8E7nxzZtjpvEzQyQc\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "_admin", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "admin", "type": "address"}, {"internalType": "address", "name": "sender", "type": "address"}], "type": "error", "name": "NotAuthorized"}, {"inputs": [{"internalType": "address", "name": "_admin", "type": "address", "indexed": true}], "type": "event", "name": "AdminAccepted", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "_admin", "type": "address", "indexed": true}], "type": "event", "name": "AdminSet", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "_admin", "type": "address", "indexed": true}], "type": "event", "name": "PendingAdminSet", "anonymous": false}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "acceptAdmin"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "admin", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "bytes32", "name": "salt", "type": "bytes32"}, {"internalType": "bytes", "name": "code", "type": "bytes"}], "stateMutability": "payable", "type": "function", "name": "create", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "pendingAdmin", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "bytes32", "name": "salt", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "precompute", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "saveEth"}, {"inputs": [{"internalType": "address", "name": "_addr", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "set<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"inputs": [{"internalType": "address", "name": "newAdmin", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setPendingAdmin"}, {"inputs": [], "stateMutability": "payable", "type": "receive"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/utils/Deployer.sol": "Deployer"}, "evmVersion": "london", "libraries": {}}, "sources": {"src/libraries/Bytes32AddressLib.sol": {"keccak256": "0xd2c90ee900d4cfe4b48254363cfbada176cd8b130ddee62ede7f9c8ec2a6b8bd", "urls": ["bzz-raw://e864869c9c87f0cb20b898c0435f9585db5e1f52ba0d235af76c8e6a8b4fbffd", "dweb:/ipfs/QmRCLJpFCGsra4VFGDNf2hEuEJP7sMs6PQxZz6CV3sGcxa"], "license": "AGPL-3.0-only"}, "src/libraries/CREATE3.sol": {"keccak256": "0x71104c800a3f8588df1011b4c03d6c206e4738b6b9f0f2b2c5910754e9e6c975", "urls": ["bzz-raw://4b1a6e66b89698a8d8d236da1eaa2be8728cb3cd872393b3b4ff00dc79fba6f2", "dweb:/ipfs/QmW78LNN4W5h19G9qQsdMd8ffc2kVBmCwT3Dqs28R6ZyPC"], "license": "BSL-1.1"}, "src/utils/Deployer.sol": {"keccak256": "0xfb7f1e6a2c171b5d0e3f864383774bee1d97e29c510b0623b357c808c5f36489", "urls": ["bzz-raw://cf252f86bb47a8c766d39602cf345c24d408e536b49053541b942878de57a64d", "dweb:/ipfs/QmNsfFKPtXaEG9yX9A7JV9MGQTZ2C8E7nxzZtjpvEzQyQc"], "license": "AGPL-3.0"}}, "version": 1}, "id": 194}