{"abi": [], "bytecode": {"object": "0x60566037600b82828239805160001a607314602a57634e487b7160e01b600052600060045260246000fd5b30600052607381538281f3fe73000000000000000000000000000000000000000030146080604052600080fdfea2646970667358221220fdd1f68d56c5c852d4f52fceca3862cd3ad72329a7f8de5d88f211810db3419164736f6c634300081c0033", "sourceMap": "361:20884:167:-:0;;;;;;;;;;;;;;;-1:-1:-1;;;361:20884:167;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x73000000000000000000000000000000000000000030146080604052600080fdfea2646970667358221220fdd1f68d56c5c852d4f52fceca3862cd3ad72329a7f8de5d88f211810db3419164736f6c634300081c0033", "sourceMap": "361:20884:167:-:0;;;;;;;;", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/libraries/BytesLib.sol\":\"BytesLib\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"src/libraries/BytesLib.sol\":{\"keccak256\":\"0xfaf080296887274c71c2f2eb3552c4742c96ff24616c61f8ab98399f30b18ef0\",\"license\":\"Unlicense\",\"urls\":[\"bzz-raw://02dcb01b6c9cf4f158792a15e9b40d046332949cd6b2c1e5e54e474901cf579b\",\"dweb:/ipfs/QmWiBhygucYoY8Uu6WQPao2r64Y25Vmmj3gYyZZ6Wy9wnE\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/libraries/BytesLib.sol": "BytesLib"}, "evmVersion": "london", "libraries": {}}, "sources": {"src/libraries/BytesLib.sol": {"keccak256": "0xfaf080296887274c71c2f2eb3552c4742c96ff24616c61f8ab98399f30b18ef0", "urls": ["bzz-raw://02dcb01b6c9cf4f158792a15e9b40d046332949cd6b2c1e5e54e474901cf579b", "dweb:/ipfs/QmWiBhygucYoY8Uu6WQPao2r64Y25Vmmj3gYyZZ6Wy9wnE"], "license": "Unlicense"}}, "version": 1}, "id": 167}