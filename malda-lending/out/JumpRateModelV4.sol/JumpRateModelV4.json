{"abi": [{"type": "constructor", "inputs": [{"name": "blocksPerYear_", "type": "uint256", "internalType": "uint256"}, {"name": "baseRatePerYear", "type": "uint256", "internalType": "uint256"}, {"name": "multiplierPerYear", "type": "uint256", "internalType": "uint256"}, {"name": "jumpMultiplierPerYear", "type": "uint256", "internalType": "uint256"}, {"name": "kink_", "type": "uint256", "internalType": "uint256"}, {"name": "owner_", "type": "address", "internalType": "address"}, {"name": "name_", "type": "string", "internalType": "string"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "baseRatePerBlock", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "blocksPerYear", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getBorrowRate", "inputs": [{"name": "cash", "type": "uint256", "internalType": "uint256"}, {"name": "borrows", "type": "uint256", "internalType": "uint256"}, {"name": "reserves", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getSupplyRate", "inputs": [{"name": "cash", "type": "uint256", "internalType": "uint256"}, {"name": "borrows", "type": "uint256", "internalType": "uint256"}, {"name": "reserves", "type": "uint256", "internalType": "uint256"}, {"name": "reserveFactorMantissa", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "isInterestRateModel", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "pure"}, {"type": "function", "name": "jumpMultiplierPerBlock", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "kink", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "multiplierPerBlock", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "name", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "updateBlocksPerYear", "inputs": [{"name": "blocksPerYear_", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "updateJumpRateModel", "inputs": [{"name": "baseRatePerYear", "type": "uint256", "internalType": "uint256"}, {"name": "multiplierPerYear", "type": "uint256", "internalType": "uint256"}, {"name": "jumpMultiplierPerYear", "type": "uint256", "internalType": "uint256"}, {"name": "kink_", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "utilizationRate", "inputs": [{"name": "cash", "type": "uint256", "internalType": "uint256"}, {"name": "borrows", "type": "uint256", "internalType": "uint256"}, {"name": "reserves", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "pure"}, {"type": "event", "name": "NewInterestParams", "inputs": [{"name": "baseRatePerBlock", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "multiplierPerBlock", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "jumpMultiplierPerBlock", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "kink", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "JumpRateModelV4_InputNotValid", "inputs": []}, {"type": "error", "name": "JumpRateModelV4_MultiplierNotValid", "inputs": []}, {"type": "error", "name": "OwnableInvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}], "bytecode": {"object": "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", "sourceMap": "1098:5457:131:-:0;;;2431:414;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2672:6;-1:-1:-1;;;;;1273:26:27;;1269:95;;1322:31;;-1:-1:-1;;;1322:31:27;;1350:1;1322:31;;;2079:51:242;2052:18;;1322:31:27;;;;;;;1269:95;1373:32;1392:12;1373:18;:32::i;:::-;-1:-1:-1;2690:13:131::1;:30:::0;;;2730:4:::1;:12;2737:5:::0;2730:4;:12:::1;:::i;:::-;-1:-1:-1::0;2752:86:131::1;2773:15:::0;2790:17;2809:21;2832:5;2752:20:::1;:86::i;:::-;2431:414:::0;;;;;;;1098:5457;;2912:187:27;2985:16;3004:6;;-1:-1:-1;;;;;3020:17:27;;;-1:-1:-1;;;;;;3020:17:27;;;;;;3052:40;;3004:6;;;;;;;3052:40;;2985:16;3052:40;2975:124;2912:187;:::o;6036:517:131:-;6257:13;;6239:31;;:15;:31;:::i;:::-;6220:16;:50;6329:13;;:21;;6345:5;;6329:21;:::i;:::-;6301:24;:17;6321:4;6301:24;:::i;:::-;:50;;;;:::i;:::-;6280:18;:71;6410:13;;6386:37;;:21;:37;:::i;:::-;6361:22;:62;;;6433:4;:12;;;6479:16;;6497:18;;6461:85;;;5373:25:242;;;5429:2;5414:18;;5407:34;;;;5457:18;;5450:34;;;;5515:2;5500:18;;5493:34;;;6461:85:131;;5360:3:242;5345:19;6461:85:131;;;;;;;6036:517;;;;:::o;14:127:242:-;75:10;70:3;66:20;63:1;56:31;106:4;103:1;96:15;130:4;127:1;120:15;146:1782;280:6;288;296;304;312;320;328;381:3;369:9;360:7;356:23;352:33;349:53;;;398:1;395;388:12;349:53;443:16;;549:2;534:18;;528:25;645:2;630:18;;624:25;741:2;726:18;;720:25;837:3;822:19;;816:26;913:3;898:19;;892:26;443:16;;-1:-1:-1;528:25:242;;-1:-1:-1;624:25:242;;-1:-1:-1;720:25:242;-1:-1:-1;816:26:242;-1:-1:-1;;;;;;949:33:242;;937:46;;927:74;;997:1;994;987:12;927:74;1071:3;1056:19;;1050:26;1020:7;;-1:-1:-1;;;;;;1088:30:242;;1085:50;;;1131:1;1128;1121:12;1085:50;1154:22;;1207:4;1199:13;;1195:27;-1:-1:-1;1185:55:242;;1236:1;1233;1226:12;1185:55;1263:9;;-1:-1:-1;;;;;1284:30:242;;1281:56;;;1317:18;;:::i;:::-;1366:2;1360:9;1458:2;1420:17;;-1:-1:-1;;1416:31:242;;;1449:2;1412:40;1408:54;1396:67;;-1:-1:-1;;;;;1478:34:242;;1514:22;;;1475:62;1472:88;;;1540:18;;:::i;:::-;1576:2;1569:22;1600;;;1641:15;;;1658:2;1637:24;1634:37;-1:-1:-1;1631:57:242;;;1684:1;1681;1674:12;1631:57;1706:1;1716:133;1730:6;1727:1;1724:13;1716:133;;;1834:2;1822:10;;;1818:19;;1812:26;1791:14;;;1787:23;;1780:59;1745:10;1716:133;;;1720:3;1895:1;1890:2;1881:6;1873;1869:19;1865:28;1858:39;1916:6;1906:16;;;;;146:1782;;;;;;;;;;:::o;2141:380::-;2220:1;2216:12;;;;2263;;;2284:61;;2338:4;2330:6;2326:17;2316:27;;2284:61;2391:2;2383:6;2380:14;2360:18;2357:38;2354:161;;2437:10;2432:3;2428:20;2425:1;2418:31;2472:4;2469:1;2462:15;2500:4;2497:1;2490:15;2354:161;;2141:380;;;:::o;2652:518::-;2754:2;2749:3;2746:11;2743:421;;;2790:5;2787:1;2780:16;2834:4;2831:1;2821:18;2904:2;2892:10;2888:19;2885:1;2881:27;2875:4;2871:38;2940:4;2928:10;2925:20;2922:47;;;-1:-1:-1;2963:4:242;2922:47;3018:2;3013:3;3009:12;3006:1;3002:20;2996:4;2992:31;2982:41;;3073:81;3091:2;3084:5;3081:13;3073:81;;;3150:1;3136:16;;3117:1;3106:13;3073:81;;;3077:3;;2743:421;2652:518;;;:::o;3346:1299::-;3466:10;;-1:-1:-1;;;;;3488:30:242;;3485:56;;;3521:18;;:::i;:::-;3550:97;3640:6;3600:38;3632:4;3626:11;3600:38;:::i;:::-;3594:4;3550:97;:::i;:::-;3696:4;3727:2;3716:14;;3744:1;3739:649;;;;4432:1;4449:6;4446:89;;;-1:-1:-1;4501:19:242;;;4495:26;4446:89;-1:-1:-1;;3303:1:242;3299:11;;;3295:24;3291:29;3281:40;3327:1;3323:11;;;3278:57;4548:81;;3709:930;;3739:649;2599:1;2592:14;;;2636:4;2623:18;;-1:-1:-1;;3775:20:242;;;3893:222;3907:7;3904:1;3901:14;3893:222;;;3989:19;;;3983:26;3968:42;;4096:4;4081:20;;;;4049:1;4037:14;;;;3923:12;3893:222;;;3897:3;4143:6;4134:7;4131:19;4128:201;;;4204:19;;;4198:26;-1:-1:-1;;4287:1:242;4283:14;;;4299:3;4279:24;4275:37;4271:42;4256:58;4241:74;;4128:201;-1:-1:-1;;;;4375:1:242;4359:14;;;4355:22;4342:36;;-1:-1:-1;3346:1299:242:o;4650:217::-;4690:1;4716;4706:132;;4760:10;4755:3;4751:20;4748:1;4741:31;4795:4;4792:1;4785:15;4823:4;4820:1;4813:15;4706:132;-1:-1:-1;4852:9:242;;4650:217::o;4872:265::-;4945:9;;;4976;;4993:15;;;4987:22;;4973:37;4963:168;;5053:10;5048:3;5044:20;5041:1;5034:31;5088:4;5085:1;5078:15;5116:4;5113:1;5106:15;4963:168;4872:265;;;;:::o;5142:391::-;1098:5457:131;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x608060405234801561001057600080fd5b50600436106100f55760003560e01c80638da5cb5b11610097578063b9f9850a11610066578063b9f9850a146101cb578063f14039de146101d4578063f2fde38b146101dd578063fd2da339146101f057600080fd5b80638da5cb5b14610181578063a3193e2e1461019c578063a385fb96146101af578063b8168816146101b857600080fd5b80632191f92a116100d35780632191f92a1461014e5780636e71e2d81461015d578063715018a6146101705780638726bb891461017857600080fd5b806306fdde03146100fa57806315f24053146101185780632037f3e714610139575b600080fd5b6101026101f9565b60405161010f91906105c0565b60405180910390f35b61012b61012636600461060e565b610287565b60405190815260200161010f565b61014c61014736600461063a565b610355565b005b6040516001815260200161010f565b61012b61016b36600461060e565b61036f565b61014c6103ba565b61012b60025481565b6000546040516001600160a01b03909116815260200161010f565b61014c6101aa36600461066c565b6103ce565b61012b60015481565b61012b6101c636600461063a565b6103db565b61012b60045481565b61012b60035481565b61014c6101eb366004610685565b610457565b61012b60055481565b60068054610206906106ae565b80601f0160208091040260200160405190810160405280929190818152602001828054610232906106ae565b801561027f5780601f106102545761010080835404028352916020019161027f565b820191906000526020600020905b81548152906001019060200180831161026257829003601f168201915b505050505081565b60008061029585858561036f565b905060055481116102d657600354670de0b6b3a7640000600254836102ba91906106fe565b6102c4919061071b565b6102ce919061073d565b91505061034e565b6000600354670de0b6b3a76400006002546005546102f491906106fe565b6102fe919061071b565b610308919061073d565b905060006005548361031a9190610750565b905081670de0b6b3a76400006004548361033491906106fe565b61033e919061071b565b610348919061073d565b93505050505b9392505050565b61035d61049a565b610369848484846104c7565b50505050565b6000826000036103815750600061034e565b8161038c848661073d565b6103969190610750565b6103a884670de0b6b3a76400006106fe565b6103b2919061071b565b949350505050565b6103c261049a565b6103cc6000610570565b565b6103d661049a565b600155565b6000806103f083670de0b6b3a7640000610750565b905060006103ff878787610287565b90506000670de0b6b3a764000061041684846106fe565b610420919061071b565b9050670de0b6b3a7640000816104378a8a8a61036f565b61044191906106fe565b61044b919061071b565b98975050505050505050565b61045f61049a565b6001600160a01b03811661048e57604051631e4fbdf760e01b8152600060048201526024015b60405180910390fd5b61049781610570565b50565b6000546001600160a01b031633146103cc5760405163118cdaa760e01b8152336004820152602401610485565b6001546104d4908561071b565b6003556001546104e59082906106fe565b6104f784670de0b6b3a76400006106fe565b610501919061071b565b600255600154610511908361071b565b60048190556005829055600354600254604080519283526020830191909152810191909152606081018290527f6960ab234c7ef4b0c9197100f5393cfcde7c453ac910a27bd2000aa1dd4c068d9060800160405180910390a150505050565b600080546001600160a01b038381166001600160a01b0319831681178455604051919092169283917f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e09190a35050565b602081526000825180602084015260005b818110156105ee57602081860181015160408684010152016105d1565b506000604082850101526040601f19601f83011684010191505092915050565b60008060006060848603121561062357600080fd5b505081359360208301359350604090920135919050565b6000806000806080858703121561065057600080fd5b5050823594602084013594506040840135936060013592509050565b60006020828403121561067e57600080fd5b5035919050565b60006020828403121561069757600080fd5b81356001600160a01b038116811461034e57600080fd5b600181811c908216806106c257607f821691505b6020821081036106e257634e487b7160e01b600052602260045260246000fd5b50919050565b634e487b7160e01b600052601160045260246000fd5b8082028115828204841417610715576107156106e8565b92915050565b60008261073857634e487b7160e01b600052601260045260246000fd5b500490565b80820180821115610715576107156106e8565b81810381811115610715576107156106e856fea2646970667358221220cc2815ad496c1227160d936817170a31ce94a34b04193807ec9a4646f343c4f864736f6c634300081c0033", "sourceMap": "1098:5457:131:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1845:27;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;4533:519;;;;;;:::i;:::-;;:::i;:::-;;;1163:25:242;;;1151:2;1136:18;4533:519:131;1017:177:242;3392:287:131;;;;;;:::i;:::-;;:::i;:::-;;4075:97;;;4161:4;1931:41:242;;1919:2;1904:18;4075:97:131;1791:187:242;4232:241:131;;;;;;:::i;:::-;;:::i;2293:101:27:-;;;:::i;1445:42:131:-;;;;;;1638:85:27;1684:7;1710:6;1638:85;;-1:-1:-1;;;;;1710:6:27;;;2129:51:242;;2117:2;2102:18;1638:85:27;1983:203:242;3857:119:131;;;;;;:::i;:::-;;:::i;1347:37::-;;;;;;5112:475;;;;;;:::i;:::-;;:::i;1649:46::-;;;;;;1548:40;;;;;;2543:215:27;;;;;;:::i;:::-;;:::i;1756:28:131:-;;;;;;1845:27;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;4533:519::-;4635:7;4654:12;4669:40;4685:4;4691:7;4700:8;4669:15;:40::i;:::-;4654:55;;4732:4;;4724;:12;4720:326;;4794:16;;4787:4;4766:18;;4759:4;:25;;;;:::i;:::-;:32;;;;:::i;:::-;:51;;;;:::i;:::-;4752:58;;;;;4720:326;4841:18;4897:16;;4890:4;4869:18;;4862:4;;:25;;;;:::i;:::-;:32;;;;:::i;:::-;:51;;;;:::i;:::-;4841:72;;4927:18;4955:4;;4948;:11;;;;:::i;:::-;4927:32;;5025:10;5018:4;4993:22;;4980:10;:35;;;;:::i;:::-;:42;;;;:::i;:::-;:55;;;;:::i;:::-;4973:62;;;;;4533:519;;;;;;:::o;3392:287::-;1531:13:27;:11;:13::i;:::-;3586:86:131::1;3607:15;3624:17;3643:21;3666:5;3586:20;:86::i;:::-;3392:287:::0;;;;:::o;4232:241::-;4336:7;4359;4370:1;4359:12;4355:51;;-1:-1:-1;4394:1:131;4387:8;;4355:51;4457:8;4440:14;4447:7;4440:4;:14;:::i;:::-;:25;;;;:::i;:::-;4422:14;:7;4432:4;4422:14;:::i;:::-;:44;;;;:::i;:::-;4415:51;4232:241;-1:-1:-1;;;;4232:241:131:o;2293:101:27:-;1531:13;:11;:13::i;:::-;2357:30:::1;2384:1;2357:18;:30::i;:::-;2293:101::o:0;3857:119:131:-;1531:13:27;:11;:13::i;:::-;3939::131::1;:30:::0;3857:119::o;5112:475::-;5279:7;;5334:28;5341:21;5334:4;:28;:::i;:::-;5302:60;;5372:18;5393:38;5407:4;5413:7;5422:8;5393:13;:38::i;:::-;5372:59;-1:-1:-1;5441:18:131;5499:4;5462:34;5475:21;5372:59;5462:34;:::i;:::-;:41;;;;:::i;:::-;5441:62;;5576:4;5563:10;5520:40;5536:4;5542:7;5551:8;5520:15;:40::i;:::-;:53;;;;:::i;:::-;:60;;;;:::i;:::-;5513:67;5112:475;-1:-1:-1;;;;;;;;5112:475:131:o;2543:215:27:-;1531:13;:11;:13::i;:::-;-1:-1:-1;;;;;2627:22:27;::::1;2623:91;;2672:31;::::0;-1:-1:-1;;;2672:31:27;;2700:1:::1;2672:31;::::0;::::1;2129:51:242::0;2102:18;;2672:31:27::1;;;;;;;;2623:91;2723:28;2742:8;2723:18;:28::i;:::-;2543:215:::0;:::o;1796:162::-;1684:7;1710:6;-1:-1:-1;;;;;1710:6:27;735:10:47;1855:23:27;1851:101;;1901:40;;-1:-1:-1;;;1901:40:27;;735:10:47;1901:40:27;;;2129:51:242;2102:18;;1901:40:27;1983:203:242;6036:517:131;6257:13;;6239:31;;:15;:31;:::i;:::-;6220:16;:50;6329:13;;:21;;6345:5;;6329:21;:::i;:::-;6301:24;:17;6321:4;6301:24;:::i;:::-;:50;;;;:::i;:::-;6280:18;:71;6410:13;;6386:37;;:21;:37;:::i;:::-;6361:22;:62;;;6433:4;:12;;;6479:16;;6497:18;;6461:85;;;4119:25:242;;;4175:2;4160:18;;4153:34;;;;4203:18;;4196:34;;;;4261:2;4246:18;;4239:34;;;6461:85:131;;4106:3:242;4091:19;6461:85:131;;;;;;;6036:517;;;;:::o;2912:187:27:-;2985:16;3004:6;;-1:-1:-1;;;;;3020:17:27;;;-1:-1:-1;;;;;;3020:17:27;;;;;;3052:40;;3004:6;;;;;;;3052:40;;2985:16;3052:40;2975:124;2912:187;:::o;14:527:242:-;163:2;152:9;145:21;126:4;195:6;189:13;238:6;233:2;222:9;218:18;211:34;263:1;273:140;287:6;284:1;281:13;273:140;;;398:2;382:14;;;378:23;;372:30;367:2;348:17;;;344:26;337:66;302:10;273:140;;;277:3;462:1;457:2;448:6;437:9;433:22;429:31;422:42;532:2;525;521:7;516:2;508:6;504:15;500:29;489:9;485:45;481:54;473:62;;;14:527;;;;:::o;546:466::-;623:6;631;639;692:2;680:9;671:7;667:23;663:32;660:52;;;708:1;705;698:12;660:52;-1:-1:-1;;753:23:242;;;873:2;858:18;;845:32;;-1:-1:-1;976:2:242;961:18;;;948:32;;546:466;-1:-1:-1;546:466:242:o;1199:587::-;1285:6;1293;1301;1309;1362:3;1350:9;1341:7;1337:23;1333:33;1330:53;;;1379:1;1376;1369:12;1330:53;-1:-1:-1;;1424:23:242;;;1544:2;1529:18;;1516:32;;-1:-1:-1;1647:2:242;1632:18;;1619:32;;1750:2;1735:18;1722:32;;-1:-1:-1;1199:587:242;-1:-1:-1;1199:587:242:o;2191:226::-;2250:6;2303:2;2291:9;2282:7;2278:23;2274:32;2271:52;;;2319:1;2316;2309:12;2271:52;-1:-1:-1;2364:23:242;;2191:226;-1:-1:-1;2191:226:242:o;2422:286::-;2481:6;2534:2;2522:9;2513:7;2509:23;2505:32;2502:52;;;2550:1;2547;2540:12;2502:52;2576:23;;-1:-1:-1;;;;;2628:31:242;;2618:42;;2608:70;;2674:1;2671;2664:12;2713:380;2792:1;2788:12;;;;2835;;;2856:61;;2910:4;2902:6;2898:17;2888:27;;2856:61;2963:2;2955:6;2952:14;2932:18;2929:38;2926:161;;3009:10;3004:3;3000:20;2997:1;2990:31;3044:4;3041:1;3034:15;3072:4;3069:1;3062:15;2926:161;;2713:380;;;:::o;3098:127::-;3159:10;3154:3;3150:20;3147:1;3140:31;3190:4;3187:1;3180:15;3214:4;3211:1;3204:15;3230:168;3303:9;;;3334;;3351:15;;;3345:22;;3331:37;3321:71;;3372:18;;:::i;:::-;3230:168;;;;:::o;3403:217::-;3443:1;3469;3459:132;;3513:10;3508:3;3504:20;3501:1;3494:31;3548:4;3545:1;3538:15;3576:4;3573:1;3566:15;3459:132;-1:-1:-1;3605:9:242;;3403:217::o;3625:125::-;3690:9;;;3711:10;;;3708:36;;;3724:18;;:::i;3755:128::-;3822:9;;;3843:11;;;3840:37;;;3857:18;;:::i", "linkReferences": {}}, "methodIdentifiers": {"baseRatePerBlock()": "f14039de", "blocksPerYear()": "a385fb96", "getBorrowRate(uint256,uint256,uint256)": "15f24053", "getSupplyRate(uint256,uint256,uint256,uint256)": "b8168816", "isInterestRateModel()": "2191f92a", "jumpMultiplierPerBlock()": "b9f9850a", "kink()": "fd2da339", "multiplierPerBlock()": "8726bb89", "name()": "06fdde03", "owner()": "8da5cb5b", "renounceOwnership()": "715018a6", "transferOwnership(address)": "f2fde38b", "updateBlocksPerYear(uint256)": "a3193e2e", "updateJumpRateModel(uint256,uint256,uint256,uint256)": "2037f3e7", "utilizationRate(uint256,uint256,uint256)": "6e71e2d8"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"blocksPerYear_\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"baseRatePerYear\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"multiplierPerYear\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"jumpMultiplierPerYear\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"kink_\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"owner_\",\"type\":\"address\"},{\"internalType\":\"string\",\"name\":\"name_\",\"type\":\"string\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"JumpRateModelV4_InputNotValid\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"JumpRateModelV4_MultiplierNotValid\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"OwnableInvalidOwner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"OwnableUnauthorizedAccount\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"baseRatePerBlock\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"multiplierPerBlock\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"jumpMultiplierPerBlock\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"kink\",\"type\":\"uint256\"}],\"name\":\"NewInterestParams\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"baseRatePerBlock\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"blocksPerYear\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"cash\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"borrows\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"reserves\",\"type\":\"uint256\"}],\"name\":\"getBorrowRate\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"cash\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"borrows\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"reserves\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"reserveFactorMantissa\",\"type\":\"uint256\"}],\"name\":\"getSupplyRate\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"isInterestRateModel\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"jumpMultiplierPerBlock\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"kink\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"multiplierPerBlock\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"name\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"blocksPerYear_\",\"type\":\"uint256\"}],\"name\":\"updateBlocksPerYear\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"baseRatePerYear\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"multiplierPerYear\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"jumpMultiplierPerYear\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"kink_\",\"type\":\"uint256\"}],\"name\":\"updateJumpRateModel\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"cash\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"borrows\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"reserves\",\"type\":\"uint256\"}],\"name\":\"utilizationRate\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"pure\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"OwnableInvalidOwner(address)\":[{\"details\":\"The owner is not a valid owner account. (eg. `address(0)`)\"}],\"OwnableUnauthorizedAccount(address)\":[{\"details\":\"The caller account is not authorized to perform an operation.\"}]},\"events\":{\"NewInterestParams(uint256,uint256,uint256,uint256)\":{\"params\":{\"baseRatePerBlock\":\"The base rate per block\",\"jumpMultiplierPerBlock\":\"The multiplier after hitting the kink\",\"kink\":\"The utilization point where the jump multiplier is applied\",\"multiplierPerBlock\":\"The multiplier per block for the interest rate slope\"}}},\"kind\":\"dev\",\"methods\":{\"constructor\":{\"params\":{\"baseRatePerYear\":\"The base APR, scaled by 1e18\",\"blocksPerYear_\":\"The estimated number of blocks per year\",\"jumpMultiplierPerYear\":\"The multiplier per block after utilization point\",\"kink_\":\"The utilization point where the jump multiplier applies\",\"multiplierPerYear\":\"The rate increase in interest wrt utilization, scaled by 1e18\",\"name_\":\"A user-friendly name for the contract\",\"owner_\":\"The owner of the contract\"}},\"getBorrowRate(uint256,uint256,uint256)\":{\"params\":{\"borrows\":\"The total borrows in the market\",\"cash\":\"The total cash in the market\",\"reserves\":\"The total reserves in the market\"},\"returns\":{\"_0\":\"The current borrow rate per block, scaled by 1e18\"}},\"getSupplyRate(uint256,uint256,uint256,uint256)\":{\"params\":{\"borrows\":\"The total borrows in the market\",\"cash\":\"The total cash in the market\",\"reserveFactorMantissa\":\"The current reserve factor for the market\",\"reserves\":\"The total reserves in the market\"},\"returns\":{\"_0\":\"The current supply rate per block, scaled by 1e18\"}},\"owner()\":{\"details\":\"Returns the address of the current owner.\"},\"renounceOwnership()\":{\"details\":\"Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner.\"},\"transferOwnership(address)\":{\"details\":\"Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner.\"},\"updateBlocksPerYear(uint256)\":{\"params\":{\"blocksPerYear_\":\"The new estimated eth blocks per year.\"}},\"updateJumpRateModel(uint256,uint256,uint256,uint256)\":{\"params\":{\"baseRatePerYear\":\"The approximate target base APR, as a mantissa (scaled by 1e18)\",\"jumpMultiplierPerYear\":\"The multiplierPerBlock after hitting a specified utilization point\",\"kink_\":\"The utilization point at which the jump multiplier is applied\",\"multiplierPerYear\":\"The rate of increase in interest rate wrt utilization (scaled by 1e18)\"}},\"utilizationRate(uint256,uint256,uint256)\":{\"params\":{\"borrows\":\"The total borrows in the market\",\"cash\":\"The total cash in the market\",\"reserves\":\"The total reserves in the market\"},\"returns\":{\"_0\":\"The utilization rate as a mantissa between [0, 1e18]\"}}},\"stateVariables\":{\"baseRatePerBlock\":{\"return\":\"The base rate per block\",\"returns\":{\"_0\":\"The base rate per block\"}},\"blocksPerYear\":{\"return\":\"The number of blocks per year\",\"returns\":{\"_0\":\"The number of blocks per year\"}},\"jumpMultiplierPerBlock\":{\"return\":\"The jump multiplier per block\",\"returns\":{\"_0\":\"The jump multiplier per block\"}},\"kink\":{\"return\":\"The utilization point (kink)\",\"returns\":{\"_0\":\"The utilization point (kink)\"}},\"multiplierPerBlock\":{\"return\":\"The multiplier per block\",\"returns\":{\"_0\":\"The multiplier per block\"}},\"name\":{\"return\":\"The name of the interest rate model\",\"returns\":{\"_0\":\"The name of the interest rate model\"}}},\"title\":\"JumpRateModelV4\",\"version\":1},\"userdoc\":{\"events\":{\"NewInterestParams(uint256,uint256,uint256,uint256)\":{\"notice\":\"Emitted when interest rate parameters are updated\"}},\"kind\":\"user\",\"methods\":{\"baseRatePerBlock()\":{\"notice\":\"The base interest rate which is the y-intercept when utilization rate is 0\"},\"blocksPerYear()\":{\"notice\":\"The approximate number of blocks per year that is assumed by the interest rate model\"},\"constructor\":{\"notice\":\"Construct an interest rate model\"},\"getBorrowRate(uint256,uint256,uint256)\":{\"notice\":\"Returns the current borrow rate per block for the market\"},\"getSupplyRate(uint256,uint256,uint256,uint256)\":{\"notice\":\"Returns the current supply rate per block for the market\"},\"isInterestRateModel()\":{\"notice\":\"Should return true\"},\"jumpMultiplierPerBlock()\":{\"notice\":\"The multiplierPerBlock after hitting a specified utilization point\"},\"kink()\":{\"notice\":\"The utilization point at which the jump multiplier is applied\"},\"multiplierPerBlock()\":{\"notice\":\"The multiplier of utilization rate that gives the slope of the interest rate\"},\"name()\":{\"notice\":\"A name for user-friendliness, e.g. WBTC\"},\"updateBlocksPerYear(uint256)\":{\"notice\":\"Updates the blocksPerYear in order to make interest calculations simpler\"},\"updateJumpRateModel(uint256,uint256,uint256,uint256)\":{\"notice\":\"Update the parameters of the interest rate model (only callable by owner, i.e. Timelock)\"},\"utilizationRate(uint256,uint256,uint256)\":{\"notice\":\"Calculates the utilization rate of the market\"}},\"notice\":\"Implementation of the IInterestRateModel interface for calculating interest rates\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interest/JumpRateModelV4.sol\":\"JumpRateModelV4\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"lib/openzeppelin-contracts/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"src/interest/JumpRateModelV4.sol\":{\"keccak256\":\"0xa93081b79a997a666c96497334d721b37abfd0f193c1031e9f7f2e1b0823c37b\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://b640c34eedc95215fc74b43637256f52acb95052a946a952402b18516a67f7d5\",\"dweb:/ipfs/QmeSemgHAutRfCAttB1mTQ2AYXQpRj1Eq7haGS8QgnTHUa\"]},\"src/interfaces/IInterestRateModel.sol\":{\"keccak256\":\"0xdc8386be37a06d93fb0967be32e465f3b1f941ec9ce2f90984a5c70488cddb1e\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://de2bf96a7d0945f58b00e9fc867ac00fc4a09480e9e27c1a800604295320b769\",\"dweb:/ipfs/Qmf5sAgA58GjVxeipJNhP4euxwsZ9FS9CgsWTJ319UmCUE\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "uint256", "name": "blocksPerYear_", "type": "uint256"}, {"internalType": "uint256", "name": "baseRatePerYear", "type": "uint256"}, {"internalType": "uint256", "name": "multiplierPerYear", "type": "uint256"}, {"internalType": "uint256", "name": "jumpMultiplierPerYear", "type": "uint256"}, {"internalType": "uint256", "name": "kink_", "type": "uint256"}, {"internalType": "address", "name": "owner_", "type": "address"}, {"internalType": "string", "name": "name_", "type": "string"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "type": "error", "name": "JumpRateModelV4_InputNotValid"}, {"inputs": [], "type": "error", "name": "JumpRateModelV4_MultiplierNotValid"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "type": "error", "name": "OwnableInvalidOwner"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "type": "error", "name": "OwnableUnauthorizedAccount"}, {"inputs": [{"internalType": "uint256", "name": "baseRatePerBlock", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "multiplierPerBlock", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "jumpMultiplierPerBlock", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "kink", "type": "uint256", "indexed": false}], "type": "event", "name": "NewInterestParams", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "previousOwner", "type": "address", "indexed": true}, {"internalType": "address", "name": "new<PERSON>wner", "type": "address", "indexed": true}], "type": "event", "name": "OwnershipTransferred", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "baseRatePerBlock", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "blocksPerYear", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "cash", "type": "uint256"}, {"internalType": "uint256", "name": "borrows", "type": "uint256"}, {"internalType": "uint256", "name": "reserves", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getBorrowRate", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "cash", "type": "uint256"}, {"internalType": "uint256", "name": "borrows", "type": "uint256"}, {"internalType": "uint256", "name": "reserves", "type": "uint256"}, {"internalType": "uint256", "name": "reserveFactorMantissa", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getSupplyRate", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "pure", "type": "function", "name": "isInterestRateModel", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "jumpMultiplierPerBlock", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "kink", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "multiplierPerBlock", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "renounceOwnership"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "transferOwnership"}, {"inputs": [{"internalType": "uint256", "name": "blocksPerYear_", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "updateBlocksPerYear"}, {"inputs": [{"internalType": "uint256", "name": "baseRatePerYear", "type": "uint256"}, {"internalType": "uint256", "name": "multiplierPerYear", "type": "uint256"}, {"internalType": "uint256", "name": "jumpMultiplierPerYear", "type": "uint256"}, {"internalType": "uint256", "name": "kink_", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "updateJumpRateModel"}, {"inputs": [{"internalType": "uint256", "name": "cash", "type": "uint256"}, {"internalType": "uint256", "name": "borrows", "type": "uint256"}, {"internalType": "uint256", "name": "reserves", "type": "uint256"}], "stateMutability": "pure", "type": "function", "name": "utilizationRate", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}], "devdoc": {"kind": "dev", "methods": {"constructor": {"params": {"baseRatePerYear": "The base APR, scaled by 1e18", "blocksPerYear_": "The estimated number of blocks per year", "jumpMultiplierPerYear": "The multiplier per block after utilization point", "kink_": "The utilization point where the jump multiplier applies", "multiplierPerYear": "The rate increase in interest wrt utilization, scaled by 1e18", "name_": "A user-friendly name for the contract", "owner_": "The owner of the contract"}}, "getBorrowRate(uint256,uint256,uint256)": {"params": {"borrows": "The total borrows in the market", "cash": "The total cash in the market", "reserves": "The total reserves in the market"}, "returns": {"_0": "The current borrow rate per block, scaled by 1e18"}}, "getSupplyRate(uint256,uint256,uint256,uint256)": {"params": {"borrows": "The total borrows in the market", "cash": "The total cash in the market", "reserveFactorMantissa": "The current reserve factor for the market", "reserves": "The total reserves in the market"}, "returns": {"_0": "The current supply rate per block, scaled by 1e18"}}, "owner()": {"details": "Returns the address of the current owner."}, "renounceOwnership()": {"details": "Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner."}, "transferOwnership(address)": {"details": "Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner."}, "updateBlocksPerYear(uint256)": {"params": {"blocksPerYear_": "The new estimated eth blocks per year."}}, "updateJumpRateModel(uint256,uint256,uint256,uint256)": {"params": {"baseRatePerYear": "The approximate target base APR, as a mantissa (scaled by 1e18)", "jumpMultiplierPerYear": "The multiplierPerBlock after hitting a specified utilization point", "kink_": "The utilization point at which the jump multiplier is applied", "multiplierPerYear": "The rate of increase in interest rate wrt utilization (scaled by 1e18)"}}, "utilizationRate(uint256,uint256,uint256)": {"params": {"borrows": "The total borrows in the market", "cash": "The total cash in the market", "reserves": "The total reserves in the market"}, "returns": {"_0": "The utilization rate as a mantissa between [0, 1e18]"}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"baseRatePerBlock()": {"notice": "The base interest rate which is the y-intercept when utilization rate is 0"}, "blocksPerYear()": {"notice": "The approximate number of blocks per year that is assumed by the interest rate model"}, "constructor": {"notice": "Construct an interest rate model"}, "getBorrowRate(uint256,uint256,uint256)": {"notice": "Returns the current borrow rate per block for the market"}, "getSupplyRate(uint256,uint256,uint256,uint256)": {"notice": "Returns the current supply rate per block for the market"}, "isInterestRateModel()": {"notice": "Should return true"}, "jumpMultiplierPerBlock()": {"notice": "The multiplierPerBlock after hitting a specified utilization point"}, "kink()": {"notice": "The utilization point at which the jump multiplier is applied"}, "multiplierPerBlock()": {"notice": "The multiplier of utilization rate that gives the slope of the interest rate"}, "name()": {"notice": "A name for user-friendliness, e.g. WBTC"}, "updateBlocksPerYear(uint256)": {"notice": "Updates the blocksPerYear in order to make interest calculations simpler"}, "updateJumpRateModel(uint256,uint256,uint256,uint256)": {"notice": "Update the parameters of the interest rate model (only callable by owner, i.e. Timelock)"}, "utilizationRate(uint256,uint256,uint256)": {"notice": "Calculates the utilization rate of the market"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/interest/JumpRateModelV4.sol": "JumpRateModelV4"}, "evmVersion": "london", "libraries": {}}, "sources": {"lib/openzeppelin-contracts/contracts/access/Ownable.sol": {"keccak256": "0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb", "urls": ["bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6", "dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "src/interest/JumpRateModelV4.sol": {"keccak256": "0xa93081b79a997a666c96497334d721b37abfd0f193c1031e9f7f2e1b0823c37b", "urls": ["bzz-raw://b640c34eedc95215fc74b43637256f52acb95052a946a952402b18516a67f7d5", "dweb:/ipfs/QmeSemgHAutRfCAttB1mTQ2AYXQpRj1Eq7haGS8QgnTHUa"], "license": "BSL-1.1"}, "src/interfaces/IInterestRateModel.sol": {"keccak256": "0xdc8386be37a06d93fb0967be32e465f3b1f941ec9ce2f90984a5c70488cddb1e", "urls": ["bzz-raw://de2bf96a7d0945f58b00e9fc867ac00fc4a09480e9e27c1a800604295320b769", "dweb:/ipfs/Qmf5sAgA58GjVxeipJNhP4euxwsZ9FS9CgsWTJ319UmCUE"], "license": "BSL-1.1"}}, "version": 1}, "id": 131}