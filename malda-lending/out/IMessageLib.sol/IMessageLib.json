{"abi": [{"type": "function", "name": "getConfig", "inputs": [{"name": "_eid", "type": "uint32", "internalType": "uint32"}, {"name": "_oapp", "type": "address", "internalType": "address"}, {"name": "_configType", "type": "uint32", "internalType": "uint32"}], "outputs": [{"name": "config", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "isSupportedEid", "inputs": [{"name": "_eid", "type": "uint32", "internalType": "uint32"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "messageLibType", "inputs": [], "outputs": [{"name": "", "type": "uint8", "internalType": "enum MessageLibType"}], "stateMutability": "view"}, {"type": "function", "name": "setConfig", "inputs": [{"name": "_oapp", "type": "address", "internalType": "address"}, {"name": "_config", "type": "tuple[]", "internalType": "struct SetConfigParam[]", "components": [{"name": "eid", "type": "uint32", "internalType": "uint32"}, {"name": "configType", "type": "uint32", "internalType": "uint32"}, {"name": "config", "type": "bytes", "internalType": "bytes"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "supportsInterface", "inputs": [{"name": "interfaceId", "type": "bytes4", "internalType": "bytes4"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "version", "inputs": [], "outputs": [{"name": "major", "type": "uint64", "internalType": "uint64"}, {"name": "minor", "type": "uint8", "internalType": "uint8"}, {"name": "endpointVersion", "type": "uint8", "internalType": "uint8"}], "stateMutability": "view"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"getConfig(uint32,address,uint32)": "9c33abf7", "isSupportedEid(uint32)": "6750cd4c", "messageLibType()": "1881d94d", "setConfig(address,(uint32,uint32,bytes)[])": "20efd722", "supportsInterface(bytes4)": "01ffc9a7", "version()": "54fd4d50"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"_eid\",\"type\":\"uint32\"},{\"internalType\":\"address\",\"name\":\"_oapp\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"_configType\",\"type\":\"uint32\"}],\"name\":\"getConfig\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"config\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"_eid\",\"type\":\"uint32\"}],\"name\":\"isSupportedEid\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"messageLibType\",\"outputs\":[{\"internalType\":\"enum MessageLibType\",\"name\":\"\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_oapp\",\"type\":\"address\"},{\"components\":[{\"internalType\":\"uint32\",\"name\":\"eid\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"configType\",\"type\":\"uint32\"},{\"internalType\":\"bytes\",\"name\":\"config\",\"type\":\"bytes\"}],\"internalType\":\"struct SetConfigParam[]\",\"name\":\"_config\",\"type\":\"tuple[]\"}],\"name\":\"setConfig\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes4\",\"name\":\"interfaceId\",\"type\":\"bytes4\"}],\"name\":\"supportsInterface\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"version\",\"outputs\":[{\"internalType\":\"uint64\",\"name\":\"major\",\"type\":\"uint64\"},{\"internalType\":\"uint8\",\"name\":\"minor\",\"type\":\"uint8\"},{\"internalType\":\"uint8\",\"name\":\"endpointVersion\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{\"supportsInterface(bytes4)\":{\"details\":\"Returns true if this contract implements the interface defined by `interfaceId`. See the corresponding https://eips.ethereum.org/EIPS/eip-165#how-interfaces-are-identified[EIP section] to learn more about how these ids are created. This function call must use less than 30 000 gas.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/external/layerzero/v2/IMessageLib.sol\":\"IMessageLib\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x4296879f55019b23e135000eb36896057e7101fb7fb859c5ef690cf14643757b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://87b3541437c8c443ccd36795e56a338ed12855eec17f8da624511b8d1a7e14df\",\"dweb:/ipfs/QmeJQCtZrQjtJLr6u7ZHWeH3pBnjtLWzvRrKViAi7UZqxL\"]},\"src/interfaces/external/layerzero/v2/IMessageLib.sol\":{\"keccak256\":\"0x649c94aad1226e6450eac4a0890444132aaec0ea1b99f7d0dd5ff27254d7896d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://900ee0d0e77a25f4e6544789ca8c98547cf3fdfc5d8e8f42b64e969af1c65abe\",\"dweb:/ipfs/QmS1LkULjX315wBMwXQQeWhNvKkAKMzj7gTNiZ6Ep7ebTD\"]},\"src/interfaces/external/layerzero/v2/IMessageLibManager.sol\":{\"keccak256\":\"0x94929bdb8d035a15c94d51c16a18903d89fc9291cb6dda23043f6c9864e664f5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c9c509b859cf878757304666a37ff894c3aa414629a19e7ed35ea09139eac3d2\",\"dweb:/ipfs/Qmb8wJfG18Kv24QCVsRQADnMbLPhQ31mVXDk8e2dF3ozJu\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "uint32", "name": "_eid", "type": "uint32"}, {"internalType": "address", "name": "_oapp", "type": "address"}, {"internalType": "uint32", "name": "_configType", "type": "uint32"}], "stateMutability": "view", "type": "function", "name": "getConfig", "outputs": [{"internalType": "bytes", "name": "config", "type": "bytes"}]}, {"inputs": [{"internalType": "uint32", "name": "_eid", "type": "uint32"}], "stateMutability": "view", "type": "function", "name": "isSupportedEid", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "messageLibType", "outputs": [{"internalType": "enum MessageLibType", "name": "", "type": "uint8"}]}, {"inputs": [{"internalType": "address", "name": "_oapp", "type": "address"}, {"internalType": "struct SetConfigParam[]", "name": "_config", "type": "tuple[]", "components": [{"internalType": "uint32", "name": "eid", "type": "uint32"}, {"internalType": "uint32", "name": "configType", "type": "uint32"}, {"internalType": "bytes", "name": "config", "type": "bytes"}]}], "stateMutability": "nonpayable", "type": "function", "name": "setConfig"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "stateMutability": "view", "type": "function", "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "version", "outputs": [{"internalType": "uint64", "name": "major", "type": "uint64"}, {"internalType": "uint8", "name": "minor", "type": "uint8"}, {"internalType": "uint8", "name": "endpointVersion", "type": "uint8"}]}], "devdoc": {"kind": "dev", "methods": {"supportsInterface(bytes4)": {"details": "Returns true if this contract implements the interface defined by `interfaceId`. See the corresponding https://eips.ethereum.org/EIPS/eip-165#how-interfaces-are-identified[EIP section] to learn more about how these ids are created. This function call must use less than 30 000 gas."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/interfaces/external/layerzero/v2/IMessageLib.sol": "IMessageLib"}, "evmVersion": "london", "libraries": {}}, "sources": {"lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x4296879f55019b23e135000eb36896057e7101fb7fb859c5ef690cf14643757b", "urls": ["bzz-raw://87b3541437c8c443ccd36795e56a338ed12855eec17f8da624511b8d1a7e14df", "dweb:/ipfs/QmeJQCtZrQjtJLr6u7ZHWeH3pBnjtLWzvRrKViAi7UZqxL"], "license": "MIT"}, "src/interfaces/external/layerzero/v2/IMessageLib.sol": {"keccak256": "0x649c94aad1226e6450eac4a0890444132aaec0ea1b99f7d0dd5ff27254d7896d", "urls": ["bzz-raw://900ee0d0e77a25f4e6544789ca8c98547cf3fdfc5d8e8f42b64e969af1c65abe", "dweb:/ipfs/QmS1LkULjX315wBMwXQQeWhNvKkAKMzj7gTNiZ6Ep7ebTD"], "license": "MIT"}, "src/interfaces/external/layerzero/v2/IMessageLibManager.sol": {"keccak256": "0x94929bdb8d035a15c94d51c16a18903d89fc9291cb6dda23043f6c9864e664f5", "urls": ["bzz-raw://c9c509b859cf878757304666a37ff894c3aa414629a19e7ed35ea09139eac3d2", "dweb:/ipfs/Qmb8wJfG18Kv24QCVsRQADnMbLPhQ31mVXDk8e2dF3ozJu"], "license": "MIT"}}, "version": 1}, "id": 160}