{"abi": [{"type": "function", "name": "domain", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "xcall", "inputs": [{"name": "_destination", "type": "uint32", "internalType": "uint32"}, {"name": "_to", "type": "address", "internalType": "address"}, {"name": "_asset", "type": "address", "internalType": "address"}, {"name": "_delegate", "type": "address", "internalType": "address"}, {"name": "_amount", "type": "uint256", "internalType": "uint256"}, {"name": "_slippage", "type": "uint256", "internalType": "uint256"}, {"name": "_callData", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "payable"}, {"type": "function", "name": "xcall", "inputs": [{"name": "_destination", "type": "uint32", "internalType": "uint32"}, {"name": "_to", "type": "address", "internalType": "address"}, {"name": "_asset", "type": "address", "internalType": "address"}, {"name": "_delegate", "type": "address", "internalType": "address"}, {"name": "_amount", "type": "uint256", "internalType": "uint256"}, {"name": "_slippage", "type": "uint256", "internalType": "uint256"}, {"name": "_callData", "type": "bytes", "internalType": "bytes"}, {"name": "_relayerFee", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "xcallIntoLocal", "inputs": [{"name": "_destination", "type": "uint32", "internalType": "uint32"}, {"name": "_to", "type": "address", "internalType": "address"}, {"name": "_asset", "type": "address", "internalType": "address"}, {"name": "_delegate", "type": "address", "internalType": "address"}, {"name": "_amount", "type": "uint256", "internalType": "uint256"}, {"name": "_slippage", "type": "uint256", "internalType": "uint256"}, {"name": "_callData", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "payable"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"domain()": "c2fb26a6", "xcall(uint32,address,address,address,uint256,uint256,bytes)": "8aac16ba", "xcall(uint32,address,address,address,uint256,uint256,bytes,uint256)": "93f18ac5", "xcallIntoLocal(uint32,address,address,address,uint256,uint256,bytes)": "91f5de79"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"domain\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"_destination\",\"type\":\"uint32\"},{\"internalType\":\"address\",\"name\":\"_to\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_asset\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_delegate\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_amount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_slippage\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"_callData\",\"type\":\"bytes\"}],\"name\":\"xcall\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"_destination\",\"type\":\"uint32\"},{\"internalType\":\"address\",\"name\":\"_to\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_asset\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_delegate\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_amount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_slippage\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"_callData\",\"type\":\"bytes\"},{\"internalType\":\"uint256\",\"name\":\"_relayerFee\",\"type\":\"uint256\"}],\"name\":\"xcall\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"_destination\",\"type\":\"uint32\"},{\"internalType\":\"address\",\"name\":\"_to\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_asset\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_delegate\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_amount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_slippage\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"_callData\",\"type\":\"bytes\"}],\"name\":\"xcallIntoLocal\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"payable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/external/connext/IConnext.sol\":\"IConnext\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"src/interfaces/external/connext/IConnext.sol\":{\"keccak256\":\"0x6c74f093404acca2b83a49ef55b25e543170805a8d952817f748d9cd77f51ae4\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://581c8e5e91fdc51fc35838fad5e5b8d39c69fb0c60b5b80726f6f7385fd194d6\",\"dweb:/ipfs/QmbWYLMB6WP6kDwVLZ6X4wAVL1zWvahGyi1GKsH4GLeM3G\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "view", "type": "function", "name": "domain", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint32", "name": "_destination", "type": "uint32"}, {"internalType": "address", "name": "_to", "type": "address"}, {"internalType": "address", "name": "_asset", "type": "address"}, {"internalType": "address", "name": "_delegate", "type": "address"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}, {"internalType": "uint256", "name": "_slippage", "type": "uint256"}, {"internalType": "bytes", "name": "_callData", "type": "bytes"}], "stateMutability": "payable", "type": "function", "name": "xcall", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "uint32", "name": "_destination", "type": "uint32"}, {"internalType": "address", "name": "_to", "type": "address"}, {"internalType": "address", "name": "_asset", "type": "address"}, {"internalType": "address", "name": "_delegate", "type": "address"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}, {"internalType": "uint256", "name": "_slippage", "type": "uint256"}, {"internalType": "bytes", "name": "_callData", "type": "bytes"}, {"internalType": "uint256", "name": "_relayerFee", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "xcall", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "uint32", "name": "_destination", "type": "uint32"}, {"internalType": "address", "name": "_to", "type": "address"}, {"internalType": "address", "name": "_asset", "type": "address"}, {"internalType": "address", "name": "_delegate", "type": "address"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}, {"internalType": "uint256", "name": "_slippage", "type": "uint256"}, {"internalType": "bytes", "name": "_callData", "type": "bytes"}], "stateMutability": "payable", "type": "function", "name": "xcallIntoLocal", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/interfaces/external/connext/IConnext.sol": "IConnext"}, "evmVersion": "london", "libraries": {}}, "sources": {"src/interfaces/external/connext/IConnext.sol": {"keccak256": "0x6c74f093404acca2b83a49ef55b25e543170805a8d952817f748d9cd77f51ae4", "urls": ["bzz-raw://581c8e5e91fdc51fc35838fad5e5b8d39c69fb0c60b5b80726f6f7385fd194d6", "dweb:/ipfs/QmbWYLMB6WP6kDwVLZ6X4wAVL1zWvahGyi1GKsH4GLeM3G"], "license": "BSL-1.1"}}, "version": 1}, "id": 151}