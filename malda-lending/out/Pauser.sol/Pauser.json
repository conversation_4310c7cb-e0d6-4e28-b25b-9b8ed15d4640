{"abi": [{"type": "constructor", "inputs": [{"name": "_roles", "type": "address", "internalType": "address"}, {"name": "_operator", "type": "address", "internalType": "address"}, {"name": "_owner", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "addPausableMarket", "inputs": [{"name": "_contract", "type": "address", "internalType": "address"}, {"name": "_contractType", "type": "uint8", "internalType": "enum IPauser.PausableType"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "contractTypes", "inputs": [{"name": "_contract", "type": "address", "internalType": "address"}], "outputs": [{"name": "_type", "type": "uint8", "internalType": "enum IPauser.PausableType"}], "stateMutability": "view"}, {"type": "function", "name": "emergencyPauseAll", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "emergencyPauseMarket", "inputs": [{"name": "_market", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "emergencyPauseMarketFor", "inputs": [{"name": "_market", "type": "address", "internalType": "address"}, {"name": "_pauseType", "type": "uint8", "internalType": "enum ImTokenOperationTypes.OperationType"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "operator", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IOperator"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "pausableContracts", "inputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "market", "type": "address", "internalType": "address"}, {"name": "contractType", "type": "uint8", "internalType": "enum IPauser.PausableType"}], "stateMutability": "view"}, {"type": "function", "name": "registeredContracts", "inputs": [{"name": "_contract", "type": "address", "internalType": "address"}], "outputs": [{"name": "_registered", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "removePausableMarket", "inputs": [{"name": "_contract", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "roles", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IRoles"}], "stateMutability": "view"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "MarketAdded", "inputs": [{"name": "market", "type": "address", "indexed": true, "internalType": "address"}, {"name": "marketType", "type": "uint8", "indexed": false, "internalType": "enum IPauser.PausableType"}], "anonymous": false}, {"type": "event", "name": "MarketPaused", "inputs": [{"name": "market", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "MarketPausedFor", "inputs": [{"name": "market", "type": "address", "indexed": true, "internalType": "address"}, {"name": "pauseType", "type": "uint8", "indexed": false, "internalType": "enum ImTokenOperationTypes.OperationType"}], "anonymous": false}, {"type": "event", "name": "MarketRemoved", "inputs": [{"name": "market", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "PauseAll", "inputs": [], "anonymous": false}, {"type": "error", "name": "OwnableInvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "Pauser_AddressNotValid", "inputs": []}, {"type": "error", "name": "Pauser_AlreadyRegistered", "inputs": []}, {"type": "error", "name": "Pauser_ContractNotEnabled", "inputs": []}, {"type": "error", "name": "Pauser_EntryNotFound", "inputs": []}, {"type": "error", "name": "Pauser_NotAuthorized", "inputs": []}], "bytecode": {"object": "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", "sourceMap": "1239:4663:187:-:0;;;1602:292;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1673:6;-1:-1:-1;;;;;1273:26:27;;1269:95;;1322:31;;-1:-1:-1;;;1322:31:27;;1350:1;1322:31;;;725:51:242;698:18;;1322:31:27;;;;;;;1269:95;1373:32;1392:12;1373:18;:32::i;:::-;-1:-1:-1;;;;;;1699:20:187;::::1;1691:55;;;;-1:-1:-1::0;;;1691:55:187::1;;;;;;;;;;;;-1:-1:-1::0;;;;;1764:23:187;::::1;1756:58;;;;-1:-1:-1::0;;;1756:58:187::1;;;;;;;;;;;;-1:-1:-1::0;;;;;;1824:22:187;;::::1;;::::0;1856:31:::1;;::::0;1239:4663;;2912:187:27;2985:16;3004:6;;-1:-1:-1;;;;;3020:17:27;;;-1:-1:-1;;;;;;3020:17:27;;;;;;3052:40;;3004:6;;;;;;;3052:40;;2985:16;3052:40;2975:124;2912:187;:::o;14:177:242:-;93:13;;-1:-1:-1;;;;;135:31:242;;125:42;;115:70;;181:1;178;171:12;115:70;14:177;;;:::o;196:378::-;284:6;292;300;353:2;341:9;332:7;328:23;324:32;321:52;;;369:1;366;359:12;321:52;392:40;422:9;392:40;:::i;:::-;382:50;;451:49;496:2;485:9;481:18;451:49;:::i;:::-;441:59;;519:49;564:2;553:9;549:18;519:49;:::i;:::-;509:59;;196:378;;;;;:::o;579:203::-;1239:4663:187;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "1239:4663:187:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1398:43;;;;;;:::i;:::-;;:::i;:::-;;;;;;;;:::i;:::-;;;;;;;;3566:292;;;:::i;:::-;;1321:29;;;;;;;;-1:-1:-1;;;;;976:32:242;;;958:51;;946:2;931:18;1321:29:187;796:219:242;1356:35:187;;;;;2091:445;;;;;;:::i;:::-;;:::i;2293:101:27:-;;;:::i;1638:85::-;1684:7;1710:6;-1:-1:-1;;;;;1710:6:27;1638:85;;1526:69:187;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;:::i;1447:73::-;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;2562:14:242;;2555:22;2537:41;;2525:2;2510:18;1447:73:187;2397:187:242;3351:166:187;;;;;;:::i;:::-;;:::i;2644:463::-;;;;;;:::i;:::-;;:::i;3195:107::-;;;;;;:::i;:::-;;:::i;2543:215:27:-;;;;;;:::i;:::-;;:::i;1398:43:187:-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;1398:43:187;;;-1:-1:-1;;;;1398:43:187;;;;;:::o;3566:292::-;3628:17;:24;3614:11;3662:165;3682:3;3678:1;:7;3662:165;;;3702:54;3728:17;3746:1;3728:20;;;;;;;;:::i;:::-;;;;;;;;;;:27;-1:-1:-1;;;;;3728:27:187;3702:25;:54::i;:::-;3799:3;;3662:165;;;-1:-1:-1;3841:10:187;;;;;;;3604:254;3566:292::o;2091:445::-;1531:13:27;:11;:13::i;:::-;-1:-1:-1;;;;;2202:23:187;::::1;2194:58;;;;-1:-1:-1::0;;;2194:58:187::1;;;;;;;;;;;;-1:-1:-1::0;;;;;2266:30:187;::::1;;::::0;;;:19:::1;:30;::::0;;;;;::::1;;2298:7;2262:43;-1:-1:-1::0;;;;;2314:30:187;::::1;;::::0;;;:19:::1;:30;::::0;;;;;;;;:37;;-1:-1:-1;;2314:37:187::1;2347:4;2314:37:::0;;::::1;::::0;;;2384:42;;;;::::1;::::0;;;;;;;::::1;::::0;2412:13;;2384:42;::::1;;;;;;:::i;:::-;::::0;;2361:66;;::::1;::::0;::::1;::::0;;-1:-1:-1;2361:66:187;;;::::1;::::0;;;;;;;::::1;::::0;;-1:-1:-1;;;;;;2361:66:187;::::1;-1:-1:-1::0;;;;;2361:66:187;;::::1;::::0;;::::1;::::0;;;;::::1;::::0;;;;;;;;-1:-1:-1;;;;;;2361:66:187;;-1:-1:-1;;;2361:66:187;::::1;::::0;::::1;;;;;;:::i;:::-;;;::::0;;-1:-1:-1;;;;;;;;2437:24:187;::::1;;::::0;;;:13:::1;:24;::::0;;;;:40;;2464:13;;2437:24;-1:-1:-1;;2437:40:187::1;::::0;2464:13;2437:40:::1;::::0;::::1;;;;;;:::i;:::-;;;;;;2504:9;-1:-1:-1::0;;;;;2492:37:187::1;;2515:13;2492:37;;;;;;:::i;:::-;;;;;;;;1554:1:27;2091:445:187::0;;:::o;2293:101:27:-;1531:13;:11;:13::i;:::-;2357:30:::1;2384:1;2357:18;:30::i;:::-;2293:101::o:0;3351:166:187:-;3468:42;3490:7;3499:10;3468:21;:42::i;2644:463::-;1531:13:27;:11;:13::i;:::-;-1:-1:-1;;;;;2727:30:187;::::1;;::::0;;;:19:::1;:30;::::0;;;;;::::1;;2722:66;;2766:22;;-1:-1:-1::0;;;2766:22:187::1;;;;;;;;;;;2722:66;2798:13;2814:21;2825:9;2814:10;:21::i;:::-;2872:17;2890:24:::0;;2798:37;;-1:-1:-1;2872:17:187;2890:28:::1;::::0;2872:17;;2890:28:::1;:::i;:::-;2872:47;;;;;;;;:::i;:::-;;;;;;;;2845:17;2863:5;2845:24;;;;;;;;:::i;:::-;;::::0;;;::::1;::::0;;;:74;;:24;::::1;:74:::0;;-1:-1:-1;;;;;;2845:74:187;::::1;-1:-1:-1::0;;;;;2845:74:187;;::::1;::::0;;::::1;::::0;;;;:24;;:74:::1;-1:-1:-1::0;;;2845:74:187;;;::::1;;::::0;:24;;-1:-1:-1;;;;;;2845:74:187;;;;;;::::1;::::0;::::1;;;;;;:::i;:::-;;;;;;;;;2929:17;:23;;;;;;;:::i;:::-;;::::0;;;::::1;::::0;;;-1:-1:-1;;2929:23:187;;;;;;;-1:-1:-1;;;;;;2929:23:187;;;;;;;;;-1:-1:-1;;;;;2962:30:187;::::1;::::0;;;:19:::1;:30:::0;;;;;;:38;;-1:-1:-1;;2962:38:187;;::::1;::::0;;;3010:13:::1;:24:::0;;;;;;:51;;;;::::1;::::0;;;3076:24;;2962:30;;3076:24:::1;::::0;::::1;2712:395;2644:463:::0;:::o;3195:107::-;3261:34;3287:7;3261:25;:34::i;:::-;3195:107;:::o;2543:215:27:-;1531:13;:11;:13::i;:::-;-1:-1:-1;;;;;2627:22:27;::::1;2623:91;;2672:31;::::0;-1:-1:-1;;;2672:31:27;;2700:1:::1;2672:31;::::0;::::1;958:51:242::0;931:18;;2672:31:27::1;;;;;;;;2623:91;2723:28;2742:8;2723:18;:28::i;3904:871:187:-:0;3974:54;3996:7;4005:22;3974:21;:54::i;:::-;4038:55;4060:7;4069:23;4038:21;:55::i;:::-;4103:58;4125:7;4134:26;4103:21;:58::i;:::-;4171:59;4193:7;4202:27;4171:21;:59::i;:::-;4240:50;4262:7;4271:18;4240:21;:50::i;:::-;4300:52;4322:7;4331:20;4300:21;:52::i;:::-;4362:54;4384:7;4393:22;4362:21;:54::i;:::-;4426:51;4448:7;4457:19;4426:21;:51::i;:::-;4487;4509:7;4518:19;4487:21;:51::i;:::-;4548:52;4570:7;4579:20;4548:21;:52::i;:::-;4610:55;4632:7;4641:23;4610:21;:55::i;:::-;4675:57;4697:7;4706:25;4675:21;:57::i;:::-;4747:21;;-1:-1:-1;;;;;4747:21:187;;;;;;;;3904:871;:::o;1796:162:27:-;1684:7;1710:6;-1:-1:-1;;;;;1710:6:27;735:10:47;1855:23:27;1851:101;;1901:40;;-1:-1:-1;;;1901:40:27;;735:10:47;1901:40:27;;;958:51:242;931:18;;1901:40:27;796:219:242;2912:187:27;2985:16;3004:6;;-1:-1:-1;;;;;3020:17:27;;;-1:-1:-1;;;;;;3020:17:27;;;;;;3052:40;;3004:6;;;;;;;3052:40;;2985:16;3052:40;2975:124;2912:187;:::o;4781:199:187:-;4895:27;4902:7;4911:10;4895:6;:27::i;:::-;4953:7;-1:-1:-1;;;;;4937:36:187;;4962:10;4937:36;;;;;;:::i;5532:368::-;5625:17;:24;5592:7;;;5659:196;5679:3;5675:1;:7;5659:196;;;5734:8;-1:-1:-1;;;;;5703:39:187;:17;5721:1;5703:20;;;;;;;;:::i;:::-;;;;;;;;;;:27;-1:-1:-1;;;;;5703:27:187;:39;5699:86;;5769:1;5532:368;-1:-1:-1;;;5532:368:187:o;5699:86::-;5827:3;;5659:196;;;;5871:22;;-1:-1:-1;;;5871:22:187;;;;;;;;;;;4986:540;5093:5;-1:-1:-1;;;;;5093:18:187;;5112:10;5124:5;-1:-1:-1;;;;;5124:19:187;;:21;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;5093:53;;-1:-1:-1;;;;;;5093:53:187;;;;;;;-1:-1:-1;;;;;4191:32:242;;;5093:53:187;;;4173:51:242;4240:18;;;4233:34;4146:18;;5093:53:187;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;5085:86;;;;-1:-1:-1;;;5085:86:187;;;;;;;;;;;;-1:-1:-1;;;;;5202:22:187;;5181:18;5202:22;;;:13;:22;;;;;;;;;5238:5;:26;;;;;;;;:::i;:::-;;5234:286;;5280:45;;-1:-1:-1;;;5280:45:187;;-1:-1:-1;;;;;5280:8:187;:18;;;;:45;;5299:7;;5308:10;;5320:4;;5280:45;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;5075:451;4986:540;;:::o;5234:286::-;5355:22;5346:5;:31;;;;;;;;:::i;:::-;;5342:178;;5393:51;;-1:-1:-1;;;5393:51:187;;-1:-1:-1;;;;;5393:33:187;;;;;:51;;5427:10;;5439:4;;5393:51;;;:::i;5342:178::-;5482:27;;-1:-1:-1;;;5482:27:187;;;;;;;;;;;14:180:242;73:6;126:2;114:9;105:7;101:23;97:32;94:52;;;142:1;139;132:12;94:52;-1:-1:-1;165:23:242;;14:180;-1:-1:-1;14:180:242:o;199:127::-;260:10;255:3;251:20;248:1;241:31;291:4;288:1;281:15;315:4;312:1;305:15;331:143;415:1;408:5;405:12;395:46;;421:18;;:::i;:::-;450;;331:143::o;479:312::-;-1:-1:-1;;;;;687:32:242;;669:51;;657:2;642:18;;729:56;781:2;766:18;;758:6;729:56;:::i;:::-;479:312;;;;;:::o;1247:173::-;1315:20;;-1:-1:-1;;;;;1364:31:242;;1354:42;;1344:70;;1410:1;1407;1400:12;1344:70;1247:173;;;:::o;1425:348::-;1511:6;1519;1572:2;1560:9;1551:7;1547:23;1543:32;1540:52;;;1588:1;1585;1578:12;1540:52;1611:29;1630:9;1611:29;:::i;:::-;1601:39;;1690:2;1679:9;1675:18;1662:32;1723:1;1716:5;1713:12;1703:40;;1739:1;1736;1729:12;1703:40;1762:5;1752:15;;;1425:348;;;;;:::o;1986:186::-;2045:6;2098:2;2086:9;2077:7;2073:23;2069:32;2066:52;;;2114:1;2111;2104:12;2066:52;2137:29;2156:9;2137:29;:::i;2177:215::-;2327:2;2312:18;;2339:47;2316:9;2368:6;2339:47;:::i;:::-;2177:215;;;;:::o;2589:350::-;2676:6;2684;2737:2;2725:9;2716:7;2712:23;2708:32;2705:52;;;2753:1;2750;2743:12;2705:52;2776:29;2795:9;2776:29;:::i;:::-;2766:39;;2855:2;2844:9;2840:18;2827:32;2888:2;2881:5;2878:13;2868:41;;2905:1;2902;2895:12;2944:127;3005:10;3000:3;2996:20;2993:1;2986:31;3036:4;3033:1;3026:15;3060:4;3057:1;3050:15;3076:225;3143:9;;;3164:11;;;3161:134;;;3217:10;3212:3;3208:20;3205:1;3198:31;3252:4;3249:1;3242:15;3280:4;3277:1;3270:15;3306:127;3367:10;3362:3;3358:20;3355:1;3348:31;3398:4;3395:1;3388:15;3422:4;3419:1;3412:15;3438:145;3523:2;3516:5;3513:13;3503:47;;3530:18;;:::i;3588:217::-;3739:2;3724:18;;3751:48;3728:9;3781:6;3751:48;:::i;3810:184::-;3880:6;3933:2;3921:9;3912:7;3908:23;3904:32;3901:52;;;3949:1;3946;3939:12;3901:52;-1:-1:-1;3972:16:242;;3810:184;-1:-1:-1;3810:184:242:o;4278:277::-;4345:6;4398:2;4386:9;4377:7;4373:23;4369:32;4366:52;;;4414:1;4411;4404:12;4366:52;4446:9;4440:16;4499:5;4492:13;4485:21;4478:5;4475:32;4465:60;;4521:1;4518;4511:12;4560:395;-1:-1:-1;;;;;4791:32:242;;4773:51;;4761:2;4746:18;;4833:57;4886:2;4871:18;;4863:6;4833:57;:::i;:::-;4940:6;4933:14;4926:22;4921:2;4910:9;4906:18;4899:50;4560:395;;;;;;:::o;4960:298::-;5133:2;5118:18;;5145:48;5122:9;5175:6;5145:48;:::i;:::-;5243:6;5236:14;5229:22;5224:2;5213:9;5209:18;5202:50;4960:298;;;;;:::o", "linkReferences": {}, "immutableReferences": {"85302": [{"start": 269, "length": 32}, {"start": 2127, "length": 32}, {"start": 2175, "length": 32}], "85305": [{"start": 332, "length": 32}, {"start": 2517, "length": 32}]}}, "methodIdentifiers": {"addPausableMarket(address,uint8)": "5ae8e129", "contractTypes(address)": "904818d1", "emergencyPauseAll()": "0775ef0f", "emergencyPauseMarket(address)": "c63ca1f7", "emergencyPauseMarketFor(address,uint8)": "a5d9b634", "operator()": "570ca735", "owner()": "8da5cb5b", "pausableContracts(uint256)": "0467a1c7", "registeredContracts(address)": "a06617cd", "removePausableMarket(address)": "c03ca45a", "renounceOwnership()": "715018a6", "roles()": "392f5f64", "transferOwnership(address)": "f2fde38b"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_roles\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_operator\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_owner\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"OwnableInvalidOwner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"OwnableUnauthorizedAccount\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Pauser_AddressNotValid\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Pauser_AlreadyRegistered\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Pauser_ContractNotEnabled\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Pauser_EntryNotFound\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Pauser_NotAuthorized\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"market\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"enum IPauser.PausableType\",\"name\":\"marketType\",\"type\":\"uint8\"}],\"name\":\"MarketAdded\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"market\",\"type\":\"address\"}],\"name\":\"MarketPaused\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"market\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"enum ImTokenOperationTypes.OperationType\",\"name\":\"pauseType\",\"type\":\"uint8\"}],\"name\":\"MarketPausedFor\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"market\",\"type\":\"address\"}],\"name\":\"MarketRemoved\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[],\"name\":\"PauseAll\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_contract\",\"type\":\"address\"},{\"internalType\":\"enum IPauser.PausableType\",\"name\":\"_contractType\",\"type\":\"uint8\"}],\"name\":\"addPausableMarket\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_contract\",\"type\":\"address\"}],\"name\":\"contractTypes\",\"outputs\":[{\"internalType\":\"enum IPauser.PausableType\",\"name\":\"_type\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"emergencyPauseAll\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_market\",\"type\":\"address\"}],\"name\":\"emergencyPauseMarket\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_market\",\"type\":\"address\"},{\"internalType\":\"enum ImTokenOperationTypes.OperationType\",\"name\":\"_pauseType\",\"type\":\"uint8\"}],\"name\":\"emergencyPauseMarketFor\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"operator\",\"outputs\":[{\"internalType\":\"contract IOperator\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"pausableContracts\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"market\",\"type\":\"address\"},{\"internalType\":\"enum IPauser.PausableType\",\"name\":\"contractType\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_contract\",\"type\":\"address\"}],\"name\":\"registeredContracts\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"_registered\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_contract\",\"type\":\"address\"}],\"name\":\"removePausableMarket\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"roles\",\"outputs\":[{\"internalType\":\"contract IRoles\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"OwnableInvalidOwner(address)\":[{\"details\":\"The owner is not a valid owner account. (eg. `address(0)`)\"}],\"OwnableUnauthorizedAccount(address)\":[{\"details\":\"The caller account is not authorized to perform an operation.\"}]},\"kind\":\"dev\",\"methods\":{\"addPausableMarket(address,uint8)\":{\"params\":{\"_contract\":\"the pausable contract\",\"_contractType\":\"the pausable contract type\"}},\"emergencyPauseMarket(address)\":{\"params\":{\"_market\":\"the mToken address\"}},\"emergencyPauseMarketFor(address,uint8)\":{\"params\":{\"_market\":\"the mToken address\",\"_pauseType\":\"the operation type\"}},\"owner()\":{\"details\":\"Returns the address of the current owner.\"},\"removePausableMarket(address)\":{\"params\":{\"_contract\":\"the pausable contract\"}},\"renounceOwnership()\":{\"details\":\"Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner.\"},\"transferOwnership(address)\":{\"details\":\"Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"addPausableMarket(address,uint8)\":{\"notice\":\"add pauable contract\"},\"emergencyPauseAll()\":{\"notice\":\"pauses all operations for all registered markets\"},\"emergencyPauseMarket(address)\":{\"notice\":\"pauses all operations for a market\"},\"emergencyPauseMarketFor(address,uint8)\":{\"notice\":\"pauses a specific operation for a market\"},\"removePausableMarket(address)\":{\"notice\":\"removes pauable contract\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/pauser/Pauser.sol\":\"Pauser\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"lib/openzeppelin-contracts/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"src/interfaces/IBlacklister.sol\":{\"keccak256\":\"0x47ed20135507a9d16473027cbee18de5d952846bada761c6248b91c75cd88be1\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://91a886b95dd578156f9b6cec3236e4751aae8a38e5d15e0d6045217c6fbd3d63\",\"dweb:/ipfs/QmRNjz7sGTuMkQNZhwwcg5DNN2eeqTZSAdZmxWxbx66CHU\"]},\"src/interfaces/IOperator.sol\":{\"keccak256\":\"0x0ae3d63eb07518da5a4fd6de7b013b6f55c87defb31900548378cffc70a51f72\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://1d9710c4ac43cb0563449aa519501b4ed18a331c90d0bc7eb79cdc56407bad8a\",\"dweb:/ipfs/Qmafe9Wup2GF9pD83CLWUrRait93vwvReqU1C7EHQhY7WV\"]},\"src/interfaces/IPauser.sol\":{\"keccak256\":\"0x8c72972618419ab401a13bd0ca2ecaf299ee91e2462b704d87bd6e99e933234c\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://9055675921383a76b4659a1b364a355618480963254b847d870697b829e2e31c\",\"dweb:/ipfs/Qma2xdDddgxjp8qs13WfU8aCFjoVMyJNxvBmo5Zgr87yGZ\"]},\"src/interfaces/IRoles.sol\":{\"keccak256\":\"0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c\",\"dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj\"]},\"src/interfaces/ImToken.sol\":{\"keccak256\":\"0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36\",\"dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL\"]},\"src/interfaces/ImTokenGateway.sol\":{\"keccak256\":\"0x7b47f5229cd07de60af49eb473eddc27e429abd846df81001ef05af2c3d47634\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://04d8c52462eeb19a4c28980ac5c81a901fd35982dc47c0ab95a4ee6466b150c1\",\"dweb:/ipfs/QmfTyZrLWMnWLU6zo85RVwjv5W2qXVQPkLWzru5zcBarYm\"]},\"src/pauser/Pauser.sol\":{\"keccak256\":\"0xa72ca5608e352415254971a1dd694faeb40488832e202d9162f95af7d41ddeb0\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://b3f14fa24b998c229e7751bdbdba8f82f7893100b836afefdc24bc2cfd6dfb99\",\"dweb:/ipfs/QmfCxQwg1Tf3wWsaG6FcMSV3X7zLf8Cfs3UKgxQcLGzJmY\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "_roles", "type": "address"}, {"internalType": "address", "name": "_operator", "type": "address"}, {"internalType": "address", "name": "_owner", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "type": "error", "name": "OwnableInvalidOwner"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "type": "error", "name": "OwnableUnauthorizedAccount"}, {"inputs": [], "type": "error", "name": "Pauser_AddressNotValid"}, {"inputs": [], "type": "error", "name": "Pauser_AlreadyRegistered"}, {"inputs": [], "type": "error", "name": "Pauser_ContractNotEnabled"}, {"inputs": [], "type": "error", "name": "Pauser_EntryNotFound"}, {"inputs": [], "type": "error", "name": "Pauser_NotAuthorized"}, {"inputs": [{"internalType": "address", "name": "market", "type": "address", "indexed": true}, {"internalType": "enum IPauser.PausableType", "name": "marketType", "type": "uint8", "indexed": false}], "type": "event", "name": "MarketAdded", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "market", "type": "address", "indexed": true}], "type": "event", "name": "MarketPaused", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "market", "type": "address", "indexed": true}, {"internalType": "enum ImTokenOperationTypes.OperationType", "name": "pauseType", "type": "uint8", "indexed": false}], "type": "event", "name": "MarketPausedFor", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "market", "type": "address", "indexed": true}], "type": "event", "name": "MarketRemoved", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "previousOwner", "type": "address", "indexed": true}, {"internalType": "address", "name": "new<PERSON>wner", "type": "address", "indexed": true}], "type": "event", "name": "OwnershipTransferred", "anonymous": false}, {"inputs": [], "type": "event", "name": "PauseAll", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "_contract", "type": "address"}, {"internalType": "enum IPauser.PausableType", "name": "_contractType", "type": "uint8"}], "stateMutability": "nonpayable", "type": "function", "name": "addPausableMarket"}, {"inputs": [{"internalType": "address", "name": "_contract", "type": "address"}], "stateMutability": "view", "type": "function", "name": "contractTypes", "outputs": [{"internalType": "enum IPauser.PausableType", "name": "_type", "type": "uint8"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "emergencyPauseAll"}, {"inputs": [{"internalType": "address", "name": "_market", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "emergencyPauseMarket"}, {"inputs": [{"internalType": "address", "name": "_market", "type": "address"}, {"internalType": "enum ImTokenOperationTypes.OperationType", "name": "_pauseType", "type": "uint8"}], "stateMutability": "nonpayable", "type": "function", "name": "emergencyPauseMarketFor"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "operator", "outputs": [{"internalType": "contract IOperator", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "pausableContracts", "outputs": [{"internalType": "address", "name": "market", "type": "address"}, {"internalType": "enum IPauser.PausableType", "name": "contractType", "type": "uint8"}]}, {"inputs": [{"internalType": "address", "name": "_contract", "type": "address"}], "stateMutability": "view", "type": "function", "name": "registeredContracts", "outputs": [{"internalType": "bool", "name": "_registered", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "_contract", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "removePausableMarket"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "renounceOwnership"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "roles", "outputs": [{"internalType": "contract IRoles", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "transferOwnership"}], "devdoc": {"kind": "dev", "methods": {"addPausableMarket(address,uint8)": {"params": {"_contract": "the pausable contract", "_contractType": "the pausable contract type"}}, "emergencyPauseMarket(address)": {"params": {"_market": "the mToken address"}}, "emergencyPauseMarketFor(address,uint8)": {"params": {"_market": "the mToken address", "_pauseType": "the operation type"}}, "owner()": {"details": "Returns the address of the current owner."}, "removePausableMarket(address)": {"params": {"_contract": "the pausable contract"}}, "renounceOwnership()": {"details": "Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner."}, "transferOwnership(address)": {"details": "Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"addPausableMarket(address,uint8)": {"notice": "add pauable contract"}, "emergencyPauseAll()": {"notice": "pauses all operations for all registered markets"}, "emergencyPauseMarket(address)": {"notice": "pauses all operations for a market"}, "emergencyPauseMarketFor(address,uint8)": {"notice": "pauses a specific operation for a market"}, "removePausableMarket(address)": {"notice": "removes pauable contract"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/pauser/Pauser.sol": "Pa<PERSON>"}, "evmVersion": "london", "libraries": {}}, "sources": {"lib/openzeppelin-contracts/contracts/access/Ownable.sol": {"keccak256": "0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb", "urls": ["bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6", "dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "src/interfaces/IBlacklister.sol": {"keccak256": "0x47ed20135507a9d16473027cbee18de5d952846bada761c6248b91c75cd88be1", "urls": ["bzz-raw://91a886b95dd578156f9b6cec3236e4751aae8a38e5d15e0d6045217c6fbd3d63", "dweb:/ipfs/QmRNjz7sGTuMkQNZhwwcg5DNN2eeqTZSAdZmxWxbx66CHU"], "license": "BSL-1.1"}, "src/interfaces/IOperator.sol": {"keccak256": "0x0ae3d63eb07518da5a4fd6de7b013b6f55c87defb31900548378cffc70a51f72", "urls": ["bzz-raw://1d9710c4ac43cb0563449aa519501b4ed18a331c90d0bc7eb79cdc56407bad8a", "dweb:/ipfs/Qmafe9Wup2GF9pD83CLWUrRait93vwvReqU1C7EHQhY7WV"], "license": "BSL-1.1"}, "src/interfaces/IPauser.sol": {"keccak256": "0x8c72972618419ab401a13bd0ca2ecaf299ee91e2462b704d87bd6e99e933234c", "urls": ["bzz-raw://9055675921383a76b4659a1b364a355618480963254b847d870697b829e2e31c", "dweb:/ipfs/Qma2xdDddgxjp8qs13WfU8aCFjoVMyJNxvBmo5Zgr87yGZ"], "license": "BSL-1.1"}, "src/interfaces/IRoles.sol": {"keccak256": "0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365", "urls": ["bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c", "dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj"], "license": "BSL-1.1"}, "src/interfaces/ImToken.sol": {"keccak256": "0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f", "urls": ["bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36", "dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL"], "license": "BSL-1.1"}, "src/interfaces/ImTokenGateway.sol": {"keccak256": "0x7b47f5229cd07de60af49eb473eddc27e429abd846df81001ef05af2c3d47634", "urls": ["bzz-raw://04d8c52462eeb19a4c28980ac5c81a901fd35982dc47c0ab95a4ee6466b150c1", "dweb:/ipfs/QmfTyZrLWMnWLU6zo85RVwjv5W2qXVQPkLWzru5zcBarYm"], "license": "BSL-1.1"}, "src/pauser/Pauser.sol": {"keccak256": "0xa72ca5608e352415254971a1dd694faeb40488832e202d9162f95af7d41ddeb0", "urls": ["bzz-raw://b3f14fa24b998c229e7751bdbdba8f82f7893100b836afefdc24bc2cfd6dfb99", "dweb:/ipfs/QmfCxQwg1Tf3wWsaG6FcMSV3X7zLf8Cfs3UKgxQcLGzJmY"], "license": "BSL-1.1"}}, "version": 1}, "id": 187}