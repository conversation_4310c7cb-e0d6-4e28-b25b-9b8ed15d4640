{"abi": [{"type": "function", "name": "blacklistOperator", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IBlacklister"}], "stateMutability": "view"}, {"type": "function", "name": "borrowCaps", "inputs": [{"name": "_mToken", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "checkMembership", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "mToken", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "claimMalda", "inputs": [{"name": "holder", "type": "address", "internalType": "address"}, {"name": "mTokens", "type": "address[]", "internalType": "address[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "claimMalda", "inputs": [{"name": "holders", "type": "address[]", "internalType": "address[]"}, {"name": "mTokens", "type": "address[]", "internalType": "address[]"}, {"name": "borrowers", "type": "bool", "internalType": "bool"}, {"name": "suppliers", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "claimMalda", "inputs": [{"name": "holder", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "closeFactorMantissa", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "cumulativeOutflowVolume", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "enterMarkets", "inputs": [{"name": "_mTokens", "type": "address[]", "internalType": "address[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "enterMarketsWithSender", "inputs": [{"name": "_account", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "exitMarket", "inputs": [{"name": "_mToken", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "getAccountLiquidity", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getAllMarkets", "inputs": [], "outputs": [{"name": "mTokens", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "getAssetsIn", "inputs": [{"name": "_user", "type": "address", "internalType": "address"}], "outputs": [{"name": "mTokens", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "getHypotheticalAccountLiquidity", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "mTokenModify", "type": "address", "internalType": "address"}, {"name": "redeemTokens", "type": "uint256", "internalType": "uint256"}, {"name": "borrowAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getUSDValueForAllMarkets", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "isDeprecated", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isMarketListed", "inputs": [{"name": "market", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isOperator", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isPaused", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}, {"name": "_type", "type": "uint8", "internalType": "enum ImTokenOperationTypes.OperationType"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "lastOutflowResetTimestamp", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "limitPerTimePeriod", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "liquidateCalculateSeizeTokens", "inputs": [{"name": "mTokenBorrowed", "type": "address", "internalType": "address"}, {"name": "mTokenCollateral", "type": "address", "internalType": "address"}, {"name": "actualRepayAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "liquidationIncentiveMantissa", "inputs": [{"name": "market", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "oracleOperator", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "outflowResetTimeWindow", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "rewardDistributor", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "rolesOperator", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IRoles"}], "stateMutability": "view"}, {"type": "function", "name": "setPaused", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}, {"name": "_type", "type": "uint8", "internalType": "enum ImTokenOperationTypes.OperationType"}, {"name": "state", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "supplyCaps", "inputs": [{"name": "_mToken", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "user<PERSON><PERSON><PERSON>sted", "inputs": [{"name": "_user", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"blacklistOperator()": "2d57d487", "borrowCaps(address)": "4a584432", "checkMembership(address,address)": "929fe9a1", "claimMalda(address)": "e44a429a", "claimMalda(address,address[])": "1c7818ac", "claimMalda(address[],address[],bool,bool)": "1fbd27a5", "closeFactorMantissa()": "e8755446", "cumulativeOutflowVolume()": "700e1212", "enterMarkets(address[])": "c2998238", "enterMarketsWithSender(address)": "973fd521", "exitMarket(address)": "ede4edd0", "getAccountLiquidity(address)": "5ec88c79", "getAllMarkets()": "b0772d0b", "getAssetsIn(address)": "abfceffc", "getHypotheticalAccountLiquidity(address,address,uint256,uint256)": "4e79238f", "getUSDValueForAllMarkets()": "d99faea5", "isDeprecated(address)": "94543c15", "isMarketListed(address)": "3d98a1e5", "isOperator()": "4456eda2", "isPaused(address,uint8)": "0d126627", "lastOutflowResetTimestamp()": "ddf46254", "limitPerTimePeriod()": "8728d8a7", "liquidateCalculateSeizeTokens(address,address,uint256)": "c488847b", "liquidationIncentiveMantissa(address)": "2e06d7b1", "oracleOperator()": "11679ef7", "outflowResetTimeWindow()": "e92081b4", "rewardDistributor()": "acc2166a", "rolesOperator()": "4fecab70", "setPaused(address,uint8,bool)": "4a675b34", "supplyCaps(address)": "02c3bcbb", "userWhitelisted(address)": "fc2e0c2f"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"blacklistOperator\",\"outputs\":[{\"internalType\":\"contract IBlacklister\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_mToken\",\"type\":\"address\"}],\"name\":\"borrowCaps\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"}],\"name\":\"checkMembership\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"holder\",\"type\":\"address\"},{\"internalType\":\"address[]\",\"name\":\"mTokens\",\"type\":\"address[]\"}],\"name\":\"claimMalda\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"holders\",\"type\":\"address[]\"},{\"internalType\":\"address[]\",\"name\":\"mTokens\",\"type\":\"address[]\"},{\"internalType\":\"bool\",\"name\":\"borrowers\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"suppliers\",\"type\":\"bool\"}],\"name\":\"claimMalda\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"holder\",\"type\":\"address\"}],\"name\":\"claimMalda\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"closeFactorMantissa\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"cumulativeOutflowVolume\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"_mTokens\",\"type\":\"address[]\"}],\"name\":\"enterMarkets\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_account\",\"type\":\"address\"}],\"name\":\"enterMarketsWithSender\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_mToken\",\"type\":\"address\"}],\"name\":\"exitMarket\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"getAccountLiquidity\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getAllMarkets\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"mTokens\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_user\",\"type\":\"address\"}],\"name\":\"getAssetsIn\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"mTokens\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"mTokenModify\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"redeemTokens\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"borrowAmount\",\"type\":\"uint256\"}],\"name\":\"getHypotheticalAccountLiquidity\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getUSDValueForAllMarkets\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"}],\"name\":\"isDeprecated\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"market\",\"type\":\"address\"}],\"name\":\"isMarketListed\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"isOperator\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"internalType\":\"enum ImTokenOperationTypes.OperationType\",\"name\":\"_type\",\"type\":\"uint8\"}],\"name\":\"isPaused\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"lastOutflowResetTimestamp\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"limitPerTimePeriod\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mTokenBorrowed\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"mTokenCollateral\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"actualRepayAmount\",\"type\":\"uint256\"}],\"name\":\"liquidateCalculateSeizeTokens\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"market\",\"type\":\"address\"}],\"name\":\"liquidationIncentiveMantissa\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"oracleOperator\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"outflowResetTimeWindow\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"rewardDistributor\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"rolesOperator\",\"outputs\":[{\"internalType\":\"contract IRoles\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"internalType\":\"enum ImTokenOperationTypes.OperationType\",\"name\":\"_type\",\"type\":\"uint8\"},{\"internalType\":\"bool\",\"name\":\"state\",\"type\":\"bool\"}],\"name\":\"setPaused\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_mToken\",\"type\":\"address\"}],\"name\":\"supplyCaps\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_user\",\"type\":\"address\"}],\"name\":\"userWhitelisted\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{\"checkMembership(address,address)\":{\"params\":{\"account\":\"The address of the account to check\",\"mToken\":\"The mToken to check\"},\"returns\":{\"_0\":\"True if the account is in the asset, otherwise false.\"}},\"claimMalda(address)\":{\"params\":{\"holder\":\"The address to claim MALDA for\"}},\"claimMalda(address,address[])\":{\"params\":{\"holder\":\"The address to claim MALDA for\",\"mTokens\":\"The list of markets to claim MALDA in\"}},\"claimMalda(address[],address[],bool,bool)\":{\"params\":{\"borrowers\":\"Whether or not to claim MALDA earned by borrowing\",\"holders\":\"The addresses to claim MALDA for\",\"mTokens\":\"The list of markets to claim MALDA in\",\"suppliers\":\"Whether or not to claim MALDA earned by supplying\"}},\"enterMarkets(address[])\":{\"params\":{\"_mTokens\":\"The list of addresses of the mToken markets to be enabled\"}},\"enterMarketsWithSender(address)\":{\"params\":{\"_account\":\"The account to add for\"}},\"exitMarket(address)\":{\"details\":\"Sender must not have an outstanding borrow balance in the asset,  or be providing necessary collateral for an outstanding borrow.\",\"params\":{\"_mToken\":\"The address of the asset to be removed\"}},\"getAccountLiquidity(address)\":{\"returns\":{\"_0\":\"account liquidity in excess of collateral requirements,          account shortfall below collateral requirements)\"}},\"getAssetsIn(address)\":{\"params\":{\"_user\":\"The address of the account to pull assets for\"},\"returns\":{\"mTokens\":\"A dynamic list with the assets the account has entered\"}},\"getHypotheticalAccountLiquidity(address,address,uint256,uint256)\":{\"params\":{\"account\":\"The account to determine liquidity for\",\"borrowAmount\":\"The amount of underlying to hypothetically borrow\",\"mTokenModify\":\"The market to hypothetically redeem/borrow in\",\"redeemTokens\":\"The number of tokens to hypothetically redeem\"},\"returns\":{\"_0\":\"hypothetical account liquidity in excess of collateral requirements,         hypothetical account shortfall below collateral requirements)\"}},\"isDeprecated(address)\":{\"details\":\"All borrows in a deprecated mToken market can be immediately liquidated\",\"params\":{\"mToken\":\"The market to check if deprecated\"}},\"isPaused(address,uint8)\":{\"params\":{\"_type\":\"the operation type\",\"mToken\":\"The mToken to check\"}},\"liquidateCalculateSeizeTokens(address,address,uint256)\":{\"details\":\"Used in liquidation (called in mTokenBorrowed.liquidate)\",\"params\":{\"actualRepayAmount\":\"The amount of mTokenBorrowed underlying to convert into mTokenCollateral tokens\",\"mTokenBorrowed\":\"The address of the borrowed mToken\",\"mTokenCollateral\":\"The address of the collateral mToken\"},\"returns\":{\"_0\":\"number of mTokenCollateral tokens to be seized in a liquidation\"}},\"setPaused(address,uint8,bool)\":{\"params\":{\"_type\":\"The pause operation type\",\"mToken\":\"The market token address\",\"state\":\"The pause operation status\"}}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"blacklistOperator()\":{\"notice\":\"Blacklist\"},\"borrowCaps(address)\":{\"notice\":\"Borrow caps enforced by borrowAllowed for each mToken address. Defaults to zero which corresponds to unlimited borrowing.\"},\"checkMembership(address,address)\":{\"notice\":\"Returns whether the given account is entered in the given asset\"},\"claimMalda(address)\":{\"notice\":\"Claim all the MALDA accrued by holder in all markets\"},\"claimMalda(address,address[])\":{\"notice\":\"Claim all the MALDA accrued by holder in the specified markets\"},\"claimMalda(address[],address[],bool,bool)\":{\"notice\":\"Claim all MALDA accrued by the holders\"},\"closeFactorMantissa()\":{\"notice\":\"Multiplier used to calculate the maximum repayAmount when liquidating a borrow\"},\"cumulativeOutflowVolume()\":{\"notice\":\"Should return outflow volume\"},\"enterMarkets(address[])\":{\"notice\":\"Add assets to be included in account liquidity calculation\"},\"enterMarketsWithSender(address)\":{\"notice\":\"Add asset (msg.sender) to be included in account liquidity calculation\"},\"exitMarket(address)\":{\"notice\":\"Removes asset from sender's account liquidity calculation\"},\"getAccountLiquidity(address)\":{\"notice\":\"Determine the current account liquidity wrt collateral requirements\"},\"getAllMarkets()\":{\"notice\":\"A list of all markets\"},\"getAssetsIn(address)\":{\"notice\":\"Returns the assets an account has entered\"},\"getHypotheticalAccountLiquidity(address,address,uint256,uint256)\":{\"notice\":\"Determine what the account liquidity would be if the given amounts were redeemed/borrowed\"},\"getUSDValueForAllMarkets()\":{\"notice\":\"Returns USD value for all markets\"},\"isDeprecated(address)\":{\"notice\":\"Returns true if the given mToken market has been deprecated\"},\"isMarketListed(address)\":{\"notice\":\"Returns true/false\"},\"isOperator()\":{\"notice\":\"Should return true\"},\"isPaused(address,uint8)\":{\"notice\":\"Returns if operation is paused\"},\"lastOutflowResetTimestamp()\":{\"notice\":\"Should return last reset time for outflow check\"},\"limitPerTimePeriod()\":{\"notice\":\"Should return outflow limit\"},\"liquidateCalculateSeizeTokens(address,address,uint256)\":{\"notice\":\"Calculate number of tokens of collateral asset to seize given an underlying amount\"},\"liquidationIncentiveMantissa(address)\":{\"notice\":\"Multiplier representing the discount on collateral that a liquidator receives\"},\"oracleOperator()\":{\"notice\":\"Oracle which gives the price of any given asset\"},\"outflowResetTimeWindow()\":{\"notice\":\"Should return the outflow volume time window\"},\"rewardDistributor()\":{\"notice\":\"Reward Distributor to markets supply and borrow (including protocol token)\"},\"rolesOperator()\":{\"notice\":\"Roles\"},\"setPaused(address,uint8,bool)\":{\"notice\":\"Set pause for a specific operation\"},\"supplyCaps(address)\":{\"notice\":\"Supply caps enforced by supplyAllowed for each mToken address. Defaults to zero which corresponds to unlimited supplying.\"},\"userWhitelisted(address)\":{\"notice\":\"Returns true/false for user\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/IOperator.sol\":\"IOperator\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"src/interfaces/IBlacklister.sol\":{\"keccak256\":\"0x47ed20135507a9d16473027cbee18de5d952846bada761c6248b91c75cd88be1\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://91a886b95dd578156f9b6cec3236e4751aae8a38e5d15e0d6045217c6fbd3d63\",\"dweb:/ipfs/QmRNjz7sGTuMkQNZhwwcg5DNN2eeqTZSAdZmxWxbx66CHU\"]},\"src/interfaces/IOperator.sol\":{\"keccak256\":\"0x0ae3d63eb07518da5a4fd6de7b013b6f55c87defb31900548378cffc70a51f72\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://1d9710c4ac43cb0563449aa519501b4ed18a331c90d0bc7eb79cdc56407bad8a\",\"dweb:/ipfs/Qmafe9Wup2GF9pD83CLWUrRait93vwvReqU1C7EHQhY7WV\"]},\"src/interfaces/IRoles.sol\":{\"keccak256\":\"0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c\",\"dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj\"]},\"src/interfaces/ImToken.sol\":{\"keccak256\":\"0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36\",\"dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "view", "type": "function", "name": "blacklistOperator", "outputs": [{"internalType": "contract IBlacklister", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "_mToken", "type": "address"}], "stateMutability": "view", "type": "function", "name": "borrowCaps", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "address", "name": "mToken", "type": "address"}], "stateMutability": "view", "type": "function", "name": "checkMembership", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "holder", "type": "address"}, {"internalType": "address[]", "name": "mTokens", "type": "address[]"}], "stateMutability": "nonpayable", "type": "function", "name": "claimMalda"}, {"inputs": [{"internalType": "address[]", "name": "holders", "type": "address[]"}, {"internalType": "address[]", "name": "mTokens", "type": "address[]"}, {"internalType": "bool", "name": "borrowers", "type": "bool"}, {"internalType": "bool", "name": "suppliers", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "claimMalda"}, {"inputs": [{"internalType": "address", "name": "holder", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "claimMalda"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "closeFactorMantissa", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "cumulativeOutflowVolume", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address[]", "name": "_mTokens", "type": "address[]"}], "stateMutability": "nonpayable", "type": "function", "name": "enterMarkets"}, {"inputs": [{"internalType": "address", "name": "_account", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "enterMarketsWithSender"}, {"inputs": [{"internalType": "address", "name": "_mToken", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "exitMarket"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getAccountLiquidity", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getAllMarkets", "outputs": [{"internalType": "address[]", "name": "mTokens", "type": "address[]"}]}, {"inputs": [{"internalType": "address", "name": "_user", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getAssetsIn", "outputs": [{"internalType": "address[]", "name": "mTokens", "type": "address[]"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "address", "name": "mTokenModify", "type": "address"}, {"internalType": "uint256", "name": "redeemTokens", "type": "uint256"}, {"internalType": "uint256", "name": "borrowAmount", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getHypotheticalAccountLiquidity", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getUSDValueForAllMarkets", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isDeprecated", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "market", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isMarketListed", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "isOperator", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}, {"internalType": "enum ImTokenOperationTypes.OperationType", "name": "_type", "type": "uint8"}], "stateMutability": "view", "type": "function", "name": "isPaused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "lastOutflowResetTimestamp", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "limitPerTimePeriod", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "mTokenBorrowed", "type": "address"}, {"internalType": "address", "name": "mTokenCollateral", "type": "address"}, {"internalType": "uint256", "name": "actualRepayAmount", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "liquidateCalculateSeizeTokens", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "market", "type": "address"}], "stateMutability": "view", "type": "function", "name": "liquidationIncentiveMantissa", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "oracleOperator", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "outflowResetTimeWindow", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "rewardDistributor", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "rolesOperator", "outputs": [{"internalType": "contract IRoles", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}, {"internalType": "enum ImTokenOperationTypes.OperationType", "name": "_type", "type": "uint8"}, {"internalType": "bool", "name": "state", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "setPaused"}, {"inputs": [{"internalType": "address", "name": "_mToken", "type": "address"}], "stateMutability": "view", "type": "function", "name": "supplyCaps", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "_user", "type": "address"}], "stateMutability": "view", "type": "function", "name": "user<PERSON><PERSON><PERSON>sted", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}], "devdoc": {"kind": "dev", "methods": {"checkMembership(address,address)": {"params": {"account": "The address of the account to check", "mToken": "The mToken to check"}, "returns": {"_0": "True if the account is in the asset, otherwise false."}}, "claimMalda(address)": {"params": {"holder": "The address to claim MALDA for"}}, "claimMalda(address,address[])": {"params": {"holder": "The address to claim MALDA for", "mTokens": "The list of markets to claim MALDA in"}}, "claimMalda(address[],address[],bool,bool)": {"params": {"borrowers": "Whether or not to claim MALDA earned by borrowing", "holders": "The addresses to claim MALDA for", "mTokens": "The list of markets to claim MALDA in", "suppliers": "Whether or not to claim MALDA earned by supplying"}}, "enterMarkets(address[])": {"params": {"_mTokens": "The list of addresses of the mToken markets to be enabled"}}, "enterMarketsWithSender(address)": {"params": {"_account": "The account to add for"}}, "exitMarket(address)": {"details": "Sender must not have an outstanding borrow balance in the asset,  or be providing necessary collateral for an outstanding borrow.", "params": {"_mToken": "The address of the asset to be removed"}}, "getAccountLiquidity(address)": {"returns": {"_0": "account liquidity in excess of collateral requirements,          account shortfall below collateral requirements)"}}, "getAssetsIn(address)": {"params": {"_user": "The address of the account to pull assets for"}, "returns": {"mTokens": "A dynamic list with the assets the account has entered"}}, "getHypotheticalAccountLiquidity(address,address,uint256,uint256)": {"params": {"account": "The account to determine liquidity for", "borrowAmount": "The amount of underlying to hypothetically borrow", "mTokenModify": "The market to hypothetically redeem/borrow in", "redeemTokens": "The number of tokens to hypothetically redeem"}, "returns": {"_0": "hypothetical account liquidity in excess of collateral requirements,         hypothetical account shortfall below collateral requirements)"}}, "isDeprecated(address)": {"details": "All borrows in a deprecated mToken market can be immediately liquidated", "params": {"mToken": "The market to check if deprecated"}}, "isPaused(address,uint8)": {"params": {"_type": "the operation type", "mToken": "The mToken to check"}}, "liquidateCalculateSeizeTokens(address,address,uint256)": {"details": "Used in liquidation (called in mTokenBorrowed.liquidate)", "params": {"actualRepayAmount": "The amount of mTokenBorrowed underlying to convert into mTokenCollateral tokens", "mTokenBorrowed": "The address of the borrowed mToken", "mTokenCollateral": "The address of the collateral mToken"}, "returns": {"_0": "number of mTokenCollateral tokens to be seized in a liquidation"}}, "setPaused(address,uint8,bool)": {"params": {"_type": "The pause operation type", "mToken": "The market token address", "state": "The pause operation status"}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"blacklistOperator()": {"notice": "Blacklist"}, "borrowCaps(address)": {"notice": "Borrow caps enforced by borrowAllowed for each mToken address. Defaults to zero which corresponds to unlimited borrowing."}, "checkMembership(address,address)": {"notice": "Returns whether the given account is entered in the given asset"}, "claimMalda(address)": {"notice": "Claim all the MALDA accrued by holder in all markets"}, "claimMalda(address,address[])": {"notice": "Claim all the MALDA accrued by holder in the specified markets"}, "claimMalda(address[],address[],bool,bool)": {"notice": "Claim all MALDA accrued by the holders"}, "closeFactorMantissa()": {"notice": "Multiplier used to calculate the maximum repayAmount when liquidating a borrow"}, "cumulativeOutflowVolume()": {"notice": "Should return outflow volume"}, "enterMarkets(address[])": {"notice": "Add assets to be included in account liquidity calculation"}, "enterMarketsWithSender(address)": {"notice": "Add asset (msg.sender) to be included in account liquidity calculation"}, "exitMarket(address)": {"notice": "Removes asset from sender's account liquidity calculation"}, "getAccountLiquidity(address)": {"notice": "Determine the current account liquidity wrt collateral requirements"}, "getAllMarkets()": {"notice": "A list of all markets"}, "getAssetsIn(address)": {"notice": "Returns the assets an account has entered"}, "getHypotheticalAccountLiquidity(address,address,uint256,uint256)": {"notice": "Determine what the account liquidity would be if the given amounts were redeemed/borrowed"}, "getUSDValueForAllMarkets()": {"notice": "Returns USD value for all markets"}, "isDeprecated(address)": {"notice": "Returns true if the given mToken market has been deprecated"}, "isMarketListed(address)": {"notice": "Returns true/false"}, "isOperator()": {"notice": "Should return true"}, "isPaused(address,uint8)": {"notice": "Returns if operation is paused"}, "lastOutflowResetTimestamp()": {"notice": "Should return last reset time for outflow check"}, "limitPerTimePeriod()": {"notice": "Should return outflow limit"}, "liquidateCalculateSeizeTokens(address,address,uint256)": {"notice": "Calculate number of tokens of collateral asset to seize given an underlying amount"}, "liquidationIncentiveMantissa(address)": {"notice": "Multiplier representing the discount on collateral that a liquidator receives"}, "oracleOperator()": {"notice": "Oracle which gives the price of any given asset"}, "outflowResetTimeWindow()": {"notice": "Should return the outflow volume time window"}, "rewardDistributor()": {"notice": "Reward Distributor to markets supply and borrow (including protocol token)"}, "rolesOperator()": {"notice": "Roles"}, "setPaused(address,uint8,bool)": {"notice": "Set pause for a specific operation"}, "supplyCaps(address)": {"notice": "Supply caps enforced by supplyAllowed for each mToken address. Defaults to zero which corresponds to unlimited supplying."}, "userWhitelisted(address)": {"notice": "Returns true/false for user"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/interfaces/IOperator.sol": "IOperator"}, "evmVersion": "london", "libraries": {}}, "sources": {"src/interfaces/IBlacklister.sol": {"keccak256": "0x47ed20135507a9d16473027cbee18de5d952846bada761c6248b91c75cd88be1", "urls": ["bzz-raw://91a886b95dd578156f9b6cec3236e4751aae8a38e5d15e0d6045217c6fbd3d63", "dweb:/ipfs/QmRNjz7sGTuMkQNZhwwcg5DNN2eeqTZSAdZmxWxbx66CHU"], "license": "BSL-1.1"}, "src/interfaces/IOperator.sol": {"keccak256": "0x0ae3d63eb07518da5a4fd6de7b013b6f55c87defb31900548378cffc70a51f72", "urls": ["bzz-raw://1d9710c4ac43cb0563449aa519501b4ed18a331c90d0bc7eb79cdc56407bad8a", "dweb:/ipfs/Qmafe9Wup2GF9pD83CLWUrRait93vwvReqU1C7EHQhY7WV"], "license": "BSL-1.1"}, "src/interfaces/IRoles.sol": {"keccak256": "0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365", "urls": ["bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c", "dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj"], "license": "BSL-1.1"}, "src/interfaces/ImToken.sol": {"keccak256": "0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f", "urls": ["bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36", "dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL"], "license": "BSL-1.1"}}, "version": 1}, "id": 137}