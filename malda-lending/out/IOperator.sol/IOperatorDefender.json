{"abi": [{"type": "function", "name": "afterMTokenMint", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "view"}, {"type": "function", "name": "beforeMTokenBorrow", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}, {"name": "borrower", "type": "address", "internalType": "address"}, {"name": "borrowAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "beforeMTokenLiquidate", "inputs": [{"name": "mTokenBorrowed", "type": "address", "internalType": "address"}, {"name": "mTokenCollateral", "type": "address", "internalType": "address"}, {"name": "borrower", "type": "address", "internalType": "address"}, {"name": "repayAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "view"}, {"type": "function", "name": "beforeMTokenMint", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}, {"name": "minter", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "beforeMTokenRedeem", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}, {"name": "redeemer", "type": "address", "internalType": "address"}, {"name": "redeemTokens", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "beforeMTokenRepay", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}, {"name": "borrower", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "beforeMTokenSeize", "inputs": [{"name": "mTokenCollateral", "type": "address", "internalType": "address"}, {"name": "mTokenBorrowed", "type": "address", "internalType": "address"}, {"name": "liquidator", "type": "address", "internalType": "address"}, {"name": "borrower", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "beforeMTokenTransfer", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}, {"name": "src", "type": "address", "internalType": "address"}, {"name": "dst", "type": "address", "internalType": "address"}, {"name": "transferTokens", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "beforeRebalancing", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "checkOutflowVolumeLimit", "inputs": [{"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"afterMTokenMint(address)": "0d926fc8", "beforeMTokenBorrow(address,address,uint256)": "50795f8a", "beforeMTokenLiquidate(address,address,address,uint256)": "b50ce762", "beforeMTokenMint(address,address)": "c0f1ee09", "beforeMTokenRedeem(address,address,uint256)": "1e32bd9b", "beforeMTokenRepay(address,address)": "c321fbcc", "beforeMTokenSeize(address,address,address,address)": "6765dff9", "beforeMTokenTransfer(address,address,address,uint256)": "17bf120e", "beforeRebalancing(address)": "68f6f4b0", "checkOutflowVolumeLimit(uint256)": "823307f2"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"}],\"name\":\"afterMTokenMint\",\"outputs\":[],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"borrowAmount\",\"type\":\"uint256\"}],\"name\":\"beforeMTokenBorrow\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mTokenBorrowed\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"mTokenCollateral\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"repayAmount\",\"type\":\"uint256\"}],\"name\":\"beforeMTokenLiquidate\",\"outputs\":[],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"minter\",\"type\":\"address\"}],\"name\":\"beforeMTokenMint\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"redeemer\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"redeemTokens\",\"type\":\"uint256\"}],\"name\":\"beforeMTokenRedeem\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"}],\"name\":\"beforeMTokenRepay\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mTokenCollateral\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"mTokenBorrowed\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"liquidator\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"}],\"name\":\"beforeMTokenSeize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"src\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"dst\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"transferTokens\",\"type\":\"uint256\"}],\"name\":\"beforeMTokenTransfer\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"}],\"name\":\"beforeRebalancing\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"checkOutflowVolumeLimit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{\"afterMTokenMint(address)\":{\"params\":{\"mToken\":\"Asset being minted\"}},\"beforeMTokenBorrow(address,address,uint256)\":{\"params\":{\"borrowAmount\":\"The amount of underlying the account would borrow\",\"borrower\":\"The account which would borrow the asset\",\"mToken\":\"The market to verify the borrow against\"}},\"beforeMTokenLiquidate(address,address,address,uint256)\":{\"params\":{\"borrower\":\"The address of the borrower\",\"mTokenBorrowed\":\"Asset which was borrowed by the borrower\",\"mTokenCollateral\":\"Asset which was used as collateral and will be seized\",\"repayAmount\":\"The amount of underlying being repaid\"}},\"beforeMTokenMint(address,address)\":{\"params\":{\"mToken\":\"The market to verify the mint against\",\"minter\":\"The account which would get the minted tokens\"}},\"beforeMTokenRedeem(address,address,uint256)\":{\"params\":{\"mToken\":\"The market to verify the redeem against\",\"redeemTokens\":\"The number of mTokens to exchange for the underlying asset in the market\",\"redeemer\":\"The account which would redeem the tokens\"}},\"beforeMTokenRepay(address,address)\":{\"params\":{\"borrower\":\"The account which would borrowed the asset\",\"mToken\":\"The market to verify the repay against\"}},\"beforeMTokenSeize(address,address,address,address)\":{\"params\":{\"borrower\":\"The address of the borrower\",\"liquidator\":\"The address repaying the borrow and seizing the collateral\",\"mTokenBorrowed\":\"Asset which was borrowed by the borrower\",\"mTokenCollateral\":\"Asset which was used as collateral and will be seized\"}},\"beforeMTokenTransfer(address,address,address,uint256)\":{\"params\":{\"dst\":\"The account which receives the tokens\",\"mToken\":\"The market to verify the transfer against\",\"src\":\"The account which sources the tokens\",\"transferTokens\":\"The number of mTokens to transfer\"}},\"beforeRebalancing(address)\":{\"params\":{\"mToken\":\"The market to verify the transfer against\"}},\"checkOutflowVolumeLimit(uint256)\":{\"details\":\"Sender must be a listed market\",\"params\":{\"amount\":\"New amount\"}}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"afterMTokenMint(address)\":{\"notice\":\"Validates mint and reverts on rejection. May emit logs.\"},\"beforeMTokenBorrow(address,address,uint256)\":{\"notice\":\"Checks if the account should be allowed to borrow the underlying asset of the given market\"},\"beforeMTokenLiquidate(address,address,address,uint256)\":{\"notice\":\"Checks if the liquidation should be allowed to occur\"},\"beforeMTokenMint(address,address)\":{\"notice\":\"Checks if the account should be allowed to mint tokens in the given market\"},\"beforeMTokenRedeem(address,address,uint256)\":{\"notice\":\"Checks if the account should be allowed to redeem tokens in the given market\"},\"beforeMTokenRepay(address,address)\":{\"notice\":\"Checks if the account should be allowed to repay a borrow in the given market\"},\"beforeMTokenSeize(address,address,address,address)\":{\"notice\":\"Checks if the seizing of assets should be allowed to occur\"},\"beforeMTokenTransfer(address,address,address,uint256)\":{\"notice\":\"Checks if the account should be allowed to transfer tokens in the given market\"},\"beforeRebalancing(address)\":{\"notice\":\"Checks if the account should be allowed to rebalance tokens\"},\"checkOutflowVolumeLimit(uint256)\":{\"notice\":\"Checks if new used amount is within the limits of the outflow volume limit\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/IOperator.sol\":\"IOperatorDefender\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"src/interfaces/IBlacklister.sol\":{\"keccak256\":\"0x47ed20135507a9d16473027cbee18de5d952846bada761c6248b91c75cd88be1\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://91a886b95dd578156f9b6cec3236e4751aae8a38e5d15e0d6045217c6fbd3d63\",\"dweb:/ipfs/QmRNjz7sGTuMkQNZhwwcg5DNN2eeqTZSAdZmxWxbx66CHU\"]},\"src/interfaces/IOperator.sol\":{\"keccak256\":\"0x0ae3d63eb07518da5a4fd6de7b013b6f55c87defb31900548378cffc70a51f72\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://1d9710c4ac43cb0563449aa519501b4ed18a331c90d0bc7eb79cdc56407bad8a\",\"dweb:/ipfs/Qmafe9Wup2GF9pD83CLWUrRait93vwvReqU1C7EHQhY7WV\"]},\"src/interfaces/IRoles.sol\":{\"keccak256\":\"0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c\",\"dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj\"]},\"src/interfaces/ImToken.sol\":{\"keccak256\":\"0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36\",\"dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}], "stateMutability": "view", "type": "function", "name": "afterMTokenMint"}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}, {"internalType": "address", "name": "borrower", "type": "address"}, {"internalType": "uint256", "name": "borrowAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "beforeMTokenBorrow"}, {"inputs": [{"internalType": "address", "name": "mTokenBorrowed", "type": "address"}, {"internalType": "address", "name": "mTokenCollateral", "type": "address"}, {"internalType": "address", "name": "borrower", "type": "address"}, {"internalType": "uint256", "name": "repayAmount", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "beforeMTokenLiquidate"}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}, {"internalType": "address", "name": "minter", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "beforeMTokenMint"}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}, {"internalType": "address", "name": "redeemer", "type": "address"}, {"internalType": "uint256", "name": "redeemTokens", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "beforeMTokenRedeem"}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}, {"internalType": "address", "name": "borrower", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "beforeMTokenRepay"}, {"inputs": [{"internalType": "address", "name": "mTokenCollateral", "type": "address"}, {"internalType": "address", "name": "mTokenBorrowed", "type": "address"}, {"internalType": "address", "name": "liquidator", "type": "address"}, {"internalType": "address", "name": "borrower", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "beforeMTokenSeize"}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}, {"internalType": "address", "name": "src", "type": "address"}, {"internalType": "address", "name": "dst", "type": "address"}, {"internalType": "uint256", "name": "transferTokens", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "beforeMTokenTransfer"}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "beforeRebalancing"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "checkOutflowVolumeLimit"}], "devdoc": {"kind": "dev", "methods": {"afterMTokenMint(address)": {"params": {"mToken": "Asset being minted"}}, "beforeMTokenBorrow(address,address,uint256)": {"params": {"borrowAmount": "The amount of underlying the account would borrow", "borrower": "The account which would borrow the asset", "mToken": "The market to verify the borrow against"}}, "beforeMTokenLiquidate(address,address,address,uint256)": {"params": {"borrower": "The address of the borrower", "mTokenBorrowed": "Asset which was borrowed by the borrower", "mTokenCollateral": "Asset which was used as collateral and will be seized", "repayAmount": "The amount of underlying being repaid"}}, "beforeMTokenMint(address,address)": {"params": {"mToken": "The market to verify the mint against", "minter": "The account which would get the minted tokens"}}, "beforeMTokenRedeem(address,address,uint256)": {"params": {"mToken": "The market to verify the redeem against", "redeemTokens": "The number of mTokens to exchange for the underlying asset in the market", "redeemer": "The account which would redeem the tokens"}}, "beforeMTokenRepay(address,address)": {"params": {"borrower": "The account which would borrowed the asset", "mToken": "The market to verify the repay against"}}, "beforeMTokenSeize(address,address,address,address)": {"params": {"borrower": "The address of the borrower", "liquidator": "The address repaying the borrow and seizing the collateral", "mTokenBorrowed": "Asset which was borrowed by the borrower", "mTokenCollateral": "Asset which was used as collateral and will be seized"}}, "beforeMTokenTransfer(address,address,address,uint256)": {"params": {"dst": "The account which receives the tokens", "mToken": "The market to verify the transfer against", "src": "The account which sources the tokens", "transferTokens": "The number of mTokens to transfer"}}, "beforeRebalancing(address)": {"params": {"mToken": "The market to verify the transfer against"}}, "checkOutflowVolumeLimit(uint256)": {"details": "Sender must be a listed market", "params": {"amount": "New amount"}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"afterMTokenMint(address)": {"notice": "Validates mint and reverts on rejection. May emit logs."}, "beforeMTokenBorrow(address,address,uint256)": {"notice": "Checks if the account should be allowed to borrow the underlying asset of the given market"}, "beforeMTokenLiquidate(address,address,address,uint256)": {"notice": "Checks if the liquidation should be allowed to occur"}, "beforeMTokenMint(address,address)": {"notice": "Checks if the account should be allowed to mint tokens in the given market"}, "beforeMTokenRedeem(address,address,uint256)": {"notice": "Checks if the account should be allowed to redeem tokens in the given market"}, "beforeMTokenRepay(address,address)": {"notice": "Checks if the account should be allowed to repay a borrow in the given market"}, "beforeMTokenSeize(address,address,address,address)": {"notice": "Checks if the seizing of assets should be allowed to occur"}, "beforeMTokenTransfer(address,address,address,uint256)": {"notice": "Checks if the account should be allowed to transfer tokens in the given market"}, "beforeRebalancing(address)": {"notice": "Checks if the account should be allowed to rebalance tokens"}, "checkOutflowVolumeLimit(uint256)": {"notice": "Checks if new used amount is within the limits of the outflow volume limit"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/interfaces/IOperator.sol": "IOperatorDefender"}, "evmVersion": "london", "libraries": {}}, "sources": {"src/interfaces/IBlacklister.sol": {"keccak256": "0x47ed20135507a9d16473027cbee18de5d952846bada761c6248b91c75cd88be1", "urls": ["bzz-raw://91a886b95dd578156f9b6cec3236e4751aae8a38e5d15e0d6045217c6fbd3d63", "dweb:/ipfs/QmRNjz7sGTuMkQNZhwwcg5DNN2eeqTZSAdZmxWxbx66CHU"], "license": "BSL-1.1"}, "src/interfaces/IOperator.sol": {"keccak256": "0x0ae3d63eb07518da5a4fd6de7b013b6f55c87defb31900548378cffc70a51f72", "urls": ["bzz-raw://1d9710c4ac43cb0563449aa519501b4ed18a331c90d0bc7eb79cdc56407bad8a", "dweb:/ipfs/Qmafe9Wup2GF9pD83CLWUrRait93vwvReqU1C7EHQhY7WV"], "license": "BSL-1.1"}, "src/interfaces/IRoles.sol": {"keccak256": "0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365", "urls": ["bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c", "dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj"], "license": "BSL-1.1"}, "src/interfaces/ImToken.sol": {"keccak256": "0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f", "urls": ["bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36", "dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL"], "license": "BSL-1.1"}}, "version": 1}, "id": 137}