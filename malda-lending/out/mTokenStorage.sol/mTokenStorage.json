{"abi": [{"type": "function", "name": "accrualBlockTimestamp", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "accrueInterest", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "admin", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address payable"}], "stateMutability": "view"}, {"type": "function", "name": "allowance", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "spender", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "approve", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "balanceOf", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "balanceOfUnderlying", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "borrowBalanceCurrent", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "borrowBalanceStored", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "borrowIndex", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "borrowRateMaxMantissa", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "borrowRatePerBlock", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "decimals", "inputs": [], "outputs": [{"name": "", "type": "uint8", "internalType": "uint8"}], "stateMutability": "view"}, {"type": "function", "name": "exchangeRateCurrent", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "exchangeRateStored", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getAccountSnapshot", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getCash", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "interestRateModel", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "name", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "operator", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "pendingAdmin", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address payable"}], "stateMutability": "view"}, {"type": "function", "name": "reduceReserves", "inputs": [{"name": "reduceAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "reserveFactorMantissa", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "rolesOperator", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IRoles"}], "stateMutability": "view"}, {"type": "function", "name": "seize", "inputs": [{"name": "liquidator", "type": "address", "internalType": "address"}, {"name": "borrower", "type": "address", "internalType": "address"}, {"name": "seizeTokens", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "supplyRatePerBlock", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "symbol", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "totalBorrows", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "totalBorrowsCurrent", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "totalReserves", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "totalSupply", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "totalUnderlying", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "transfer", "inputs": [{"name": "dst", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferFrom", "inputs": [{"name": "src", "type": "address", "internalType": "address"}, {"name": "dst", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "underlying", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "event", "name": "AccrueInterest", "inputs": [{"name": "cashPrior", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "interestAccumulated", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "borrowIndex", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "totalBorrows", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Approval", "inputs": [{"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "spender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Borrow", "inputs": [{"name": "borrower", "type": "address", "indexed": true, "internalType": "address"}, {"name": "borrowAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "accountBorrows", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "totalBorrows", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "LiquidateBorrow", "inputs": [{"name": "liquidator", "type": "address", "indexed": true, "internalType": "address"}, {"name": "borrower", "type": "address", "indexed": true, "internalType": "address"}, {"name": "repayAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "mTokenCollateral", "type": "address", "indexed": true, "internalType": "address"}, {"name": "seizeTokens", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Mint", "inputs": [{"name": "minter", "type": "address", "indexed": true, "internalType": "address"}, {"name": "receiver", "type": "address", "indexed": true, "internalType": "address"}, {"name": "mintAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "mintTokens", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "NewBorrowRateMaxMantissa", "inputs": [{"name": "oldVal", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "maxMantissa", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "NewMarketInterestRateModel", "inputs": [{"name": "oldInterestRateModel", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newInterestRateModel", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "NewOperator", "inputs": [{"name": "oldOperator", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newOperator", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "NewReserveFactor", "inputs": [{"name": "oldReserveFactorMantissa", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "newReserveFactorMantissa", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "NewRolesOperator", "inputs": [{"name": "oldRoles", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newRoles", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Redeem", "inputs": [{"name": "redeemer", "type": "address", "indexed": true, "internalType": "address"}, {"name": "redeemAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "redeemTokens", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "RepayBorrow", "inputs": [{"name": "payer", "type": "address", "indexed": true, "internalType": "address"}, {"name": "borrower", "type": "address", "indexed": true, "internalType": "address"}, {"name": "repayAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "accountBorrows", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "totalBorrows", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "ReservesAdded", "inputs": [{"name": "benefactor", "type": "address", "indexed": true, "internalType": "address"}, {"name": "addAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "newTotalReserves", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "ReservesReduced", "inputs": [{"name": "admin", "type": "address", "indexed": true, "internalType": "address"}, {"name": "reduceAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "newTotalReserves", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "SameChainFlowStateUpdated", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "_oldState", "type": "bool", "indexed": false, "internalType": "bool"}, {"name": "_newState", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "Transfer", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "ZkVerifierUpdated", "inputs": [{"name": "oldVerifier", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newVerifier", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "mt_AlreadyInitialized", "inputs": []}, {"type": "error", "name": "mt_BorrowCashNotAvailable", "inputs": []}, {"type": "error", "name": "mt_BorrowRateTooHigh", "inputs": []}, {"type": "error", "name": "mt_CollateralBlockTimestampNotValid", "inputs": []}, {"type": "error", "name": "mt_ExchangeRateNotValid", "inputs": []}, {"type": "error", "name": "mt_InvalidInput", "inputs": []}, {"type": "error", "name": "mt_LiquidateSeizeTooMuch", "inputs": []}, {"type": "error", "name": "mt_MarketMethodNotValid", "inputs": []}, {"type": "error", "name": "mt_MinAmountNotValid", "inputs": []}, {"type": "error", "name": "mt_OnlyAdmin", "inputs": []}, {"type": "error", "name": "mt_OnlyAdminOrRole", "inputs": []}, {"type": "error", "name": "mt_RedeemCashNotAvailable", "inputs": []}, {"type": "error", "name": "mt_RedeemEmpty", "inputs": []}, {"type": "error", "name": "mt_RedeemTransferOutNotPossible", "inputs": []}, {"type": "error", "name": "mt_ReserveCashNotAvailable", "inputs": []}, {"type": "error", "name": "mt_ReserveFactorTooHigh", "inputs": []}, {"type": "error", "name": "mt_SameChainOperationsAreDisabled", "inputs": []}, {"type": "error", "name": "mt_TransferNotValid", "inputs": []}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"accrualBlockTimestamp()": "cfa99201", "accrueInterest()": "a6afed95", "admin()": "f851a440", "allowance(address,address)": "dd62ed3e", "approve(address,uint256)": "095ea7b3", "balanceOf(address)": "70a08231", "balanceOfUnderlying(address)": "3af9e669", "borrowBalanceCurrent(address)": "17bfdfbc", "borrowBalanceStored(address)": "95dd9193", "borrowIndex()": "aa5af0fd", "borrowRateMaxMantissa()": "ee27a2f2", "borrowRatePerBlock()": "f8f9da28", "decimals()": "313ce567", "exchangeRateCurrent()": "bd6d894d", "exchangeRateStored()": "182df0f5", "getAccountSnapshot(address)": "c37f68e2", "getCash()": "3b1d21a2", "interestRateModel()": "f3fdb15a", "name()": "06fdde03", "operator()": "570ca735", "pendingAdmin()": "********", "reduceReserves(uint256)": "07e27959", "reserveFactorMantissa()": "173b9904", "rolesOperator()": "4fecab70", "seize(address,address,uint256)": "b2a02ff1", "supplyRatePerBlock()": "ae9d70b0", "symbol()": "95d89b41", "totalBorrows()": "47bd3718", "totalBorrowsCurrent()": "73acee98", "totalReserves()": "8f840ddd", "totalSupply()": "18160ddd", "totalUnderlying()": "c70920bc", "transfer(address,uint256)": "a9059cbb", "transferFrom(address,address,uint256)": "23b872dd", "underlying()": "6f307dc3"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"mt_AlreadyInitialized\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_BorrowCashNotAvailable\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_BorrowRateTooHigh\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_CollateralBlockTimestampNotValid\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_ExchangeRateNotValid\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_InvalidInput\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_LiquidateSeizeTooMuch\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_MarketMethodNotValid\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_MinAmountNotValid\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_OnlyAdmin\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_OnlyAdminOrRole\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_RedeemCashNotAvailable\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_RedeemEmpty\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_RedeemTransferOutNotPossible\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_ReserveCashNotAvailable\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_ReserveFactorTooHigh\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_SameChainOperationsAreDisabled\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_TransferNotValid\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"cashPrior\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"interestAccumulated\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"borrowIndex\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"totalBorrows\",\"type\":\"uint256\"}],\"name\":\"AccrueInterest\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"Approval\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"borrowAmount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"accountBorrows\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"totalBorrows\",\"type\":\"uint256\"}],\"name\":\"Borrow\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"liquidator\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"repayAmount\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"mTokenCollateral\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"seizeTokens\",\"type\":\"uint256\"}],\"name\":\"LiquidateBorrow\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"minter\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"mintAmount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"mintTokens\",\"type\":\"uint256\"}],\"name\":\"Mint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"oldVal\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"maxMantissa\",\"type\":\"uint256\"}],\"name\":\"NewBorrowRateMaxMantissa\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"oldInterestRateModel\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newInterestRateModel\",\"type\":\"address\"}],\"name\":\"NewMarketInterestRateModel\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"oldOperator\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOperator\",\"type\":\"address\"}],\"name\":\"NewOperator\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"oldReserveFactorMantissa\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"newReserveFactorMantissa\",\"type\":\"uint256\"}],\"name\":\"NewReserveFactor\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"oldRoles\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newRoles\",\"type\":\"address\"}],\"name\":\"NewRolesOperator\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"redeemer\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"redeemAmount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"redeemTokens\",\"type\":\"uint256\"}],\"name\":\"Redeem\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"payer\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"repayAmount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"accountBorrows\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"totalBorrows\",\"type\":\"uint256\"}],\"name\":\"RepayBorrow\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"benefactor\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"addAmount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"newTotalReserves\",\"type\":\"uint256\"}],\"name\":\"ReservesAdded\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"admin\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"reduceAmount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"newTotalReserves\",\"type\":\"uint256\"}],\"name\":\"ReservesReduced\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"_oldState\",\"type\":\"bool\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"_newState\",\"type\":\"bool\"}],\"name\":\"SameChainFlowStateUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"oldVerifier\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newVerifier\",\"type\":\"address\"}],\"name\":\"ZkVerifierUpdated\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"accrualBlockTimestamp\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"accrueInterest\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"admin\",\"outputs\":[{\"internalType\":\"address payable\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"}],\"name\":\"allowance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"approve\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"balanceOfUnderlying\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"borrowBalanceCurrent\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"borrowBalanceStored\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"borrowIndex\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"borrowRateMaxMantissa\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"borrowRatePerBlock\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"decimals\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"exchangeRateCurrent\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"exchangeRateStored\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"getAccountSnapshot\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getCash\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"interestRateModel\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"name\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"operator\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"pendingAdmin\",\"outputs\":[{\"internalType\":\"address payable\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"reduceAmount\",\"type\":\"uint256\"}],\"name\":\"reduceReserves\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"reserveFactorMantissa\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"rolesOperator\",\"outputs\":[{\"internalType\":\"contract IRoles\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"liquidator\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"seizeTokens\",\"type\":\"uint256\"}],\"name\":\"seize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"supplyRatePerBlock\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"symbol\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalBorrows\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalBorrowsCurrent\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalReserves\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalSupply\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalUnderlying\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"dst\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"transfer\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"src\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"dst\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"transferFrom\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"underlying\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{\"allowance(address,address)\":{\"params\":{\"owner\":\"The address of the token holder\",\"spender\":\"The address authorized to spend the tokens\"},\"returns\":{\"_0\":\"The current remaining number of tokens `spender` can spend\"}},\"approve(address,uint256)\":{\"params\":{\"amount\":\"The number of tokens to approve\",\"spender\":\"The address authorized to spend tokens\"},\"returns\":{\"_0\":\"Whether the approval was successful or not\"}},\"balanceOf(address)\":{\"params\":{\"owner\":\"The address to query the balance for\"},\"returns\":{\"_0\":\"The balance of tokens owned by `owner`\"}},\"balanceOfUnderlying(address)\":{\"params\":{\"owner\":\"The address to query the balance of underlying assets for\"},\"returns\":{\"_0\":\"The balance of underlying assets owned by `owner`\"}},\"borrowBalanceCurrent(address)\":{\"params\":{\"account\":\"The address to query the borrow balance for\"},\"returns\":{\"_0\":\"The current borrow balance\"}},\"borrowBalanceStored(address)\":{\"params\":{\"account\":\"The address to query the stored borrow balance for\"},\"returns\":{\"_0\":\"The stored borrow balance\"}},\"borrowRatePerBlock()\":{\"returns\":{\"_0\":\"The current borrow rate per block, scaled by 1e18\"}},\"exchangeRateCurrent()\":{\"returns\":{\"_0\":\"The current exchange rate\"}},\"exchangeRateStored()\":{\"returns\":{\"_0\":\"The stored exchange rate\"}},\"getAccountSnapshot(address)\":{\"params\":{\"account\":\"The address to query the account snapshot for\"},\"returns\":{\"_0\":\"(token balance, borrow balance, exchange rate)\"}},\"getCash()\":{\"returns\":{\"_0\":\"The total amount of cash\"}},\"reduceReserves(uint256)\":{\"params\":{\"reduceAmount\":\"Amount of reduction to reserves\"}},\"seize(address,address,uint256)\":{\"details\":\"Will fail unless called by another mToken during the process of liquidation.  Its absolutely critical to use msg.sender as the borrowed mToken and not a parameter.\",\"params\":{\"borrower\":\"The account having collateral seized\",\"liquidator\":\"The account receiving seized collateral\",\"seizeTokens\":\"The number of mTokens to seize\"}},\"supplyRatePerBlock()\":{\"returns\":{\"_0\":\"The current supply rate per block, scaled by 1e18\"}},\"totalBorrowsCurrent()\":{\"returns\":{\"_0\":\"The total amount of borrows\"}},\"transfer(address,uint256)\":{\"params\":{\"amount\":\"The number of tokens to transfer\",\"dst\":\"The address of the recipient\"},\"returns\":{\"_0\":\"Whether the transfer was successful or not\"}},\"transferFrom(address,address,uint256)\":{\"params\":{\"amount\":\"The number of tokens to transfer\",\"dst\":\"The address to which tokens are transferred\",\"src\":\"The address from which tokens are transferred\"},\"returns\":{\"_0\":\"Whether the transfer was successful or not\"}},\"underlying()\":{\"details\":\"Returns the underlying address\"}},\"version\":1},\"userdoc\":{\"events\":{\"AccrueInterest(uint256,uint256,uint256,uint256)\":{\"notice\":\"Event emitted when interest is accrued\"},\"Approval(address,address,uint256)\":{\"notice\":\"EIP20 Approval event\"},\"Borrow(address,uint256,uint256,uint256)\":{\"notice\":\"Event emitted when underlying is borrowed\"},\"LiquidateBorrow(address,address,uint256,address,uint256)\":{\"notice\":\"Event emitted when a borrow is liquidated\"},\"Mint(address,address,uint256,uint256)\":{\"notice\":\"Event emitted when tokens are minted\"},\"NewBorrowRateMaxMantissa(uint256,uint256)\":{\"notice\":\"Event emitted when the borrow max mantissa is updated\"},\"NewMarketInterestRateModel(address,address)\":{\"notice\":\"Event emitted when interestRateModel is changed\"},\"NewOperator(address,address)\":{\"notice\":\"Event emitted when Operator is changed\"},\"NewReserveFactor(uint256,uint256)\":{\"notice\":\"Event emitted when the reserve factor is changed\"},\"NewRolesOperator(address,address)\":{\"notice\":\"Event emitted when rolesOperator is changed\"},\"Redeem(address,uint256,uint256)\":{\"notice\":\"Event emitted when tokens are redeemed\"},\"RepayBorrow(address,address,uint256,uint256,uint256)\":{\"notice\":\"Event emitted when a borrow is repaid\"},\"ReservesAdded(address,uint256,uint256)\":{\"notice\":\"Event emitted when the reserves are added\"},\"ReservesReduced(address,uint256,uint256)\":{\"notice\":\"Event emitted when the reserves are reduced\"},\"SameChainFlowStateUpdated(address,bool,bool)\":{\"notice\":\"Event emitted when same chain flow state is enabled or disabled\"},\"Transfer(address,address,uint256)\":{\"notice\":\"EIP20 Transfer event\"},\"ZkVerifierUpdated(address,address)\":{\"notice\":\"Event emitted when same chain flow state is enabled or disabled\"}},\"kind\":\"user\",\"methods\":{\"accrualBlockTimestamp()\":{\"notice\":\"Block timestamp that interest was last accrued at\"},\"accrueInterest()\":{\"notice\":\"Accrues interest on the contract's outstanding loans\"},\"admin()\":{\"notice\":\"Administrator for this contract\"},\"allowance(address,address)\":{\"notice\":\"Returns the current allowance the `spender` has from the `owner`\"},\"approve(address,uint256)\":{\"notice\":\"Approves `spender` to spend `amount` tokens on behalf of the caller\"},\"balanceOf(address)\":{\"notice\":\"Returns the balance of tokens held by `owner`\"},\"balanceOfUnderlying(address)\":{\"notice\":\"Returns the underlying asset balance of the `owner`\"},\"borrowBalanceCurrent(address)\":{\"notice\":\"Returns the current borrow balance for `account`, accounting for interest\"},\"borrowBalanceStored(address)\":{\"notice\":\"Returns the stored borrow balance for `account`, without accruing interest\"},\"borrowIndex()\":{\"notice\":\"Accumulator of the total earned interest rate since the opening of the market\"},\"borrowRateMaxMantissa()\":{\"notice\":\"Maximum borrow rate that can ever be applied\"},\"borrowRatePerBlock()\":{\"notice\":\"Returns the current borrow rate per block\"},\"decimals()\":{\"notice\":\"EIP-20 token decimals for this token\"},\"exchangeRateCurrent()\":{\"notice\":\"Returns the current exchange rate, with interest accrued\"},\"exchangeRateStored()\":{\"notice\":\"Returns the stored exchange rate, without accruing interest\"},\"getAccountSnapshot(address)\":{\"notice\":\"Returns the snapshot of account details for the given `account`\"},\"getCash()\":{\"notice\":\"Returns the total amount of available cash in the contract\"},\"interestRateModel()\":{\"notice\":\"Model which tells what the current interest rate should be\"},\"name()\":{\"notice\":\"EIP-20 token name for this token\"},\"operator()\":{\"notice\":\"Contract which oversees inter-mToken operations\"},\"pendingAdmin()\":{\"notice\":\"Pending administrator for this contract\"},\"reduceReserves(uint256)\":{\"notice\":\"Accrues interest and reduces reserves by transferring to admin\"},\"reserveFactorMantissa()\":{\"notice\":\"Fraction of interest currently set aside for reserves\"},\"rolesOperator()\":{\"notice\":\"Roles manager\"},\"seize(address,address,uint256)\":{\"notice\":\"Transfers collateral tokens (this market) to the liquidator.\"},\"supplyRatePerBlock()\":{\"notice\":\"Returns the current supply rate per block\"},\"symbol()\":{\"notice\":\"EIP-20 token symbol for this token\"},\"totalBorrows()\":{\"notice\":\"Total amount of outstanding borrows of the underlying in this market\"},\"totalBorrowsCurrent()\":{\"notice\":\"Returns the total amount of borrows, accounting for interest\"},\"totalReserves()\":{\"notice\":\"Total amount of reserves of the underlying held in this market\"},\"totalSupply()\":{\"notice\":\"Returns the value of tokens in existence.\"},\"totalUnderlying()\":{\"notice\":\"Returns the amount of underlying tokens\"},\"transfer(address,uint256)\":{\"notice\":\"Transfers `amount` tokens to the `dst` address\"},\"transferFrom(address,address,uint256)\":{\"notice\":\"Transfers `amount` tokens from the `src` address to the `dst` address\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/mToken/mTokenStorage.sol\":\"mTokenStorage\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"src/interfaces/IInterestRateModel.sol\":{\"keccak256\":\"0xdc8386be37a06d93fb0967be32e465f3b1f941ec9ce2f90984a5c70488cddb1e\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://de2bf96a7d0945f58b00e9fc867ac00fc4a09480e9e27c1a800604295320b769\",\"dweb:/ipfs/Qmf5sAgA58GjVxeipJNhP4euxwsZ9FS9CgsWTJ319UmCUE\"]},\"src/interfaces/IRoles.sol\":{\"keccak256\":\"0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c\",\"dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj\"]},\"src/interfaces/ImToken.sol\":{\"keccak256\":\"0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36\",\"dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL\"]},\"src/mToken/mTokenStorage.sol\":{\"keccak256\":\"0x42236cd9e37c2eef2d317f05510bbe21cf5174813ee40f9a6fcbaf84d4fed2f7\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://2679166e1ca0e5d21294447566bba7549900a8f9a9b1882c8c2895a9751a1792\",\"dweb:/ipfs/Qmb49FVvre1He18e6ABHjNJ2yzCPdkYeyzSwMXixZ7fUw3\"]},\"src/utils/ExponentialNoError.sol\":{\"keccak256\":\"0xa97178117d8c32059d55dd2224e5514c54b31f896e565c3834cab3d7502b1bb6\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://1cecc0713709f57a5bb7f64d2fa06b9437a42752fa3fed5d3c0b19d82aa56ef4\",\"dweb:/ipfs/QmQqBCZkzmZxomXLr3b1tHUV55n8EPk3wAUsXgojYSpR41\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "mt_AlreadyInitialized"}, {"inputs": [], "type": "error", "name": "mt_BorrowCashNotAvailable"}, {"inputs": [], "type": "error", "name": "mt_BorrowRateTooHigh"}, {"inputs": [], "type": "error", "name": "mt_CollateralBlockTimestampNotValid"}, {"inputs": [], "type": "error", "name": "mt_ExchangeRateNotValid"}, {"inputs": [], "type": "error", "name": "mt_InvalidInput"}, {"inputs": [], "type": "error", "name": "mt_LiquidateSeizeTooMuch"}, {"inputs": [], "type": "error", "name": "mt_MarketMethodNotValid"}, {"inputs": [], "type": "error", "name": "mt_MinAmountNotValid"}, {"inputs": [], "type": "error", "name": "mt_OnlyAdmin"}, {"inputs": [], "type": "error", "name": "mt_OnlyAdminOrRole"}, {"inputs": [], "type": "error", "name": "mt_RedeemCashNotAvailable"}, {"inputs": [], "type": "error", "name": "mt_RedeemEmpty"}, {"inputs": [], "type": "error", "name": "mt_RedeemTransferOutNotPossible"}, {"inputs": [], "type": "error", "name": "mt_ReserveCashNotAvailable"}, {"inputs": [], "type": "error", "name": "mt_ReserveFactorTooHigh"}, {"inputs": [], "type": "error", "name": "mt_SameChainOperationsAreDisabled"}, {"inputs": [], "type": "error", "name": "mt_TransferNotValid"}, {"inputs": [{"internalType": "uint256", "name": "cashPrior", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "interestAccumulated", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "borrowIndex", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "totalBorrows", "type": "uint256", "indexed": false}], "type": "event", "name": "AccrueInterest", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address", "indexed": true}, {"internalType": "address", "name": "spender", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}], "type": "event", "name": "Approval", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "borrower", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "borrowAmount", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "accountBorrows", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "totalBorrows", "type": "uint256", "indexed": false}], "type": "event", "name": "Borrow", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "liquidator", "type": "address", "indexed": true}, {"internalType": "address", "name": "borrower", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "repayAmount", "type": "uint256", "indexed": false}, {"internalType": "address", "name": "mTokenCollateral", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "seizeTokens", "type": "uint256", "indexed": false}], "type": "event", "name": "LiquidateBorrow", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "minter", "type": "address", "indexed": true}, {"internalType": "address", "name": "receiver", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "mintAmount", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "mintTokens", "type": "uint256", "indexed": false}], "type": "event", "name": "Mint", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "oldVal", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "maxMantissa", "type": "uint256", "indexed": false}], "type": "event", "name": "NewBorrowRateMaxMantissa", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "oldInterestRateModel", "type": "address", "indexed": true}, {"internalType": "address", "name": "newInterestRateModel", "type": "address", "indexed": true}], "type": "event", "name": "NewMarketInterestRateModel", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "oldOperator", "type": "address", "indexed": true}, {"internalType": "address", "name": "newOperator", "type": "address", "indexed": true}], "type": "event", "name": "NewOperator", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "oldReserveFactorMantissa", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "newReserveFactorMantissa", "type": "uint256", "indexed": false}], "type": "event", "name": "NewReserveFactor", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "oldRoles", "type": "address", "indexed": true}, {"internalType": "address", "name": "newRoles", "type": "address", "indexed": true}], "type": "event", "name": "NewRolesOperator", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "redeemer", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "redeemAmount", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "redeemTokens", "type": "uint256", "indexed": false}], "type": "event", "name": "Redeem", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "payer", "type": "address", "indexed": true}, {"internalType": "address", "name": "borrower", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "repayAmount", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "accountBorrows", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "totalBorrows", "type": "uint256", "indexed": false}], "type": "event", "name": "RepayBorrow", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "benefactor", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "addAmount", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "newTotalReserves", "type": "uint256", "indexed": false}], "type": "event", "name": "ReservesAdded", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "admin", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "reduceAmount", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "newTotalReserves", "type": "uint256", "indexed": false}], "type": "event", "name": "ReservesReduced", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address", "indexed": true}, {"internalType": "bool", "name": "_oldState", "type": "bool", "indexed": false}, {"internalType": "bool", "name": "_newState", "type": "bool", "indexed": false}], "type": "event", "name": "SameChainFlowStateUpdated", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "from", "type": "address", "indexed": true}, {"internalType": "address", "name": "to", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}], "type": "event", "name": "Transfer", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "oldVerifier", "type": "address", "indexed": true}, {"internalType": "address", "name": "newVerifier", "type": "address", "indexed": true}], "type": "event", "name": "ZkVerifierUpdated", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "accrualBlockTimestamp", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "accrueInterest"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "admin", "outputs": [{"internalType": "address payable", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "stateMutability": "view", "type": "function", "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "stateMutability": "view", "type": "function", "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "balanceOfUnderlying", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "borrowBalanceCurrent", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "borrowBalanceStored", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "borrowIndex", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "borrowRateMaxMantissa", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "borrowRatePerBlock", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "exchangeRateCurrent", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "exchangeRateStored", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getAccountSnapshot", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getCash", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "interestRateModel", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "operator", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "pendingAdmin", "outputs": [{"internalType": "address payable", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "uint256", "name": "reduceAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "reduceReserves"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "reserveFactorMantissa", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "rolesOperator", "outputs": [{"internalType": "contract IRoles", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "liquidator", "type": "address"}, {"internalType": "address", "name": "borrower", "type": "address"}, {"internalType": "uint256", "name": "seizeTokens", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "seize"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "supplyRatePerBlock", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "totalBorrows", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "totalBorrowsCurrent", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "totalReserves", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "totalUnderlying", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "dst", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "src", "type": "address"}, {"internalType": "address", "name": "dst", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "underlying", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}], "devdoc": {"kind": "dev", "methods": {"allowance(address,address)": {"params": {"owner": "The address of the token holder", "spender": "The address authorized to spend the tokens"}, "returns": {"_0": "The current remaining number of tokens `spender` can spend"}}, "approve(address,uint256)": {"params": {"amount": "The number of tokens to approve", "spender": "The address authorized to spend tokens"}, "returns": {"_0": "Whether the approval was successful or not"}}, "balanceOf(address)": {"params": {"owner": "The address to query the balance for"}, "returns": {"_0": "The balance of tokens owned by `owner`"}}, "balanceOfUnderlying(address)": {"params": {"owner": "The address to query the balance of underlying assets for"}, "returns": {"_0": "The balance of underlying assets owned by `owner`"}}, "borrowBalanceCurrent(address)": {"params": {"account": "The address to query the borrow balance for"}, "returns": {"_0": "The current borrow balance"}}, "borrowBalanceStored(address)": {"params": {"account": "The address to query the stored borrow balance for"}, "returns": {"_0": "The stored borrow balance"}}, "borrowRatePerBlock()": {"returns": {"_0": "The current borrow rate per block, scaled by 1e18"}}, "exchangeRateCurrent()": {"returns": {"_0": "The current exchange rate"}}, "exchangeRateStored()": {"returns": {"_0": "The stored exchange rate"}}, "getAccountSnapshot(address)": {"params": {"account": "The address to query the account snapshot for"}, "returns": {"_0": "(token balance, borrow balance, exchange rate)"}}, "getCash()": {"returns": {"_0": "The total amount of cash"}}, "reduceReserves(uint256)": {"params": {"reduceAmount": "Amount of reduction to reserves"}}, "seize(address,address,uint256)": {"details": "Will fail unless called by another mToken during the process of liquidation.  Its absolutely critical to use msg.sender as the borrowed mToken and not a parameter.", "params": {"borrower": "The account having collateral seized", "liquidator": "The account receiving seized collateral", "seizeTokens": "The number of mTokens to seize"}}, "supplyRatePerBlock()": {"returns": {"_0": "The current supply rate per block, scaled by 1e18"}}, "totalBorrowsCurrent()": {"returns": {"_0": "The total amount of borrows"}}, "transfer(address,uint256)": {"params": {"amount": "The number of tokens to transfer", "dst": "The address of the recipient"}, "returns": {"_0": "Whether the transfer was successful or not"}}, "transferFrom(address,address,uint256)": {"params": {"amount": "The number of tokens to transfer", "dst": "The address to which tokens are transferred", "src": "The address from which tokens are transferred"}, "returns": {"_0": "Whether the transfer was successful or not"}}, "underlying()": {"details": "Returns the underlying address"}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"accrualBlockTimestamp()": {"notice": "Block timestamp that interest was last accrued at"}, "accrueInterest()": {"notice": "Accrues interest on the contract's outstanding loans"}, "admin()": {"notice": "Administrator for this contract"}, "allowance(address,address)": {"notice": "Returns the current allowance the `spender` has from the `owner`"}, "approve(address,uint256)": {"notice": "Approves `spender` to spend `amount` tokens on behalf of the caller"}, "balanceOf(address)": {"notice": "Returns the balance of tokens held by `owner`"}, "balanceOfUnderlying(address)": {"notice": "Returns the underlying asset balance of the `owner`"}, "borrowBalanceCurrent(address)": {"notice": "Returns the current borrow balance for `account`, accounting for interest"}, "borrowBalanceStored(address)": {"notice": "Returns the stored borrow balance for `account`, without accruing interest"}, "borrowIndex()": {"notice": "Accumulator of the total earned interest rate since the opening of the market"}, "borrowRateMaxMantissa()": {"notice": "Maximum borrow rate that can ever be applied"}, "borrowRatePerBlock()": {"notice": "Returns the current borrow rate per block"}, "decimals()": {"notice": "EIP-20 token decimals for this token"}, "exchangeRateCurrent()": {"notice": "Returns the current exchange rate, with interest accrued"}, "exchangeRateStored()": {"notice": "Returns the stored exchange rate, without accruing interest"}, "getAccountSnapshot(address)": {"notice": "Returns the snapshot of account details for the given `account`"}, "getCash()": {"notice": "Returns the total amount of available cash in the contract"}, "interestRateModel()": {"notice": "Model which tells what the current interest rate should be"}, "name()": {"notice": "EIP-20 token name for this token"}, "operator()": {"notice": "Contract which oversees inter-mToken operations"}, "pendingAdmin()": {"notice": "Pending administrator for this contract"}, "reduceReserves(uint256)": {"notice": "Accrues interest and reduces reserves by transferring to admin"}, "reserveFactorMantissa()": {"notice": "Fraction of interest currently set aside for reserves"}, "rolesOperator()": {"notice": "Roles manager"}, "seize(address,address,uint256)": {"notice": "Transfers collateral tokens (this market) to the liquidator."}, "supplyRatePerBlock()": {"notice": "Returns the current supply rate per block"}, "symbol()": {"notice": "EIP-20 token symbol for this token"}, "totalBorrows()": {"notice": "Total amount of outstanding borrows of the underlying in this market"}, "totalBorrowsCurrent()": {"notice": "Returns the total amount of borrows, accounting for interest"}, "totalReserves()": {"notice": "Total amount of reserves of the underlying held in this market"}, "totalSupply()": {"notice": "Returns the value of tokens in existence."}, "totalUnderlying()": {"notice": "Returns the amount of underlying tokens"}, "transfer(address,uint256)": {"notice": "Transfers `amount` tokens to the `dst` address"}, "transferFrom(address,address,uint256)": {"notice": "Transfers `amount` tokens from the `src` address to the `dst` address"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/mToken/mTokenStorage.sol": "mTokenStorage"}, "evmVersion": "london", "libraries": {}}, "sources": {"src/interfaces/IInterestRateModel.sol": {"keccak256": "0xdc8386be37a06d93fb0967be32e465f3b1f941ec9ce2f90984a5c70488cddb1e", "urls": ["bzz-raw://de2bf96a7d0945f58b00e9fc867ac00fc4a09480e9e27c1a800604295320b769", "dweb:/ipfs/Qmf5sAgA58GjVxeipJNhP4euxwsZ9FS9CgsWTJ319UmCUE"], "license": "BSL-1.1"}, "src/interfaces/IRoles.sol": {"keccak256": "0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365", "urls": ["bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c", "dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj"], "license": "BSL-1.1"}, "src/interfaces/ImToken.sol": {"keccak256": "0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f", "urls": ["bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36", "dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL"], "license": "BSL-1.1"}, "src/mToken/mTokenStorage.sol": {"keccak256": "0x42236cd9e37c2eef2d317f05510bbe21cf5174813ee40f9a6fcbaf84d4fed2f7", "urls": ["bzz-raw://2679166e1ca0e5d21294447566bba7549900a8f9a9b1882c8c2895a9751a1792", "dweb:/ipfs/Qmb49FVvre1He18e6ABHjNJ2yzCPdkYeyzSwMXixZ7fUw3"], "license": "BSL-1.1"}, "src/utils/ExponentialNoError.sol": {"keccak256": "0xa97178117d8c32059d55dd2224e5514c54b31f896e565c3834cab3d7502b1bb6", "urls": ["bzz-raw://1cecc0713709f57a5bb7f64d2fa06b9437a42752fa3fed5d3c0b19d82aa56ef4", "dweb:/ipfs/QmQqBCZkzmZxomXLr3b1tHUV55n8EPk3wAUsXgojYSpR41"], "license": "BSL-1.1"}}, "version": 1}, "id": 180}