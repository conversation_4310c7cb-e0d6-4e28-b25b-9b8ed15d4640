{"abi": [{"type": "function", "name": "setStatus", "inputs": [{"name": "_failure", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "shouldRevert", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "verify", "inputs": [{"name": "", "type": "bytes", "internalType": "bytes"}, {"name": "", "type": "bytes32", "internalType": "bytes32"}, {"name": "", "type": "bytes32", "internalType": "bytes32"}], "outputs": [], "stateMutability": "view"}, {"type": "function", "name": "verifyIntegrity", "inputs": [{"name": "", "type": "tuple", "internalType": "struct Risc0VerifierMock.Receipt", "components": [{"name": "seal", "type": "bytes", "internalType": "bytes"}, {"name": "claimDigest", "type": "bytes32", "internalType": "bytes32"}]}], "outputs": [], "stateMutability": "view"}], "bytecode": {"object": "0x6080604052348015600f57600080fd5b5061025e8061001f6000396000f3fe608060405234801561001057600080fd5b506004361061004c5760003560e01c80631599ead5146100515780635c40f6f414610066578063ab750e7514610087578063d3072d821461009a575b600080fd5b61006461005f366004610143565b6100bb565b005b610064610074366004610185565b6000805460ff1916911515919091179055565b6100646100953660046101a7565b610100565b6000546100a79060ff1681565b604051901515815260200160405180910390f35b60005460ff16156100fd5760405162461bcd60e51b81526020600482015260076024820152664661696c75726560c81b60448201526064015b60405180910390fd5b50565b60005460ff161561013d5760405162461bcd60e51b81526020600482015260076024820152664661696c75726560c81b60448201526064016100f4565b50505050565b60006020828403121561015557600080fd5b813567ffffffffffffffff81111561016c57600080fd5b82016040818503121561017e57600080fd5b9392505050565b60006020828403121561019757600080fd5b8135801515811461017e57600080fd5b600080600080606085870312156101bd57600080fd5b843567ffffffffffffffff8111156101d457600080fd5b8501601f810187136101e557600080fd5b803567ffffffffffffffff8111156101fc57600080fd5b87602082840101111561020e57600080fd5b60209182019890975090860135956040013594509250505056fea26469706673582212202c024315c830463f9c9e7bcd0b957f83d54c65aac5007358e7231a8492ea4a7164736f6c634300081c0033", "sourceMap": "121:463:208:-:0;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x608060405234801561001057600080fd5b506004361061004c5760003560e01c80631599ead5146100515780635c40f6f414610066578063ab750e7514610087578063d3072d821461009a575b600080fd5b61006461005f366004610143565b6100bb565b005b610064610074366004610185565b6000805460ff1916911515919091179055565b6100646100953660046101a7565b610100565b6000546100a79060ff1681565b604051901515815260200160405180910390f35b60005460ff16156100fd5760405162461bcd60e51b81526020600482015260076024820152664661696c75726560c81b60448201526064015b60405180910390fd5b50565b60005460ff161561013d5760405162461bcd60e51b81526020600482015260076024820152664661696c75726560c81b60448201526064016100f4565b50505050565b60006020828403121561015557600080fd5b813567ffffffffffffffff81111561016c57600080fd5b82016040818503121561017e57600080fd5b9392505050565b60006020828403121561019757600080fd5b8135801515811461017e57600080fd5b600080600080606085870312156101bd57600080fd5b843567ffffffffffffffff8111156101d457600080fd5b8501601f810187136101e557600080fd5b803567ffffffffffffffff8111156101fc57600080fd5b87602082840101111561020e57600080fd5b60209182019890975090860135956040013594509250505056fea26469706673582212202c024315c830463f9c9e7bcd0b957f83d54c65aac5007358e7231a8492ea4a7164736f6c634300081c0033", "sourceMap": "121:463:208:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;473:109;;;;;;:::i;:::-;;:::i;:::-;;262:83;;;;;;:::i;:::-;315:12;:23;;-1:-1:-1;;315:23:208;;;;;;;;;;262:83;351:116;;;;;;:::i;:::-;;:::i;231:24::-;;;;;;;;;;;;1680:14:242;;1673:22;1655:41;;1643:2;1628:18;231:24:208;;;;;;;473:109;544:12;;;;540:35;;;558:17;;-1:-1:-1;;;558:17:208;;1909:2:242;558:17:208;;;1891:21:242;1948:1;1928:18;;;1921:29;-1:-1:-1;;;1966:18:242;;;1959:37;2013:18;;558:17:208;;;;;;;;540:35;473:109;:::o;351:116::-;429:12;;;;425:35;;;443:17;;-1:-1:-1;;;443:17:208;;1909:2:242;443:17:208;;;1891:21:242;1948:1;1928:18;;;1921:29;-1:-1:-1;;;1966:18:242;;;1959:37;2013:18;;443:17:208;1707:330:242;425:35:208;351:116;;;;:::o;14:387:242:-;101:6;154:2;142:9;133:7;129:23;125:32;122:52;;;170:1;167;160:12;122:52;210:9;197:23;243:18;235:6;232:30;229:50;;;275:1;272;265:12;229:50;298:22;;354:2;336:16;;;332:25;329:45;;;370:1;367;360:12;329:45;393:2;14:387;-1:-1:-1;;;14:387:242:o;406:273::-;462:6;515:2;503:9;494:7;490:23;486:32;483:52;;;531:1;528;521:12;483:52;570:9;557:23;623:5;616:13;609:21;602:5;599:32;589:60;;645:1;642;635:12;684:826;772:6;780;788;796;849:2;837:9;828:7;824:23;820:32;817:52;;;865:1;862;855:12;817:52;905:9;892:23;938:18;930:6;927:30;924:50;;;970:1;967;960:12;924:50;993:22;;1046:4;1038:13;;1034:27;-1:-1:-1;1024:55:242;;1075:1;1072;1065:12;1024:55;1115:2;1102:16;1141:18;1133:6;1130:30;1127:50;;;1173:1;1170;1163:12;1127:50;1220:7;1213:4;1204:6;1200:2;1196:15;1192:26;1189:39;1186:59;;;1241:1;1238;1231:12;1186:59;1272:4;1264:13;;;;1296:6;;-1:-1:-1;1356:20:242;;;1343:34;;1474:2;1459:18;1446:32;;-1:-1:-1;684:826:242;-1:-1:-1;;;684:826:242:o", "linkReferences": {}}, "methodIdentifiers": {"setStatus(bool)": "5c40f6f4", "shouldRevert()": "d3072d82", "verify(bytes,bytes32,bytes32)": "ab750e75", "verifyIntegrity((bytes,bytes32))": "1599ead5"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"bool\",\"name\":\"_failure\",\"type\":\"bool\"}],\"name\":\"setStatus\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"shouldRevert\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"},{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"name\":\"verify\",\"outputs\":[],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"bytes\",\"name\":\"seal\",\"type\":\"bytes\"},{\"internalType\":\"bytes32\",\"name\":\"claimDigest\",\"type\":\"bytes32\"}],\"internalType\":\"struct Risc0VerifierMock.Receipt\",\"name\":\"\",\"type\":\"tuple\"}],\"name\":\"verifyIntegrity\",\"outputs\":[],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"test/mocks/Risc0VerifierMock.sol\":\"Risc0VerifierMock\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"lib/risc0-ethereum/contracts/src/steel/Steel.sol\":{\"keccak256\":\"0x20811a302f2bc3e36fb612026319c83cf79c16aa56b822d40bcbe8173d1e9544\",\"license\":\"Apache-2.0\",\"urls\":[\"bzz-raw://ed9f7a15c3be0bdc79217feae9e773d280fcfb44aa450d37c6c891f0b505210f\",\"dweb:/ipfs/QmXHtzvLT2AHHHojJ42epUSBF4HYTaVohSmyZft6KZoVqE\"]},\"test/mocks/Risc0VerifierMock.sol\":{\"keccak256\":\"0x3f6f728dcdcffec55a6c869fdf9e69a0463cccd07ed7784e5ab3dbee0058df85\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://f024f438e89b4698292d0b6a61485594f4ed737af80c25fa36bf862211f16732\",\"dweb:/ipfs/QmZXYmyVyHVHAyZ5PENfhoRphASQGb5WrtsdQN7XyMbGEm\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "bool", "name": "_failure", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "setStatus"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "shouldRevert", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes"}, {"internalType": "bytes32", "name": "", "type": "bytes32"}, {"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "verify"}, {"inputs": [{"internalType": "struct Risc0VerifierMock.Receipt", "name": "", "type": "tuple", "components": [{"internalType": "bytes", "name": "seal", "type": "bytes"}, {"internalType": "bytes32", "name": "claimDigest", "type": "bytes32"}]}], "stateMutability": "view", "type": "function", "name": "verifyIntegrity"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"test/mocks/Risc0VerifierMock.sol": "Risc0VerifierMock"}, "evmVersion": "london", "libraries": {}}, "sources": {"lib/risc0-ethereum/contracts/src/steel/Steel.sol": {"keccak256": "0x20811a302f2bc3e36fb612026319c83cf79c16aa56b822d40bcbe8173d1e9544", "urls": ["bzz-raw://ed9f7a15c3be0bdc79217feae9e773d280fcfb44aa450d37c6c891f0b505210f", "dweb:/ipfs/QmXHtzvLT2AHHHojJ42epUSBF4HYTaVohSmyZft6KZoVqE"], "license": "Apache-2.0"}, "test/mocks/Risc0VerifierMock.sol": {"keccak256": "0x3f6f728dcdcffec55a6c869fdf9e69a0463cccd07ed7784e5ab3dbee0058df85", "urls": ["bzz-raw://f024f438e89b4698292d0b6a61485594f4ed737af80c25fa36bf862211f16732", "dweb:/ipfs/QmZXYmyVyHVHAyZ5PENfhoRphASQGb5WrtsdQN7XyMbGEm"], "license": "BSL-1.1"}}, "version": 1}, "id": 208}