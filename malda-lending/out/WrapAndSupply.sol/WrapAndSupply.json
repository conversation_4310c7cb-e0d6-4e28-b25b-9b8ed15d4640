{"abi": [{"type": "constructor", "inputs": [{"name": "_wrappedNative", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "wrapAndSupplyOnExtensionMarket", "inputs": [{"name": "mTokenGateway", "type": "address", "internalType": "address"}, {"name": "receiver", "type": "address", "internalType": "address"}, {"name": "selector", "type": "bytes4", "internalType": "bytes4"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "wrapAndSupplyOnHostMarket", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}, {"name": "receiver", "type": "address", "internalType": "address"}, {"name": "minAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "wrappedNative", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IWrappedNative"}], "stateMutability": "view"}, {"type": "event", "name": "WrappedAndSupplied", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "receiver", "type": "address", "indexed": true, "internalType": "address"}, {"name": "market", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "error", "name": "WrapAndSupply_AddressNotValid", "inputs": []}, {"type": "error", "name": "WrapAndSupply_AmountNotValid", "inputs": []}], "bytecode": {"object": "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", "sourceMap": "1265:2367:197:-:0;;;1631:179;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;1685:28:197;;1677:70;;;;-1:-1:-1;;;1677:70:197;;;;;;;;;;;;-1:-1:-1;;;;;1757:46:197;;;1265:2367;;14:290:242;84:6;137:2;125:9;116:7;112:23;108:32;105:52;;;153:1;150;143:12;105:52;179:16;;-1:-1:-1;;;;;224:31:242;;214:42;;204:70;;270:1;267;260:12;204:70;293:5;14:290;-1:-1:-1;;;14:290:242:o;:::-;1265:2367:197;;;;;;;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "1265:2367:197:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2833:532;;;;;;:::i;:::-;;:::i;:::-;;2044;;;;;;:::i;:::-;;:::i;1294:45::-;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;1374:32:242;;;1356:51;;1344:2;1329:18;1294:45:197;;;;;;;2833:532;2978:18;3014:13;-1:-1:-1;;;;;2999:40:197;;:42;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2978:63;;3081:13;-1:-1:-1;;;;;3059:36:197;:10;-1:-1:-1;;;;;3059:36:197;;3051:78;;;;-1:-1:-1;;;3051:78:197;;;;;;;;;;;;3140:14;3157:7;:5;:7::i;:::-;3175:44;;-1:-1:-1;;;3175:44:197;;-1:-1:-1;;;;;1874:32:242;;;3175:44:197;;;1856:51:242;3217:1:197;1923:18:242;;;1916:34;3140:24:197;;-1:-1:-1;3175:26:197;;;;;;1829:18:242;;3175:44:197;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;3229:49:197;;-1:-1:-1;;;3229:49:197;;-1:-1:-1;;;;;1874:32:242;;;3229:49:197;;;1856:51:242;1923:18;;;1916:34;;;3229:26:197;;;;;1829:18:242;;3229:49:197;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;3288:70:197;;-1:-1:-1;;;3288:70:197;;;;;2722:25:242;;;-1:-1:-1;;;;;2783:32:242;;;2763:18;;;2756:60;-1:-1:-1;;;;;;2852:33:242;;2832:18;;;2825:61;3288:42:197;;;;;2695:18:242;;3288:70:197;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2968:397;;2833:532;;;:::o;2044:::-;2159:18;2195:6;-1:-1:-1;;;;;2180:33:197;;:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2159:56;;2255:13;-1:-1:-1;;;;;2233:36:197;:10;-1:-1:-1;;;;;2233:36:197;;2225:78;;;;-1:-1:-1;;;2225:78:197;;;;;;;;;;;;2314:14;2331:7;:5;:7::i;:::-;2349:37;;-1:-1:-1;;;2349:37:197;;-1:-1:-1;;;;;1874:32:242;;;2349:37:197;;;1856:51:242;2384:1:197;1923:18:242;;;1916:34;2314:24:197;;-1:-1:-1;2349:26:197;;;;;;1829:18:242;;2349:37:197;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;2396:42:197;;-1:-1:-1;;;2396:42:197;;-1:-1:-1;;;;;1874:32:242;;;2396:42:197;;;1856:51:242;1923:18;;;1916:34;;;2396:26:197;;;;;1829:18:242;;2396:42:197;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;2448:49:197;;-1:-1:-1;;;2448:49:197;;;;;3099:25:242;;;-1:-1:-1;;;;;3160:32:242;;;3140:18;;;3133:60;3209:18;;;3202:34;;;2448:20:197;;;;;3072:18:242;;2448:49:197;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2554:6;-1:-1:-1;;;;;2513:56:197;2544:8;-1:-1:-1;;;;;2513:56:197;2532:10;-1:-1:-1;;;;;2513:56:197;;2562:6;2513:56;;;;3393:25:242;;3381:2;3366:18;;3247:177;2513:56:197;;;;;;;;2149:427;;2044:532;;;:::o;3411:219::-;3445:7;3481:9;3508:10;3500:51;;;;-1:-1:-1;;;3500:51:197;;;;;;;;;;;;3562:13;-1:-1:-1;;;;;3562:21:197;;3591:6;3562:38;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;3617:6:197;;3411:219;-1:-1:-1;;;;;3411:219:197:o;14:131:242:-;-1:-1:-1;;;;;89:31:242;;79:42;;69:70;;135:1;132;125:12;69:70;14:131;:::o;150:570::-;226:6;234;242;295:2;283:9;274:7;270:23;266:32;263:52;;;311:1;308;301:12;263:52;350:9;337:23;369:31;394:5;369:31;:::i;:::-;419:5;-1:-1:-1;476:2:242;461:18;;448:32;489:33;448:32;489:33;:::i;:::-;541:7;-1:-1:-1;600:2:242;585:18;;572:32;-1:-1:-1;;;;;;635:34:242;;623:47;;613:75;;684:1;681;674:12;613:75;707:7;697:17;;;150:570;;;;;:::o;725:456::-;802:6;810;818;871:2;859:9;850:7;846:23;842:32;839:52;;;887:1;884;877:12;839:52;926:9;913:23;945:31;970:5;945:31;:::i;:::-;995:5;-1:-1:-1;1052:2:242;1037:18;;1024:32;1065:33;1024:32;1065:33;:::i;:::-;725:456;;1117:7;;-1:-1:-1;;;1171:2:242;1156:18;;;;1143:32;;725:456::o;1418:251::-;1488:6;1541:2;1529:9;1520:7;1516:23;1512:32;1509:52;;;1557:1;1554;1547:12;1509:52;1589:9;1583:16;1608:31;1633:5;1608:31;:::i;:::-;1658:5;1418:251;-1:-1:-1;;;1418:251:242:o;1961:277::-;2028:6;2081:2;2069:9;2060:7;2056:23;2052:32;2049:52;;;2097:1;2094;2087:12;2049:52;2129:9;2123:16;2182:5;2175:13;2168:21;2161:5;2158:32;2148:60;;2204:1;2201;2194:12", "linkReferences": {}, "immutableReferences": {"89534": [{"start": 115, "length": 32}, {"start": 281, "length": 32}, {"start": 832, "length": 32}, {"start": 1392, "length": 32}]}}, "methodIdentifiers": {"wrapAndSupplyOnExtensionMarket(address,address,bytes4)": "a8372833", "wrapAndSupplyOnHostMarket(address,address,uint256)": "ea25f619", "wrappedNative()": "eb6d3a11"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_wrappedNative\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"WrapAndSupply_AddressNotValid\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"WrapAndSupply_AmountNotValid\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"market\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"WrappedAndSupplied\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mTokenGateway\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"internalType\":\"bytes4\",\"name\":\"selector\",\"type\":\"bytes4\"}],\"name\":\"wrapAndSupplyOnExtensionMarket\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"minAmount\",\"type\":\"uint256\"}],\"name\":\"wrapAndSupplyOnHostMarket\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"wrappedNative\",\"outputs\":[{\"internalType\":\"contract IWrappedNative\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{\"wrapAndSupplyOnExtensionMarket(address,address,bytes4)\":{\"params\":{\"mTokenGateway\":\"The market address\",\"receiver\":\"The receiver\",\"selector\":\"The host chain function selector\"}},\"wrapAndSupplyOnHostMarket(address,address,uint256)\":{\"params\":{\"mToken\":\"The market address\",\"receiver\":\"The mToken receiver\"}}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"wrapAndSupplyOnExtensionMarket(address,address,bytes4)\":{\"notice\":\"Wraps a native coin into its wrapped version and supplies on an extension market\"},\"wrapAndSupplyOnHostMarket(address,address,uint256)\":{\"notice\":\"Wraps a native coin into its wrapped version and supplies on a host market\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/utils/WrapAndSupply.sol\":\"WrapAndSupply\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xc6a8ff0ea489379b61faa647490411b80102578440ab9d84e9a957cc12164e70\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0ea104e577e63faea3b69c415637e99e755dcbf64c5833d7140c35a714d6d90c\",\"dweb:/ipfs/Qmau6x4Ns9XdyynRCNNp3RhLqijJjFm7z5fyZazfYFGYdq\"]},\"src/interfaces/IBlacklister.sol\":{\"keccak256\":\"0x47ed20135507a9d16473027cbee18de5d952846bada761c6248b91c75cd88be1\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://91a886b95dd578156f9b6cec3236e4751aae8a38e5d15e0d6045217c6fbd3d63\",\"dweb:/ipfs/QmRNjz7sGTuMkQNZhwwcg5DNN2eeqTZSAdZmxWxbx66CHU\"]},\"src/interfaces/IRoles.sol\":{\"keccak256\":\"0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c\",\"dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj\"]},\"src/interfaces/ImErc20.sol\":{\"keccak256\":\"0x86a1dcaf0d188107ceae7c5eb0911ac63feafbbeda6d0686e4f9950a51d17888\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://d7dd8e0fcfd68501d9f40e37e1c95d8cb653d65626aeeeb5492b73c0e967e6cb\",\"dweb:/ipfs/QmcadG9QuHtAX5qxTKzNr6FMaXSgijr542tFUVTLBkYhAF\"]},\"src/interfaces/ImToken.sol\":{\"keccak256\":\"0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36\",\"dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL\"]},\"src/interfaces/ImTokenGateway.sol\":{\"keccak256\":\"0x7b47f5229cd07de60af49eb473eddc27e429abd846df81001ef05af2c3d47634\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://04d8c52462eeb19a4c28980ac5c81a901fd35982dc47c0ab95a4ee6466b150c1\",\"dweb:/ipfs/QmfTyZrLWMnWLU6zo85RVwjv5W2qXVQPkLWzru5zcBarYm\"]},\"src/utils/WrapAndSupply.sol\":{\"keccak256\":\"0x48cfb003c2c2247b87c753bc11f377f1eb4141dc3a35c694742596de349badd6\",\"license\":\"AGPL-3.0\",\"urls\":[\"bzz-raw://6c0cd2fa3eda8acb21c91d97ff0e3c71384ac6d19ad7d41cde161e15985ff13f\",\"dweb:/ipfs/QmPe2VXx7rmitV42gBJrqvoXLZdVV2ePWcx7XDP5qcPC7v\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "_wrappedNative", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "type": "error", "name": "WrapAndSupply_AddressNotValid"}, {"inputs": [], "type": "error", "name": "WrapAndSupply_AmountNotValid"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address", "indexed": true}, {"internalType": "address", "name": "receiver", "type": "address", "indexed": true}, {"internalType": "address", "name": "market", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}], "type": "event", "name": "WrappedAndSupplied", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "mTokenGateway", "type": "address"}, {"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "bytes4", "name": "selector", "type": "bytes4"}], "stateMutability": "payable", "type": "function", "name": "wrapAndSupplyOnExtensionMarket"}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}, {"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "uint256", "name": "minAmount", "type": "uint256"}], "stateMutability": "payable", "type": "function", "name": "wrapAndSupplyOnHostMarket"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "wrappedNative", "outputs": [{"internalType": "contract IWrappedNative", "name": "", "type": "address"}]}], "devdoc": {"kind": "dev", "methods": {"wrapAndSupplyOnExtensionMarket(address,address,bytes4)": {"params": {"mTokenGateway": "The market address", "receiver": "The receiver", "selector": "The host chain function selector"}}, "wrapAndSupplyOnHostMarket(address,address,uint256)": {"params": {"mToken": "The market address", "receiver": "The mToken receiver"}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"wrapAndSupplyOnExtensionMarket(address,address,bytes4)": {"notice": "Wraps a native coin into its wrapped version and supplies on an extension market"}, "wrapAndSupplyOnHostMarket(address,address,uint256)": {"notice": "Wraps a native coin into its wrapped version and supplies on a host market"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/utils/WrapAndSupply.sol": "WrapAndSupply"}, "evmVersion": "london", "libraries": {}}, "sources": {"lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0xc6a8ff0ea489379b61faa647490411b80102578440ab9d84e9a957cc12164e70", "urls": ["bzz-raw://0ea104e577e63faea3b69c415637e99e755dcbf64c5833d7140c35a714d6d90c", "dweb:/ipfs/Qmau6x4Ns9XdyynRCNNp3RhLqijJjFm7z5fyZazfYFGYdq"], "license": "MIT"}, "src/interfaces/IBlacklister.sol": {"keccak256": "0x47ed20135507a9d16473027cbee18de5d952846bada761c6248b91c75cd88be1", "urls": ["bzz-raw://91a886b95dd578156f9b6cec3236e4751aae8a38e5d15e0d6045217c6fbd3d63", "dweb:/ipfs/QmRNjz7sGTuMkQNZhwwcg5DNN2eeqTZSAdZmxWxbx66CHU"], "license": "BSL-1.1"}, "src/interfaces/IRoles.sol": {"keccak256": "0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365", "urls": ["bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c", "dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj"], "license": "BSL-1.1"}, "src/interfaces/ImErc20.sol": {"keccak256": "0x86a1dcaf0d188107ceae7c5eb0911ac63feafbbeda6d0686e4f9950a51d17888", "urls": ["bzz-raw://d7dd8e0fcfd68501d9f40e37e1c95d8cb653d65626aeeeb5492b73c0e967e6cb", "dweb:/ipfs/QmcadG9QuHtAX5qxTKzNr6FMaXSgijr542tFUVTLBkYhAF"], "license": "BSL-1.1"}, "src/interfaces/ImToken.sol": {"keccak256": "0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f", "urls": ["bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36", "dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL"], "license": "BSL-1.1"}, "src/interfaces/ImTokenGateway.sol": {"keccak256": "0x7b47f5229cd07de60af49eb473eddc27e429abd846df81001ef05af2c3d47634", "urls": ["bzz-raw://04d8c52462eeb19a4c28980ac5c81a901fd35982dc47c0ab95a4ee6466b150c1", "dweb:/ipfs/QmfTyZrLWMnWLU6zo85RVwjv5W2qXVQPkLWzru5zcBarYm"], "license": "BSL-1.1"}, "src/utils/WrapAndSupply.sol": {"keccak256": "0x48cfb003c2c2247b87c753bc11f377f1eb4141dc3a35c694742596de349badd6", "urls": ["bzz-raw://6c0cd2fa3eda8acb21c91d97ff0e3c71384ac6d19ad7d41cde161e15985ff13f", "dweb:/ipfs/QmPe2VXx7rmitV42gBJrqvoXLZdVV2ePWcx7XDP5qcPC7v"], "license": "AGPL-3.0"}}, "version": 1}, "id": 197}