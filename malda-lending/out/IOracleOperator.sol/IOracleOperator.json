{"abi": [{"type": "function", "name": "getPrice", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getUnderlyingPrice", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"getPrice(address)": "41976e09", "getUnderlyingPrice(address)": "fc57d4df"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"}],\"name\":\"getPrice\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"}],\"name\":\"getUnderlyingPrice\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{\"getPrice(address)\":{\"params\":{\"mToken\":\"The mToken to get the price of\"},\"returns\":{\"_0\":\"The underlying asset price mantissa (scaled by 1e18).  Zero means the price is unavailable.\"}},\"getUnderlyingPrice(address)\":{\"params\":{\"mToken\":\"The mToken to get the underlying price of\"},\"returns\":{\"_0\":\"The underlying asset price mantissa (scaled by 1e18).  Zero means the price is unavailable.\"}}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"getPrice(address)\":{\"notice\":\"Get the price of a mToken asset\"},\"getUnderlyingPrice(address)\":{\"notice\":\"Get the underlying price of a mToken asset\"}},\"notice\":\"Prices are returned in USD\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/IOracleOperator.sol\":\"IOracleOperator\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"src/interfaces/IOracleOperator.sol\":{\"keccak256\":\"0xcc2848c75dd5e1531c2b958746cdd76a3b2b329be98fdca4311219a12a6b409e\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://337bdabf03ac279d95ba96aca60105932c7af5c15fdb4b21ab1c369509310fbc\",\"dweb:/ipfs/QmNYrNYNUwWWSrPwHdarhxt1ShdH1dy8B94M49TZ7XvmK1\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getPrice", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getUnderlyingPrice", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}], "devdoc": {"kind": "dev", "methods": {"getPrice(address)": {"params": {"mToken": "The mToken to get the price of"}, "returns": {"_0": "The underlying asset price mantissa (scaled by 1e18).  Zero means the price is unavailable."}}, "getUnderlyingPrice(address)": {"params": {"mToken": "The mToken to get the underlying price of"}, "returns": {"_0": "The underlying asset price mantissa (scaled by 1e18).  Zero means the price is unavailable."}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"getPrice(address)": {"notice": "Get the price of a mToken asset"}, "getUnderlyingPrice(address)": {"notice": "Get the underlying price of a mToken asset"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/interfaces/IOracleOperator.sol": "IOracleOperator"}, "evmVersion": "london", "libraries": {}}, "sources": {"src/interfaces/IOracleOperator.sol": {"keccak256": "0xcc2848c75dd5e1531c2b958746cdd76a3b2b329be98fdca4311219a12a6b409e", "urls": ["bzz-raw://337bdabf03ac279d95ba96aca60105932c7af5c15fdb4b21ab1c369509310fbc", "dweb:/ipfs/QmNYrNYNUwWWSrPwHdarhxt1ShdH1dy8B94M49TZ7XvmK1"], "license": "BSL-1.1"}}, "version": 1}, "id": 138}