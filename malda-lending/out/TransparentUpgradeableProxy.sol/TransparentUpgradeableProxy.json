{"abi": [{"type": "constructor", "inputs": [{"name": "_logic", "type": "address", "internalType": "address"}, {"name": "initialOwner", "type": "address", "internalType": "address"}, {"name": "_data", "type": "bytes", "internalType": "bytes"}], "stateMutability": "payable"}, {"type": "fallback", "stateMutability": "payable"}, {"type": "event", "name": "Admin<PERSON><PERSON>ed", "inputs": [{"name": "previousAdmin", "type": "address", "indexed": false, "internalType": "address"}, {"name": "newAdmin", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Upgraded", "inputs": [{"name": "implementation", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "AddressEmptyCode", "inputs": [{"name": "target", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC1967InvalidAdmin", "inputs": [{"name": "admin", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC1967InvalidImplementation", "inputs": [{"name": "implementation", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC1967Non<PERSON>ayable", "inputs": []}, {"type": "error", "name": "FailedInnerCall", "inputs": []}, {"type": "error", "name": "ProxyDeniedAdminAccess", "inputs": []}], "bytecode": {"object": "0x60a0604052604051610e84380380610e848339810160408190526100229161039d565b828161002e828261008f565b50508160405161003d9061033a565b6001600160a01b039091168152602001604051809103906000f080158015610069573d6000803e3d6000fd5b506001600160a01b031660805261008761008260805190565b6100ee565b50505061048f565b6100988261015c565b6040516001600160a01b038316907fbc7cd75a20ee27fd9adebab32041f755214dbc6bffa90cc0225b39da2e5c2d3b90600090a28051156100e2576100dd82826101db565b505050565b6100ea610252565b5050565b7f7e644d79422f17c01e4894b5f4f588d331ebfa28653d42ae832dc59e38c9798f61012e600080516020610e64833981519152546001600160a01b031690565b604080516001600160a01b03928316815291841660208301520160405180910390a161015981610273565b50565b806001600160a01b03163b60000361019757604051634c9c8ce360e01b81526001600160a01b03821660048201526024015b60405180910390fd5b807f360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc5b80546001600160a01b0319166001600160a01b039290921691909117905550565b6060600080846001600160a01b0316846040516101f89190610473565b600060405180830381855af49150503d8060008114610233576040519150601f19603f3d011682016040523d82523d6000602084013e610238565b606091505b5090925090506102498583836102b2565b95945050505050565b34156102715760405163b398979f60e01b815260040160405180910390fd5b565b6001600160a01b03811661029d57604051633173bdd160e11b81526000600482015260240161018e565b80600080516020610e648339815191526101ba565b6060826102c7576102c282610311565b61030a565b81511580156102de57506001600160a01b0384163b155b1561030757604051639996b31560e01b81526001600160a01b038516600482015260240161018e565b50805b9392505050565b8051156103215780518082602001fd5b604051630a12f52160e11b815260040160405180910390fd5b61052c8061093883390190565b80516001600160a01b038116811461035e57600080fd5b919050565b634e487b7160e01b600052604160045260246000fd5b60005b8381101561039457818101518382015260200161037c565b50506000910152565b6000806000606084860312156103b257600080fd5b6103bb84610347565b92506103c960208501610347565b60408501519092506001600160401b038111156103e557600080fd5b8401601f810186136103f657600080fd5b80516001600160401b0381111561040f5761040f610363565b604051601f8201601f19908116603f011681016001600160401b038111828210171561043d5761043d610363565b60405281815282820160200188101561045557600080fd5b610466826020830160208601610379565b8093505050509250925092565b60008251610485818460208701610379565b9190910192915050565b60805161048f6104a960003960006010015261048f6000f3fe608060405261000c61000e565b005b7f00000000000000000000000000000000000000000000000000000000000000006001600160a01b0316330361007b576000356001600160e01b03191663278f794360e11b14610071576040516334ad5dbb60e21b815260040160405180910390fd5b610079610083565b565b6100796100b2565b6000806100933660048184610312565b8101906100a09190610352565b915091506100ae82826100c2565b5050565b6100796100bd61011d565b610155565b6100cb82610179565b6040516001600160a01b038316907fbc7cd75a20ee27fd9adebab32041f755214dbc6bffa90cc0225b39da2e5c2d3b90600090a28051156101155761011082826101f5565b505050565b6100ae61026b565b60006101507f360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc546001600160a01b031690565b905090565b3660008037600080366000845af43d6000803e808015610174573d6000f35b3d6000fd5b806001600160a01b03163b6000036101b457604051634c9c8ce360e01b81526001600160a01b03821660048201526024015b60405180910390fd5b7f360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc80546001600160a01b0319166001600160a01b0392909216919091179055565b6060600080846001600160a01b031684604051610212919061042a565b600060405180830381855af49150503d806000811461024d576040519150601f19603f3d011682016040523d82523d6000602084013e610252565b606091505b509150915061026285838361028a565b95945050505050565b34156100795760405163b398979f60e01b815260040160405180910390fd5b60608261029f5761029a826102e9565b6102e2565b81511580156102b657506001600160a01b0384163b155b156102df57604051639996b31560e01b81526001600160a01b03851660048201526024016101ab565b50805b9392505050565b8051156102f95780518082602001fd5b604051630a12f52160e11b815260040160405180910390fd5b6000808585111561032257600080fd5b8386111561032f57600080fd5b5050820193919092039150565b634e487b7160e01b600052604160045260246000fd5b6000806040838503121561036557600080fd5b82356001600160a01b038116811461037c57600080fd5b9150602083013567ffffffffffffffff81111561039857600080fd5b8301601f810185136103a957600080fd5b803567ffffffffffffffff8111156103c3576103c361033c565b604051601f8201601f19908116603f0116810167ffffffffffffffff811182821017156103f2576103f261033c565b60405281815282820160200187101561040a57600080fd5b816020840160208301376000602083830101528093505050509250929050565b6000825160005b8181101561044b5760208186018101518583015201610431565b50600092019182525091905056fea26469706673582212202d583cd6476836f5a6f5720901499ad7b79750860e35b8c2335b0195e9ae4a6564736f6c634300081c0033608060405234801561001057600080fd5b5060405161052c38038061052c83398101604081905261002f916100be565b806001600160a01b03811661005e57604051631e4fbdf760e01b81526000600482015260240160405180910390fd5b6100678161006e565b50506100ee565b600080546001600160a01b038381166001600160a01b0319831681178455604051919092169283917f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e09190a35050565b6000602082840312156100d057600080fd5b81516001600160a01b03811681146100e757600080fd5b9392505050565b61042f806100fd6000396000f3fe60806040526004361061004a5760003560e01c8063715018a61461004f5780638da5cb5b146100665780639623609d14610093578063ad3cb1cc146100a6578063f2fde38b146100e4575b600080fd5b34801561005b57600080fd5b50610064610104565b005b34801561007257600080fd5b506000546040516001600160a01b0390911681526020015b60405180910390f35b6100646100a1366004610272565b610118565b3480156100b257600080fd5b506100d7604051806040016040528060058152602001640352e302e360dc1b81525081565b60405161008a9190610396565b3480156100f057600080fd5b506100646100ff3660046103b0565b610187565b61010c6101ca565b61011660006101f7565b565b6101206101ca565b60405163278f794360e11b81526001600160a01b03841690634f1ef28690349061015090869086906004016103cd565b6000604051808303818588803b15801561016957600080fd5b505af115801561017d573d6000803e3d6000fd5b5050505050505050565b61018f6101ca565b6001600160a01b0381166101be57604051631e4fbdf760e01b8152600060048201526024015b60405180910390fd5b6101c7816101f7565b50565b6000546001600160a01b031633146101165760405163118cdaa760e01b81523360048201526024016101b5565b600080546001600160a01b038381166001600160a01b0319831681178455604051919092169283917f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e09190a35050565b6001600160a01b03811681146101c757600080fd5b634e487b7160e01b600052604160045260246000fd5b60008060006060848603121561028757600080fd5b833561029281610247565b925060208401356102a281610247565b9150604084013567ffffffffffffffff8111156102be57600080fd5b8401601f810186136102cf57600080fd5b803567ffffffffffffffff8111156102e9576102e961025c565b604051601f8201601f19908116603f0116810167ffffffffffffffff811182821017156103185761031861025c565b60405281815282820160200188101561033057600080fd5b816020840160208301376000602083830101528093505050509250925092565b6000815180845260005b818110156103765760208185018101518683018201520161035a565b506000602082860101526020601f19601f83011685010191505092915050565b6020815260006103a96020830184610350565b9392505050565b6000602082840312156103c257600080fd5b81356103a981610247565b6001600160a01b03831681526040602082018190526000906103f190830184610350565b94935050505056fea2646970667358221220c3ea6f4b74d4bcf7f7cc1deba4140fb44e59e602d9f0a6b5b98dde9f6d2f429464736f6c634300081c0033b53127684a568b3173ae13b9f8a6016e243e63b6e8ee1178d6a717850b5d6103", "sourceMap": "4145:2226:36:-:0;;;4988:296;;;;;;;;;;;;;;;;;;:::i;:::-;5079:6;5087:5;1154:52:31;5079:6:36;5087:5;1154:29:31;:52::i;:::-;1080:133;;5136:12:36::1;5121:28;;;;;:::i;:::-;-1:-1:-1::0;;;;;1837:32:242;;;1819:51;;1807:2;1792:18;5121:28:36::1;;;;;;;;;;;;;;;;::::0;::::1;;;;;-1:-1:-1::0;;;;;;5104:46:36::1;;::::0;5238:39:::1;5263:13;5426:6:::0;;;5351:88;5263:13:::1;5238:24;:39::i;:::-;4988:296:::0;;;4145:2226;;2779:335:32;2870:37;2889:17;2870:18;:37::i;:::-;2922:27;;-1:-1:-1;;;;;2922:27:32;;;;;;;;2964:11;;:15;2960:148;;2995:53;3024:17;3043:4;2995:28;:53::i;:::-;;2779:335;;:::o;2960:148::-;3079:18;:16;:18::i;:::-;2779:335;;:::o;4331:133::-;4394:34;4407:10;-1:-1:-1;;;;;;;;;;;3861:44:32;-1:-1:-1;;;;;3861:44:32;;3792:120;4407:10;4394:34;;;-1:-1:-1;;;;;2073:32:242;;;2055:51;;2142:32;;;2137:2;2122:18;;2115:60;2028:18;4394:34:32;;;;;;;4438:19;4448:8;4438:9;:19::i;:::-;4331:133;:::o;2186:281::-;2263:17;-1:-1:-1;;;;;2263:29:32;;2296:1;2263:34;2259:119;;2320:47;;-1:-1:-1;;;2320:47:32;;-1:-1:-1;;;;;1837:32:242;;2320:47:32;;;1819:51:242;1792:18;;2320:47:32;;;;;;;;2259:119;2443:17;1327:66;2387:47;:73;;-1:-1:-1;;;;;;2387:73:32;-1:-1:-1;;;;;2387:73:32;;;;;;;;;;-1:-1:-1;2186:281:32:o;4106:253:46:-;4189:12;4214;4228:23;4255:6;-1:-1:-1;;;;;4255:19:46;4275:4;4255:25;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;4213:67:46;;-1:-1:-1;4213:67:46;-1:-1:-1;4297:55:46;4324:6;4213:67;;4297:26;:55::i;:::-;4290:62;4106:253;-1:-1:-1;;;;;4106:253:46:o;6598:122:32:-;6648:9;:13;6644:70;;6684:19;;-1:-1:-1;;;6684:19:32;;;;;;;;;;;6644:70;6598:122::o;3994:217::-;-1:-1:-1;;;;;4053:22:32;;4049:91;;4098:31;;-1:-1:-1;;;4098:31:32;;4126:1;4098:31;;;1819:51:242;1792:18;;4098:31:32;1673:203:242;4049:91:32;4196:8;-1:-1:-1;;;;;;;;;;;4149:38:32;1684:190:49:o;4625:582:46:-;4769:12;4798:7;4793:408;;4821:19;4829:10;4821:7;:19::i;:::-;4793:408;;;5045:17;;:22;:49;;;;-1:-1:-1;;;;;;5071:18:46;;;:23;5045:49;5041:119;;;5121:24;;-1:-1:-1;;;5121:24:46;;-1:-1:-1;;;;;1837:32:242;;5121:24:46;;;1819:51:242;1792:18;;5121:24:46;1673:203:242;5041:119:46;-1:-1:-1;5180:10:46;4793:408;4625:582;;;;;:::o;5743:516::-;5874:17;;:21;5870:383;;6102:10;6096:17;6158:15;6145:10;6141:2;6137:19;6130:44;5870:383;6225:17;;-1:-1:-1;;;6225:17:46;;;;;;;;;;;4145:2226:36;;;;;;;;:::o;14:177:242:-;93:13;;-1:-1:-1;;;;;135:31:242;;125:42;;115:70;;181:1;178;171:12;115:70;14:177;;;:::o;196:127::-;257:10;252:3;248:20;245:1;238:31;288:4;285:1;278:15;312:4;309:1;302:15;328:250;413:1;423:113;437:6;434:1;431:13;423:113;;;513:11;;;507:18;494:11;;;487:39;459:2;452:10;423:113;;;-1:-1:-1;;570:1:242;552:16;;545:27;328:250::o;583:1085::-;680:6;688;696;749:2;737:9;728:7;724:23;720:32;717:52;;;765:1;762;755:12;717:52;788:40;818:9;788:40;:::i;:::-;778:50;;847:49;892:2;881:9;877:18;847:49;:::i;:::-;940:2;925:18;;919:25;837:59;;-1:-1:-1;;;;;;956:30:242;;953:50;;;999:1;996;989:12;953:50;1022:22;;1075:4;1067:13;;1063:27;-1:-1:-1;1053:55:242;;1104:1;1101;1094:12;1053:55;1131:9;;-1:-1:-1;;;;;1152:30:242;;1149:56;;;1185:18;;:::i;:::-;1234:2;1228:9;1326:2;1288:17;;-1:-1:-1;;1284:31:242;;;1317:2;1280:40;1276:54;1264:67;;-1:-1:-1;;;;;1346:34:242;;1382:22;;;1343:62;1340:88;;;1408:18;;:::i;:::-;1444:2;1437:22;1468;;;1509:15;;;1526:2;1505:24;1502:37;-1:-1:-1;1499:57:242;;;1552:1;1549;1542:12;1499:57;1565:72;1630:6;1625:2;1617:6;1613:15;1608:2;1604;1600:11;1565:72;:::i;:::-;1656:6;1646:16;;;;;583:1085;;;;;:::o;2186:287::-;2315:3;2353:6;2347:13;2369:66;2428:6;2423:3;2416:4;2408:6;2404:17;2369:66;:::i;:::-;2451:16;;;;;2186:287;-1:-1:-1;;2186:287:242:o;:::-;4145:2226:36;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "4145:2226:36:-:0;;;2649:11:33;:9;:11::i;:::-;4145:2226:36;5581:369;5426:6;-1:-1:-1;;;;;5642:27:36;:10;:27;5638:306;;5689:7;;-1:-1:-1;;;;;;5689:7:36;-1:-1:-1;;;5689:65:36;5685:201;;5781:24;;-1:-1:-1;;;5781:24:36;;;;;;;;;;;5685:201;5844:27;:25;:27::i;:::-;5581:369::o;5638:306::-;5916:17;:15;:17::i;6152:217::-;6208:25;;6267:12;:8;6276:1;6267:8;6208:25;6267:12;:::i;:::-;6256:42;;;;;;;:::i;:::-;6207:91;;;;6308:54;6338:17;6357:4;6308:29;:54::i;:::-;6197:172;;6152:217::o;2323:83:33:-;2371:28;2381:17;:15;:17::i;:::-;2371:9;:28::i;2779:335:32:-;2870:37;2889:17;2870:18;:37::i;:::-;2922:27;;-1:-1:-1;;;;;2922:27:32;;;;;;;;2964:11;;:15;2960:148;;2995:53;3024:17;3043:4;2995:28;:53::i;:::-;;6197:172:36;;6152:217::o;2960:148:32:-;3079:18;:16;:18::i;1581:132:31:-;1648:7;1674:32;1327:66:32;2035:53;-1:-1:-1;;;;;2035:53:32;;1957:138;1674:32:31;1667:39;;1581:132;:::o;949:895:33:-;1287:14;1284:1;1281;1268:34;1501:1;1498;1482:14;1479:1;1463:14;1456:5;1443:60;1577:16;1574:1;1571;1556:38;1615:6;1682:66;;;;1797:16;1794:1;1787:27;1682:66;1717:16;1714:1;1707:27;2186:281:32;2263:17;-1:-1:-1;;;;;2263:29:32;;2296:1;2263:34;2259:119;;2320:47;;-1:-1:-1;;;2320:47:32;;-1:-1:-1;;;;;1777:32:242;;2320:47:32;;;1759:51:242;1732:18;;2320:47:32;;;;;;;;2259:119;1327:66;2387:73;;-1:-1:-1;;;;;;2387:73:32;-1:-1:-1;;;;;2387:73:32;;;;;;;;;;2186:281::o;4106:253:46:-;4189:12;4214;4228:23;4255:6;-1:-1:-1;;;;;4255:19:46;4275:4;4255:25;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4213:67;;;;4297:55;4324:6;4332:7;4341:10;4297:26;:55::i;:::-;4290:62;4106:253;-1:-1:-1;;;;;4106:253:46:o;6598:122:32:-;6648:9;:13;6644:70;;6684:19;;-1:-1:-1;;;6684:19:32;;;;;;;;;;;4625:582:46;4769:12;4798:7;4793:408;;4821:19;4829:10;4821:7;:19::i;:::-;4793:408;;;5045:17;;:22;:49;;;;-1:-1:-1;;;;;;5071:18:46;;;:23;5045:49;5041:119;;;5121:24;;-1:-1:-1;;;5121:24:46;;-1:-1:-1;;;;;1777:32:242;;5121:24:46;;;1759:51:242;1732:18;;5121:24:46;1613:203:242;5041:119:46;-1:-1:-1;5180:10:46;4793:408;4625:582;;;;;:::o;5743:516::-;5874:17;;:21;5870:383;;6102:10;6096:17;6158:15;6145:10;6141:2;6137:19;6130:44;5870:383;6225:17;;-1:-1:-1;;;6225:17:46;;;;;;;;;;;14:331:242;119:9;130;172:8;160:10;157:24;154:44;;;194:1;191;184:12;154:44;223:6;213:8;210:20;207:40;;;243:1;240;233:12;207:40;-1:-1:-1;;269:23:242;;;314:25;;;;;-1:-1:-1;14:331:242:o;350:127::-;411:10;406:3;402:20;399:1;392:31;442:4;439:1;432:15;466:4;463:1;456:15;482:1126;567:6;575;628:2;616:9;607:7;603:23;599:32;596:52;;;644:1;641;634:12;596:52;670:23;;-1:-1:-1;;;;;722:31:242;;712:42;;702:70;;768:1;765;758:12;702:70;791:5;-1:-1:-1;847:2:242;832:18;;819:32;874:18;863:30;;860:50;;;906:1;903;896:12;860:50;929:22;;982:4;974:13;;970:27;-1:-1:-1;960:55:242;;1011:1;1008;1001:12;960:55;1051:2;1038:16;1077:18;1069:6;1066:30;1063:56;;;1099:18;;:::i;:::-;1148:2;1142:9;1240:2;1202:17;;-1:-1:-1;;1198:31:242;;;1231:2;1194:40;1190:54;1178:67;;1275:18;1260:34;;1296:22;;;1257:62;1254:88;;;1322:18;;:::i;:::-;1358:2;1351:22;1382;;;1423:15;;;1440:2;1419:24;1416:37;-1:-1:-1;1413:57:242;;;1466:1;1463;1456:12;1413:57;1522:6;1517:2;1513;1509:11;1504:2;1496:6;1492:15;1479:50;1575:1;1570:2;1561:6;1553;1549:19;1545:28;1538:39;1596:6;1586:16;;;;;482:1126;;;;;:::o;1821:412::-;1950:3;1988:6;1982:13;2013:1;2023:129;2037:6;2034:1;2031:13;2023:129;;;2135:4;2119:14;;;2115:25;;2109:32;2096:11;;;2089:53;2052:12;2023:129;;;-1:-1:-1;2207:1:242;2171:16;;2196:13;;;-1:-1:-1;2171:16:242;1821:412;-1:-1:-1;1821:412:242:o", "linkReferences": {}, "immutableReferences": {"42245": [{"start": 16, "length": 32}]}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_logic\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"initialOwner\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"_data\",\"type\":\"bytes\"}],\"stateMutability\":\"payable\",\"type\":\"constructor\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"target\",\"type\":\"address\"}],\"name\":\"AddressEmptyCode\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"admin\",\"type\":\"address\"}],\"name\":\"ERC1967InvalidAdmin\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"implementation\",\"type\":\"address\"}],\"name\":\"ERC1967InvalidImplementation\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ERC1967NonPayable\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"FailedInnerCall\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ProxyDeniedAdminAccess\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"previousAdmin\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"newAdmin\",\"type\":\"address\"}],\"name\":\"AdminChanged\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"implementation\",\"type\":\"address\"}],\"name\":\"Upgraded\",\"type\":\"event\"},{\"stateMutability\":\"payable\",\"type\":\"fallback\"}],\"devdoc\":{\"details\":\"This contract implements a proxy that is upgradeable through an associated {ProxyAdmin} instance. To avoid https://medium.com/nomic-labs-blog/malicious-backdoors-in-ethereum-proxies-62629adf3357[proxy selector clashing], which can potentially be used in an attack, this contract uses the https://blog.openzeppelin.com/the-transparent-proxy-pattern/[transparent proxy pattern]. This pattern implies two things that go hand in hand: 1. If any account other than the admin calls the proxy, the call will be forwarded to the implementation, even if that call matches the {ITransparentUpgradeableProxy-upgradeToAndCall} function exposed by the proxy itself. 2. If the admin calls the proxy, it can call the `upgradeToAndCall` function but any other call won't be forwarded to the implementation. If the admin tries to call a function on the implementation it will fail with an error indicating the proxy admin cannot fallback to the target implementation. These properties mean that the admin account can only be used for upgrading the proxy, so it's best if it's a dedicated account that is not used for anything else. This will avoid headaches due to sudden errors when trying to call a function from the proxy implementation. For this reason, the proxy deploys an instance of {ProxyAdmin} and allows upgrades only if they come through it. You should think of the `ProxyAdmin` instance as the administrative interface of the proxy, including the ability to change who can trigger upgrades by transferring ownership. NOTE: The real interface of this proxy is that defined in `ITransparentUpgradeableProxy`. This contract does not inherit from that interface, and instead `upgradeToAndCall` is implicitly implemented using a custom dispatch mechanism in `_fallback`. Consequently, the compiler will not produce an ABI for this contract. This is necessary to fully implement transparency without decoding reverts caused by selector clashes between the proxy and the implementation. NOTE: This proxy does not inherit from {Context} deliberately. The {ProxyAdmin} of this contract won't send a meta-transaction in any way, and any other meta-transaction setup should be made in the implementation contract. IMPORTANT: This contract avoids unnecessary storage reads by setting the admin only during construction as an immutable variable, preventing any changes thereafter. However, the admin slot defined in ERC-1967 can still be overwritten by the implementation logic pointed to by this proxy. In such cases, the contract may end up in an undesirable state where the admin slot is different from the actual admin. WARNING: It is not recommended to extend this contract to add additional external functions. If you do so, the compiler will not check that there are no selector conflicts, due to the note above. A selector clash between any new function and the functions declared in {ITransparentUpgradeableProxy} will be resolved in favor of the new one. This could render the `upgradeToAndCall` function inaccessible, preventing upgradeability and compromising transparency.\",\"errors\":{\"AddressEmptyCode(address)\":[{\"details\":\"There's no code at `target` (it is not a contract).\"}],\"ERC1967InvalidAdmin(address)\":[{\"details\":\"The `admin` of the proxy is invalid.\"}],\"ERC1967InvalidImplementation(address)\":[{\"details\":\"The `implementation` of the proxy is invalid.\"}],\"ERC1967NonPayable()\":[{\"details\":\"An upgrade function sees `msg.value > 0` that may be lost.\"}],\"FailedInnerCall()\":[{\"details\":\"A call to an address target failed. The target may have reverted.\"}],\"ProxyDeniedAdminAccess()\":[{\"details\":\"The proxy caller is the current admin, and can't fallback to the proxy target.\"}]},\"events\":{\"AdminChanged(address,address)\":{\"details\":\"Emitted when the admin account has changed.\"},\"Upgraded(address)\":{\"details\":\"Emitted when the implementation is upgraded.\"}},\"kind\":\"dev\",\"methods\":{\"constructor\":{\"details\":\"Initializes an upgradeable proxy managed by an instance of a {ProxyAdmin} with an `initialOwner`, backed by the implementation at `_logic`, and optionally initialized with `_data` as explained in {ERC1967Proxy-constructor}.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol\":\"TransparentUpgradeableProxy\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"lib/openzeppelin-contracts/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC1967.sol\":{\"keccak256\":\"0xb25a4f11fa80c702bf5cd85adec90e6f6f507f32f4a8e6f5dbc31e8c10029486\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6917f8a323e7811f041aecd4d9fd6e92455a6fba38a797ac6f6e208c7912b79d\",\"dweb:/ipfs/QmShuYv55wYHGi4EFkDB8QfF7ZCHoKk2efyz3AWY1ExSq7\"]},\"lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol\":{\"keccak256\":\"0xbfb6695731de677140fbf76c772ab08c4233a122fb51ac28ac120fc49bbbc4ec\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://68f8fded7cc318efa15874b7c6a983fe17a4a955d72d240353a9a4ca1e1b824c\",\"dweb:/ipfs/QmdcmBL9Qo4Tk3Dby4wFYabGyot9JNeLPxpSXZUgUm92BV\"]},\"lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Utils.sol\":{\"keccak256\":\"0x06a78f9b3ee3e6d0eb4e4cd635ba49960bea34cac1db8c0a27c75f2319f1fd65\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://547d21aa17f4f3f1a1a7edf7167beff8dd9496a0348d5588f15cc8a4b29d052a\",\"dweb:/ipfs/QmT16JtRQSWNpLo9W23jr6CzaMuTAcQcjJJcdRd8HLJ6cE\"]},\"lib/openzeppelin-contracts/contracts/proxy/Proxy.sol\":{\"keccak256\":\"0xc3f2ec76a3de8ed7a7007c46166f5550c72c7709e3fc7e8bb3111a7191cdedbd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e73efb4c2ca655882dc237c6b4f234a9bd36d97159d8fcaa837eb01171f726ac\",\"dweb:/ipfs/QmTNnnv7Gu5fs5G1ZMh7Fexp8N4XUs3XrNAngjcxgiss3e\"]},\"lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol\":{\"keccak256\":\"0xc59a78b07b44b2cf2e8ab4175fca91e8eca1eee2df7357b8d2a8833e5ea1f64c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5aa4f07e65444784c29cd7bfcc2341b34381e4e5b5da9f0c5bd00d7f430e66fa\",\"dweb:/ipfs/QmWRMh4Q9DpaU9GvsiXmDdoNYMyyece9if7hnfLz7uqzWM\"]},\"lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol\":{\"keccak256\":\"0x371c3467dccfbb2ac03b0edb4fadaacb9ad382772cee7850a3e73f39a56d102c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3574f1cc3ff2cb985a1385949bd3c76888e8dcf59e6a770ccb15c79b145b39bb\",\"dweb:/ipfs/QmXZDrFibUAMqjoRrpKxV3ma5RR9YfJXZyMrLWJ3bix9gi\"]},\"lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol\":{\"keccak256\":\"0xbc9babed4d136e6cc1e74354aa66538a234f3808645fbe30463b4a7b7d8ca789\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://75fd9323b206da2a87924fca2ca996dbd04ae0963f79f66fb8a0ef94bf283a3d\",\"dweb:/ipfs/QmYSoLcvpq8gRo8doLP4nnB43UAT1D3KvfFJRDSD2y4nYu\"]},\"lib/openzeppelin-contracts/contracts/utils/Address.sol\":{\"keccak256\":\"0xaf28a975a78550e45f65e559a3ad6a5ad43b9b8a37366999abd1b7084eb70721\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b7bd24e224f67f65bfadf85dc2929fa965456bb2415478bd0125471b5ce35245\",\"dweb:/ipfs/QmRaydGr8BTHs1kvaZfsNU69pKzUAGFrvABn1KiRSbE51y\"]},\"lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol\":{\"keccak256\":\"0x32ba59b4b7299237c8ba56319110989d7978a039faf754793064e967e5894418\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1ae50c8b562427df610cc4540c9bf104acca7ef8e2dcae567ae7e52272281e9c\",\"dweb:/ipfs/QmTHiadFCSJUPpRjNegc5SahmeU8bAoY8i9Aq6tVscbcKR\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "_logic", "type": "address"}, {"internalType": "address", "name": "initialOwner", "type": "address"}, {"internalType": "bytes", "name": "_data", "type": "bytes"}], "stateMutability": "payable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "target", "type": "address"}], "type": "error", "name": "AddressEmptyCode"}, {"inputs": [{"internalType": "address", "name": "admin", "type": "address"}], "type": "error", "name": "ERC1967InvalidAdmin"}, {"inputs": [{"internalType": "address", "name": "implementation", "type": "address"}], "type": "error", "name": "ERC1967InvalidImplementation"}, {"inputs": [], "type": "error", "name": "ERC1967Non<PERSON>ayable"}, {"inputs": [], "type": "error", "name": "FailedInnerCall"}, {"inputs": [], "type": "error", "name": "ProxyDeniedAdminAccess"}, {"inputs": [{"internalType": "address", "name": "previousAdmin", "type": "address", "indexed": false}, {"internalType": "address", "name": "newAdmin", "type": "address", "indexed": false}], "type": "event", "name": "Admin<PERSON><PERSON>ed", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "implementation", "type": "address", "indexed": true}], "type": "event", "name": "Upgraded", "anonymous": false}, {"inputs": [], "stateMutability": "payable", "type": "fallback"}], "devdoc": {"kind": "dev", "methods": {"constructor": {"details": "Initializes an upgradeable proxy managed by an instance of a {ProxyAdmin} with an `initialOwner`, backed by the implementation at `_logic`, and optionally initialized with `_data` as explained in {ERC1967Proxy-constructor}."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol": "TransparentUpgradeableProxy"}, "evmVersion": "london", "libraries": {}}, "sources": {"lib/openzeppelin-contracts/contracts/access/Ownable.sol": {"keccak256": "0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb", "urls": ["bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6", "dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC1967.sol": {"keccak256": "0xb25a4f11fa80c702bf5cd85adec90e6f6f507f32f4a8e6f5dbc31e8c10029486", "urls": ["bzz-raw://6917f8a323e7811f041aecd4d9fd6e92455a6fba38a797ac6f6e208c7912b79d", "dweb:/ipfs/QmShuYv55wYHGi4EFkDB8QfF7ZCHoKk2efyz3AWY1ExSq7"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol": {"keccak256": "0xbfb6695731de677140fbf76c772ab08c4233a122fb51ac28ac120fc49bbbc4ec", "urls": ["bzz-raw://68f8fded7cc318efa15874b7c6a983fe17a4a955d72d240353a9a4ca1e1b824c", "dweb:/ipfs/QmdcmBL9Qo4Tk3Dby4wFYabGyot9JNeLPxpSXZUgUm92BV"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Utils.sol": {"keccak256": "0x06a78f9b3ee3e6d0eb4e4cd635ba49960bea34cac1db8c0a27c75f2319f1fd65", "urls": ["bzz-raw://547d21aa17f4f3f1a1a7edf7167beff8dd9496a0348d5588f15cc8a4b29d052a", "dweb:/ipfs/QmT16JtRQSWNpLo9W23jr6CzaMuTAcQcjJJcdRd8HLJ6cE"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol": {"keccak256": "0xc3f2ec76a3de8ed7a7007c46166f5550c72c7709e3fc7e8bb3111a7191cdedbd", "urls": ["bzz-raw://e73efb4c2ca655882dc237c6b4f234a9bd36d97159d8fcaa837eb01171f726ac", "dweb:/ipfs/QmTNnnv7Gu5fs5G1ZMh7Fexp8N4XUs3XrNAngjcxgiss3e"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol": {"keccak256": "0xc59a78b07b44b2cf2e8ab4175fca91e8eca1eee2df7357b8d2a8833e5ea1f64c", "urls": ["bzz-raw://5aa4f07e65444784c29cd7bfcc2341b34381e4e5b5da9f0c5bd00d7f430e66fa", "dweb:/ipfs/QmWRMh4Q9DpaU9GvsiXmDdoNYMyyece9if7hnfLz7uqzWM"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol": {"keccak256": "0x371c3467dccfbb2ac03b0edb4fadaacb9ad382772cee7850a3e73f39a56d102c", "urls": ["bzz-raw://3574f1cc3ff2cb985a1385949bd3c76888e8dcf59e6a770ccb15c79b145b39bb", "dweb:/ipfs/QmXZDrFibUAMqjoRrpKxV3ma5RR9YfJXZyMrLWJ3bix9gi"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol": {"keccak256": "0xbc9babed4d136e6cc1e74354aa66538a234f3808645fbe30463b4a7b7d8ca789", "urls": ["bzz-raw://75fd9323b206da2a87924fca2ca996dbd04ae0963f79f66fb8a0ef94bf283a3d", "dweb:/ipfs/QmYSoLcvpq8gRo8doLP4nnB43UAT1D3KvfFJRDSD2y4nYu"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Address.sol": {"keccak256": "0xaf28a975a78550e45f65e559a3ad6a5ad43b9b8a37366999abd1b7084eb70721", "urls": ["bzz-raw://b7bd24e224f67f65bfadf85dc2929fa965456bb2415478bd0125471b5ce35245", "dweb:/ipfs/QmRaydGr8BTHs1kvaZfsNU69pKzUAGFrvABn1KiRSbE51y"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol": {"keccak256": "0x32ba59b4b7299237c8ba56319110989d7978a039faf754793064e967e5894418", "urls": ["bzz-raw://1ae50c8b562427df610cc4540c9bf104acca7ef8e2dcae567ae7e52272281e9c", "dweb:/ipfs/QmTHiadFCSJUPpRjNegc5SahmeU8bAoY8i9Aq6tVscbcKR"], "license": "MIT"}}, "version": 1}, "id": 36}