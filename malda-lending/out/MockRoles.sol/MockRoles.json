{"abi": [{"type": "function", "name": "GUARDIAN_BLACKLIST", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "isAllowedFor", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "permissions", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "setAllowed", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "allowed", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}], "bytecode": {"object": "0x6080604052348015600f57600080fd5b506102008061001f6000396000f3fe608060405234801561001057600080fd5b506004361061004c5760003560e01c806301e882081461005157806338dd8c2c146100895780634697f05d146100b6578063ce848e13146100f1575b600080fd5b61007461005f366004610142565b60006020819052908152604090205460ff1681565b60405190151581526020015b60405180910390f35b610074610097366004610164565b506001600160a01b031660009081526020819052604090205460ff1690565b6100ef6100c436600461018e565b6001600160a01b03919091166000908152602081905260409020805460ff1916911515919091179055565b005b6101187fd7f04f034163f8e54d3edad14592dc01fc248c04ff4006634300788f87b6946a81565b604051908152602001610080565b80356001600160a01b038116811461013d57600080fd5b919050565b60006020828403121561015457600080fd5b61015d82610126565b9392505050565b6000806040838503121561017757600080fd5b61018083610126565b946020939093013593505050565b600080604083850312156101a157600080fd5b6101aa83610126565b9150602083013580151581146101bf57600080fd5b80915050925092905056fea26469706673582212200dd4f78766ee3f0f8db09636497929755d6dadc75f848906e20ea5798a50a08064736f6c634300081c0033", "sourceMap": "65:394:206:-:0;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x608060405234801561001057600080fd5b506004361061004c5760003560e01c806301e882081461005157806338dd8c2c146100895780634697f05d146100b6578063ce848e13146100f1575b600080fd5b61007461005f366004610142565b60006020819052908152604090205460ff1681565b60405190151581526020015b60405180910390f35b610074610097366004610164565b506001600160a01b031660009081526020819052604090205460ff1690565b6100ef6100c436600461018e565b6001600160a01b03919091166000908152602081905260409020805460ff1916911515919091179055565b005b6101187fd7f04f034163f8e54d3edad14592dc01fc248c04ff4006634300788f87b6946a81565b604051908152602001610080565b80356001600160a01b038116811461013d57600080fd5b919050565b60006020828403121561015457600080fd5b61015d82610126565b9392505050565b6000806040838503121561017757600080fd5b61018083610126565b946020939093013593505050565b600080604083850312156101a157600080fd5b6101aa83610126565b9150602083013580151581146101bf57600080fd5b80915050925092905056fea26469706673582212200dd4f78766ee3f0f8db09636497929755d6dadc75f848906e20ea5798a50a08064736f6c634300081c0033", "sourceMap": "65:394:206:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;90:43;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;548:14:242;;541:22;523:41;;511:2;496:18;90:43:206;;;;;;;;223:121;;;;;;:::i;:::-;-1:-1:-1;;;;;;317:20:206;294:4;317:20;;;;;;;;;;;;;;223:121;350:107;;;;;;:::i;:::-;-1:-1:-1;;;;;420:20:206;;;;:11;:20;;;;;;;;;;:30;;-1:-1:-1;;420:30:206;;;;;;;;;;350:107;;;140:76;;185:31;140:76;;;;;1332:25:242;;;1320:2;1305:18;140:76:206;1186:177:242;14:173;82:20;;-1:-1:-1;;;;;131:31:242;;121:42;;111:70;;177:1;174;167:12;111:70;14:173;;;:::o;192:186::-;251:6;304:2;292:9;283:7;279:23;275:32;272:52;;;320:1;317;310:12;272:52;343:29;362:9;343:29;:::i;:::-;333:39;192:186;-1:-1:-1;;;192:186:242:o;575:254::-;643:6;651;704:2;692:9;683:7;679:23;675:32;672:52;;;720:1;717;710:12;672:52;743:29;762:9;743:29;:::i;:::-;733:39;819:2;804:18;;;;791:32;;-1:-1:-1;;;575:254:242:o;834:347::-;899:6;907;960:2;948:9;939:7;935:23;931:32;928:52;;;976:1;973;966:12;928:52;999:29;1018:9;999:29;:::i;:::-;989:39;;1078:2;1067:9;1063:18;1050:32;1125:5;1118:13;1111:21;1104:5;1101:32;1091:60;;1147:1;1144;1137:12;1091:60;1170:5;1160:15;;;834:347;;;;;:::o", "linkReferences": {}}, "methodIdentifiers": {"GUARDIAN_BLACKLIST()": "ce848e13", "isAllowedFor(address,bytes32)": "38dd8c2c", "permissions(address)": "01e88208", "setAllowed(address,bool)": "4697f05d"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"GUARDIAN_BLACKLIST\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"name\":\"isAllowedFor\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"permissions\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"allowed\",\"type\":\"bool\"}],\"name\":\"setAllowed\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"test/mocks/MockRoles.sol\":\"MockRoles\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"test/mocks/MockRoles.sol\":{\"keccak256\":\"0x07b30c7f8734f08d9159e28b4a3ad37ab8b3f484d9c10aedbe7562dacd14a218\",\"license\":\"UNLICENSED\",\"urls\":[\"bzz-raw://ac417b2b6f324eec0b62e2cd028a57c3beba748476595ffbf657c1c794f9b52a\",\"dweb:/ipfs/QmUYtpSLqQ7cYSLKkh81SBu2vKbvkES17RQw46REFiEdoe\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "view", "type": "function", "name": "GUARDIAN_BLACKLIST", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "isAllowedFor", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "permissions", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bool", "name": "allowed", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "setAllowed"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"test/mocks/MockRoles.sol": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "evmVersion": "london", "libraries": {}}, "sources": {"test/mocks/MockRoles.sol": {"keccak256": "0x07b30c7f8734f08d9159e28b4a3ad37ab8b3f484d9c10aedbe7562dacd14a218", "urls": ["bzz-raw://ac417b2b6f324eec0b62e2cd028a57c3beba748476595ffbf657c1c794f9b52a", "dweb:/ipfs/QmUYtpSLqQ7cYSLKkh81SBu2vKbvkES17RQw46REFiEdoe"], "license": "UNLICENSED"}}, "version": 1}, "id": 206}