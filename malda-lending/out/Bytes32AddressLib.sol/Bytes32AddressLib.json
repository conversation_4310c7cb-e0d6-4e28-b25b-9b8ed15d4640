{"abi": [], "bytecode": {"object": "0x60566037600b82828239805160001a607314602a57634e487b7160e01b600052600060045260246000fd5b30600052607381538281f3fe73000000000000000000000000000000000000000030146080604052600080fdfea2646970667358221220309ca4413d04d49cb6aabc9da48e48dc6bea92864236a83d22bdf11f2acea07264736f6c634300081c0033", "sourceMap": "248:311:166:-:0;;;;;;;;;;;;;;;-1:-1:-1;;;248:311:166;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x73000000000000000000000000000000000000000030146080604052600080fdfea2646970667358221220309ca4413d04d49cb6aabc9da48e48dc6bea92864236a83d22bdf11f2acea07264736f6c634300081c0033", "sourceMap": "248:311:166:-:0;;;;;;;;", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[],\"devdoc\":{\"author\":\"Solmate (https://github.com/transmissions11/solmate/blob/main/src/utils/Bytes32AddressLib.sol)\",\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"notice\":\"Library for converting between addresses and bytes32 values.\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/libraries/Bytes32AddressLib.sol\":\"Bytes32AddressLib\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"src/libraries/Bytes32AddressLib.sol\":{\"keccak256\":\"0xd2c90ee900d4cfe4b48254363cfbada176cd8b130ddee62ede7f9c8ec2a6b8bd\",\"license\":\"AGPL-3.0-only\",\"urls\":[\"bzz-raw://e864869c9c87f0cb20b898c0435f9585db5e1f52ba0d235af76c8e6a8b4fbffd\",\"dweb:/ipfs/QmRCLJpFCGsra4VFGDNf2hEuEJP7sMs6PQxZz6CV3sGcxa\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/libraries/Bytes32AddressLib.sol": "Bytes32AddressLib"}, "evmVersion": "london", "libraries": {}}, "sources": {"src/libraries/Bytes32AddressLib.sol": {"keccak256": "0xd2c90ee900d4cfe4b48254363cfbada176cd8b130ddee62ede7f9c8ec2a6b8bd", "urls": ["bzz-raw://e864869c9c87f0cb20b898c0435f9585db5e1f52ba0d235af76c8e6a8b4fbffd", "dweb:/ipfs/QmRCLJpFCGsra4VFGDNf2hEuEJP7sMs6PQxZz6CV3sGcxa"], "license": "AGPL-3.0-only"}}, "version": 1}, "id": 166}