{"abi": [{"type": "constructor", "inputs": [{"name": "_roles", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "roles", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IRoles"}], "stateMutability": "view"}, {"type": "function", "name": "sendMsg", "inputs": [{"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "uint32", "internalType": "uint32"}, {"name": "_token", "type": "address", "internalType": "address"}, {"name": "_message", "type": "bytes", "internalType": "bytes"}, {"name": "", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "payable"}, {"type": "error", "name": "BaseBridge_NotAuthorized", "inputs": []}], "bytecode": {"object": "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", "sourceMap": "184:609:203:-:0;;;275:67;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;313:5;:22;;-1:-1:-1;;;;;;313:22:203;-1:-1:-1;;;;;313:22:203;;;;;;;;;;184:609;;14:290:242;84:6;137:2;125:9;116:7;112:23;108:32;105:52;;;153:1;150;143:12;105:52;179:16;;-1:-1:-1;;;;;224:31:242;;214:42;;204:70;;270:1;267;260:12;204:70;293:5;14:290;-1:-1:-1;;;14:290:242:o;:::-;184:609:203;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x6080604052600436106100295760003560e01c8063392f5f641461002e578063f2db52a71461006a575b600080fd5b34801561003a57600080fd5b5060005461004e906001600160a01b031681565b6040516001600160a01b03909116815260200160405180910390f35b61007d6100783660046102db565b61007f565b005b60005460408051639e106dc760e01b815290516001600160a01b03909216916338dd8c2c9133918491639e106dc79160048083019260209291908290030181865afa1580156100d2573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906100f69190610389565b6040516001600160e01b031960e085901b1681526001600160a01b0390921660048301526024820152604401602060405180830381865afa15801561013f573d6000803e3d6000fd5b505050506040513d601f19601f8201168201806040525081019061016391906103a2565b6101805760405163239e3f0760e21b815260040160405180910390fd5b6000828060200190518101906101969190610389565b6040516323b872dd60e01b8152336004820152306024820152604481018290529091506001600160a01b038516906323b872dd906064016020604051808303816000875af11580156101ec573d6000803e3d6000fd5b505050506040513d601f19601f8201168201806040525081019061021091906103a2565b5050505050505050565b80356001600160a01b038116811461023157600080fd5b919050565b634e487b7160e01b600052604160045260246000fd5b600082601f83011261025d57600080fd5b813567ffffffffffffffff81111561027757610277610236565b604051601f8201601f19908116603f0116810167ffffffffffffffff811182821017156102a6576102a6610236565b6040528181528382016020018510156102be57600080fd5b816020850160208301376000918101602001919091529392505050565b60008060008060008060c087890312156102f457600080fd5b863595506103046020880161021a565b9450604087013563ffffffff8116811461031d57600080fd5b935061032b6060880161021a565b9250608087013567ffffffffffffffff81111561034757600080fd5b61035389828a0161024c565b92505060a087013567ffffffffffffffff81111561037057600080fd5b61037c89828a0161024c565b9150509295509295509295565b60006020828403121561039b57600080fd5b5051919050565b6000602082840312156103b457600080fd5b815180151581146103c457600080fd5b939250505056fea264697066735822122026afab194c87af7d766e73fb8edd581764d4938f9b4b18b7639b5a4dc32b3a4c64736f6c634300081c0033", "sourceMap": "184:609:203:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;210:19;;;;;;;;;;-1:-1:-1;210:19:203;;;;-1:-1:-1;;;;;210:19:203;;;;;;-1:-1:-1;;;;;194:32:242;;;176:51;;164:2;149:18;210:19:203;;;;;;;498:293;;;;;;:::i;:::-;;:::i;:::-;;;389:5;;420:18;;;-1:-1:-1;;;420:18:203;;;;-1:-1:-1;;;;;389:5:203;;;;:18;;408:10;;389:5;;420:16;;:18;;;;;;;;;;;;;;389:5;420:18;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;389:50;;-1:-1:-1;;;;;;389:50:203;;;;;;;-1:-1:-1;;;;;2581:32:242;;;389:50:203;;;2563:51:242;2630:18;;;2623:34;2536:18;;389:50:203;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;384:90;;448:26;;-1:-1:-1;;;448:26:203;;;;;;;;;;;384:90;664:14:::1;692:8;681:31;;;;;;;;;;;;:::i;:::-;722:62;::::0;-1:-1:-1;;;722:62:203;;750:10:::1;722:62;::::0;::::1;3341:51:242::0;770:4:203::1;3408:18:242::0;;;3401:60;3477:18;;;3470:34;;;664:48:203;;-1:-1:-1;;;;;;722:27:203;::::1;::::0;::::1;::::0;3314:18:242;;722:62:203::1;;;;;;;;;;;;;;;;;;::::0;::::1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;654:137;498:293:::0;;;;;;:::o;238:173:242:-;306:20;;-1:-1:-1;;;;;355:31:242;;345:42;;335:70;;401:1;398;391:12;335:70;238:173;;;:::o;416:127::-;477:10;472:3;468:20;465:1;458:31;508:4;505:1;498:15;532:4;529:1;522:15;548:725;590:5;643:3;636:4;628:6;624:17;620:27;610:55;;661:1;658;651:12;610:55;701:6;688:20;731:18;723:6;720:30;717:56;;;753:18;;:::i;:::-;802:2;796:9;894:2;856:17;;-1:-1:-1;;852:31:242;;;885:2;848:40;844:54;832:67;;929:18;914:34;;950:22;;;911:62;908:88;;;976:18;;:::i;:::-;1012:2;1005:22;1036;;;1077:19;;;1098:4;1073:30;1070:39;-1:-1:-1;1067:59:242;;;1122:1;1119;1112:12;1067:59;1186:6;1179:4;1171:6;1167:17;1160:4;1152:6;1148:17;1135:58;1241:1;1213:19;;;1234:4;1209:30;1202:41;;;;1217:6;548:725;-1:-1:-1;;;548:725:242:o;1278:917::-;1399:6;1407;1415;1423;1431;1439;1492:3;1480:9;1471:7;1467:23;1463:33;1460:53;;;1509:1;1506;1499:12;1460:53;1545:9;1532:23;1522:33;;1574:38;1608:2;1597:9;1593:18;1574:38;:::i;:::-;1564:48;;1662:2;1651:9;1647:18;1634:32;1706:10;1699:5;1695:22;1688:5;1685:33;1675:61;;1732:1;1729;1722:12;1675:61;1755:5;-1:-1:-1;1779:38:242;1813:2;1798:18;;1779:38;:::i;:::-;1769:48;;1868:3;1857:9;1853:19;1840:33;1896:18;1888:6;1885:30;1882:50;;;1928:1;1925;1918:12;1882:50;1951:49;1992:7;1983:6;1972:9;1968:22;1951:49;:::i;:::-;1941:59;;;2053:3;2042:9;2038:19;2025:33;2083:18;2073:8;2070:32;2067:52;;;2115:1;2112;2105:12;2067:52;2138:51;2181:7;2170:8;2159:9;2155:24;2138:51;:::i;:::-;2128:61;;;1278:917;;;;;;;;:::o;2200:184::-;2270:6;2323:2;2311:9;2302:7;2298:23;2294:32;2291:52;;;2339:1;2336;2329:12;2291:52;-1:-1:-1;2362:16:242;;2200:184;-1:-1:-1;2200:184:242:o;2668:277::-;2735:6;2788:2;2776:9;2767:7;2763:23;2759:32;2756:52;;;2804:1;2801;2794:12;2756:52;2836:9;2830:16;2889:5;2882:13;2875:21;2868:5;2865:32;2855:60;;2911:1;2908;2901:12;2855:60;2934:5;2668:277;-1:-1:-1;;;2668:277:242:o", "linkReferences": {}}, "methodIdentifiers": {"roles()": "392f5f64", "sendMsg(uint256,address,uint32,address,bytes,bytes)": "f2db52a7"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_roles\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"BaseBridge_NotAuthorized\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"roles\",\"outputs\":[{\"internalType\":\"contract IRoles\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"\",\"type\":\"uint32\"},{\"internalType\":\"address\",\"name\":\"_token\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"_message\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"sendMsg\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"test/mocks/BridgeMock.sol\":\"BridgeMock\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xc6a8ff0ea489379b61faa647490411b80102578440ab9d84e9a957cc12164e70\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0ea104e577e63faea3b69c415637e99e755dcbf64c5833d7140c35a714d6d90c\",\"dweb:/ipfs/Qmau6x4Ns9XdyynRCNNp3RhLqijJjFm7z5fyZazfYFGYdq\"]},\"src/interfaces/IRoles.sol\":{\"keccak256\":\"0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c\",\"dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj\"]},\"test/mocks/BridgeMock.sol\":{\"keccak256\":\"0x40e59f3cabd186df201b67d374f36a20ff615f92ca661a504e5c0c462f3f1561\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://fd5a353f0757f76bfd406ffab051e68cb521f75b7b0e087c0cf03956e3b6390a\",\"dweb:/ipfs/QmPMU8E52FRTeYzhacmkzpgr4K6gAT1ckaiNWQySY6uhrK\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "_roles", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "type": "error", "name": "BaseBridge_NotAuthorized"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "roles", "outputs": [{"internalType": "contract IRoles", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint32", "name": "", "type": "uint32"}, {"internalType": "address", "name": "_token", "type": "address"}, {"internalType": "bytes", "name": "_message", "type": "bytes"}, {"internalType": "bytes", "name": "", "type": "bytes"}], "stateMutability": "payable", "type": "function", "name": "sendMsg"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"test/mocks/BridgeMock.sol": "BridgeMock"}, "evmVersion": "london", "libraries": {}}, "sources": {"lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0xc6a8ff0ea489379b61faa647490411b80102578440ab9d84e9a957cc12164e70", "urls": ["bzz-raw://0ea104e577e63faea3b69c415637e99e755dcbf64c5833d7140c35a714d6d90c", "dweb:/ipfs/Qmau6x4Ns9XdyynRCNNp3RhLqijJjFm7z5fyZazfYFGYdq"], "license": "MIT"}, "src/interfaces/IRoles.sol": {"keccak256": "0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365", "urls": ["bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c", "dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj"], "license": "BSL-1.1"}, "test/mocks/BridgeMock.sol": {"keccak256": "0x40e59f3cabd186df201b67d374f36a20ff615f92ca661a504e5c0c462f3f1561", "urls": ["bzz-raw://fd5a353f0757f76bfd406ffab051e68cb521f75b7b0e087c0cf03956e3b6390a", "dweb:/ipfs/QmPMU8E52FRTeYzhacmkzpgr4K6gAT1ckaiNWQySY6uhrK"], "license": "BSL-1.1"}}, "version": 1}, "id": 203}