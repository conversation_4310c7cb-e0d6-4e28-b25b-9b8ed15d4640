{"abi": [{"type": "function", "name": "ALICE_KEY", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "BOB_KEY", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "DEFAULT_COLLATERAL_FACTOR", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "DEFAULT_INFLATION_INCREASE", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "DEFAULT_LIQUIDATOR_ORACLE_PRICE", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "DEFAULT_ORACLE_PRICE", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "DEFAULT_ORACLE_PRICE36", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "FOO_KEY", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "IS_TEST", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "LARGE", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "LINEA_CHAIN_ID", "inputs": [], "outputs": [{"name": "", "type": "uint32", "internalType": "uint32"}], "stateMutability": "view"}, {"type": "function", "name": "MEDIUM", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "SMALL", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "ZERO_ADDRESS", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "ZERO_VALUE", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "alice", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "blacklister", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract Blacklister"}], "stateMutability": "view"}, {"type": "function", "name": "bob", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "dai", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract ERC20Mock"}], "stateMutability": "view"}, {"type": "function", "name": "excludeArtifacts", "inputs": [], "outputs": [{"name": "excludedArtifacts_", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "excludeContracts", "inputs": [], "outputs": [{"name": "excludedContracts_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "excludeSelectors", "inputs": [], "outputs": [{"name": "excludedSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzSelector[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "excludeSenders", "inputs": [], "outputs": [{"name": "excludedSenders_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "failed", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "foo", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "interestModel", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract JumpRateModelV4"}], "stateMutability": "view"}, {"type": "function", "name": "mWethExtension", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract mTokenGateway"}], "stateMutability": "view"}, {"type": "function", "name": "mWethHost", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract mErc20Host"}], "stateMutability": "view"}, {"type": "function", "name": "operator", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract Operator"}], "stateMutability": "view"}, {"type": "function", "name": "oracleOperator", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract OracleMock"}], "stateMutability": "view"}, {"type": "function", "name": "pauser", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract Pauser"}], "stateMutability": "view"}, {"type": "function", "name": "rewards", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract RewardDistributor"}], "stateMutability": "view"}, {"type": "function", "name": "roles", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract Roles"}], "stateMutability": "view"}, {"type": "function", "name": "setUp", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "targetArtifactSelectors", "inputs": [], "outputs": [{"name": "targetedArtifactSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzArtifactSelector[]", "components": [{"name": "artifact", "type": "string", "internalType": "string"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetArtifacts", "inputs": [], "outputs": [{"name": "targetedArtifacts_", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "targetContracts", "inputs": [], "outputs": [{"name": "targetedContracts_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "targetInterfaces", "inputs": [], "outputs": [{"name": "targetedInterfaces_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzInterface[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "artifacts", "type": "string[]", "internalType": "string[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetSelectors", "inputs": [], "outputs": [{"name": "targetedSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzSelector[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetSenders", "inputs": [], "outputs": [{"name": "targetedSenders_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "usdc", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract ERC20Mock"}], "stateMutability": "view"}, {"type": "function", "name": "verifierMock", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract Risc0VerifierMock"}], "stateMutability": "view"}, {"type": "function", "name": "weth", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract ERC20Mock"}], "stateMutability": "view"}, {"type": "function", "name": "zkVerifier", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract ZkVerifier"}], "stateMutability": "view"}, {"type": "event", "name": "log", "inputs": [{"name": "", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_address", "inputs": [{"name": "", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "int256[]", "indexed": false, "internalType": "int256[]"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "event", "name": "log_bytes", "inputs": [{"name": "", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "log_bytes32", "inputs": [{"name": "", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "log_int", "inputs": [{"name": "", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "log_named_address", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256[]", "indexed": false, "internalType": "int256[]"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "event", "name": "log_named_bytes", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "log_named_bytes32", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "log_named_decimal_int", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256", "indexed": false, "internalType": "int256"}, {"name": "decimals", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_named_decimal_uint", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "decimals", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_named_int", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "log_named_string", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_named_uint", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_string", "inputs": [{"name": "", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_uint", "inputs": [{"name": "", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "logs", "inputs": [{"name": "", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"ALICE_KEY()": "4f7a95a6", "BOB_KEY()": "65c9b6b4", "DEFAULT_COLLATERAL_FACTOR()": "6805f9e5", "DEFAULT_INFLATION_INCREASE()": "8df13dce", "DEFAULT_LIQUIDATOR_ORACLE_PRICE()": "d3ba839d", "DEFAULT_ORACLE_PRICE()": "ec51597c", "DEFAULT_ORACLE_PRICE36()": "da0de28e", "FOO_KEY()": "19794c7e", "IS_TEST()": "fa7626d4", "LARGE()": "aed9a992", "LINEA_CHAIN_ID()": "e014812a", "MEDIUM()": "edee709e", "SMALL()": "e8b7c8ad", "ZERO_ADDRESS()": "538ba4f9", "ZERO_VALUE()": "ec732959", "alice()": "fb47e3a2", "blacklister()": "bd102430", "bob()": "c09cec77", "dai()": "f4b9fa75", "excludeArtifacts()": "b5508aa9", "excludeContracts()": "e20c9f71", "excludeSelectors()": "b0464fdc", "excludeSenders()": "1ed7831c", "failed()": "ba414fa6", "foo()": "c2985578", "interestModel()": "ac165d7a", "mWethExtension()": "182cf67f", "mWethHost()": "1a49ddc2", "operator()": "570ca735", "oracleOperator()": "11679ef7", "pauser()": "9fd0506d", "rewards()": "9ec5a894", "roles()": "392f5f64", "setUp()": "0a9254e4", "targetArtifactSelectors()": "66d9a9a0", "targetArtifacts()": "85226c81", "targetContracts()": "3f7286f4", "targetInterfaces()": "2ade3880", "targetSelectors()": "916a17c6", "targetSenders()": "3e5e3c23", "usdc()": "3e413bee", "verifierMock()": "6acf0eda", "weth()": "3fc8cef3", "zkVerifier()": "d6df096d"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"log\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"log_address\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"val\",\"type\":\"uint256[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int256[]\",\"name\":\"val\",\"type\":\"int256[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"val\",\"type\":\"address[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"log_bytes\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"name\":\"log_bytes32\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"\",\"type\":\"int256\"}],\"name\":\"log_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"val\",\"type\":\"address\"}],\"name\":\"log_named_address\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"val\",\"type\":\"uint256[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256[]\",\"name\":\"val\",\"type\":\"int256[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"val\",\"type\":\"address[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"val\",\"type\":\"bytes\"}],\"name\":\"log_named_bytes\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"val\",\"type\":\"bytes32\"}],\"name\":\"log_named_bytes32\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"val\",\"type\":\"int256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"log_named_decimal_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"val\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"log_named_decimal_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"val\",\"type\":\"int256\"}],\"name\":\"log_named_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"val\",\"type\":\"string\"}],\"name\":\"log_named_string\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"val\",\"type\":\"uint256\"}],\"name\":\"log_named_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"log_string\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"log_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"logs\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"ALICE_KEY\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"BOB_KEY\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"DEFAULT_COLLATERAL_FACTOR\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"DEFAULT_INFLATION_INCREASE\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"DEFAULT_LIQUIDATOR_ORACLE_PRICE\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"DEFAULT_ORACLE_PRICE\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"DEFAULT_ORACLE_PRICE36\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"FOO_KEY\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"IS_TEST\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"LARGE\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"LINEA_CHAIN_ID\",\"outputs\":[{\"internalType\":\"uint32\",\"name\":\"\",\"type\":\"uint32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"MEDIUM\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"SMALL\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"ZERO_ADDRESS\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"ZERO_VALUE\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"alice\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"blacklister\",\"outputs\":[{\"internalType\":\"contract Blacklister\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"bob\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"dai\",\"outputs\":[{\"internalType\":\"contract ERC20Mock\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeArtifacts\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"excludedArtifacts_\",\"type\":\"string[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeContracts\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"excludedContracts_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzSelector[]\",\"name\":\"excludedSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeSenders\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"excludedSenders_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"failed\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"foo\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"interestModel\",\"outputs\":[{\"internalType\":\"contract JumpRateModelV4\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"mWethExtension\",\"outputs\":[{\"internalType\":\"contract mTokenGateway\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"mWethHost\",\"outputs\":[{\"internalType\":\"contract mErc20Host\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"operator\",\"outputs\":[{\"internalType\":\"contract Operator\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"oracleOperator\",\"outputs\":[{\"internalType\":\"contract OracleMock\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"pauser\",\"outputs\":[{\"internalType\":\"contract Pauser\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"rewards\",\"outputs\":[{\"internalType\":\"contract RewardDistributor\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"roles\",\"outputs\":[{\"internalType\":\"contract Roles\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"setUp\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetArtifactSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"string\",\"name\":\"artifact\",\"type\":\"string\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzArtifactSelector[]\",\"name\":\"targetedArtifactSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetArtifacts\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"targetedArtifacts_\",\"type\":\"string[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetContracts\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"targetedContracts_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetInterfaces\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"string[]\",\"name\":\"artifacts\",\"type\":\"string[]\"}],\"internalType\":\"struct StdInvariant.FuzzInterface[]\",\"name\":\"targetedInterfaces_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzSelector[]\",\"name\":\"targetedSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetSenders\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"targetedSenders_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"usdc\",\"outputs\":[{\"internalType\":\"contract ERC20Mock\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"verifierMock\",\"outputs\":[{\"internalType\":\"contract Risc0VerifierMock\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"weth\",\"outputs\":[{\"internalType\":\"contract ERC20Mock\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"zkVerifier\",\"outputs\":[{\"internalType\":\"contract ZkVerifier\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"test/unit/shared/Pauser_Unit_Shared.t.sol\":\"Pauser_Unit_Shared\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"lib/forge-std/src/Base.sol\":{\"keccak256\":\"0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d\",\"dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z\"]},\"lib/forge-std/src/StdAssertions.sol\":{\"keccak256\":\"0x4584f551c3a875c26423c1e50a77179bc2f9c9c1ee6d0f0c6be0f89ca5ee8270\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ef118876a862b60ba8a6612b8b3f3a6c226e11a0071a2e9695f021586d1bfdbe\",\"dweb:/ipfs/QmXd2vD91PkvoFWasFkgbDi89PctmgsHVxGvGNyhjmRK2b\"]},\"lib/forge-std/src/StdChains.sol\":{\"keccak256\":\"0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351\",\"dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm\"]},\"lib/forge-std/src/StdCheats.sol\":{\"keccak256\":\"0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41\",\"dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK\"]},\"lib/forge-std/src/StdConstants.sol\":{\"keccak256\":\"0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc\",\"dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r\"]},\"lib/forge-std/src/StdError.sol\":{\"keccak256\":\"0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6\",\"dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj\"]},\"lib/forge-std/src/StdInvariant.sol\":{\"keccak256\":\"0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391\",\"dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5\"]},\"lib/forge-std/src/StdJson.sol\":{\"keccak256\":\"0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974\",\"dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3\"]},\"lib/forge-std/src/StdMath.sol\":{\"keccak256\":\"0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92\",\"dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC\"]},\"lib/forge-std/src/StdStorage.sol\":{\"keccak256\":\"0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57\",\"dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ\"]},\"lib/forge-std/src/StdStyle.sol\":{\"keccak256\":\"0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8\",\"dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK\"]},\"lib/forge-std/src/StdToml.sol\":{\"keccak256\":\"0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3\",\"dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8\"]},\"lib/forge-std/src/StdUtils.sol\":{\"keccak256\":\"0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138\",\"dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776\"]},\"lib/forge-std/src/Test.sol\":{\"keccak256\":\"0x3dda6083a83dfa3e8526e97bcc28e862ee2442dd58fe94d5c426d65b8e38f73c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://33f8c02e4dabdab86a6825125856446a8657eacd712318b51b7818e4a6f6e3f4\",\"dweb:/ipfs/QmRKSjVnrk54yr8wTK2e6QxRjiuba2H8HJSKunHAkdo7RG\"]},\"lib/forge-std/src/Vm.sol\":{\"keccak256\":\"0xaae88142a348064677cf81169ea0ca706be3e99e2a0f13a01878ff8d52cf6b25\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://da4fb0ed1b31381c7e60ee386acc72796e8a8325c83739ec9a3678e2f841073c\",\"dweb:/ipfs/Qmb4MU6FpAi8Wt5kUQ3wa7ooHnfMVbdd9zuvnqbTehLmxJ\"]},\"lib/forge-std/src/console.sol\":{\"keccak256\":\"0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57\",\"dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP\"]},\"lib/forge-std/src/console2.sol\":{\"keccak256\":\"0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d\",\"dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ\"]},\"lib/forge-std/src/interfaces/IMulticall3.sol\":{\"keccak256\":\"0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0\",\"dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2\"]},\"lib/forge-std/src/safeconsole.sol\":{\"keccak256\":\"0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab\",\"dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol\":{\"keccak256\":\"0xc163fcf9bb10138631a9ba5564df1fa25db9adff73bd9ee868a8ae1858fe093a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9706d43a0124053d9880f6e31a59f31bc0a6a3dc1acd66ce0a16e1111658c5f6\",\"dweb:/ipfs/QmUFmfowzkRwGtDu36cXV9SPTBHJ3n7dG9xQiK5B28jTf2\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol\":{\"keccak256\":\"0x631188737069917d2f909d29ce62c4d48611d326686ba6683e26b72a23bfac0b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7a61054ae84cd6c4d04c0c4450ba1d6de41e27e0a2c4f1bcdf58f796b401c609\",\"dweb:/ipfs/QmUvtdp7X1mRVyC3CsHrtPbgoqWaXHp3S1ZR24tpAQYJWM\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol\":{\"keccak256\":\"0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9\",\"dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/ReentrancyGuardUpgradeable.sol\":{\"keccak256\":\"0xb44e086e941292cdc7f440de51478493894ef0b1aeccb0c4047445919f667f74\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://942dad22fbdc1669f025540ba63aa3ccfad5f8458fc5d4525b31ebf272e7af45\",\"dweb:/ipfs/Qmdo4X2M82aM3AMoW2kf2jhYkSCyC4T1pHNd6obdsDFnAB\"]},\"lib/openzeppelin-contracts/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol\":{\"keccak256\":\"0x60c65f701957fdd6faea1acb0bb45825791d473693ed9ecb34726fdfaa849dd7\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ea290300e0efc4d901244949dc4d877fd46e6c5e43dc2b26620e8efab3ab803f\",\"dweb:/ipfs/QmcLLJppxKeJWqHxE2CUkcfhuRTgHSn8J4kijcLa5MYhSt\"]},\"lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol\":{\"keccak256\":\"0xbfb6695731de677140fbf76c772ab08c4233a122fb51ac28ac120fc49bbbc4ec\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://68f8fded7cc318efa15874b7c6a983fe17a4a955d72d240353a9a4ca1e1b824c\",\"dweb:/ipfs/QmdcmBL9Qo4Tk3Dby4wFYabGyot9JNeLPxpSXZUgUm92BV\"]},\"lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Utils.sol\":{\"keccak256\":\"0x06a78f9b3ee3e6d0eb4e4cd635ba49960bea34cac1db8c0a27c75f2319f1fd65\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://547d21aa17f4f3f1a1a7edf7167beff8dd9496a0348d5588f15cc8a4b29d052a\",\"dweb:/ipfs/QmT16JtRQSWNpLo9W23jr6CzaMuTAcQcjJJcdRd8HLJ6cE\"]},\"lib/openzeppelin-contracts/contracts/proxy/Proxy.sol\":{\"keccak256\":\"0xc3f2ec76a3de8ed7a7007c46166f5550c72c7709e3fc7e8bb3111a7191cdedbd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e73efb4c2ca655882dc237c6b4f234a9bd36d97159d8fcaa837eb01171f726ac\",\"dweb:/ipfs/QmTNnnv7Gu5fs5G1ZMh7Fexp8N4XUs3XrNAngjcxgiss3e\"]},\"lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol\":{\"keccak256\":\"0xc59a78b07b44b2cf2e8ab4175fca91e8eca1eee2df7357b8d2a8833e5ea1f64c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5aa4f07e65444784c29cd7bfcc2341b34381e4e5b5da9f0c5bd00d7f430e66fa\",\"dweb:/ipfs/QmWRMh4Q9DpaU9GvsiXmDdoNYMyyece9if7hnfLz7uqzWM\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol\":{\"keccak256\":\"0xc3e1fa9d1987f8d349dfb4d6fe93bf2ca014b52ba335cfac30bfe71e357e6f80\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c5703ccdeb7b1d685e375ed719117e9edf2ab4bc544f24f23b0d50ec82257229\",\"dweb:/ipfs/QmTdwkbQq7owpCiyuzE7eh5LrD2ddrBCZ5WHVsWPi1RrTS\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xc6a8ff0ea489379b61faa647490411b80102578440ab9d84e9a957cc12164e70\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0ea104e577e63faea3b69c415637e99e755dcbf64c5833d7140c35a714d6d90c\",\"dweb:/ipfs/Qmau6x4Ns9XdyynRCNNp3RhLqijJjFm7z5fyZazfYFGYdq\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xaa761817f6cd7892fcf158b3c776b34551cde36f48ff9703d53898bc45a94ea2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0ad7c8d4d08938c8dfc43d75a148863fb324b80cf53e0a36f7e5a4ac29008850\",\"dweb:/ipfs/QmcrhfPgVNf5mkdhQvy1pMv51TFokD3Y4Wa5WZhFqVh8UV\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol\":{\"keccak256\":\"0x6008dabfe393240d73d7dd7688033f72740d570aa422254d29a7dce8568f3aff\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f5196ec75139918c6c7bb4251b36395e668f1fa6d206beba7e7520e74913940d\",\"dweb:/ipfs/QmSyqjksXxmm2mCG6qRd1yuwLykypkSVBbnBnGqJRcuJMi\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol\":{\"keccak256\":\"0x37bb49513c49c87c4642a891b13b63571bc87013dde806617aa1efb54605f386\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b3036b3a83b7c48f96641f2a9002b9f2dcb6a5958dd670894ada21ae8229b3d0\",\"dweb:/ipfs/QmUNfSBdoVtjhETaUJCYcaC7pTMgbhht926tJ2uXJbiVd3\"]},\"lib/openzeppelin-contracts/contracts/utils/Address.sol\":{\"keccak256\":\"0xaf28a975a78550e45f65e559a3ad6a5ad43b9b8a37366999abd1b7084eb70721\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b7bd24e224f67f65bfadf85dc2929fa965456bb2415478bd0125471b5ce35245\",\"dweb:/ipfs/QmRaydGr8BTHs1kvaZfsNU69pKzUAGFrvABn1KiRSbE51y\"]},\"lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"lib/openzeppelin-contracts/contracts/utils/ReentrancyGuard.sol\":{\"keccak256\":\"0xf980daa263b661ab8ddee7d4fd833c7da7e7995e2c359ff1f17e67e4112f2236\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7448ab095d6940130bcf76ba47a2eab14148c83119523b93dd89f6d84edd6c02\",\"dweb:/ipfs/QmawrZ4voKQjH3oomXT3Kuheb3Mnmo2VvVpxg8Ne5UJUrd\"]},\"lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol\":{\"keccak256\":\"0x32ba59b4b7299237c8ba56319110989d7978a039faf754793064e967e5894418\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1ae50c8b562427df610cc4540c9bf104acca7ef8e2dcae567ae7e52272281e9c\",\"dweb:/ipfs/QmTHiadFCSJUPpRjNegc5SahmeU8bAoY8i9Aq6tVscbcKR\"]},\"lib/risc0-ethereum/contracts/src/IRiscZeroVerifier.sol\":{\"keccak256\":\"0xae73e91e4d2829b0a88176584d876382b4b7540ff07c62cb947cdf9ea43d5cf3\",\"license\":\"Apache-2.0\",\"urls\":[\"bzz-raw://f2dd7da39a6f9b100fc1dcde565d799462cdc7d27722d2265317c3a01619c818\",\"dweb:/ipfs/Qmd3k3kcvrtfkLqVza4jHodbFH8wvq5vmfPtwekBKtPjmz\"]},\"lib/risc0-ethereum/contracts/src/Util.sol\":{\"keccak256\":\"0x230a9a58bb2b0c68d4921ecc3672783f941890a2fa3a238dbe5188cfb7658b82\",\"license\":\"Apache-2.0\",\"urls\":[\"bzz-raw://586cb5296c564761609ca8aaae5b1fbbdb19ab1e174f7197f602572d9e82b70c\",\"dweb:/ipfs/QmWxDmucSfg3Wgdk1rVcheDHwkziTsXWvKpKSCnxtpQBqg\"]},\"lib/risc0-ethereum/contracts/src/steel/Steel.sol\":{\"keccak256\":\"0x20811a302f2bc3e36fb612026319c83cf79c16aa56b822d40bcbe8173d1e9544\",\"license\":\"Apache-2.0\",\"urls\":[\"bzz-raw://ed9f7a15c3be0bdc79217feae9e773d280fcfb44aa450d37c6c891f0b505210f\",\"dweb:/ipfs/QmXHtzvLT2AHHHojJ42epUSBF4HYTaVohSmyZft6KZoVqE\"]},\"src/Operator/Operator.sol\":{\"keccak256\":\"0x1486ed6af6f40f0e045403ac9e35bc045ae7031d9ecac7d1f0358f78ff83b469\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://e136048d46ae9b4384f561e16dfd4638e68db987007d2059aaf19011dcfc8f65\",\"dweb:/ipfs/QmcyrVa45W2qYtd9UuJz9C89M3xKzKzACDQqVMAk4hvPeq\"]},\"src/Operator/OperatorStorage.sol\":{\"keccak256\":\"0x3d9e7f2bc64ab9b5de39d4a58f0a72ac0f86c3b95a6318dd03a7b8acde166783\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://dc0d5e026e00f5f5f92cded7704bb602b06f387370ddd7ee0d75111099b01f3a\",\"dweb:/ipfs/QmQfZzADQpBuvTHJpbHz6P4fGhaGWvQgkU8qMkKULCLdjQ\"]},\"src/Roles.sol\":{\"keccak256\":\"0xaa5bbf73d5d2ba654b7f0fe960ae9e17282d959fc559d09c2222b78dad1911b0\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://e683df4c25f2f8f725915d68e8ee9e0871c0d93ad592b0ca985b1829e460cabc\",\"dweb:/ipfs/QmTsQgUKXCtzucNF9DSq1wKVfeqSUeoKPMFCkM8hNLxTmV\"]},\"src/blacklister/Blacklister.sol\":{\"keccak256\":\"0xfa0b52f7467330267cf6ed2d17615b56a75a8a477a5a6c7670ebd0f4636a4e7c\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://d4b9df3910bdba55615acdf7fbef0772b4a7746d9f5f2c01b3ac41b42bd5d6c4\",\"dweb:/ipfs/QmWbo7tK6A57dQiWzX5rgWuQTXo1EGuTvNmG57A93rJHuA\"]},\"src/interest/JumpRateModelV4.sol\":{\"keccak256\":\"0xa93081b79a997a666c96497334d721b37abfd0f193c1031e9f7f2e1b0823c37b\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://b640c34eedc95215fc74b43637256f52acb95052a946a952402b18516a67f7d5\",\"dweb:/ipfs/QmeSemgHAutRfCAttB1mTQ2AYXQpRj1Eq7haGS8QgnTHUa\"]},\"src/interfaces/IBlacklister.sol\":{\"keccak256\":\"0x47ed20135507a9d16473027cbee18de5d952846bada761c6248b91c75cd88be1\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://91a886b95dd578156f9b6cec3236e4751aae8a38e5d15e0d6045217c6fbd3d63\",\"dweb:/ipfs/QmRNjz7sGTuMkQNZhwwcg5DNN2eeqTZSAdZmxWxbx66CHU\"]},\"src/interfaces/IGasFeesHelper.sol\":{\"keccak256\":\"0xb8d36f15aea38582370a4660ff72bba857a7f0050107b52e20f0171ba027ba2b\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://2db8bf066e2a10e0c49f839a577ed2bc3a5d07632ab646878fdba56a322fb7c0\",\"dweb:/ipfs/QmQWHYawhBzmKb7WM5TxyyZJBw6MerMUBu2UjoHj3uPK6d\"]},\"src/interfaces/IInterestRateModel.sol\":{\"keccak256\":\"0xdc8386be37a06d93fb0967be32e465f3b1f941ec9ce2f90984a5c70488cddb1e\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://de2bf96a7d0945f58b00e9fc867ac00fc4a09480e9e27c1a800604295320b769\",\"dweb:/ipfs/Qmf5sAgA58GjVxeipJNhP4euxwsZ9FS9CgsWTJ319UmCUE\"]},\"src/interfaces/IOperator.sol\":{\"keccak256\":\"0x0ae3d63eb07518da5a4fd6de7b013b6f55c87defb31900548378cffc70a51f72\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://1d9710c4ac43cb0563449aa519501b4ed18a331c90d0bc7eb79cdc56407bad8a\",\"dweb:/ipfs/Qmafe9Wup2GF9pD83CLWUrRait93vwvReqU1C7EHQhY7WV\"]},\"src/interfaces/IOracleOperator.sol\":{\"keccak256\":\"0xcc2848c75dd5e1531c2b958746cdd76a3b2b329be98fdca4311219a12a6b409e\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://337bdabf03ac279d95ba96aca60105932c7af5c15fdb4b21ab1c369509310fbc\",\"dweb:/ipfs/QmNYrNYNUwWWSrPwHdarhxt1ShdH1dy8B94M49TZ7XvmK1\"]},\"src/interfaces/IPauser.sol\":{\"keccak256\":\"0x8c72972618419ab401a13bd0ca2ecaf299ee91e2462b704d87bd6e99e933234c\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://9055675921383a76b4659a1b364a355618480963254b847d870697b829e2e31c\",\"dweb:/ipfs/Qma2xdDddgxjp8qs13WfU8aCFjoVMyJNxvBmo5Zgr87yGZ\"]},\"src/interfaces/IRewardDistributor.sol\":{\"keccak256\":\"0xd39c4db6e2a85e90db82e20685d4b235966d993c3407e1c0fb52add6421b86df\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://e865fe1569ec1ac79cd924a9aa2e2b43754555e510526f6cd030395e9923f01d\",\"dweb:/ipfs/QmXcshgzCxLKBZJYGuQviEamruhmwXWNJ3ZYLxazWBahuc\"]},\"src/interfaces/IRoles.sol\":{\"keccak256\":\"0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c\",\"dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj\"]},\"src/interfaces/ImErc20.sol\":{\"keccak256\":\"0x86a1dcaf0d188107ceae7c5eb0911ac63feafbbeda6d0686e4f9950a51d17888\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://d7dd8e0fcfd68501d9f40e37e1c95d8cb653d65626aeeeb5492b73c0e967e6cb\",\"dweb:/ipfs/QmcadG9QuHtAX5qxTKzNr6FMaXSgijr542tFUVTLBkYhAF\"]},\"src/interfaces/ImErc20Host.sol\":{\"keccak256\":\"0x90f1ba59e63b0bd8d11deb1154bb885906daf858e81ff3eca579db73281a1577\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://4202334b0825195dc2d50cf29420f4bcebdf1d91ebcadc56155d92243f133c11\",\"dweb:/ipfs/QmVrcpP8YcTHNUCGJQCeBMUZU9VMpvsvUfVwN14pVzkD5o\"]},\"src/interfaces/ImToken.sol\":{\"keccak256\":\"0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36\",\"dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL\"]},\"src/interfaces/ImTokenGateway.sol\":{\"keccak256\":\"0x7b47f5229cd07de60af49eb473eddc27e429abd846df81001ef05af2c3d47634\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://04d8c52462eeb19a4c28980ac5c81a901fd35982dc47c0ab95a4ee6466b150c1\",\"dweb:/ipfs/QmfTyZrLWMnWLU6zo85RVwjv5W2qXVQPkLWzru5zcBarYm\"]},\"src/interfaces/external/poh/IPohVerifier.sol\":{\"keccak256\":\"0xce93e2d734074add2a6d36867d59fd24e2d44ca48c524bd8dddabd4163038205\",\"license\":\"AGPL-3.0\",\"urls\":[\"bzz-raw://9e94dc14721e8873d15d7032afa3d2b5fea76e9c4029698aa489f2ea2edcedf6\",\"dweb:/ipfs/QmR55sQx36Ub7wUjDeemUSNotdmYCkK8AmUEYbRezF5Xhy\"]},\"src/libraries/BytesLib.sol\":{\"keccak256\":\"0xfaf080296887274c71c2f2eb3552c4742c96ff24616c61f8ab98399f30b18ef0\",\"license\":\"Unlicense\",\"urls\":[\"bzz-raw://02dcb01b6c9cf4f158792a15e9b40d046332949cd6b2c1e5e54e474901cf579b\",\"dweb:/ipfs/QmWiBhygucYoY8Uu6WQPao2r64Y25Vmmj3gYyZZ6Wy9wnE\"]},\"src/libraries/CommonLib.sol\":{\"keccak256\":\"0x623d4c8f1f699570493de557fe93b355a662ca6caab81f4c84b02777fd7d31d8\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://866dc2b2215ae0dae498833b8d647d0c8b011d9086eeadc8fd6acfac2191c232\",\"dweb:/ipfs/QmRwNuVDFD4wjo4CjgMaJJKQ8yZU3PgS6L3k6NkCJnwyNZ\"]},\"src/libraries/mTokenProofDecoderLib.sol\":{\"keccak256\":\"0x00216e7389b2d64450d9d13b648f80e459742e1dd91dec543d415df920f8ce71\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://660deae410969bca243a7b8ad2e6ca6d1cb11d3647af0e227355e5b74f949ea0\",\"dweb:/ipfs/QmUmynqsY1kdEoWHNwqHEVBedepdDBaNHotAP7CCQ7PBzN\"]},\"src/mToken/extension/mTokenGateway.sol\":{\"keccak256\":\"0x3032b6ebde1f45e840a58637b38a34fda2464322a9fce0455c51160fe6b7578a\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://09f97ecce05268a85fcedf34de77d6623bd07cd743d28891aeec2716ab254163\",\"dweb:/ipfs/QmT87wtFZLjku1Lg7Fn4VYp52ubpsoYxUnEyccHmX7n1Ex\"]},\"src/mToken/host/mErc20Host.sol\":{\"keccak256\":\"0x65a79ff94abf90ab7cb2720702872324049bc70f668987fdc9affa0cb5a83e3e\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://7e31aeb1b939cff561cb07e924df3e43c62a93654f742d5d4f3e45d98f8ec586\",\"dweb:/ipfs/QmXPzSvPLYf2WVrfkuxuuSyt2AZw7b8efiz1KrtgxF4oJG\"]},\"src/mToken/mErc20.sol\":{\"keccak256\":\"0x46aa77f6808c1ca56c7d51b4822bc03d26d036151b4eeb324a3dbdc52bc90643\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://4681c86b025cb7d0872c003146b4e6d56b2e86387c832134f4224fc9d559218c\",\"dweb:/ipfs/QmWM9zVEmNWYSYEvGPd8jwSioG8zBC3hQnCEHbup1gCCLe\"]},\"src/mToken/mErc20Upgradable.sol\":{\"keccak256\":\"0x52fb217ef3aff11698af9e57f237fa449ca536a072c5fa3b3fcb5206ff3d7a99\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://4077d30ef391bb5d4c2a3bab863e8e5db105c4f05d1a80c382c34ea83ebed431\",\"dweb:/ipfs/QmXC7ofa9Nvyqgw2H9TLQqkCoD4ThT6SAx8CZ2GP2rRJft\"]},\"src/mToken/mToken.sol\":{\"keccak256\":\"0xeefa3394ae7a01c38bc97404ca6375a497e0bce2a8ae3f83feb4c5bd681aaf43\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://cd94a416031e33c169d36692a661899dc9697b8c543bdb8c051f70a3e7eced2c\",\"dweb:/ipfs/QmRBoo7LQ5nbwLpBjB9yEEib8RM5i9yQjfeE7FELHXvBBk\"]},\"src/mToken/mTokenConfiguration.sol\":{\"keccak256\":\"0x4ac1b80b93e3b13ce568f859c1fc4dd54360578d6055027819fee3e6d0b2b83f\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://e27a83d36a038105631a1ce53f92d225b9949a2cc6aed6d898c736850c10676a\",\"dweb:/ipfs/QmZhvhCfZsDxvyYJ1x9TNSfnHKrxpbgLq3qr7hJiL3XpZa\"]},\"src/mToken/mTokenStorage.sol\":{\"keccak256\":\"0x42236cd9e37c2eef2d317f05510bbe21cf5174813ee40f9a6fcbaf84d4fed2f7\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://2679166e1ca0e5d21294447566bba7549900a8f9a9b1882c8c2895a9751a1792\",\"dweb:/ipfs/Qmb49FVvre1He18e6ABHjNJ2yzCPdkYeyzSwMXixZ7fUw3\"]},\"src/migration/IMigrator.sol\":{\"keccak256\":\"0xbb7b40994ce7362a28dc4997d11105d3f5a904f26432f735ff68622d779341a8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://091dee95ab811def8446c4a5768b3caafe5504c5d16955fc0adb76ee6140198b\",\"dweb:/ipfs/QmNqVbBQ7QuN1NgtFiZVSPtwq5UnBAKes9TxC9fgBueNvy\"]},\"src/migration/Migrator.sol\":{\"keccak256\":\"0xa34d08708ab2976389b2f8f5255869440d47e2f7f9849eafd6e7221defc64720\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://52331e338f89658fad9ac954e9c9ce274418056645b42d144ba215c96e9a402e\",\"dweb:/ipfs/Qmc4VzufbwwAuJbqkWWs2V11hodnkiTK4Q5NZFAEctEhNQ\"]},\"src/pauser/Pauser.sol\":{\"keccak256\":\"0xa72ca5608e352415254971a1dd694faeb40488832e202d9162f95af7d41ddeb0\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://b3f14fa24b998c229e7751bdbdba8f82f7893100b836afefdc24bc2cfd6dfb99\",\"dweb:/ipfs/QmfCxQwg1Tf3wWsaG6FcMSV3X7zLf8Cfs3UKgxQcLGzJmY\"]},\"src/rewards/RewardDistributor.sol\":{\"keccak256\":\"0x8d5c3e5e5050121d4f1310479a2cadde7dc97ff8a57115021cafb2032aaf50c2\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://e200d18803bb0680b3007c79c23a8a7e31f054ce6e4f36c376f7b43419679322\",\"dweb:/ipfs/QmV7CRA5HSB89fwCVF7VWGArZnWoH2BB1heXu9SaMkbL9H\"]},\"src/utils/ExponentialNoError.sol\":{\"keccak256\":\"0xa97178117d8c32059d55dd2224e5514c54b31f896e565c3834cab3d7502b1bb6\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://1cecc0713709f57a5bb7f64d2fa06b9437a42752fa3fed5d3c0b19d82aa56ef4\",\"dweb:/ipfs/QmQqBCZkzmZxomXLr3b1tHUV55n8EPk3wAUsXgojYSpR41\"]},\"src/verifier/ZkVerifier.sol\":{\"keccak256\":\"0x1abaa285ad4ee09461aae5df4aecff9bd38b2b3da19b5a920ab1b7d2208e04ec\",\"license\":\"AGPL-3.0\",\"urls\":[\"bzz-raw://7db45391afa7244f70303c5ace73b7937ee30261fd2015e4a7bfa11ec85480cc\",\"dweb:/ipfs/QmTdhCwcbTCRCMF1ifCJoifqwxhKpKBEE8zq9ph6hYhogG\"]},\"test/Base_Unit_Test.t.sol\":{\"keccak256\":\"0x2f61ec9614bbfeed1c1f8555f20fb0d4c584dc3452369b53417ab3fba144f330\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://0f7a32bd42554d42a3062092b80a6a0fb90bea17a3cad2c12863688b811e6b4c\",\"dweb:/ipfs/QmZNFWWTyTFEjSErQAErkmEdzAPf2Eyeb2ir4fgRqQ5bKJ\"]},\"test/mocks/ERC20Mock.sol\":{\"keccak256\":\"0x86e2d4e622d3172aff95ad8436b1af1213c7be008fbbe1e74f29577ea0afd92f\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://2e2a534d2bf62fd7be511ac74e372b1d1e4cc08033fba742950785315553e3fb\",\"dweb:/ipfs/Qme24GSF4BFzuHETCfPzUafV1bsmVHVvbqQPNxDoU8AafR\"]},\"test/mocks/OracleMock.sol\":{\"keccak256\":\"0xb749ba9d217b9aedc9010690022b31672eeacf0927f43efe9769ae886f4ce855\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://5167ca5dcd31bc0ae6dcc22e43276e8f746b018d7d9c2edc332d1160024f6328\",\"dweb:/ipfs/QmVdgBmaBntj9TMV4z65bga1uuN2YkfUbLNN9Hsmvu4x6f\"]},\"test/mocks/Risc0VerifierMock.sol\":{\"keccak256\":\"0x3f6f728dcdcffec55a6c869fdf9e69a0463cccd07ed7784e5ab3dbee0058df85\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://f024f438e89b4698292d0b6a61485594f4ed737af80c25fa36bf862211f16732\",\"dweb:/ipfs/QmZXYmyVyHVHAyZ5PENfhoRphASQGb5WrtsdQN7XyMbGEm\"]},\"test/unit/shared/Pauser_Unit_Shared.t.sol\":{\"keccak256\":\"0x82f89bae96681d85bbe653ce26b745601be28943a4f453a687ea48da94112541\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://706f6cba381185dd3f8236987632b47510112c87fd4091fdcdcb92b59a21f0e9\",\"dweb:/ipfs/QmPxbJEmwSCpYVbKL7Sbd7fZe7G6KPZvCL21PU6AtQhMqh\"]},\"test/utils/Constants.sol\":{\"keccak256\":\"0xa2611aa14c45b8ea8b276aacad47d78f33908fab8c6ed0ff35cef76fd41c695b\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://f60db39c1ce5c7260361664fec9d731d3ba329055a0810d83491ce54ad7d2a01\",\"dweb:/ipfs/QmSzv3VXBa6q6bowzAfZ4Afcp4UWwGUKJFB72xV6MYyCNn\"]},\"test/utils/Events.sol\":{\"keccak256\":\"0xb0b41707dca3af9d783239cb5c96a2e9347e03b5529c944565ac9de2f33ae82a\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://e8fad0826e747465c9208ad6a7d52cd50205972cfea7fa8a206be750cf1e8a80\",\"dweb:/ipfs/QmR5mWoVf2ZcETLJVuCMHvWPBfQ3CNxD8Gx8Endms5AwmR\"]},\"test/utils/Helpers.sol\":{\"keccak256\":\"0xa59b1e23b76c632e72c93dbd612c9279b2cad6d8915c31c04e62af0d46becf4d\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://2a7d815eeebeea85ec405483ec6d55a61f1a984d68d3a6020d3444915aa6610c\",\"dweb:/ipfs/QmQ6qWmTAdWnnursoU4F2pYCa3tpTtS2qjPFht1kWT2KDT\"]},\"test/utils/Types.sol\":{\"keccak256\":\"0x696166d23b74196cb6a66bbd72f25024bb251be99ab2a6d8c9ba86f5b47f22d6\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://a302c977aea4ccbc54de408575dd5b52b00b9d62512da0d7eb71edb46eff1366\",\"dweb:/ipfs/QmUjRq9fjukqZL59ABU2Xp6KfR21sPvdBVcWWzjrMLxpzP\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "string", "name": "", "type": "string", "indexed": false}], "type": "event", "name": "log", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "", "type": "address", "indexed": false}], "type": "event", "name": "log_address", "anonymous": false}, {"inputs": [{"internalType": "uint256[]", "name": "val", "type": "uint256[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "int256[]", "name": "val", "type": "int256[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "address[]", "name": "val", "type": "address[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes", "indexed": false}], "type": "event", "name": "log_bytes", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32", "indexed": false}], "type": "event", "name": "log_bytes32", "anonymous": false}, {"inputs": [{"internalType": "int256", "name": "", "type": "int256", "indexed": false}], "type": "event", "name": "log_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "address", "name": "val", "type": "address", "indexed": false}], "type": "event", "name": "log_named_address", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256[]", "name": "val", "type": "uint256[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256[]", "name": "val", "type": "int256[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "address[]", "name": "val", "type": "address[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "bytes", "name": "val", "type": "bytes", "indexed": false}], "type": "event", "name": "log_named_bytes", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "bytes32", "name": "val", "type": "bytes32", "indexed": false}], "type": "event", "name": "log_named_bytes32", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256", "name": "val", "type": "int256", "indexed": false}, {"internalType": "uint256", "name": "decimals", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_decimal_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256", "name": "val", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "decimals", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_decimal_uint", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256", "name": "val", "type": "int256", "indexed": false}], "type": "event", "name": "log_named_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "string", "name": "val", "type": "string", "indexed": false}], "type": "event", "name": "log_named_string", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256", "name": "val", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_uint", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "", "type": "string", "indexed": false}], "type": "event", "name": "log_string", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256", "indexed": false}], "type": "event", "name": "log_uint", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes", "indexed": false}], "type": "event", "name": "logs", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "ALICE_KEY", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "BOB_KEY", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "DEFAULT_COLLATERAL_FACTOR", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "DEFAULT_INFLATION_INCREASE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "DEFAULT_LIQUIDATOR_ORACLE_PRICE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "DEFAULT_ORACLE_PRICE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "DEFAULT_ORACLE_PRICE36", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "FOO_KEY", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "IS_TEST", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "LARGE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "LINEA_CHAIN_ID", "outputs": [{"internalType": "uint32", "name": "", "type": "uint32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "MEDIUM", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "SMALL", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "ZERO_ADDRESS", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "ZERO_VALUE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "alice", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "blacklister", "outputs": [{"internalType": "contract Blacklister", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "bob", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "dai", "outputs": [{"internalType": "contract ERC20Mock", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeArtifacts", "outputs": [{"internalType": "string[]", "name": "excludedArtifacts_", "type": "string[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeContracts", "outputs": [{"internalType": "address[]", "name": "excludedContracts_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzSelector[]", "name": "excludedSelectors_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeSenders", "outputs": [{"internalType": "address[]", "name": "excludedSenders_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "failed", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "foo", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "interestModel", "outputs": [{"internalType": "contract JumpRateModelV4", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "mWethExtension", "outputs": [{"internalType": "contract mTokenGateway", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "mWethHost", "outputs": [{"internalType": "contract mErc20Host", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "operator", "outputs": [{"internalType": "contract Operator", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "oracleOperator", "outputs": [{"internalType": "contract OracleMock", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "pauser", "outputs": [{"internalType": "contract Pauser", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "rewards", "outputs": [{"internalType": "contract RewardDistributor", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "roles", "outputs": [{"internalType": "contract Roles", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "setUp"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetArtifactSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzArtifactSelector[]", "name": "targetedArtifactSelectors_", "type": "tuple[]", "components": [{"internalType": "string", "name": "artifact", "type": "string"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetArtifacts", "outputs": [{"internalType": "string[]", "name": "targetedArtifacts_", "type": "string[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetContracts", "outputs": [{"internalType": "address[]", "name": "targetedContracts_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetInterfaces", "outputs": [{"internalType": "struct StdInvariant.FuzzInterface[]", "name": "targetedInterfaces_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "string[]", "name": "artifacts", "type": "string[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzSelector[]", "name": "targetedSelectors_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetSenders", "outputs": [{"internalType": "address[]", "name": "targetedSenders_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "usdc", "outputs": [{"internalType": "contract ERC20Mock", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "verifierMock", "outputs": [{"internalType": "contract Risc0VerifierMock", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "weth", "outputs": [{"internalType": "contract ERC20Mock", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "zkVerifier", "outputs": [{"internalType": "contract ZkVerifier", "name": "", "type": "address"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"test/unit/shared/Pauser_Unit_Shared.t.sol": "Pauser_Unit_Shared"}, "evmVersion": "london", "libraries": {}}, "sources": {"lib/forge-std/src/Base.sol": {"keccak256": "0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd", "urls": ["bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d", "dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z"], "license": "MIT"}, "lib/forge-std/src/StdAssertions.sol": {"keccak256": "0x4584f551c3a875c26423c1e50a77179bc2f9c9c1ee6d0f0c6be0f89ca5ee8270", "urls": ["bzz-raw://ef118876a862b60ba8a6612b8b3f3a6c226e11a0071a2e9695f021586d1bfdbe", "dweb:/ipfs/QmXd2vD91PkvoFWasFkgbDi89PctmgsHVxGvGNyhjmRK2b"], "license": "MIT"}, "lib/forge-std/src/StdChains.sol": {"keccak256": "0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e", "urls": ["bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351", "dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm"], "license": "MIT"}, "lib/forge-std/src/StdCheats.sol": {"keccak256": "0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746", "urls": ["bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41", "dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK"], "license": "MIT"}, "lib/forge-std/src/StdConstants.sol": {"keccak256": "0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534", "urls": ["bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc", "dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r"], "license": "MIT"}, "lib/forge-std/src/StdError.sol": {"keccak256": "0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77", "urls": ["bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6", "dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj"], "license": "MIT"}, "lib/forge-std/src/StdInvariant.sol": {"keccak256": "0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d", "urls": ["bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391", "dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5"], "license": "MIT"}, "lib/forge-std/src/StdJson.sol": {"keccak256": "0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500", "urls": ["bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974", "dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3"], "license": "MIT"}, "lib/forge-std/src/StdMath.sol": {"keccak256": "0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2", "urls": ["bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92", "dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC"], "license": "MIT"}, "lib/forge-std/src/StdStorage.sol": {"keccak256": "0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc", "urls": ["bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57", "dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ"], "license": "MIT"}, "lib/forge-std/src/StdStyle.sol": {"keccak256": "0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d", "urls": ["bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8", "dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK"], "license": "MIT"}, "lib/forge-std/src/StdToml.sol": {"keccak256": "0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861", "urls": ["bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3", "dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8"], "license": "MIT"}, "lib/forge-std/src/StdUtils.sol": {"keccak256": "0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737", "urls": ["bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138", "dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776"], "license": "MIT"}, "lib/forge-std/src/Test.sol": {"keccak256": "0x3dda6083a83dfa3e8526e97bcc28e862ee2442dd58fe94d5c426d65b8e38f73c", "urls": ["bzz-raw://33f8c02e4dabdab86a6825125856446a8657eacd712318b51b7818e4a6f6e3f4", "dweb:/ipfs/QmRKSjVnrk54yr8wTK2e6QxRjiuba2H8HJSKunHAkdo7RG"], "license": "MIT"}, "lib/forge-std/src/Vm.sol": {"keccak256": "0xaae88142a348064677cf81169ea0ca706be3e99e2a0f13a01878ff8d52cf6b25", "urls": ["bzz-raw://da4fb0ed1b31381c7e60ee386acc72796e8a8325c83739ec9a3678e2f841073c", "dweb:/ipfs/Qmb4MU6FpAi8Wt5kUQ3wa7ooHnfMVbdd9zuvnqbTehLmxJ"], "license": "MIT OR Apache-2.0"}, "lib/forge-std/src/console.sol": {"keccak256": "0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5", "urls": ["bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57", "dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP"], "license": "MIT"}, "lib/forge-std/src/console2.sol": {"keccak256": "0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f", "urls": ["bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d", "dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ"], "license": "MIT"}, "lib/forge-std/src/interfaces/IMulticall3.sol": {"keccak256": "0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a", "urls": ["bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0", "dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2"], "license": "MIT"}, "lib/forge-std/src/safeconsole.sol": {"keccak256": "0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11", "urls": ["bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab", "dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol": {"keccak256": "0xc163fcf9bb10138631a9ba5564df1fa25db9adff73bd9ee868a8ae1858fe093a", "urls": ["bzz-raw://9706d43a0124053d9880f6e31a59f31bc0a6a3dc1acd66ce0a16e1111658c5f6", "dweb:/ipfs/QmUFmfowzkRwGtDu36cXV9SPTBHJ3n7dG9xQiK5B28jTf2"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol": {"keccak256": "0x631188737069917d2f909d29ce62c4d48611d326686ba6683e26b72a23bfac0b", "urls": ["bzz-raw://7a61054ae84cd6c4d04c0c4450ba1d6de41e27e0a2c4f1bcdf58f796b401c609", "dweb:/ipfs/QmUvtdp7X1mRVyC3CsHrtPbgoqWaXHp3S1ZR24tpAQYJWM"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol": {"keccak256": "0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397", "urls": ["bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9", "dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/ReentrancyGuardUpgradeable.sol": {"keccak256": "0xb44e086e941292cdc7f440de51478493894ef0b1aeccb0c4047445919f667f74", "urls": ["bzz-raw://942dad22fbdc1669f025540ba63aa3ccfad5f8458fc5d4525b31ebf272e7af45", "dweb:/ipfs/Qmdo4X2M82aM3AMoW2kf2jhYkSCyC4T1pHNd6obdsDFnAB"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/access/Ownable.sol": {"keccak256": "0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb", "urls": ["bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6", "dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol": {"keccak256": "0x60c65f701957fdd6faea1acb0bb45825791d473693ed9ecb34726fdfaa849dd7", "urls": ["bzz-raw://ea290300e0efc4d901244949dc4d877fd46e6c5e43dc2b26620e8efab3ab803f", "dweb:/ipfs/QmcLLJppxKeJWqHxE2CUkcfhuRTgHSn8J4kijcLa5MYhSt"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol": {"keccak256": "0xbfb6695731de677140fbf76c772ab08c4233a122fb51ac28ac120fc49bbbc4ec", "urls": ["bzz-raw://68f8fded7cc318efa15874b7c6a983fe17a4a955d72d240353a9a4ca1e1b824c", "dweb:/ipfs/QmdcmBL9Qo4Tk3Dby4wFYabGyot9JNeLPxpSXZUgUm92BV"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Utils.sol": {"keccak256": "0x06a78f9b3ee3e6d0eb4e4cd635ba49960bea34cac1db8c0a27c75f2319f1fd65", "urls": ["bzz-raw://547d21aa17f4f3f1a1a7edf7167beff8dd9496a0348d5588f15cc8a4b29d052a", "dweb:/ipfs/QmT16JtRQSWNpLo9W23jr6CzaMuTAcQcjJJcdRd8HLJ6cE"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol": {"keccak256": "0xc3f2ec76a3de8ed7a7007c46166f5550c72c7709e3fc7e8bb3111a7191cdedbd", "urls": ["bzz-raw://e73efb4c2ca655882dc237c6b4f234a9bd36d97159d8fcaa837eb01171f726ac", "dweb:/ipfs/QmTNnnv7Gu5fs5G1ZMh7Fexp8N4XUs3XrNAngjcxgiss3e"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol": {"keccak256": "0xc59a78b07b44b2cf2e8ab4175fca91e8eca1eee2df7357b8d2a8833e5ea1f64c", "urls": ["bzz-raw://5aa4f07e65444784c29cd7bfcc2341b34381e4e5b5da9f0c5bd00d7f430e66fa", "dweb:/ipfs/QmWRMh4Q9DpaU9GvsiXmDdoNYMyyece9if7hnfLz7uqzWM"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol": {"keccak256": "0xc3e1fa9d1987f8d349dfb4d6fe93bf2ca014b52ba335cfac30bfe71e357e6f80", "urls": ["bzz-raw://c5703ccdeb7b1d685e375ed719117e9edf2ab4bc544f24f23b0d50ec82257229", "dweb:/ipfs/QmTdwkbQq7owpCiyuzE7eh5LrD2ddrBCZ5WHVsWPi1RrTS"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0xc6a8ff0ea489379b61faa647490411b80102578440ab9d84e9a957cc12164e70", "urls": ["bzz-raw://0ea104e577e63faea3b69c415637e99e755dcbf64c5833d7140c35a714d6d90c", "dweb:/ipfs/Qmau6x4Ns9XdyynRCNNp3RhLqijJjFm7z5fyZazfYFGYdq"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0xaa761817f6cd7892fcf158b3c776b34551cde36f48ff9703d53898bc45a94ea2", "urls": ["bzz-raw://0ad7c8d4d08938c8dfc43d75a148863fb324b80cf53e0a36f7e5a4ac29008850", "dweb:/ipfs/QmcrhfPgVNf5mkdhQvy1pMv51TFokD3Y4Wa5WZhFqVh8UV"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol": {"keccak256": "0x6008dabfe393240d73d7dd7688033f72740d570aa422254d29a7dce8568f3aff", "urls": ["bzz-raw://f5196ec75139918c6c7bb4251b36395e668f1fa6d206beba7e7520e74913940d", "dweb:/ipfs/QmSyqjksXxmm2mCG6qRd1yuwLykypkSVBbnBnGqJRcuJMi"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol": {"keccak256": "0x37bb49513c49c87c4642a891b13b63571bc87013dde806617aa1efb54605f386", "urls": ["bzz-raw://b3036b3a83b7c48f96641f2a9002b9f2dcb6a5958dd670894ada21ae8229b3d0", "dweb:/ipfs/QmUNfSBdoVtjhETaUJCYcaC7pTMgbhht926tJ2uXJbiVd3"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Address.sol": {"keccak256": "0xaf28a975a78550e45f65e559a3ad6a5ad43b9b8a37366999abd1b7084eb70721", "urls": ["bzz-raw://b7bd24e224f67f65bfadf85dc2929fa965456bb2415478bd0125471b5ce35245", "dweb:/ipfs/QmRaydGr8BTHs1kvaZfsNU69pKzUAGFrvABn1KiRSbE51y"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/ReentrancyGuard.sol": {"keccak256": "0xf980daa263b661ab8ddee7d4fd833c7da7e7995e2c359ff1f17e67e4112f2236", "urls": ["bzz-raw://7448ab095d6940130bcf76ba47a2eab14148c83119523b93dd89f6d84edd6c02", "dweb:/ipfs/QmawrZ4voKQjH3oomXT3Kuheb3Mnmo2VvVpxg8Ne5UJUrd"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol": {"keccak256": "0x32ba59b4b7299237c8ba56319110989d7978a039faf754793064e967e5894418", "urls": ["bzz-raw://1ae50c8b562427df610cc4540c9bf104acca7ef8e2dcae567ae7e52272281e9c", "dweb:/ipfs/QmTHiadFCSJUPpRjNegc5SahmeU8bAoY8i9Aq6tVscbcKR"], "license": "MIT"}, "lib/risc0-ethereum/contracts/src/IRiscZeroVerifier.sol": {"keccak256": "0xae73e91e4d2829b0a88176584d876382b4b7540ff07c62cb947cdf9ea43d5cf3", "urls": ["bzz-raw://f2dd7da39a6f9b100fc1dcde565d799462cdc7d27722d2265317c3a01619c818", "dweb:/ipfs/Qmd3k3kcvrtfkLqVza4jHodbFH8wvq5vmfPtwekBKtPjmz"], "license": "Apache-2.0"}, "lib/risc0-ethereum/contracts/src/Util.sol": {"keccak256": "0x230a9a58bb2b0c68d4921ecc3672783f941890a2fa3a238dbe5188cfb7658b82", "urls": ["bzz-raw://586cb5296c564761609ca8aaae5b1fbbdb19ab1e174f7197f602572d9e82b70c", "dweb:/ipfs/QmWxDmucSfg3Wgdk1rVcheDHwkziTsXWvKpKSCnxtpQBqg"], "license": "Apache-2.0"}, "lib/risc0-ethereum/contracts/src/steel/Steel.sol": {"keccak256": "0x20811a302f2bc3e36fb612026319c83cf79c16aa56b822d40bcbe8173d1e9544", "urls": ["bzz-raw://ed9f7a15c3be0bdc79217feae9e773d280fcfb44aa450d37c6c891f0b505210f", "dweb:/ipfs/QmXHtzvLT2AHHHojJ42epUSBF4HYTaVohSmyZft6KZoVqE"], "license": "Apache-2.0"}, "src/Operator/Operator.sol": {"keccak256": "0x1486ed6af6f40f0e045403ac9e35bc045ae7031d9ecac7d1f0358f78ff83b469", "urls": ["bzz-raw://e136048d46ae9b4384f561e16dfd4638e68db987007d2059aaf19011dcfc8f65", "dweb:/ipfs/QmcyrVa45W2qYtd9UuJz9C89M3xKzKzACDQqVMAk4hvPeq"], "license": "BSL-1.1"}, "src/Operator/OperatorStorage.sol": {"keccak256": "0x3d9e7f2bc64ab9b5de39d4a58f0a72ac0f86c3b95a6318dd03a7b8acde166783", "urls": ["bzz-raw://dc0d5e026e00f5f5f92cded7704bb602b06f387370ddd7ee0d75111099b01f3a", "dweb:/ipfs/QmQfZzADQpBuvTHJpbHz6P4fGhaGWvQgkU8qMkKULCLdjQ"], "license": "BSL-1.1"}, "src/Roles.sol": {"keccak256": "0xaa5bbf73d5d2ba654b7f0fe960ae9e17282d959fc559d09c2222b78dad1911b0", "urls": ["bzz-raw://e683df4c25f2f8f725915d68e8ee9e0871c0d93ad592b0ca985b1829e460cabc", "dweb:/ipfs/QmTsQgUKXCtzucNF9DSq1wKVfeqSUeoKPMFCkM8hNLxTmV"], "license": "BSL-1.1"}, "src/blacklister/Blacklister.sol": {"keccak256": "0xfa0b52f7467330267cf6ed2d17615b56a75a8a477a5a6c7670ebd0f4636a4e7c", "urls": ["bzz-raw://d4b9df3910bdba55615acdf7fbef0772b4a7746d9f5f2c01b3ac41b42bd5d6c4", "dweb:/ipfs/QmWbo7tK6A57dQiWzX5rgWuQTXo1EGuTvNmG57A93rJHuA"], "license": "BSL-1.1"}, "src/interest/JumpRateModelV4.sol": {"keccak256": "0xa93081b79a997a666c96497334d721b37abfd0f193c1031e9f7f2e1b0823c37b", "urls": ["bzz-raw://b640c34eedc95215fc74b43637256f52acb95052a946a952402b18516a67f7d5", "dweb:/ipfs/QmeSemgHAutRfCAttB1mTQ2AYXQpRj1Eq7haGS8QgnTHUa"], "license": "BSL-1.1"}, "src/interfaces/IBlacklister.sol": {"keccak256": "0x47ed20135507a9d16473027cbee18de5d952846bada761c6248b91c75cd88be1", "urls": ["bzz-raw://91a886b95dd578156f9b6cec3236e4751aae8a38e5d15e0d6045217c6fbd3d63", "dweb:/ipfs/QmRNjz7sGTuMkQNZhwwcg5DNN2eeqTZSAdZmxWxbx66CHU"], "license": "BSL-1.1"}, "src/interfaces/IGasFeesHelper.sol": {"keccak256": "0xb8d36f15aea38582370a4660ff72bba857a7f0050107b52e20f0171ba027ba2b", "urls": ["bzz-raw://2db8bf066e2a10e0c49f839a577ed2bc3a5d07632ab646878fdba56a322fb7c0", "dweb:/ipfs/QmQWHYawhBzmKb7WM5TxyyZJBw6MerMUBu2UjoHj3uPK6d"], "license": "BSL-1.1"}, "src/interfaces/IInterestRateModel.sol": {"keccak256": "0xdc8386be37a06d93fb0967be32e465f3b1f941ec9ce2f90984a5c70488cddb1e", "urls": ["bzz-raw://de2bf96a7d0945f58b00e9fc867ac00fc4a09480e9e27c1a800604295320b769", "dweb:/ipfs/Qmf5sAgA58GjVxeipJNhP4euxwsZ9FS9CgsWTJ319UmCUE"], "license": "BSL-1.1"}, "src/interfaces/IOperator.sol": {"keccak256": "0x0ae3d63eb07518da5a4fd6de7b013b6f55c87defb31900548378cffc70a51f72", "urls": ["bzz-raw://1d9710c4ac43cb0563449aa519501b4ed18a331c90d0bc7eb79cdc56407bad8a", "dweb:/ipfs/Qmafe9Wup2GF9pD83CLWUrRait93vwvReqU1C7EHQhY7WV"], "license": "BSL-1.1"}, "src/interfaces/IOracleOperator.sol": {"keccak256": "0xcc2848c75dd5e1531c2b958746cdd76a3b2b329be98fdca4311219a12a6b409e", "urls": ["bzz-raw://337bdabf03ac279d95ba96aca60105932c7af5c15fdb4b21ab1c369509310fbc", "dweb:/ipfs/QmNYrNYNUwWWSrPwHdarhxt1ShdH1dy8B94M49TZ7XvmK1"], "license": "BSL-1.1"}, "src/interfaces/IPauser.sol": {"keccak256": "0x8c72972618419ab401a13bd0ca2ecaf299ee91e2462b704d87bd6e99e933234c", "urls": ["bzz-raw://9055675921383a76b4659a1b364a355618480963254b847d870697b829e2e31c", "dweb:/ipfs/Qma2xdDddgxjp8qs13WfU8aCFjoVMyJNxvBmo5Zgr87yGZ"], "license": "BSL-1.1"}, "src/interfaces/IRewardDistributor.sol": {"keccak256": "0xd39c4db6e2a85e90db82e20685d4b235966d993c3407e1c0fb52add6421b86df", "urls": ["bzz-raw://e865fe1569ec1ac79cd924a9aa2e2b43754555e510526f6cd030395e9923f01d", "dweb:/ipfs/QmXcshgzCxLKBZJYGuQviEamruhmwXWNJ3ZYLxazWBahuc"], "license": "BSL-1.1"}, "src/interfaces/IRoles.sol": {"keccak256": "0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365", "urls": ["bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c", "dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj"], "license": "BSL-1.1"}, "src/interfaces/ImErc20.sol": {"keccak256": "0x86a1dcaf0d188107ceae7c5eb0911ac63feafbbeda6d0686e4f9950a51d17888", "urls": ["bzz-raw://d7dd8e0fcfd68501d9f40e37e1c95d8cb653d65626aeeeb5492b73c0e967e6cb", "dweb:/ipfs/QmcadG9QuHtAX5qxTKzNr6FMaXSgijr542tFUVTLBkYhAF"], "license": "BSL-1.1"}, "src/interfaces/ImErc20Host.sol": {"keccak256": "0x90f1ba59e63b0bd8d11deb1154bb885906daf858e81ff3eca579db73281a1577", "urls": ["bzz-raw://4202334b0825195dc2d50cf29420f4bcebdf1d91ebcadc56155d92243f133c11", "dweb:/ipfs/QmVrcpP8YcTHNUCGJQCeBMUZU9VMpvsvUfVwN14pVzkD5o"], "license": "BSL-1.1"}, "src/interfaces/ImToken.sol": {"keccak256": "0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f", "urls": ["bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36", "dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL"], "license": "BSL-1.1"}, "src/interfaces/ImTokenGateway.sol": {"keccak256": "0x7b47f5229cd07de60af49eb473eddc27e429abd846df81001ef05af2c3d47634", "urls": ["bzz-raw://04d8c52462eeb19a4c28980ac5c81a901fd35982dc47c0ab95a4ee6466b150c1", "dweb:/ipfs/QmfTyZrLWMnWLU6zo85RVwjv5W2qXVQPkLWzru5zcBarYm"], "license": "BSL-1.1"}, "src/interfaces/external/poh/IPohVerifier.sol": {"keccak256": "0xce93e2d734074add2a6d36867d59fd24e2d44ca48c524bd8dddabd4163038205", "urls": ["bzz-raw://9e94dc14721e8873d15d7032afa3d2b5fea76e9c4029698aa489f2ea2edcedf6", "dweb:/ipfs/QmR55sQx36Ub7wUjDeemUSNotdmYCkK8AmUEYbRezF5Xhy"], "license": "AGPL-3.0"}, "src/libraries/BytesLib.sol": {"keccak256": "0xfaf080296887274c71c2f2eb3552c4742c96ff24616c61f8ab98399f30b18ef0", "urls": ["bzz-raw://02dcb01b6c9cf4f158792a15e9b40d046332949cd6b2c1e5e54e474901cf579b", "dweb:/ipfs/QmWiBhygucYoY8Uu6WQPao2r64Y25Vmmj3gYyZZ6Wy9wnE"], "license": "Unlicense"}, "src/libraries/CommonLib.sol": {"keccak256": "0x623d4c8f1f699570493de557fe93b355a662ca6caab81f4c84b02777fd7d31d8", "urls": ["bzz-raw://866dc2b2215ae0dae498833b8d647d0c8b011d9086eeadc8fd6acfac2191c232", "dweb:/ipfs/QmRwNuVDFD4wjo4CjgMaJJKQ8yZU3PgS6L3k6NkCJnwyNZ"], "license": "BSL-1.1"}, "src/libraries/mTokenProofDecoderLib.sol": {"keccak256": "0x00216e7389b2d64450d9d13b648f80e459742e1dd91dec543d415df920f8ce71", "urls": ["bzz-raw://660deae410969bca243a7b8ad2e6ca6d1cb11d3647af0e227355e5b74f949ea0", "dweb:/ipfs/QmUmynqsY1kdEoWHNwqHEVBedepdDBaNHotAP7CCQ7PBzN"], "license": "BSL-1.1"}, "src/mToken/extension/mTokenGateway.sol": {"keccak256": "0x3032b6ebde1f45e840a58637b38a34fda2464322a9fce0455c51160fe6b7578a", "urls": ["bzz-raw://09f97ecce05268a85fcedf34de77d6623bd07cd743d28891aeec2716ab254163", "dweb:/ipfs/QmT87wtFZLjku1Lg7Fn4VYp52ubpsoYxUnEyccHmX7n1Ex"], "license": "BSL-1.1"}, "src/mToken/host/mErc20Host.sol": {"keccak256": "0x65a79ff94abf90ab7cb2720702872324049bc70f668987fdc9affa0cb5a83e3e", "urls": ["bzz-raw://7e31aeb1b939cff561cb07e924df3e43c62a93654f742d5d4f3e45d98f8ec586", "dweb:/ipfs/QmXPzSvPLYf2WVrfkuxuuSyt2AZw7b8efiz1KrtgxF4oJG"], "license": "BSL-1.1"}, "src/mToken/mErc20.sol": {"keccak256": "0x46aa77f6808c1ca56c7d51b4822bc03d26d036151b4eeb324a3dbdc52bc90643", "urls": ["bzz-raw://4681c86b025cb7d0872c003146b4e6d56b2e86387c832134f4224fc9d559218c", "dweb:/ipfs/QmWM9zVEmNWYSYEvGPd8jwSioG8zBC3hQnCEHbup1gCCLe"], "license": "BSL-1.1"}, "src/mToken/mErc20Upgradable.sol": {"keccak256": "0x52fb217ef3aff11698af9e57f237fa449ca536a072c5fa3b3fcb5206ff3d7a99", "urls": ["bzz-raw://4077d30ef391bb5d4c2a3bab863e8e5db105c4f05d1a80c382c34ea83ebed431", "dweb:/ipfs/QmXC7ofa9Nvyqgw2H9TLQqkCoD4ThT6SAx8CZ2GP2rRJft"], "license": "BSL-1.1"}, "src/mToken/mToken.sol": {"keccak256": "0xeefa3394ae7a01c38bc97404ca6375a497e0bce2a8ae3f83feb4c5bd681aaf43", "urls": ["bzz-raw://cd94a416031e33c169d36692a661899dc9697b8c543bdb8c051f70a3e7eced2c", "dweb:/ipfs/QmRBoo7LQ5nbwLpBjB9yEEib8RM5i9yQjfeE7FELHXvBBk"], "license": "BSL-1.1"}, "src/mToken/mTokenConfiguration.sol": {"keccak256": "0x4ac1b80b93e3b13ce568f859c1fc4dd54360578d6055027819fee3e6d0b2b83f", "urls": ["bzz-raw://e27a83d36a038105631a1ce53f92d225b9949a2cc6aed6d898c736850c10676a", "dweb:/ipfs/QmZhvhCfZsDxvyYJ1x9TNSfnHKrxpbgLq3qr7hJiL3XpZa"], "license": "BSL-1.1"}, "src/mToken/mTokenStorage.sol": {"keccak256": "0x42236cd9e37c2eef2d317f05510bbe21cf5174813ee40f9a6fcbaf84d4fed2f7", "urls": ["bzz-raw://2679166e1ca0e5d21294447566bba7549900a8f9a9b1882c8c2895a9751a1792", "dweb:/ipfs/Qmb49FVvre1He18e6ABHjNJ2yzCPdkYeyzSwMXixZ7fUw3"], "license": "BSL-1.1"}, "src/migration/IMigrator.sol": {"keccak256": "0xbb7b40994ce7362a28dc4997d11105d3f5a904f26432f735ff68622d779341a8", "urls": ["bzz-raw://091dee95ab811def8446c4a5768b3caafe5504c5d16955fc0adb76ee6140198b", "dweb:/ipfs/QmNqVbBQ7QuN1NgtFiZVSPtwq5UnBAKes9TxC9fgBueNvy"], "license": "MIT"}, "src/migration/Migrator.sol": {"keccak256": "0xa34d08708ab2976389b2f8f5255869440d47e2f7f9849eafd6e7221defc64720", "urls": ["bzz-raw://52331e338f89658fad9ac954e9c9ce274418056645b42d144ba215c96e9a402e", "dweb:/ipfs/Qmc4VzufbwwAuJbqkWWs2V11hodnkiTK4Q5NZFAEctEhNQ"], "license": "MIT"}, "src/pauser/Pauser.sol": {"keccak256": "0xa72ca5608e352415254971a1dd694faeb40488832e202d9162f95af7d41ddeb0", "urls": ["bzz-raw://b3f14fa24b998c229e7751bdbdba8f82f7893100b836afefdc24bc2cfd6dfb99", "dweb:/ipfs/QmfCxQwg1Tf3wWsaG6FcMSV3X7zLf8Cfs3UKgxQcLGzJmY"], "license": "BSL-1.1"}, "src/rewards/RewardDistributor.sol": {"keccak256": "0x8d5c3e5e5050121d4f1310479a2cadde7dc97ff8a57115021cafb2032aaf50c2", "urls": ["bzz-raw://e200d18803bb0680b3007c79c23a8a7e31f054ce6e4f36c376f7b43419679322", "dweb:/ipfs/QmV7CRA5HSB89fwCVF7VWGArZnWoH2BB1heXu9SaMkbL9H"], "license": "BSL-1.1"}, "src/utils/ExponentialNoError.sol": {"keccak256": "0xa97178117d8c32059d55dd2224e5514c54b31f896e565c3834cab3d7502b1bb6", "urls": ["bzz-raw://1cecc0713709f57a5bb7f64d2fa06b9437a42752fa3fed5d3c0b19d82aa56ef4", "dweb:/ipfs/QmQqBCZkzmZxomXLr3b1tHUV55n8EPk3wAUsXgojYSpR41"], "license": "BSL-1.1"}, "src/verifier/ZkVerifier.sol": {"keccak256": "0x1abaa285ad4ee09461aae5df4aecff9bd38b2b3da19b5a920ab1b7d2208e04ec", "urls": ["bzz-raw://7db45391afa7244f70303c5ace73b7937ee30261fd2015e4a7bfa11ec85480cc", "dweb:/ipfs/QmTdhCwcbTCRCMF1ifCJoifqwxhKpKBEE8zq9ph6hYhogG"], "license": "AGPL-3.0"}, "test/Base_Unit_Test.t.sol": {"keccak256": "0x2f61ec9614bbfeed1c1f8555f20fb0d4c584dc3452369b53417ab3fba144f330", "urls": ["bzz-raw://0f7a32bd42554d42a3062092b80a6a0fb90bea17a3cad2c12863688b811e6b4c", "dweb:/ipfs/QmZNFWWTyTFEjSErQAErkmEdzAPf2Eyeb2ir4fgRqQ5bKJ"], "license": "BSL-1.1"}, "test/mocks/ERC20Mock.sol": {"keccak256": "0x86e2d4e622d3172aff95ad8436b1af1213c7be008fbbe1e74f29577ea0afd92f", "urls": ["bzz-raw://2e2a534d2bf62fd7be511ac74e372b1d1e4cc08033fba742950785315553e3fb", "dweb:/ipfs/Qme24GSF4BFzuHETCfPzUafV1bsmVHVvbqQPNxDoU8AafR"], "license": "BSL-1.1"}, "test/mocks/OracleMock.sol": {"keccak256": "0xb749ba9d217b9aedc9010690022b31672eeacf0927f43efe9769ae886f4ce855", "urls": ["bzz-raw://5167ca5dcd31bc0ae6dcc22e43276e8f746b018d7d9c2edc332d1160024f6328", "dweb:/ipfs/QmVdgBmaBntj9TMV4z65bga1uuN2YkfUbLNN9Hsmvu4x6f"], "license": "BSL-1.1"}, "test/mocks/Risc0VerifierMock.sol": {"keccak256": "0x3f6f728dcdcffec55a6c869fdf9e69a0463cccd07ed7784e5ab3dbee0058df85", "urls": ["bzz-raw://f024f438e89b4698292d0b6a61485594f4ed737af80c25fa36bf862211f16732", "dweb:/ipfs/QmZXYmyVyHVHAyZ5PENfhoRphASQGb5WrtsdQN7XyMbGEm"], "license": "BSL-1.1"}, "test/unit/shared/Pauser_Unit_Shared.t.sol": {"keccak256": "0x82f89bae96681d85bbe653ce26b745601be28943a4f453a687ea48da94112541", "urls": ["bzz-raw://706f6cba381185dd3f8236987632b47510112c87fd4091fdcdcb92b59a21f0e9", "dweb:/ipfs/QmPxbJEmwSCpYVbKL7Sbd7fZe7G6KPZvCL21PU6AtQhMqh"], "license": "BSL-1.1"}, "test/utils/Constants.sol": {"keccak256": "0xa2611aa14c45b8ea8b276aacad47d78f33908fab8c6ed0ff35cef76fd41c695b", "urls": ["bzz-raw://f60db39c1ce5c7260361664fec9d731d3ba329055a0810d83491ce54ad7d2a01", "dweb:/ipfs/QmSzv3VXBa6q6bowzAfZ4Afcp4UWwGUKJFB72xV6MYyCNn"], "license": "BSL-1.1"}, "test/utils/Events.sol": {"keccak256": "0xb0b41707dca3af9d783239cb5c96a2e9347e03b5529c944565ac9de2f33ae82a", "urls": ["bzz-raw://e8fad0826e747465c9208ad6a7d52cd50205972cfea7fa8a206be750cf1e8a80", "dweb:/ipfs/QmR5mWoVf2ZcETLJVuCMHvWPBfQ3CNxD8Gx8Endms5AwmR"], "license": "BSL-1.1"}, "test/utils/Helpers.sol": {"keccak256": "0xa59b1e23b76c632e72c93dbd612c9279b2cad6d8915c31c04e62af0d46becf4d", "urls": ["bzz-raw://2a7d815eeebeea85ec405483ec6d55a61f1a984d68d3a6020d3444915aa6610c", "dweb:/ipfs/QmQ6qWmTAdWnnursoU4F2pYCa3tpTtS2qjPFht1kWT2KDT"], "license": "BSL-1.1"}, "test/utils/Types.sol": {"keccak256": "0x696166d23b74196cb6a66bbd72f25024bb251be99ab2a6d8c9ba86f5b47f22d6", "urls": ["bzz-raw://a302c977aea4ccbc54de408575dd5b52b00b9d62512da0d7eb71edb46eff1366", "dweb:/ipfs/QmUjRq9fjukqZL59ABU2Xp6KfR21sPvdBVcWWzjrMLxpzP"], "license": "BSL-1.1"}}, "version": 1}, "id": 235}