{"abi": [{"type": "function", "name": "isOperator", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "pure"}], "bytecode": {"object": "0x6080604052348015600f57600080fd5b50607780601d6000396000f3fe6080604052348015600f57600080fd5b506004361060285760003560e01c80634456eda214602d575b600080fd5b604080516001815290519081900360200190f3fea264697066735822122062f619fab9c392a17198debd3288bf3b1b46824944ab07df269a2c945c7e480b64736f6c634300081c0033", "sourceMap": "828:110:126:-:0;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x6080604052348015600f57600080fd5b506004361060285760003560e01c80634456eda214602d575b600080fd5b604080516001815290519081900360200190f3fea264697066735822122062f619fab9c392a17198debd3288bf3b1b46824944ab07df269a2c945c7e480b64736f6c634300081c0033", "sourceMap": "828:110:126:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;857:79;;;;925:4;154:41:242;;857:79:126;;;;;;142:2:242;857:79:126;;", "linkReferences": {}}, "methodIdentifiers": {"isOperator()": "4456eda2"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"isOperator\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"pure\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/Operator/EmptyOperator.sol\":\"EmptyOperator\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"src/Operator/EmptyOperator.sol\":{\"keccak256\":\"0x0e9fda031db516f67c1a031509fd86a92109fa7915285445b18fe1ffb05162cd\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://f6913b05a3f797849371f799101c4f54e2f52154872f9bd1dd90bf97d285ac26\",\"dweb:/ipfs/QmUYhn7JKb3gv5eQhTXKqW4Zt9DDEnGDwyPD7mpsPfA1Dm\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "pure", "type": "function", "name": "isOperator", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/Operator/EmptyOperator.sol": "EmptyOperator"}, "evmVersion": "london", "libraries": {}}, "sources": {"src/Operator/EmptyOperator.sol": {"keccak256": "0x0e9fda031db516f67c1a031509fd86a92109fa7915285445b18fe1ffb05162cd", "urls": ["bzz-raw://f6913b05a3f797849371f799101c4f54e2f52154872f9bd1dd90bf97d285ac26", "dweb:/ipfs/QmUYhn7JKb3gv5eQhTXKqW4Zt9DDEnGDwyPD7mpsPfA1Dm"], "license": "BSL-1.1"}}, "version": 1}, "id": 126}