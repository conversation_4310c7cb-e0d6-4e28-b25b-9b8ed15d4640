{"abi": [{"type": "function", "name": "<PERSON><PERSON><PERSON><PERSON>", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "verify", "inputs": [{"name": "signature", "type": "bytes", "internalType": "bytes"}, {"name": "human", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"getSigner()": "7ac3c02f", "verify(bytes,address)": "3d3ac1b5"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"getSigner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"signature\",\"type\":\"bytes\"},{\"internalType\":\"address\",\"name\":\"human\",\"type\":\"address\"}],\"name\":\"verify\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{\"verify(bytes,address)\":{\"details\":\"human is supposed to be a POH address, this is what is being signed by the POH API\",\"params\":{\"human\":\"the address for which the signature has been crafted\",\"signature\":\"The signature to check\"},\"returns\":{\"_0\":\"True if the signature was made by signer, false otherwise\"}}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"getSigner()\":{\"notice\":\"Returns the signer's address\"},\"verify(bytes,address)\":{\"notice\":\"Check if the provided signature has been signed by signer\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/external/poh/IPohVerifier.sol\":\"IPohVerifier\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"src/interfaces/external/poh/IPohVerifier.sol\":{\"keccak256\":\"0xce93e2d734074add2a6d36867d59fd24e2d44ca48c524bd8dddabd4163038205\",\"license\":\"AGPL-3.0\",\"urls\":[\"bzz-raw://9e94dc14721e8873d15d7032afa3d2b5fea76e9c4029698aa489f2ea2edcedf6\",\"dweb:/ipfs/QmR55sQx36Ub7wUjDeemUSNotdmYCkK8AmUEYbRezF5Xhy\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "view", "type": "function", "name": "<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "bytes", "name": "signature", "type": "bytes"}, {"internalType": "address", "name": "human", "type": "address"}], "stateMutability": "view", "type": "function", "name": "verify", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}], "devdoc": {"kind": "dev", "methods": {"verify(bytes,address)": {"details": "human is supposed to be a POH address, this is what is being signed by the POH API", "params": {"human": "the address for which the signature has been crafted", "signature": "The signature to check"}, "returns": {"_0": "True if the signature was made by signer, false otherwise"}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"getSigner()": {"notice": "Returns the signer's address"}, "verify(bytes,address)": {"notice": "Check if the provided signature has been signed by signer"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/interfaces/external/poh/IPohVerifier.sol": "IPohVerifier"}, "evmVersion": "london", "libraries": {}}, "sources": {"src/interfaces/external/poh/IPohVerifier.sol": {"keccak256": "0xce93e2d734074add2a6d36867d59fd24e2d44ca48c524bd8dddabd4163038205", "urls": ["bzz-raw://9e94dc14721e8873d15d7032afa3d2b5fea76e9c4029698aa489f2ea2edcedf6", "dweb:/ipfs/QmR55sQx36Ub7wUjDeemUSNotdmYCkK8AmUEYbRezF5Xhy"], "license": "AGPL-3.0"}}, "version": 1}, "id": 165}