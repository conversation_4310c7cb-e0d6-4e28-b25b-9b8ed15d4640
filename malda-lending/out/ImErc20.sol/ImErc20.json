{"abi": [{"type": "function", "name": "addReserves", "inputs": [{"name": "addAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "borrow", "inputs": [{"name": "borrowAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "liquidate", "inputs": [{"name": "borrower", "type": "address", "internalType": "address"}, {"name": "repayAmount", "type": "uint256", "internalType": "uint256"}, {"name": "mTokenCollateral", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "mint", "inputs": [{"name": "mintAmount", "type": "uint256", "internalType": "uint256"}, {"name": "receiver", "type": "address", "internalType": "address"}, {"name": "minAmountOut", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "redeem", "inputs": [{"name": "redeemTokens", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "redeemUnderlying", "inputs": [{"name": "redeemAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "repay", "inputs": [{"name": "repayAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "repayBehalf", "inputs": [{"name": "borrower", "type": "address", "internalType": "address"}, {"name": "repayAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"addReserves(uint256)": "7821a514", "borrow(uint256)": "c5ebeaec", "liquidate(address,uint256,address)": "4914c008", "mint(uint256,address,uint256)": "836a1040", "redeem(uint256)": "db006a75", "redeemUnderlying(uint256)": "852a12e3", "repay(uint256)": "371fd8e6", "repayBehalf(address,uint256)": "5bdcecb7"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"addAmount\",\"type\":\"uint256\"}],\"name\":\"addReserves\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"borrowAmount\",\"type\":\"uint256\"}],\"name\":\"borrow\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"repayAmount\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"mTokenCollateral\",\"type\":\"address\"}],\"name\":\"liquidate\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"mintAmount\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"minAmountOut\",\"type\":\"uint256\"}],\"name\":\"mint\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"redeemTokens\",\"type\":\"uint256\"}],\"name\":\"redeem\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"redeemAmount\",\"type\":\"uint256\"}],\"name\":\"redeemUnderlying\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"repayAmount\",\"type\":\"uint256\"}],\"name\":\"repay\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"repayAmount\",\"type\":\"uint256\"}],\"name\":\"repayBehalf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{\"addReserves(uint256)\":{\"params\":{\"addAmount\":\"The amount fo underlying token to add as reserves\"}},\"borrow(uint256)\":{\"params\":{\"borrowAmount\":\"The amount of the underlying asset to borrow\"}},\"liquidate(address,uint256,address)\":{\"params\":{\"borrower\":\"The borrower of this mToken to be liquidated\",\"mTokenCollateral\":\"The market in which to seize collateral from the borrower\",\"repayAmount\":\"The amount of the underlying borrowed asset to repay\"}},\"mint(uint256,address,uint256)\":{\"details\":\"Accrues interest whether or not the operation succeeds, unless reverted\",\"params\":{\"minAmountOut\":\"The min amounts to be received\",\"mintAmount\":\"The amount of the underlying asset to supply\",\"receiver\":\"The mTokens receiver\"}},\"redeem(uint256)\":{\"details\":\"Accrues interest whether or not the operation succeeds, unless reverted\",\"params\":{\"redeemTokens\":\"The number of mTokens to redeem into underlying\"}},\"redeemUnderlying(uint256)\":{\"details\":\"Accrues interest whether or not the operation succeeds, unless reverted\",\"params\":{\"redeemAmount\":\"The amount of underlying to redeem\"}},\"repay(uint256)\":{\"params\":{\"repayAmount\":\"The amount to repay, or type(uint256).max for the full outstanding amount\"}},\"repayBehalf(address,uint256)\":{\"params\":{\"borrower\":\"the account with the debt being payed off\",\"repayAmount\":\"The amount to repay, or type(uint256).max for the full outstanding amount\"}}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"addReserves(uint256)\":{\"notice\":\"The sender adds to reserves.\"},\"borrow(uint256)\":{\"notice\":\"Sender borrows assets from the protocol to their own address\"},\"liquidate(address,uint256,address)\":{\"notice\":\"The sender liquidates the borrowers collateral.  The collateral seized is transferred to the liquidator.\"},\"mint(uint256,address,uint256)\":{\"notice\":\"Sender supplies assets into the market and receives mTokens in exchange\"},\"redeem(uint256)\":{\"notice\":\"Sender redeems mTokens in exchange for the underlying asset\"},\"redeemUnderlying(uint256)\":{\"notice\":\"Sender redeems mTokens in exchange for a specified amount of underlying asset\"},\"repay(uint256)\":{\"notice\":\"Sender repays their own borrow\"},\"repayBehalf(address,uint256)\":{\"notice\":\"Sender repays a borrow belonging to borrower\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/ImErc20.sol\":\"ImErc20\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"src/interfaces/ImErc20.sol\":{\"keccak256\":\"0x86a1dcaf0d188107ceae7c5eb0911ac63feafbbeda6d0686e4f9950a51d17888\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://d7dd8e0fcfd68501d9f40e37e1c95d8cb653d65626aeeeb5492b73c0e967e6cb\",\"dweb:/ipfs/QmcadG9QuHtAX5qxTKzNr6FMaXSgijr542tFUVTLBkYhAF\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "uint256", "name": "addAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "addReserves"}, {"inputs": [{"internalType": "uint256", "name": "borrowAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "borrow"}, {"inputs": [{"internalType": "address", "name": "borrower", "type": "address"}, {"internalType": "uint256", "name": "repayAmount", "type": "uint256"}, {"internalType": "address", "name": "mTokenCollateral", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "liquidate"}, {"inputs": [{"internalType": "uint256", "name": "mintAmount", "type": "uint256"}, {"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "uint256", "name": "minAmountOut", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "mint"}, {"inputs": [{"internalType": "uint256", "name": "redeemTokens", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "redeem"}, {"inputs": [{"internalType": "uint256", "name": "redeemAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "redeemUnderlying"}, {"inputs": [{"internalType": "uint256", "name": "repayAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "repay", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "borrower", "type": "address"}, {"internalType": "uint256", "name": "repayAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "repayBehalf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}], "devdoc": {"kind": "dev", "methods": {"addReserves(uint256)": {"params": {"addAmount": "The amount fo underlying token to add as reserves"}}, "borrow(uint256)": {"params": {"borrowAmount": "The amount of the underlying asset to borrow"}}, "liquidate(address,uint256,address)": {"params": {"borrower": "The borrower of this mToken to be liquidated", "mTokenCollateral": "The market in which to seize collateral from the borrower", "repayAmount": "The amount of the underlying borrowed asset to repay"}}, "mint(uint256,address,uint256)": {"details": "Accrues interest whether or not the operation succeeds, unless reverted", "params": {"minAmountOut": "The min amounts to be received", "mintAmount": "The amount of the underlying asset to supply", "receiver": "The mTokens receiver"}}, "redeem(uint256)": {"details": "Accrues interest whether or not the operation succeeds, unless reverted", "params": {"redeemTokens": "The number of mTokens to redeem into underlying"}}, "redeemUnderlying(uint256)": {"details": "Accrues interest whether or not the operation succeeds, unless reverted", "params": {"redeemAmount": "The amount of underlying to redeem"}}, "repay(uint256)": {"params": {"repayAmount": "The amount to repay, or type(uint256).max for the full outstanding amount"}}, "repayBehalf(address,uint256)": {"params": {"borrower": "the account with the debt being payed off", "repayAmount": "The amount to repay, or type(uint256).max for the full outstanding amount"}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"addReserves(uint256)": {"notice": "The sender adds to reserves."}, "borrow(uint256)": {"notice": "Sender borrows assets from the protocol to their own address"}, "liquidate(address,uint256,address)": {"notice": "The sender liquidates the borrowers collateral.  The collateral seized is transferred to the liquidator."}, "mint(uint256,address,uint256)": {"notice": "Sender supplies assets into the market and receives mTokens in exchange"}, "redeem(uint256)": {"notice": "Sender redeems mTokens in exchange for the underlying asset"}, "redeemUnderlying(uint256)": {"notice": "Sender redeems mTokens in exchange for a specified amount of underlying asset"}, "repay(uint256)": {"notice": "Sender repays their own borrow"}, "repayBehalf(address,uint256)": {"notice": "Sender repays a borrow belonging to borrower"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/interfaces/ImErc20.sol": "ImErc20"}, "evmVersion": "london", "libraries": {}}, "sources": {"src/interfaces/ImErc20.sol": {"keccak256": "0x86a1dcaf0d188107ceae7c5eb0911ac63feafbbeda6d0686e4f9950a51d17888", "urls": ["bzz-raw://d7dd8e0fcfd68501d9f40e37e1c95d8cb653d65626aeeeb5492b73c0e967e6cb", "dweb:/ipfs/QmcadG9QuHtAX5qxTKzNr6FMaXSgijr542tFUVTLBkYhAF"], "license": "BSL-1.1"}}, "version": 1}, "id": 144}