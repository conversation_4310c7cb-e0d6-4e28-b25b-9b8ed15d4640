{"abi": [{"type": "function", "name": "forceResumeReceive", "inputs": [{"name": "_srcChainId", "type": "uint16", "internalType": "uint16"}, {"name": "_srcAddress", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setConfig", "inputs": [{"name": "_version", "type": "uint16", "internalType": "uint16"}, {"name": "_chainId", "type": "uint16", "internalType": "uint16"}, {"name": "_configType", "type": "uint256", "internalType": "uint256"}, {"name": "_config", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setReceiveVersion", "inputs": [{"name": "_version", "type": "uint16", "internalType": "uint16"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setSendVersion", "inputs": [{"name": "_version", "type": "uint16", "internalType": "uint16"}], "outputs": [], "stateMutability": "nonpayable"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"forceResumeReceive(uint16,bytes)": "42d65a8d", "setConfig(uint16,uint16,uint256,bytes)": "cbed8b9c", "setReceiveVersion(uint16)": "10ddb137", "setSendVersion(uint16)": "07e0db17"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"uint16\",\"name\":\"_src<PERSON>hainId\",\"type\":\"uint16\"},{\"internalType\":\"bytes\",\"name\":\"_srcAddress\",\"type\":\"bytes\"}],\"name\":\"forceResumeReceive\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint16\",\"name\":\"_version\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"_chainId\",\"type\":\"uint16\"},{\"internalType\":\"uint256\",\"name\":\"_configType\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"_config\",\"type\":\"bytes\"}],\"name\":\"setConfig\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint16\",\"name\":\"_version\",\"type\":\"uint16\"}],\"name\":\"setReceiveVersion\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint16\",\"name\":\"_version\",\"type\":\"uint16\"}],\"name\":\"setSendVersion\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"is imported from (https://github.com/LayerZero-Labs/LayerZero/blob/main/contracts/interfaces/ILayerZeroUserApplicationConfig.sol)\",\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/external/layerzero/ILayerZeroUserApplicationConfig.sol\":\"ILayerZeroUserApplicationConfig\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"src/interfaces/external/layerzero/ILayerZeroUserApplicationConfig.sol\":{\"keccak256\":\"0x3e3d9949a418520798f7c4eb179d6b6ec6584a75b680e7eb19d8e880287043ee\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://dff2ef4cac1e5f6517c68c0a048f1910ca0dde45b03522539bd04ac8b92b514e\",\"dweb:/ipfs/QmZ2S26Nse3FMy8sqEr4kjM5DfeVpaZyjyKtNd9Xtq64Bd\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "uint16", "name": "_srcChainId", "type": "uint16"}, {"internalType": "bytes", "name": "_srcAddress", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "forceResumeReceive"}, {"inputs": [{"internalType": "uint16", "name": "_version", "type": "uint16"}, {"internalType": "uint16", "name": "_chainId", "type": "uint16"}, {"internalType": "uint256", "name": "_configType", "type": "uint256"}, {"internalType": "bytes", "name": "_config", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "setConfig"}, {"inputs": [{"internalType": "uint16", "name": "_version", "type": "uint16"}], "stateMutability": "nonpayable", "type": "function", "name": "setReceiveVersion"}, {"inputs": [{"internalType": "uint16", "name": "_version", "type": "uint16"}], "stateMutability": "nonpayable", "type": "function", "name": "setSendVersion"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/interfaces/external/layerzero/ILayerZeroUserApplicationConfig.sol": "ILayerZeroUserApplicationConfig"}, "evmVersion": "london", "libraries": {}}, "sources": {"src/interfaces/external/layerzero/ILayerZeroUserApplicationConfig.sol": {"keccak256": "0x3e3d9949a418520798f7c4eb179d6b6ec6584a75b680e7eb19d8e880287043ee", "urls": ["bzz-raw://dff2ef4cac1e5f6517c68c0a048f1910ca0dde45b03522539bd04ac8b92b514e", "dweb:/ipfs/QmZ2S26Nse3FMy8sqEr4kjM5DfeVpaZyjyKtNd9Xtq64Bd"], "license": "BUSL-1.1"}}, "version": 1}, "id": 156}