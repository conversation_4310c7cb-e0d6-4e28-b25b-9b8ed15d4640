{"abi": [{"type": "function", "name": "getFee", "inputs": [{"name": "_dst<PERSON><PERSON>nId", "type": "uint32", "internalType": "uint32"}, {"name": "_message", "type": "bytes", "internalType": "bytes"}, {"name": "_bridgeData", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "sendMsg", "inputs": [{"name": "_extractedAmount", "type": "uint256", "internalType": "uint256"}, {"name": "_market", "type": "address", "internalType": "address"}, {"name": "_dst<PERSON><PERSON>nId", "type": "uint32", "internalType": "uint32"}, {"name": "_token", "type": "address", "internalType": "address"}, {"name": "_message", "type": "bytes", "internalType": "bytes"}, {"name": "_bridgeData", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "payable"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"getFee(uint32,bytes,bytes)": "b3d300f4", "sendMsg(uint256,address,uint32,address,bytes,bytes)": "f2db52a7"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"_dst<PERSON><PERSON>nId\",\"type\":\"uint32\"},{\"internalType\":\"bytes\",\"name\":\"_message\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"_bridgeData\",\"type\":\"bytes\"}],\"name\":\"getFee\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_extractedAmount\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"_market\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"_dst<PERSON><PERSON>nId\",\"type\":\"uint32\"},{\"internalType\":\"address\",\"name\":\"_token\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"_message\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"_bridgeData\",\"type\":\"bytes\"}],\"name\":\"sendMsg\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{\"getFee(uint32,bytes,bytes)\":{\"params\":{\"_bridgeData\":\"specific bridge data\",\"_dstChainId\":\"destination chain id\",\"_message\":\"operation message data\"}},\"sendMsg(uint256,address,uint32,address,bytes,bytes)\":{\"params\":{\"_bridgeData\":\"specific bridge datas\",\"_dstChainId\":\"destination chain id\",\"_extractedAmount\":\"extracted amount for rebalancing\",\"_market\":\"destination address\",\"_message\":\"operation message data\",\"_token\":\"the token to rebalance\"}}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"getFee(uint32,bytes,bytes)\":{\"notice\":\"computes fee for bridge operation\"},\"sendMsg(uint256,address,uint32,address,bytes,bytes)\":{\"notice\":\"rebalance through bridge\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/IBridge.sol\":\"IBridge\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"src/interfaces/IBridge.sol\":{\"keccak256\":\"0x52c9927e9c2ef9f9f82164cd536d38c3e21800b86e5326aa51020046d140ac7f\",\"license\":\"AGPL-3.0\",\"urls\":[\"bzz-raw://88cc76f70a53faed9140bf048c994dc719eae830327d7d98f21aa0372172f4ca\",\"dweb:/ipfs/QmYnRkEbqn1QSFKq8MRUBE8z2RvX71CFstej5kpzvuLsUG\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "uint32", "name": "_dst<PERSON><PERSON>nId", "type": "uint32"}, {"internalType": "bytes", "name": "_message", "type": "bytes"}, {"internalType": "bytes", "name": "_bridgeData", "type": "bytes"}], "stateMutability": "view", "type": "function", "name": "getFee", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "_extractedAmount", "type": "uint256"}, {"internalType": "address", "name": "_market", "type": "address"}, {"internalType": "uint32", "name": "_dst<PERSON><PERSON>nId", "type": "uint32"}, {"internalType": "address", "name": "_token", "type": "address"}, {"internalType": "bytes", "name": "_message", "type": "bytes"}, {"internalType": "bytes", "name": "_bridgeData", "type": "bytes"}], "stateMutability": "payable", "type": "function", "name": "sendMsg"}], "devdoc": {"kind": "dev", "methods": {"getFee(uint32,bytes,bytes)": {"params": {"_bridgeData": "specific bridge data", "_dstChainId": "destination chain id", "_message": "operation message data"}}, "sendMsg(uint256,address,uint32,address,bytes,bytes)": {"params": {"_bridgeData": "specific bridge datas", "_dstChainId": "destination chain id", "_extractedAmount": "extracted amount for rebalancing", "_market": "destination address", "_message": "operation message data", "_token": "the token to rebalance"}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"getFee(uint32,bytes,bytes)": {"notice": "computes fee for bridge operation"}, "sendMsg(uint256,address,uint32,address,bytes,bytes)": {"notice": "rebalance through bridge"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/interfaces/IBridge.sol": "IBridge"}, "evmVersion": "london", "libraries": {}}, "sources": {"src/interfaces/IBridge.sol": {"keccak256": "0x52c9927e9c2ef9f9f82164cd536d38c3e21800b86e5326aa51020046d140ac7f", "urls": ["bzz-raw://88cc76f70a53faed9140bf048c994dc719eae830327d7d98f21aa0372172f4ca", "dweb:/ipfs/QmYnRkEbqn1QSFKq8MRUBE8z2RvX71CFstej5kpzvuLsUG"], "license": "AGPL-3.0"}}, "version": 1}, "id": 133}