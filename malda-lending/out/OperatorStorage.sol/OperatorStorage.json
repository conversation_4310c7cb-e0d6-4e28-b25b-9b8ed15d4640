{"abi": [{"type": "function", "name": "accountAssets", "inputs": [{"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "afterMTokenMint", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "view"}, {"type": "function", "name": "allMarkets", "inputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "beforeMTokenBorrow", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}, {"name": "borrower", "type": "address", "internalType": "address"}, {"name": "borrowAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "beforeMTokenLiquidate", "inputs": [{"name": "mTokenBorrowed", "type": "address", "internalType": "address"}, {"name": "mTokenCollateral", "type": "address", "internalType": "address"}, {"name": "borrower", "type": "address", "internalType": "address"}, {"name": "repayAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "view"}, {"type": "function", "name": "beforeMTokenMint", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}, {"name": "minter", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "beforeMTokenRedeem", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}, {"name": "redeemer", "type": "address", "internalType": "address"}, {"name": "redeemTokens", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "beforeMTokenRepay", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}, {"name": "borrower", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "beforeMTokenSeize", "inputs": [{"name": "mTokenCollateral", "type": "address", "internalType": "address"}, {"name": "mTokenBorrowed", "type": "address", "internalType": "address"}, {"name": "liquidator", "type": "address", "internalType": "address"}, {"name": "borrower", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "beforeMTokenTransfer", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}, {"name": "src", "type": "address", "internalType": "address"}, {"name": "dst", "type": "address", "internalType": "address"}, {"name": "transferTokens", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "beforeRebalancing", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "blacklistOperator", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IBlacklister"}], "stateMutability": "view"}, {"type": "function", "name": "borrowCaps", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "checkMembership", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "mToken", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "checkOutflowVolumeLimit", "inputs": [{"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "claimMalda", "inputs": [{"name": "holder", "type": "address", "internalType": "address"}, {"name": "mTokens", "type": "address[]", "internalType": "address[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "claimMalda", "inputs": [{"name": "holders", "type": "address[]", "internalType": "address[]"}, {"name": "mTokens", "type": "address[]", "internalType": "address[]"}, {"name": "borrowers", "type": "bool", "internalType": "bool"}, {"name": "suppliers", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "claimMalda", "inputs": [{"name": "holder", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "closeFactorMantissa", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "cumulativeOutflowVolume", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "enterMarkets", "inputs": [{"name": "_mTokens", "type": "address[]", "internalType": "address[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "enterMarketsWithSender", "inputs": [{"name": "_account", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "exitMarket", "inputs": [{"name": "_mToken", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "getAccountLiquidity", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getAllMarkets", "inputs": [], "outputs": [{"name": "mTokens", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "getAssetsIn", "inputs": [{"name": "_user", "type": "address", "internalType": "address"}], "outputs": [{"name": "mTokens", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "getHypotheticalAccountLiquidity", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "mTokenModify", "type": "address", "internalType": "address"}, {"name": "redeemTokens", "type": "uint256", "internalType": "uint256"}, {"name": "borrowAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getUSDValueForAllMarkets", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "isDeprecated", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isMarketListed", "inputs": [{"name": "market", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isOperator", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isPaused", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}, {"name": "_type", "type": "uint8", "internalType": "enum ImTokenOperationTypes.OperationType"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "lastOutflowResetTimestamp", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "limitPerTimePeriod", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "liquidateCalculateSeizeTokens", "inputs": [{"name": "mTokenBorrowed", "type": "address", "internalType": "address"}, {"name": "mTokenCollateral", "type": "address", "internalType": "address"}, {"name": "actualRepayAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "liquidationIncentiveMantissa", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "markets", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "isListed", "type": "bool", "internalType": "bool"}, {"name": "collateralFactorMantissa", "type": "uint256", "internalType": "uint256"}, {"name": "isMalded", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "oracleOperator", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "outflowResetTimeWindow", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "rewardDistributor", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "rolesOperator", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IRoles"}], "stateMutability": "view"}, {"type": "function", "name": "setPaused", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}, {"name": "_type", "type": "uint8", "internalType": "enum ImTokenOperationTypes.OperationType"}, {"name": "state", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "supplyCaps", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "user<PERSON><PERSON><PERSON>sted", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "whitelistEnabled", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "event", "name": "ActionPaused", "inputs": [{"name": "mToken", "type": "address", "indexed": true, "internalType": "address"}, {"name": "_type", "type": "uint8", "indexed": false, "internalType": "enum ImTokenOperationTypes.OperationType"}, {"name": "state", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "MarketEntered", "inputs": [{"name": "mToken", "type": "address", "indexed": true, "internalType": "address"}, {"name": "account", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "MarketExited", "inputs": [{"name": "mToken", "type": "address", "indexed": true, "internalType": "address"}, {"name": "account", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "MarketListed", "inputs": [{"name": "mToken", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "NewBorrowCap", "inputs": [{"name": "mToken", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newBorrowCap", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "NewCloseFactor", "inputs": [{"name": "oldCloseFactorMantissa", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "newCloseFactorMantissa", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "NewCollateralFactor", "inputs": [{"name": "mToken", "type": "address", "indexed": true, "internalType": "address"}, {"name": "oldCollateralFactorMantissa", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "newCollateralFactorMantissa", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "NewLiquidationIncentive", "inputs": [{"name": "market", "type": "address", "indexed": false, "internalType": "address"}, {"name": "oldLiquidationIncentiveMantissa", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "newLiquidationIncentiveMantissa", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "NewPriceOracle", "inputs": [{"name": "oldPriceOracle", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newPriceOracle", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "NewRewardDistributor", "inputs": [{"name": "oldRewardDistributor", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newRewardDistributor", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "NewRolesOperator", "inputs": [{"name": "oldRoles", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newRoles", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "NewSupplyCap", "inputs": [{"name": "mToken", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newBorrowCap", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "OutflowLimitUpdated", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "oldLimit", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "newLimit", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "OutflowTimeWindowUpdated", "inputs": [{"name": "oldWindow", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "newWindow", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "OutflowVolumeReset", "inputs": [], "anonymous": false}, {"type": "event", "name": "<PERSON>r<PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "user", "type": "address", "indexed": true, "internalType": "address"}, {"name": "state", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "WhitelistDisabled", "inputs": [], "anonymous": false}, {"type": "event", "name": "WhitelistEnabled", "inputs": [], "anonymous": false}, {"type": "error", "name": "Operator_AssetNotFound", "inputs": []}, {"type": "error", "name": "Operator_Deactivate_MarketBalanceOwed", "inputs": []}, {"type": "error", "name": "Operator_EmptyPrice", "inputs": []}, {"type": "error", "name": "Operator_InsufficientLiquidity", "inputs": []}, {"type": "error", "name": "Operator_InvalidBlacklistOperator", "inputs": []}, {"type": "error", "name": "Operator_InvalidCollateralFactor", "inputs": []}, {"type": "error", "name": "Operator_InvalidInput", "inputs": []}, {"type": "error", "name": "Operator_InvalidRewardDistributor", "inputs": []}, {"type": "error", "name": "Operator_InvalidRolesOperator", "inputs": []}, {"type": "error", "name": "Operator_MarketAlreadyListed", "inputs": []}, {"type": "error", "name": "Operator_MarketBorrowCapReached", "inputs": []}, {"type": "error", "name": "Operator_MarketNotListed", "inputs": []}, {"type": "error", "name": "Operator_MarketSupplyReached", "inputs": []}, {"type": "error", "name": "Operator_Mismatch", "inputs": []}, {"type": "error", "name": "Operator_OnlyAdmin", "inputs": []}, {"type": "error", "name": "Operator_OnlyAdminOrRole", "inputs": []}, {"type": "error", "name": "Operator_OracleUnderlyingFetchError", "inputs": []}, {"type": "error", "name": "Operator_OutflowVolumeReached", "inputs": []}, {"type": "error", "name": "Operator_Paused", "inputs": []}, {"type": "error", "name": "Operator_PriceFetchFailed", "inputs": []}, {"type": "error", "name": "Operator_RepayAmountNotValid", "inputs": []}, {"type": "error", "name": "Operator_RepayingTooMuch", "inputs": []}, {"type": "error", "name": "Operator_SenderMustBeToken", "inputs": []}, {"type": "error", "name": "Operator_UserBlacklisted", "inputs": []}, {"type": "error", "name": "Operator_UserNotWhitelisted", "inputs": []}, {"type": "error", "name": "Operator_WrongMarket", "inputs": []}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"accountAssets(address,uint256)": "dce15449", "afterMTokenMint(address)": "0d926fc8", "allMarkets(uint256)": "52d84d1e", "beforeMTokenBorrow(address,address,uint256)": "50795f8a", "beforeMTokenLiquidate(address,address,address,uint256)": "b50ce762", "beforeMTokenMint(address,address)": "c0f1ee09", "beforeMTokenRedeem(address,address,uint256)": "1e32bd9b", "beforeMTokenRepay(address,address)": "c321fbcc", "beforeMTokenSeize(address,address,address,address)": "6765dff9", "beforeMTokenTransfer(address,address,address,uint256)": "17bf120e", "beforeRebalancing(address)": "68f6f4b0", "blacklistOperator()": "2d57d487", "borrowCaps(address)": "4a584432", "checkMembership(address,address)": "929fe9a1", "checkOutflowVolumeLimit(uint256)": "823307f2", "claimMalda(address)": "e44a429a", "claimMalda(address,address[])": "1c7818ac", "claimMalda(address[],address[],bool,bool)": "1fbd27a5", "closeFactorMantissa()": "e8755446", "cumulativeOutflowVolume()": "700e1212", "enterMarkets(address[])": "c2998238", "enterMarketsWithSender(address)": "973fd521", "exitMarket(address)": "ede4edd0", "getAccountLiquidity(address)": "5ec88c79", "getAllMarkets()": "b0772d0b", "getAssetsIn(address)": "abfceffc", "getHypotheticalAccountLiquidity(address,address,uint256,uint256)": "4e79238f", "getUSDValueForAllMarkets()": "d99faea5", "isDeprecated(address)": "94543c15", "isMarketListed(address)": "3d98a1e5", "isOperator()": "4456eda2", "isPaused(address,uint8)": "0d126627", "lastOutflowResetTimestamp()": "ddf46254", "limitPerTimePeriod()": "8728d8a7", "liquidateCalculateSeizeTokens(address,address,uint256)": "c488847b", "liquidationIncentiveMantissa(address)": "2e06d7b1", "markets(address)": "8e8f294b", "oracleOperator()": "11679ef7", "outflowResetTimeWindow()": "e92081b4", "rewardDistributor()": "acc2166a", "rolesOperator()": "4fecab70", "setPaused(address,uint8,bool)": "4a675b34", "supplyCaps(address)": "02c3bcbb", "userWhitelisted(address)": "fc2e0c2f", "whitelistEnabled()": "51fb012d"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"Operator_AssetNotFound\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_Deactivate_MarketBalanceOwed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_EmptyPrice\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_InsufficientLiquidity\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_InvalidBlacklistOperator\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_InvalidCollateralFactor\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_InvalidInput\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_InvalidRewardDistributor\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_InvalidRolesOperator\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_MarketAlreadyListed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_MarketBorrowCapReached\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_MarketNotListed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_MarketSupplyReached\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_Mismatch\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_OnlyAdmin\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_OnlyAdminOrRole\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_OracleUnderlyingFetchError\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_OutflowVolumeReached\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_Paused\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_PriceFetchFailed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_RepayAmountNotValid\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_RepayingTooMuch\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_SenderMustBeToken\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_UserBlacklisted\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_UserNotWhitelisted\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_WrongMarket\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"enum ImTokenOperationTypes.OperationType\",\"name\":\"_type\",\"type\":\"uint8\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"state\",\"type\":\"bool\"}],\"name\":\"ActionPaused\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"MarketEntered\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"MarketExited\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"}],\"name\":\"MarketListed\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"newBorrowCap\",\"type\":\"uint256\"}],\"name\":\"NewBorrowCap\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"oldCloseFactorMantissa\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"newCloseFactorMantissa\",\"type\":\"uint256\"}],\"name\":\"NewCloseFactor\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"oldCollateralFactorMantissa\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"newCollateralFactorMantissa\",\"type\":\"uint256\"}],\"name\":\"NewCollateralFactor\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"market\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"oldLiquidationIncentiveMantissa\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"newLiquidationIncentiveMantissa\",\"type\":\"uint256\"}],\"name\":\"NewLiquidationIncentive\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"oldPriceOracle\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newPriceOracle\",\"type\":\"address\"}],\"name\":\"NewPriceOracle\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"oldRewardDistributor\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newRewardDistributor\",\"type\":\"address\"}],\"name\":\"NewRewardDistributor\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"oldRoles\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newRoles\",\"type\":\"address\"}],\"name\":\"NewRolesOperator\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"newBorrowCap\",\"type\":\"uint256\"}],\"name\":\"NewSupplyCap\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"oldLimit\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"newLimit\",\"type\":\"uint256\"}],\"name\":\"OutflowLimitUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"oldWindow\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"newWindow\",\"type\":\"uint256\"}],\"name\":\"OutflowTimeWindowUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[],\"name\":\"OutflowVolumeReset\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"state\",\"type\":\"bool\"}],\"name\":\"UserWhitelisted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[],\"name\":\"WhitelistDisabled\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[],\"name\":\"WhitelistEnabled\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"accountAssets\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"}],\"name\":\"afterMTokenMint\",\"outputs\":[],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"allMarkets\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"borrowAmount\",\"type\":\"uint256\"}],\"name\":\"beforeMTokenBorrow\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mTokenBorrowed\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"mTokenCollateral\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"repayAmount\",\"type\":\"uint256\"}],\"name\":\"beforeMTokenLiquidate\",\"outputs\":[],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"minter\",\"type\":\"address\"}],\"name\":\"beforeMTokenMint\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"redeemer\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"redeemTokens\",\"type\":\"uint256\"}],\"name\":\"beforeMTokenRedeem\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"}],\"name\":\"beforeMTokenRepay\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mTokenCollateral\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"mTokenBorrowed\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"liquidator\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"}],\"name\":\"beforeMTokenSeize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"src\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"dst\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"transferTokens\",\"type\":\"uint256\"}],\"name\":\"beforeMTokenTransfer\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"}],\"name\":\"beforeRebalancing\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"blacklistOperator\",\"outputs\":[{\"internalType\":\"contract IBlacklister\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"borrowCaps\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"}],\"name\":\"checkMembership\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"checkOutflowVolumeLimit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"holder\",\"type\":\"address\"},{\"internalType\":\"address[]\",\"name\":\"mTokens\",\"type\":\"address[]\"}],\"name\":\"claimMalda\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"holders\",\"type\":\"address[]\"},{\"internalType\":\"address[]\",\"name\":\"mTokens\",\"type\":\"address[]\"},{\"internalType\":\"bool\",\"name\":\"borrowers\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"suppliers\",\"type\":\"bool\"}],\"name\":\"claimMalda\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"holder\",\"type\":\"address\"}],\"name\":\"claimMalda\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"closeFactorMantissa\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"cumulativeOutflowVolume\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"_mTokens\",\"type\":\"address[]\"}],\"name\":\"enterMarkets\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_account\",\"type\":\"address\"}],\"name\":\"enterMarketsWithSender\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_mToken\",\"type\":\"address\"}],\"name\":\"exitMarket\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"getAccountLiquidity\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getAllMarkets\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"mTokens\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_user\",\"type\":\"address\"}],\"name\":\"getAssetsIn\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"mTokens\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"mTokenModify\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"redeemTokens\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"borrowAmount\",\"type\":\"uint256\"}],\"name\":\"getHypotheticalAccountLiquidity\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getUSDValueForAllMarkets\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"}],\"name\":\"isDeprecated\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"market\",\"type\":\"address\"}],\"name\":\"isMarketListed\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"isOperator\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"internalType\":\"enum ImTokenOperationTypes.OperationType\",\"name\":\"_type\",\"type\":\"uint8\"}],\"name\":\"isPaused\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"lastOutflowResetTimestamp\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"limitPerTimePeriod\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mTokenBorrowed\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"mTokenCollateral\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"actualRepayAmount\",\"type\":\"uint256\"}],\"name\":\"liquidateCalculateSeizeTokens\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"liquidationIncentiveMantissa\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"markets\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"isListed\",\"type\":\"bool\"},{\"internalType\":\"uint256\",\"name\":\"collateralFactorMantissa\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"isMalded\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"oracleOperator\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"outflowResetTimeWindow\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"rewardDistributor\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"rolesOperator\",\"outputs\":[{\"internalType\":\"contract IRoles\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"internalType\":\"enum ImTokenOperationTypes.OperationType\",\"name\":\"_type\",\"type\":\"uint8\"},{\"internalType\":\"bool\",\"name\":\"state\",\"type\":\"bool\"}],\"name\":\"setPaused\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"supplyCaps\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"userWhitelisted\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"whitelistEnabled\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{\"afterMTokenMint(address)\":{\"params\":{\"mToken\":\"Asset being minted\"}},\"beforeMTokenBorrow(address,address,uint256)\":{\"params\":{\"borrowAmount\":\"The amount of underlying the account would borrow\",\"borrower\":\"The account which would borrow the asset\",\"mToken\":\"The market to verify the borrow against\"}},\"beforeMTokenLiquidate(address,address,address,uint256)\":{\"params\":{\"borrower\":\"The address of the borrower\",\"mTokenBorrowed\":\"Asset which was borrowed by the borrower\",\"mTokenCollateral\":\"Asset which was used as collateral and will be seized\",\"repayAmount\":\"The amount of underlying being repaid\"}},\"beforeMTokenMint(address,address)\":{\"params\":{\"mToken\":\"The market to verify the mint against\",\"minter\":\"The account which would get the minted tokens\"}},\"beforeMTokenRedeem(address,address,uint256)\":{\"params\":{\"mToken\":\"The market to verify the redeem against\",\"redeemTokens\":\"The number of mTokens to exchange for the underlying asset in the market\",\"redeemer\":\"The account which would redeem the tokens\"}},\"beforeMTokenRepay(address,address)\":{\"params\":{\"borrower\":\"The account which would borrowed the asset\",\"mToken\":\"The market to verify the repay against\"}},\"beforeMTokenSeize(address,address,address,address)\":{\"params\":{\"borrower\":\"The address of the borrower\",\"liquidator\":\"The address repaying the borrow and seizing the collateral\",\"mTokenBorrowed\":\"Asset which was borrowed by the borrower\",\"mTokenCollateral\":\"Asset which was used as collateral and will be seized\"}},\"beforeMTokenTransfer(address,address,address,uint256)\":{\"params\":{\"dst\":\"The account which receives the tokens\",\"mToken\":\"The market to verify the transfer against\",\"src\":\"The account which sources the tokens\",\"transferTokens\":\"The number of mTokens to transfer\"}},\"beforeRebalancing(address)\":{\"params\":{\"mToken\":\"The market to verify the transfer against\"}},\"checkMembership(address,address)\":{\"params\":{\"account\":\"The address of the account to check\",\"mToken\":\"The mToken to check\"},\"returns\":{\"_0\":\"True if the account is in the asset, otherwise false.\"}},\"checkOutflowVolumeLimit(uint256)\":{\"details\":\"Sender must be a listed market\",\"params\":{\"amount\":\"New amount\"}},\"claimMalda(address)\":{\"params\":{\"holder\":\"The address to claim MALDA for\"}},\"claimMalda(address,address[])\":{\"params\":{\"holder\":\"The address to claim MALDA for\",\"mTokens\":\"The list of markets to claim MALDA in\"}},\"claimMalda(address[],address[],bool,bool)\":{\"params\":{\"borrowers\":\"Whether or not to claim MALDA earned by borrowing\",\"holders\":\"The addresses to claim MALDA for\",\"mTokens\":\"The list of markets to claim MALDA in\",\"suppliers\":\"Whether or not to claim MALDA earned by supplying\"}},\"enterMarkets(address[])\":{\"params\":{\"_mTokens\":\"The list of addresses of the mToken markets to be enabled\"}},\"enterMarketsWithSender(address)\":{\"params\":{\"_account\":\"The account to add for\"}},\"exitMarket(address)\":{\"details\":\"Sender must not have an outstanding borrow balance in the asset,  or be providing necessary collateral for an outstanding borrow.\",\"params\":{\"_mToken\":\"The address of the asset to be removed\"}},\"getAccountLiquidity(address)\":{\"returns\":{\"_0\":\"account liquidity in excess of collateral requirements,          account shortfall below collateral requirements)\"}},\"getAssetsIn(address)\":{\"params\":{\"_user\":\"The address of the account to pull assets for\"},\"returns\":{\"mTokens\":\"A dynamic list with the assets the account has entered\"}},\"getHypotheticalAccountLiquidity(address,address,uint256,uint256)\":{\"params\":{\"account\":\"The account to determine liquidity for\",\"borrowAmount\":\"The amount of underlying to hypothetically borrow\",\"mTokenModify\":\"The market to hypothetically redeem/borrow in\",\"redeemTokens\":\"The number of tokens to hypothetically redeem\"},\"returns\":{\"_0\":\"hypothetical account liquidity in excess of collateral requirements,         hypothetical account shortfall below collateral requirements)\"}},\"isDeprecated(address)\":{\"details\":\"All borrows in a deprecated mToken market can be immediately liquidated\",\"params\":{\"mToken\":\"The market to check if deprecated\"}},\"isPaused(address,uint8)\":{\"params\":{\"_type\":\"the operation type\",\"mToken\":\"The mToken to check\"}},\"liquidateCalculateSeizeTokens(address,address,uint256)\":{\"details\":\"Used in liquidation (called in mTokenBorrowed.liquidate)\",\"params\":{\"actualRepayAmount\":\"The amount of mTokenBorrowed underlying to convert into mTokenCollateral tokens\",\"mTokenBorrowed\":\"The address of the borrowed mToken\",\"mTokenCollateral\":\"The address of the collateral mToken\"},\"returns\":{\"_0\":\"number of mTokenCollateral tokens to be seized in a liquidation\"}},\"setPaused(address,uint8,bool)\":{\"params\":{\"_type\":\"The pause operation type\",\"mToken\":\"The market token address\",\"state\":\"The pause operation status\"}}},\"stateVariables\":{\"markets\":{\"details\":\"Used e.g. to determine if a market is supported\"}},\"version\":1},\"userdoc\":{\"events\":{\"ActionPaused(address,uint8,bool)\":{\"notice\":\"Emitted when pause status is changed\"},\"MarketEntered(address,address)\":{\"notice\":\"Emitted when an account enters a market\"},\"MarketExited(address,address)\":{\"notice\":\"Emitted when an account exits a market\"},\"MarketListed(address)\":{\"notice\":\"Emitted when an admin supports a market\"},\"NewBorrowCap(address,uint256)\":{\"notice\":\"Emitted when borrow cap for a mToken is changed\"},\"NewCloseFactor(uint256,uint256)\":{\"notice\":\"Emitted Emitted when close factor is changed by admin\"},\"NewCollateralFactor(address,uint256,uint256)\":{\"notice\":\"Emitted when a collateral factor is changed by admin\"},\"NewLiquidationIncentive(address,uint256,uint256)\":{\"notice\":\"Emitted when liquidation incentive is changed by admin\"},\"NewPriceOracle(address,address)\":{\"notice\":\"Emitted when price oracle is changed\"},\"NewRewardDistributor(address,address)\":{\"notice\":\"Emitted when reward distributor is changed\"},\"NewRolesOperator(address,address)\":{\"notice\":\"Event emitted when rolesOperator is changed\"},\"NewSupplyCap(address,uint256)\":{\"notice\":\"Emitted when supply cap for a mToken is changed\"},\"OutflowLimitUpdated(address,uint256,uint256)\":{\"notice\":\"Event emitted when outflow limit is updated\"},\"OutflowTimeWindowUpdated(uint256,uint256)\":{\"notice\":\"Event emitted when outflow reset time window is updated\"},\"OutflowVolumeReset()\":{\"notice\":\"Event emitted when outflow volume has been reset\"},\"UserWhitelisted(address,bool)\":{\"notice\":\"Emitted when user whitelist status is changed\"}},\"kind\":\"user\",\"methods\":{\"accountAssets(address,uint256)\":{\"notice\":\"Per-account mapping of \\\"assets you are in\\\", capped by maxAssets\"},\"afterMTokenMint(address)\":{\"notice\":\"Validates mint and reverts on rejection. May emit logs.\"},\"allMarkets(uint256)\":{\"notice\":\"A list of all markets\"},\"beforeMTokenBorrow(address,address,uint256)\":{\"notice\":\"Checks if the account should be allowed to borrow the underlying asset of the given market\"},\"beforeMTokenLiquidate(address,address,address,uint256)\":{\"notice\":\"Checks if the liquidation should be allowed to occur\"},\"beforeMTokenMint(address,address)\":{\"notice\":\"Checks if the account should be allowed to mint tokens in the given market\"},\"beforeMTokenRedeem(address,address,uint256)\":{\"notice\":\"Checks if the account should be allowed to redeem tokens in the given market\"},\"beforeMTokenRepay(address,address)\":{\"notice\":\"Checks if the account should be allowed to repay a borrow in the given market\"},\"beforeMTokenSeize(address,address,address,address)\":{\"notice\":\"Checks if the seizing of assets should be allowed to occur\"},\"beforeMTokenTransfer(address,address,address,uint256)\":{\"notice\":\"Checks if the account should be allowed to transfer tokens in the given market\"},\"beforeRebalancing(address)\":{\"notice\":\"Checks if the account should be allowed to rebalance tokens\"},\"blacklistOperator()\":{\"notice\":\"Blacklist\"},\"borrowCaps(address)\":{\"notice\":\"Borrow caps enforced by borrowAllowed for each mToken address. Defaults to zero which corresponds to unlimited borrowing.\"},\"checkMembership(address,address)\":{\"notice\":\"Returns whether the given account is entered in the given asset\"},\"checkOutflowVolumeLimit(uint256)\":{\"notice\":\"Checks if new used amount is within the limits of the outflow volume limit\"},\"claimMalda(address)\":{\"notice\":\"Claim all the MALDA accrued by holder in all markets\"},\"claimMalda(address,address[])\":{\"notice\":\"Claim all the MALDA accrued by holder in the specified markets\"},\"claimMalda(address[],address[],bool,bool)\":{\"notice\":\"Claim all MALDA accrued by the holders\"},\"closeFactorMantissa()\":{\"notice\":\"Multiplier used to calculate the maximum repayAmount when liquidating a borrow\"},\"cumulativeOutflowVolume()\":{\"notice\":\"Should return outflow volume\"},\"enterMarkets(address[])\":{\"notice\":\"Add assets to be included in account liquidity calculation\"},\"enterMarketsWithSender(address)\":{\"notice\":\"Add asset (msg.sender) to be included in account liquidity calculation\"},\"exitMarket(address)\":{\"notice\":\"Removes asset from sender's account liquidity calculation\"},\"getAccountLiquidity(address)\":{\"notice\":\"Determine the current account liquidity wrt collateral requirements\"},\"getAllMarkets()\":{\"notice\":\"A list of all markets\"},\"getAssetsIn(address)\":{\"notice\":\"Returns the assets an account has entered\"},\"getHypotheticalAccountLiquidity(address,address,uint256,uint256)\":{\"notice\":\"Determine what the account liquidity would be if the given amounts were redeemed/borrowed\"},\"getUSDValueForAllMarkets()\":{\"notice\":\"Returns USD value for all markets\"},\"isDeprecated(address)\":{\"notice\":\"Returns true if the given mToken market has been deprecated\"},\"isMarketListed(address)\":{\"notice\":\"Returns true/false\"},\"isOperator()\":{\"notice\":\"Should return true\"},\"isPaused(address,uint8)\":{\"notice\":\"Returns if operation is paused\"},\"lastOutflowResetTimestamp()\":{\"notice\":\"Should return last reset time for outflow check\"},\"limitPerTimePeriod()\":{\"notice\":\"Should return outflow limit\"},\"liquidateCalculateSeizeTokens(address,address,uint256)\":{\"notice\":\"Calculate number of tokens of collateral asset to seize given an underlying amount\"},\"liquidationIncentiveMantissa(address)\":{\"notice\":\"Multiplier representing the discount on collateral that a liquidator receives\"},\"markets(address)\":{\"notice\":\"Official mapping of mTokens -> Market metadata\"},\"oracleOperator()\":{\"notice\":\"Oracle which gives the price of any given asset\"},\"outflowResetTimeWindow()\":{\"notice\":\"Should return the outflow volume time window\"},\"rewardDistributor()\":{\"notice\":\"Reward Distributor to markets supply and borrow (including protocol token)\"},\"rolesOperator()\":{\"notice\":\"Roles\"},\"setPaused(address,uint8,bool)\":{\"notice\":\"Set pause for a specific operation\"},\"supplyCaps(address)\":{\"notice\":\"Supply caps enforced by supplyAllowed for each mToken address. Defaults to zero which corresponds to unlimited supplying.\"},\"userWhitelisted(address)\":{\"notice\":\"Returns true/false for user\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/Operator/OperatorStorage.sol\":\"OperatorStorage\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"src/Operator/OperatorStorage.sol\":{\"keccak256\":\"0x3d9e7f2bc64ab9b5de39d4a58f0a72ac0f86c3b95a6318dd03a7b8acde166783\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://dc0d5e026e00f5f5f92cded7704bb602b06f387370ddd7ee0d75111099b01f3a\",\"dweb:/ipfs/QmQfZzADQpBuvTHJpbHz6P4fGhaGWvQgkU8qMkKULCLdjQ\"]},\"src/interfaces/IBlacklister.sol\":{\"keccak256\":\"0x47ed20135507a9d16473027cbee18de5d952846bada761c6248b91c75cd88be1\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://91a886b95dd578156f9b6cec3236e4751aae8a38e5d15e0d6045217c6fbd3d63\",\"dweb:/ipfs/QmRNjz7sGTuMkQNZhwwcg5DNN2eeqTZSAdZmxWxbx66CHU\"]},\"src/interfaces/IOperator.sol\":{\"keccak256\":\"0x0ae3d63eb07518da5a4fd6de7b013b6f55c87defb31900548378cffc70a51f72\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://1d9710c4ac43cb0563449aa519501b4ed18a331c90d0bc7eb79cdc56407bad8a\",\"dweb:/ipfs/Qmafe9Wup2GF9pD83CLWUrRait93vwvReqU1C7EHQhY7WV\"]},\"src/interfaces/IRoles.sol\":{\"keccak256\":\"0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c\",\"dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj\"]},\"src/interfaces/ImToken.sol\":{\"keccak256\":\"0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36\",\"dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL\"]},\"src/utils/ExponentialNoError.sol\":{\"keccak256\":\"0xa97178117d8c32059d55dd2224e5514c54b31f896e565c3834cab3d7502b1bb6\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://1cecc0713709f57a5bb7f64d2fa06b9437a42752fa3fed5d3c0b19d82aa56ef4\",\"dweb:/ipfs/QmQqBCZkzmZxomXLr3b1tHUV55n8EPk3wAUsXgojYSpR41\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "Operator_AssetNotFound"}, {"inputs": [], "type": "error", "name": "Operator_Deactivate_MarketBalanceOwed"}, {"inputs": [], "type": "error", "name": "Operator_EmptyPrice"}, {"inputs": [], "type": "error", "name": "Operator_InsufficientLiquidity"}, {"inputs": [], "type": "error", "name": "Operator_InvalidBlacklistOperator"}, {"inputs": [], "type": "error", "name": "Operator_InvalidCollateralFactor"}, {"inputs": [], "type": "error", "name": "Operator_InvalidInput"}, {"inputs": [], "type": "error", "name": "Operator_InvalidRewardDistributor"}, {"inputs": [], "type": "error", "name": "Operator_InvalidRolesOperator"}, {"inputs": [], "type": "error", "name": "Operator_MarketAlreadyListed"}, {"inputs": [], "type": "error", "name": "Operator_MarketBorrowCapReached"}, {"inputs": [], "type": "error", "name": "Operator_MarketNotListed"}, {"inputs": [], "type": "error", "name": "Operator_MarketSupplyReached"}, {"inputs": [], "type": "error", "name": "Operator_Mismatch"}, {"inputs": [], "type": "error", "name": "Operator_OnlyAdmin"}, {"inputs": [], "type": "error", "name": "Operator_OnlyAdminOrRole"}, {"inputs": [], "type": "error", "name": "Operator_OracleUnderlyingFetchError"}, {"inputs": [], "type": "error", "name": "Operator_OutflowVolumeReached"}, {"inputs": [], "type": "error", "name": "Operator_Paused"}, {"inputs": [], "type": "error", "name": "Operator_PriceFetchFailed"}, {"inputs": [], "type": "error", "name": "Operator_RepayAmountNotValid"}, {"inputs": [], "type": "error", "name": "Operator_RepayingTooMuch"}, {"inputs": [], "type": "error", "name": "Operator_SenderMustBeToken"}, {"inputs": [], "type": "error", "name": "Operator_UserBlacklisted"}, {"inputs": [], "type": "error", "name": "Operator_UserNotWhitelisted"}, {"inputs": [], "type": "error", "name": "Operator_WrongMarket"}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address", "indexed": true}, {"internalType": "enum ImTokenOperationTypes.OperationType", "name": "_type", "type": "uint8", "indexed": false}, {"internalType": "bool", "name": "state", "type": "bool", "indexed": false}], "type": "event", "name": "ActionPaused", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address", "indexed": true}, {"internalType": "address", "name": "account", "type": "address", "indexed": true}], "type": "event", "name": "MarketEntered", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address", "indexed": true}, {"internalType": "address", "name": "account", "type": "address", "indexed": true}], "type": "event", "name": "MarketExited", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address", "indexed": false}], "type": "event", "name": "MarketListed", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "newBorrowCap", "type": "uint256", "indexed": false}], "type": "event", "name": "NewBorrowCap", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "oldCloseFactorMantissa", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "newCloseFactorMantissa", "type": "uint256", "indexed": false}], "type": "event", "name": "NewCloseFactor", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "oldCollateralFactorMantissa", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "newCollateralFactorMantissa", "type": "uint256", "indexed": false}], "type": "event", "name": "NewCollateralFactor", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "market", "type": "address", "indexed": false}, {"internalType": "uint256", "name": "oldLiquidationIncentiveMantissa", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "newLiquidationIncentiveMantissa", "type": "uint256", "indexed": false}], "type": "event", "name": "NewLiquidationIncentive", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "oldPriceOracle", "type": "address", "indexed": true}, {"internalType": "address", "name": "newPriceOracle", "type": "address", "indexed": true}], "type": "event", "name": "NewPriceOracle", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "oldRewardDistributor", "type": "address", "indexed": true}, {"internalType": "address", "name": "newRewardDistributor", "type": "address", "indexed": true}], "type": "event", "name": "NewRewardDistributor", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "oldRoles", "type": "address", "indexed": true}, {"internalType": "address", "name": "newRoles", "type": "address", "indexed": true}], "type": "event", "name": "NewRolesOperator", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "newBorrowCap", "type": "uint256", "indexed": false}], "type": "event", "name": "NewSupplyCap", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "oldLimit", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "newLimit", "type": "uint256", "indexed": false}], "type": "event", "name": "OutflowLimitUpdated", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "oldWindow", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "newWindow", "type": "uint256", "indexed": false}], "type": "event", "name": "OutflowTimeWindowUpdated", "anonymous": false}, {"inputs": [], "type": "event", "name": "OutflowVolumeReset", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "user", "type": "address", "indexed": true}, {"internalType": "bool", "name": "state", "type": "bool", "indexed": false}], "type": "event", "name": "<PERSON>r<PERSON><PERSON><PERSON><PERSON>", "anonymous": false}, {"inputs": [], "type": "event", "name": "WhitelistDisabled", "anonymous": false}, {"inputs": [], "type": "event", "name": "WhitelistEnabled", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "accountAssets", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}], "stateMutability": "view", "type": "function", "name": "afterMTokenMint"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "allMarkets", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}, {"internalType": "address", "name": "borrower", "type": "address"}, {"internalType": "uint256", "name": "borrowAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "beforeMTokenBorrow"}, {"inputs": [{"internalType": "address", "name": "mTokenBorrowed", "type": "address"}, {"internalType": "address", "name": "mTokenCollateral", "type": "address"}, {"internalType": "address", "name": "borrower", "type": "address"}, {"internalType": "uint256", "name": "repayAmount", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "beforeMTokenLiquidate"}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}, {"internalType": "address", "name": "minter", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "beforeMTokenMint"}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}, {"internalType": "address", "name": "redeemer", "type": "address"}, {"internalType": "uint256", "name": "redeemTokens", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "beforeMTokenRedeem"}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}, {"internalType": "address", "name": "borrower", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "beforeMTokenRepay"}, {"inputs": [{"internalType": "address", "name": "mTokenCollateral", "type": "address"}, {"internalType": "address", "name": "mTokenBorrowed", "type": "address"}, {"internalType": "address", "name": "liquidator", "type": "address"}, {"internalType": "address", "name": "borrower", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "beforeMTokenSeize"}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}, {"internalType": "address", "name": "src", "type": "address"}, {"internalType": "address", "name": "dst", "type": "address"}, {"internalType": "uint256", "name": "transferTokens", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "beforeMTokenTransfer"}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "beforeRebalancing"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "blacklistOperator", "outputs": [{"internalType": "contract IBlacklister", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "borrowCaps", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "address", "name": "mToken", "type": "address"}], "stateMutability": "view", "type": "function", "name": "checkMembership", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "checkOutflowVolumeLimit"}, {"inputs": [{"internalType": "address", "name": "holder", "type": "address"}, {"internalType": "address[]", "name": "mTokens", "type": "address[]"}], "stateMutability": "nonpayable", "type": "function", "name": "claimMalda"}, {"inputs": [{"internalType": "address[]", "name": "holders", "type": "address[]"}, {"internalType": "address[]", "name": "mTokens", "type": "address[]"}, {"internalType": "bool", "name": "borrowers", "type": "bool"}, {"internalType": "bool", "name": "suppliers", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "claimMalda"}, {"inputs": [{"internalType": "address", "name": "holder", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "claimMalda"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "closeFactorMantissa", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "cumulativeOutflowVolume", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address[]", "name": "_mTokens", "type": "address[]"}], "stateMutability": "nonpayable", "type": "function", "name": "enterMarkets"}, {"inputs": [{"internalType": "address", "name": "_account", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "enterMarketsWithSender"}, {"inputs": [{"internalType": "address", "name": "_mToken", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "exitMarket"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getAccountLiquidity", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getAllMarkets", "outputs": [{"internalType": "address[]", "name": "mTokens", "type": "address[]"}]}, {"inputs": [{"internalType": "address", "name": "_user", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getAssetsIn", "outputs": [{"internalType": "address[]", "name": "mTokens", "type": "address[]"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "address", "name": "mTokenModify", "type": "address"}, {"internalType": "uint256", "name": "redeemTokens", "type": "uint256"}, {"internalType": "uint256", "name": "borrowAmount", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getHypotheticalAccountLiquidity", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getUSDValueForAllMarkets", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isDeprecated", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "market", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isMarketListed", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "isOperator", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}, {"internalType": "enum ImTokenOperationTypes.OperationType", "name": "_type", "type": "uint8"}], "stateMutability": "view", "type": "function", "name": "isPaused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "lastOutflowResetTimestamp", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "limitPerTimePeriod", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "mTokenBorrowed", "type": "address"}, {"internalType": "address", "name": "mTokenCollateral", "type": "address"}, {"internalType": "uint256", "name": "actualRepayAmount", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "liquidateCalculateSeizeTokens", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "liquidationIncentiveMantissa", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "markets", "outputs": [{"internalType": "bool", "name": "isListed", "type": "bool"}, {"internalType": "uint256", "name": "collateralFactorMantissa", "type": "uint256"}, {"internalType": "bool", "name": "isMalded", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "oracleOperator", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "outflowResetTimeWindow", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "rewardDistributor", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "rolesOperator", "outputs": [{"internalType": "contract IRoles", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}, {"internalType": "enum ImTokenOperationTypes.OperationType", "name": "_type", "type": "uint8"}, {"internalType": "bool", "name": "state", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "setPaused"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "supplyCaps", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "user<PERSON><PERSON><PERSON>sted", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "whitelistEnabled", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}], "devdoc": {"kind": "dev", "methods": {"afterMTokenMint(address)": {"params": {"mToken": "Asset being minted"}}, "beforeMTokenBorrow(address,address,uint256)": {"params": {"borrowAmount": "The amount of underlying the account would borrow", "borrower": "The account which would borrow the asset", "mToken": "The market to verify the borrow against"}}, "beforeMTokenLiquidate(address,address,address,uint256)": {"params": {"borrower": "The address of the borrower", "mTokenBorrowed": "Asset which was borrowed by the borrower", "mTokenCollateral": "Asset which was used as collateral and will be seized", "repayAmount": "The amount of underlying being repaid"}}, "beforeMTokenMint(address,address)": {"params": {"mToken": "The market to verify the mint against", "minter": "The account which would get the minted tokens"}}, "beforeMTokenRedeem(address,address,uint256)": {"params": {"mToken": "The market to verify the redeem against", "redeemTokens": "The number of mTokens to exchange for the underlying asset in the market", "redeemer": "The account which would redeem the tokens"}}, "beforeMTokenRepay(address,address)": {"params": {"borrower": "The account which would borrowed the asset", "mToken": "The market to verify the repay against"}}, "beforeMTokenSeize(address,address,address,address)": {"params": {"borrower": "The address of the borrower", "liquidator": "The address repaying the borrow and seizing the collateral", "mTokenBorrowed": "Asset which was borrowed by the borrower", "mTokenCollateral": "Asset which was used as collateral and will be seized"}}, "beforeMTokenTransfer(address,address,address,uint256)": {"params": {"dst": "The account which receives the tokens", "mToken": "The market to verify the transfer against", "src": "The account which sources the tokens", "transferTokens": "The number of mTokens to transfer"}}, "beforeRebalancing(address)": {"params": {"mToken": "The market to verify the transfer against"}}, "checkMembership(address,address)": {"params": {"account": "The address of the account to check", "mToken": "The mToken to check"}, "returns": {"_0": "True if the account is in the asset, otherwise false."}}, "checkOutflowVolumeLimit(uint256)": {"details": "Sender must be a listed market", "params": {"amount": "New amount"}}, "claimMalda(address)": {"params": {"holder": "The address to claim MALDA for"}}, "claimMalda(address,address[])": {"params": {"holder": "The address to claim MALDA for", "mTokens": "The list of markets to claim MALDA in"}}, "claimMalda(address[],address[],bool,bool)": {"params": {"borrowers": "Whether or not to claim MALDA earned by borrowing", "holders": "The addresses to claim MALDA for", "mTokens": "The list of markets to claim MALDA in", "suppliers": "Whether or not to claim MALDA earned by supplying"}}, "enterMarkets(address[])": {"params": {"_mTokens": "The list of addresses of the mToken markets to be enabled"}}, "enterMarketsWithSender(address)": {"params": {"_account": "The account to add for"}}, "exitMarket(address)": {"details": "Sender must not have an outstanding borrow balance in the asset,  or be providing necessary collateral for an outstanding borrow.", "params": {"_mToken": "The address of the asset to be removed"}}, "getAccountLiquidity(address)": {"returns": {"_0": "account liquidity in excess of collateral requirements,          account shortfall below collateral requirements)"}}, "getAssetsIn(address)": {"params": {"_user": "The address of the account to pull assets for"}, "returns": {"mTokens": "A dynamic list with the assets the account has entered"}}, "getHypotheticalAccountLiquidity(address,address,uint256,uint256)": {"params": {"account": "The account to determine liquidity for", "borrowAmount": "The amount of underlying to hypothetically borrow", "mTokenModify": "The market to hypothetically redeem/borrow in", "redeemTokens": "The number of tokens to hypothetically redeem"}, "returns": {"_0": "hypothetical account liquidity in excess of collateral requirements,         hypothetical account shortfall below collateral requirements)"}}, "isDeprecated(address)": {"details": "All borrows in a deprecated mToken market can be immediately liquidated", "params": {"mToken": "The market to check if deprecated"}}, "isPaused(address,uint8)": {"params": {"_type": "the operation type", "mToken": "The mToken to check"}}, "liquidateCalculateSeizeTokens(address,address,uint256)": {"details": "Used in liquidation (called in mTokenBorrowed.liquidate)", "params": {"actualRepayAmount": "The amount of mTokenBorrowed underlying to convert into mTokenCollateral tokens", "mTokenBorrowed": "The address of the borrowed mToken", "mTokenCollateral": "The address of the collateral mToken"}, "returns": {"_0": "number of mTokenCollateral tokens to be seized in a liquidation"}}, "setPaused(address,uint8,bool)": {"params": {"_type": "The pause operation type", "mToken": "The market token address", "state": "The pause operation status"}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"accountAssets(address,uint256)": {"notice": "Per-account mapping of \"assets you are in\", capped by maxAssets"}, "afterMTokenMint(address)": {"notice": "Validates mint and reverts on rejection. May emit logs."}, "allMarkets(uint256)": {"notice": "A list of all markets"}, "beforeMTokenBorrow(address,address,uint256)": {"notice": "Checks if the account should be allowed to borrow the underlying asset of the given market"}, "beforeMTokenLiquidate(address,address,address,uint256)": {"notice": "Checks if the liquidation should be allowed to occur"}, "beforeMTokenMint(address,address)": {"notice": "Checks if the account should be allowed to mint tokens in the given market"}, "beforeMTokenRedeem(address,address,uint256)": {"notice": "Checks if the account should be allowed to redeem tokens in the given market"}, "beforeMTokenRepay(address,address)": {"notice": "Checks if the account should be allowed to repay a borrow in the given market"}, "beforeMTokenSeize(address,address,address,address)": {"notice": "Checks if the seizing of assets should be allowed to occur"}, "beforeMTokenTransfer(address,address,address,uint256)": {"notice": "Checks if the account should be allowed to transfer tokens in the given market"}, "beforeRebalancing(address)": {"notice": "Checks if the account should be allowed to rebalance tokens"}, "blacklistOperator()": {"notice": "Blacklist"}, "borrowCaps(address)": {"notice": "Borrow caps enforced by borrowAllowed for each mToken address. Defaults to zero which corresponds to unlimited borrowing."}, "checkMembership(address,address)": {"notice": "Returns whether the given account is entered in the given asset"}, "checkOutflowVolumeLimit(uint256)": {"notice": "Checks if new used amount is within the limits of the outflow volume limit"}, "claimMalda(address)": {"notice": "Claim all the MALDA accrued by holder in all markets"}, "claimMalda(address,address[])": {"notice": "Claim all the MALDA accrued by holder in the specified markets"}, "claimMalda(address[],address[],bool,bool)": {"notice": "Claim all MALDA accrued by the holders"}, "closeFactorMantissa()": {"notice": "Multiplier used to calculate the maximum repayAmount when liquidating a borrow"}, "cumulativeOutflowVolume()": {"notice": "Should return outflow volume"}, "enterMarkets(address[])": {"notice": "Add assets to be included in account liquidity calculation"}, "enterMarketsWithSender(address)": {"notice": "Add asset (msg.sender) to be included in account liquidity calculation"}, "exitMarket(address)": {"notice": "Removes asset from sender's account liquidity calculation"}, "getAccountLiquidity(address)": {"notice": "Determine the current account liquidity wrt collateral requirements"}, "getAllMarkets()": {"notice": "A list of all markets"}, "getAssetsIn(address)": {"notice": "Returns the assets an account has entered"}, "getHypotheticalAccountLiquidity(address,address,uint256,uint256)": {"notice": "Determine what the account liquidity would be if the given amounts were redeemed/borrowed"}, "getUSDValueForAllMarkets()": {"notice": "Returns USD value for all markets"}, "isDeprecated(address)": {"notice": "Returns true if the given mToken market has been deprecated"}, "isMarketListed(address)": {"notice": "Returns true/false"}, "isOperator()": {"notice": "Should return true"}, "isPaused(address,uint8)": {"notice": "Returns if operation is paused"}, "lastOutflowResetTimestamp()": {"notice": "Should return last reset time for outflow check"}, "limitPerTimePeriod()": {"notice": "Should return outflow limit"}, "liquidateCalculateSeizeTokens(address,address,uint256)": {"notice": "Calculate number of tokens of collateral asset to seize given an underlying amount"}, "liquidationIncentiveMantissa(address)": {"notice": "Multiplier representing the discount on collateral that a liquidator receives"}, "markets(address)": {"notice": "Official mapping of mTokens -> Market metadata"}, "oracleOperator()": {"notice": "Oracle which gives the price of any given asset"}, "outflowResetTimeWindow()": {"notice": "Should return the outflow volume time window"}, "rewardDistributor()": {"notice": "Reward Distributor to markets supply and borrow (including protocol token)"}, "rolesOperator()": {"notice": "Roles"}, "setPaused(address,uint8,bool)": {"notice": "Set pause for a specific operation"}, "supplyCaps(address)": {"notice": "Supply caps enforced by supplyAllowed for each mToken address. Defaults to zero which corresponds to unlimited supplying."}, "userWhitelisted(address)": {"notice": "Returns true/false for user"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/Operator/OperatorStorage.sol": "OperatorStorage"}, "evmVersion": "london", "libraries": {}}, "sources": {"src/Operator/OperatorStorage.sol": {"keccak256": "0x3d9e7f2bc64ab9b5de39d4a58f0a72ac0f86c3b95a6318dd03a7b8acde166783", "urls": ["bzz-raw://dc0d5e026e00f5f5f92cded7704bb602b06f387370ddd7ee0d75111099b01f3a", "dweb:/ipfs/QmQfZzADQpBuvTHJpbHz6P4fGhaGWvQgkU8qMkKULCLdjQ"], "license": "BSL-1.1"}, "src/interfaces/IBlacklister.sol": {"keccak256": "0x47ed20135507a9d16473027cbee18de5d952846bada761c6248b91c75cd88be1", "urls": ["bzz-raw://91a886b95dd578156f9b6cec3236e4751aae8a38e5d15e0d6045217c6fbd3d63", "dweb:/ipfs/QmRNjz7sGTuMkQNZhwwcg5DNN2eeqTZSAdZmxWxbx66CHU"], "license": "BSL-1.1"}, "src/interfaces/IOperator.sol": {"keccak256": "0x0ae3d63eb07518da5a4fd6de7b013b6f55c87defb31900548378cffc70a51f72", "urls": ["bzz-raw://1d9710c4ac43cb0563449aa519501b4ed18a331c90d0bc7eb79cdc56407bad8a", "dweb:/ipfs/Qmafe9Wup2GF9pD83CLWUrRait93vwvReqU1C7EHQhY7WV"], "license": "BSL-1.1"}, "src/interfaces/IRoles.sol": {"keccak256": "0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365", "urls": ["bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c", "dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj"], "license": "BSL-1.1"}, "src/interfaces/ImToken.sol": {"keccak256": "0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f", "urls": ["bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36", "dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL"], "license": "BSL-1.1"}, "src/utils/ExponentialNoError.sol": {"keccak256": "0xa97178117d8c32059d55dd2224e5514c54b31f896e565c3834cab3d7502b1bb6", "urls": ["bzz-raw://1cecc0713709f57a5bb7f64d2fa06b9437a42752fa3fed5d3c0b19d82aa56ef4", "dweb:/ipfs/QmQqBCZkzmZxomXLr3b1tHUV55n8EPk3wAUsXgojYSpR41"], "license": "BSL-1.1"}}, "version": 1}, "id": 128}