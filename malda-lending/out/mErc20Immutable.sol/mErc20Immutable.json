{"abi": [{"type": "constructor", "inputs": [{"name": "underlying_", "type": "address", "internalType": "address"}, {"name": "operator_", "type": "address", "internalType": "address"}, {"name": "interestRateModel_", "type": "address", "internalType": "address"}, {"name": "initialExchangeRateMantissa_", "type": "uint256", "internalType": "uint256"}, {"name": "name_", "type": "string", "internalType": "string"}, {"name": "symbol_", "type": "string", "internalType": "string"}, {"name": "decimals_", "type": "uint8", "internalType": "uint8"}, {"name": "admin_", "type": "address", "internalType": "address payable"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "acceptAdmin", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "accrualBlockTimestamp", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "accrueInterest", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "addReserves", "inputs": [{"name": "addAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "admin", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address payable"}], "stateMutability": "view"}, {"type": "function", "name": "allowance", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "spender", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "approve", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "balanceOf", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "balanceOfUnderlying", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "borrow", "inputs": [{"name": "borrowAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "borrowBalanceCurrent", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "borrowBalanceStored", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "borrowIndex", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "borrowRateMaxMantissa", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "borrowRatePerBlock", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "decimals", "inputs": [], "outputs": [{"name": "", "type": "uint8", "internalType": "uint8"}], "stateMutability": "view"}, {"type": "function", "name": "exchangeRateCurrent", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "exchangeRateStored", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getAccountSnapshot", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getCash", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "interestRateModel", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "liquidate", "inputs": [{"name": "borrower", "type": "address", "internalType": "address"}, {"name": "repayAmount", "type": "uint256", "internalType": "uint256"}, {"name": "mTokenCollateral", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "mint", "inputs": [{"name": "mintAmount", "type": "uint256", "internalType": "uint256"}, {"name": "receiver", "type": "address", "internalType": "address"}, {"name": "minAmountOut", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "name", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "operator", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "pendingAdmin", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address payable"}], "stateMutability": "view"}, {"type": "function", "name": "redeem", "inputs": [{"name": "redeemTokens", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "redeemUnderlying", "inputs": [{"name": "redeemAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "reduceReserves", "inputs": [{"name": "reduceAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "repay", "inputs": [{"name": "repayAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "repayBehalf", "inputs": [{"name": "borrower", "type": "address", "internalType": "address"}, {"name": "repayAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "reserveFactorMantissa", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "rolesOperator", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IRoles"}], "stateMutability": "view"}, {"type": "function", "name": "seize", "inputs": [{"name": "liquidator", "type": "address", "internalType": "address"}, {"name": "borrower", "type": "address", "internalType": "address"}, {"name": "seizeTokens", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setBorrowRateMaxMantissa", "inputs": [{"name": "maxMantissa", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setInterestRateModel", "inputs": [{"name": "newInterestRateModel", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setOperator", "inputs": [{"name": "_operator", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setPendingAdmin", "inputs": [{"name": "newPendingAdmin", "type": "address", "internalType": "address payable"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setReserveFactor", "inputs": [{"name": "newReserveFactorMantissa", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setRolesOperator", "inputs": [{"name": "_roles", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "supplyRatePerBlock", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "sweepToken", "inputs": [{"name": "token", "type": "address", "internalType": "contract IERC20"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "symbol", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "totalBorrows", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "totalBorrowsCurrent", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "totalReserves", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "totalSupply", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "totalUnderlying", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "transfer", "inputs": [{"name": "dst", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferFrom", "inputs": [{"name": "src", "type": "address", "internalType": "address"}, {"name": "dst", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "underlying", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "event", "name": "AccrueInterest", "inputs": [{"name": "cashPrior", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "interestAccumulated", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "borrowIndex", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "totalBorrows", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Approval", "inputs": [{"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "spender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Borrow", "inputs": [{"name": "borrower", "type": "address", "indexed": true, "internalType": "address"}, {"name": "borrowAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "accountBorrows", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "totalBorrows", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "LiquidateBorrow", "inputs": [{"name": "liquidator", "type": "address", "indexed": true, "internalType": "address"}, {"name": "borrower", "type": "address", "indexed": true, "internalType": "address"}, {"name": "repayAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "mTokenCollateral", "type": "address", "indexed": true, "internalType": "address"}, {"name": "seizeTokens", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Mint", "inputs": [{"name": "minter", "type": "address", "indexed": true, "internalType": "address"}, {"name": "receiver", "type": "address", "indexed": true, "internalType": "address"}, {"name": "mintAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "mintTokens", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "NewBorrowRateMaxMantissa", "inputs": [{"name": "oldVal", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "maxMantissa", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "NewMarketInterestRateModel", "inputs": [{"name": "oldInterestRateModel", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newInterestRateModel", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "NewOperator", "inputs": [{"name": "oldOperator", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newOperator", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "NewReserveFactor", "inputs": [{"name": "oldReserveFactorMantissa", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "newReserveFactorMantissa", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "NewRolesOperator", "inputs": [{"name": "oldRoles", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newRoles", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Redeem", "inputs": [{"name": "redeemer", "type": "address", "indexed": true, "internalType": "address"}, {"name": "redeemAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "redeemTokens", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "RepayBorrow", "inputs": [{"name": "payer", "type": "address", "indexed": true, "internalType": "address"}, {"name": "borrower", "type": "address", "indexed": true, "internalType": "address"}, {"name": "repayAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "accountBorrows", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "totalBorrows", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "ReservesAdded", "inputs": [{"name": "benefactor", "type": "address", "indexed": true, "internalType": "address"}, {"name": "addAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "newTotalReserves", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "ReservesReduced", "inputs": [{"name": "admin", "type": "address", "indexed": true, "internalType": "address"}, {"name": "reduceAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "newTotalReserves", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "SameChainFlowStateUpdated", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "_oldState", "type": "bool", "indexed": false, "internalType": "bool"}, {"name": "_newState", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "Transfer", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "ZkVerifierUpdated", "inputs": [{"name": "oldVerifier", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newVerifier", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "AddressEmptyCode", "inputs": [{"name": "target", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "AddressInsufficientBalance", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "FailedInnerCall", "inputs": []}, {"type": "error", "name": "ReentrancyGuardReentrantCall", "inputs": []}, {"type": "error", "name": "SafeERC20FailedOperation", "inputs": [{"name": "token", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "mErc20_TokenNotValid", "inputs": []}, {"type": "error", "name": "mt_AlreadyInitialized", "inputs": []}, {"type": "error", "name": "mt_BorrowCashNotAvailable", "inputs": []}, {"type": "error", "name": "mt_BorrowRateTooHigh", "inputs": []}, {"type": "error", "name": "mt_CollateralBlockTimestampNotValid", "inputs": []}, {"type": "error", "name": "mt_ExchangeRateNotValid", "inputs": []}, {"type": "error", "name": "mt_InvalidInput", "inputs": []}, {"type": "error", "name": "mt_LiquidateSeizeTooMuch", "inputs": []}, {"type": "error", "name": "mt_MarketMethodNotValid", "inputs": []}, {"type": "error", "name": "mt_MinAmountNotValid", "inputs": []}, {"type": "error", "name": "mt_OnlyAdmin", "inputs": []}, {"type": "error", "name": "mt_OnlyAdminOrRole", "inputs": []}, {"type": "error", "name": "mt_RedeemCashNotAvailable", "inputs": []}, {"type": "error", "name": "mt_RedeemEmpty", "inputs": []}, {"type": "error", "name": "mt_RedeemTransferOutNotPossible", "inputs": []}, {"type": "error", "name": "mt_ReserveCashNotAvailable", "inputs": []}, {"type": "error", "name": "mt_ReserveFactorTooHigh", "inputs": []}, {"type": "error", "name": "mt_SameChainOperationsAreDisabled", "inputs": []}, {"type": "error", "name": "mt_TransferNotValid", "inputs": []}], "bytecode": {"object": "0x608060405265048c27395000600e5534801561001a57600080fd5b5060405161340c38038061340c83398101604081905261003991610477565b600160135565048c27395000600e55600080546001600160a01b0319163317905561006988888888888888610096565b600080546001600160a01b0319166001600160a01b0392909216919091179055506106d995505050505050565b6100a486868686868661012b565b601480546001600160a01b0319166001600160a01b038916908117909155604080516318160ddd60e01b815290516318160ddd916004808201926020929091908290030181865afa1580156100fd573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906101219190610550565b5050505050505050565b60085415801561013b5750600954155b6101585760405163544e961960e11b815260040160405180910390fd5b6000841161017957604051631df62a7d60e31b815260040160405180910390fd5b6012849055610187866101d5565b42600855670de0b6b3a76400006009556101a0856102b0565b60046101ac84826105f2565b5060056101b983826105f2565b506006805460ff191660ff929092169190911790555050505050565b806001600160a01b0316634456eda26040518163ffffffff1660e01b8152600401602060405180830381865afa158015610213573d6000803e3d6000fd5b505050506040513d601f19601f8201168201806040525081019061023791906106b0565b61025457604051631ffd35c760e11b815260040160405180910390fd5b6002546040516001600160a01b038084169216907ff1e04d73c4304b5ff164f9d10c7473e2a1593b740674a6107975e2a7001c1e5c90600090a3600280546001600160a01b0319166001600160a01b0392909216919091179055565b806001600160a01b0316632191f92a6040518163ffffffff1660e01b8152600401602060405180830381865afa1580156102ee573d6000803e3d6000fd5b505050506040513d601f19601f8201168201806040525081019061031291906106b0565b61032f57604051631ffd35c760e11b815260040160405180910390fd5b6006546040516001600160a01b0380841692610100900416907fedffc32e068c7c95dfd4bdfd5c4d939a084d6b11c4199eac8436ed234d72f92690600090a3600680546001600160a01b0390921661010002610100600160a81b0319909216919091179055565b6001600160a01b03811681146103ab57600080fd5b50565b634e487b7160e01b600052604160045260246000fd5b600082601f8301126103d557600080fd5b81516001600160401b038111156103ee576103ee6103ae565b604051601f8201601f19908116603f011681016001600160401b038111828210171561041c5761041c6103ae565b60405281815283820160200185101561043457600080fd5b60005b8281101561045357602081860181015183830182015201610437565b506000918101602001919091529392505050565b805161047281610396565b919050565b600080600080600080600080610100898b03121561049457600080fd5b885161049f81610396565b60208a01519098506104b081610396565b60408a01519097506104c181610396565b60608a015160808b015191975095506001600160401b038111156104e457600080fd5b6104f08b828c016103c4565b60a08b015190955090506001600160401b0381111561050e57600080fd5b61051a8b828c016103c4565b60c08b0151909450905060ff8116811461053357600080fd5b915061054160e08a01610467565b90509295985092959890939650565b60006020828403121561056257600080fd5b5051919050565b600181811c9082168061057d57607f821691505b60208210810361059d57634e487b7160e01b600052602260045260246000fd5b50919050565b601f8211156105ed57806000526020600020601f840160051c810160208510156105ca5750805b601f840160051c820191505b818110156105ea57600081556001016105d6565b50505b505050565b81516001600160401b0381111561060b5761060b6103ae565b61061f816106198454610569565b846105a3565b6020601f821160018114610653576000831561063b5750848201515b600019600385901b1c1916600184901b1784556105ea565b600084815260208120601f198516915b828110156106835787850151825560209485019460019092019101610663565b50848210156106a15786840151600019600387901b60f8161c191681555b50505050600190811b01905550565b6000602082840312156106c257600080fd5b815180151581146106d257600080fd5b9392505050565b612d24806106e86000396000f3fe608060405234801561001057600080fd5b50600436106102f15760003560e01c8063836a10401161019d578063c37f68e2116100e9578063e67218cd116100a2578063f3fdb15a1161007c578063f3fdb15a14610671578063f851a44014610689578063f89416ee1461069c578063f8f9da28146106af57600080fd5b8063e67218cd14610642578063e90a182f14610655578063ee27a2f21461066857600080fd5b8063c37f68e2146105a3578063c5ebeaec146105d1578063c70920bc146105e4578063cfa99201146105ed578063db006a75146105f6578063dd62ed3e1461060957600080fd5b8063a6afed9511610156578063ae9d70b011610130578063ae9d70b01461056d578063b2a02ff114610575578063b3ab15fb14610588578063bd6d894d1461059b57600080fd5b8063a6afed9514610549578063a9059cbb14610551578063aa5af0fd1461056457600080fd5b8063836a1040146104ec578063852a12e3146104ff5780638bcd4016146105125780638f840ddd1461052557806395d89b411461052e57806395dd91931461053657600080fd5b8063371fd8e61161025c5780634fecab70116102155780636f307dc3116101ef5780636f307dc31461049557806370a08231146104a857806373acee98146104d15780637821a514146104d957600080fd5b80634fecab701461045c578063570ca7351461046f5780635bdcecb71461048257600080fd5b8063371fd8e6146103ff5780633af9e669146104125780633b1d21a21461042557806347bd37181461042d5780634914c008146104365780634dd18bf51461044957600080fd5b806318160ddd116102ae57806318160ddd1461037e578063182df0f5146103875780631c4469831461038f57806323b872dd146103a2578063********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", "sourceMap": "866:1190:176:-:0;;;2641:9:180;2602:48;;1464:590:176;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1713:1:48;1917:7;:21;1339:9:178;1315:21;:33;-1:-1:-1;1747:27:176;;-1:-1:-1;;;;;;1747:27:176;1763:10;1747:27;;;1818:140;1849:11;1862:9;1873:18;1893:28;1923:5;1930:7;1939:9;1818:17;:140::i;:::-;2033:5;:14;;-1:-1:-1;;;;;;2033:14:176;-1:-1:-1;;;;;2033:14:176;;;;;;;;;;-1:-1:-1;866:1190:176;;-1:-1:-1;;;;;;866:1190:176;2056:573:175;2386:105;2404:9;2415:18;2435:28;2465:5;2472:7;2481:9;2386:17;:105::i;:::-;2548:10;:24;;-1:-1:-1;;;;;;2548:24:175;-1:-1:-1;;;;;2548:24:175;;;;;;;;2582:40;;;-1:-1:-1;;;2582:40:175;;;;:38;;:40;;;;;;;;;;;;;;;2548:24;2582:40;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;2056:573;;;;;;;:::o;1788:771:178:-;2042:21;;:26;:46;;;;-1:-1:-1;2072:11:178;;:16;2042:46;2034:80;;;;-1:-1:-1;;;2034:80:178;;;;;;;;;;;;2163:1;2132:28;:32;2124:68;;;;-1:-1:-1;;;2124:68:178;;;;;;;;;;;;2239:27;:58;;;2308:23;2321:9;2308:12;:23::i;:::-;8220:15:180;2342:21:178;:44;1224:4:195;2396:11:178;:25;2432:41;2454:18;2432:21;:41::i;:::-;2484:4;:12;2491:5;2484:4;:12;:::i;:::-;-1:-1:-1;2506:6:178;:16;2515:7;2506:6;:16;:::i;:::-;-1:-1:-1;2532:8:178;:20;;-1:-1:-1;;2532:20:178;;;;;;;;;;;;-1:-1:-1;;;;;1788:771:178:o;4923:215:179:-;5001:9;-1:-1:-1;;;;;4991:31:179;;:33;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;4983:69;;;;-1:-1:-1;;;4983:69:179;;;;;;;;;;;;5080:8;;5068:32;;-1:-1:-1;;;;;5068:32:179;;;;5080:8;;5068:32;;5080:8;;5068:32;5111:8;:20;;-1:-1:-1;;;;;;5111:20:179;-1:-1:-1;;;;;5111:20:179;;;;;;;;;;4923:215::o;4518:399::-;4706:20;-1:-1:-1;;;;;4687:60:179;;:62;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;4679:98;;;;-1:-1:-1;;;4679:98:179;;;;;;;;;;;;4820:17;;4793:67;;-1:-1:-1;;;;;4793:67:179;;;;4820:17;;;;;4793:67;;;;;4870:17;:40;;-1:-1:-1;;;;;4870:40:179;;;;;-1:-1:-1;;;;;;4870:40:179;;;;;;;;;4518:399::o;14:131:242:-;-1:-1:-1;;;;;89:31:242;;79:42;;69:70;;135:1;132;125:12;69:70;14:131;:::o;150:127::-;211:10;206:3;202:20;199:1;192:31;242:4;239:1;232:15;266:4;263:1;256:15;282:834;336:5;389:3;382:4;374:6;370:17;366:27;356:55;;407:1;404;397:12;356:55;434:13;;-1:-1:-1;;;;;459:30:242;;456:56;;;492:18;;:::i;:::-;541:2;535:9;633:2;595:17;;-1:-1:-1;;591:31:242;;;624:2;587:40;583:54;571:67;;-1:-1:-1;;;;;653:34:242;;689:22;;;650:62;647:88;;;715:18;;:::i;:::-;751:2;744:22;775;;;816:19;;;837:4;812:30;809:39;-1:-1:-1;806:59:242;;;861:1;858;851:12;806:59;883:1;893:143;907:6;904:1;901:13;893:143;;;1019:4;1003:14;;;999:25;;993:32;970:14;;;966:25;;959:67;922:12;893:143;;;-1:-1:-1;1084:1:242;1056:19;;;1077:4;1052:30;1045:41;;;;1060:6;282:834;-1:-1:-1;;;282:834:242:o;1121:146::-;1208:13;;1230:31;1208:13;1230:31;:::i;:::-;1121:146;;;:::o;1272:1351::-;1431:6;1439;1447;1455;1463;1471;1479;1487;1540:3;1528:9;1519:7;1515:23;1511:33;1508:53;;;1557:1;1554;1547:12;1508:53;1589:9;1583:16;1608:31;1633:5;1608:31;:::i;:::-;1708:2;1693:18;;1687:25;1658:5;;-1:-1:-1;1721:33:242;1687:25;1721:33;:::i;:::-;1825:2;1810:18;;1804:25;1773:7;;-1:-1:-1;1838:33:242;1804:25;1838:33;:::i;:::-;1963:2;1948:18;;1942:25;2037:3;2022:19;;2016:26;1890:7;;-1:-1:-1;1942:25:242;-1:-1:-1;;;;;;2054:30:242;;2051:50;;;2097:1;2094;2087:12;2051:50;2120:61;2173:7;2164:6;2153:9;2149:22;2120:61;:::i;:::-;2227:3;2212:19;;2206:26;2110:71;;-1:-1:-1;2206:26:242;-1:-1:-1;;;;;;2244:32:242;;2241:52;;;2289:1;2286;2279:12;2241:52;2312:63;2367:7;2356:8;2345:9;2341:24;2312:63;:::i;:::-;2441:3;2426:19;;2420:26;2302:73;;-1:-1:-1;2420:26:242;-1:-1:-1;2490:4:242;2477:18;;2465:31;;2455:59;;2510:1;2507;2500:12;2455:59;2533:7;-1:-1:-1;2559:58:242;2612:3;2597:19;;2559:58;:::i;:::-;2549:68;;1272:1351;;;;;;;;;;;:::o;2628:230::-;2698:6;2751:2;2739:9;2730:7;2726:23;2722:32;2719:52;;;2767:1;2764;2757:12;2719:52;-1:-1:-1;2812:16:242;;2628:230;-1:-1:-1;2628:230:242:o;2863:380::-;2942:1;2938:12;;;;2985;;;3006:61;;3060:4;3052:6;3048:17;3038:27;;3006:61;3113:2;3105:6;3102:14;3082:18;3079:38;3076:161;;3159:10;3154:3;3150:20;3147:1;3140:31;3194:4;3191:1;3184:15;3222:4;3219:1;3212:15;3076:161;;2863:380;;;:::o;3374:518::-;3476:2;3471:3;3468:11;3465:421;;;3512:5;3509:1;3502:16;3556:4;3553:1;3543:18;3626:2;3614:10;3610:19;3607:1;3603:27;3597:4;3593:38;3662:4;3650:10;3647:20;3644:47;;;-1:-1:-1;3685:4:242;3644:47;3740:2;3735:3;3731:12;3728:1;3724:20;3718:4;3714:31;3704:41;;3795:81;3813:2;3806:5;3803:13;3795:81;;;3872:1;3858:16;;3839:1;3828:13;3795:81;;;3799:3;;3465:421;3374:518;;;:::o;4068:1299::-;4188:10;;-1:-1:-1;;;;;4210:30:242;;4207:56;;;4243:18;;:::i;:::-;4272:97;4362:6;4322:38;4354:4;4348:11;4322:38;:::i;:::-;4316:4;4272:97;:::i;:::-;4418:4;4449:2;4438:14;;4466:1;4461:649;;;;5154:1;5171:6;5168:89;;;-1:-1:-1;5223:19:242;;;5217:26;5168:89;-1:-1:-1;;4025:1:242;4021:11;;;4017:24;4013:29;4003:40;4049:1;4045:11;;;4000:57;5270:81;;4431:930;;4461:649;3321:1;3314:14;;;3358:4;3345:18;;-1:-1:-1;;4497:20:242;;;4615:222;4629:7;4626:1;4623:14;4615:222;;;4711:19;;;4705:26;4690:42;;4818:4;4803:20;;;;4771:1;4759:14;;;;4645:12;4615:222;;;4619:3;4865:6;4856:7;4853:19;4850:201;;;4926:19;;;4920:26;-1:-1:-1;;5009:1:242;5005:14;;;5021:3;5001:24;4997:37;4993:42;4978:58;4963:74;;4850:201;-1:-1:-1;;;;5097:1:242;5081:14;;;5077:22;5064:36;;-1:-1:-1;4068:1299:242:o;5372:277::-;5439:6;5492:2;5480:9;5471:7;5467:23;5463:32;5460:52;;;5508:1;5505;5498:12;5460:52;5540:9;5534:16;5593:5;5586:13;5579:21;5572:5;5569:32;5559:60;;5615:1;5612;5605:12;5559:60;5638:5;5372:277;-1:-1:-1;;;5372:277:242:o;:::-;866:1190:176;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x608060405234801561001057600080fd5b50600436106102f15760003560e01c8063836a10401161019d578063c37f68e2116100e9578063e67218cd116100a2578063f3fdb15a1161007c578063f3fdb15a14610671578063f851a44014610689578063f89416ee1461069c578063f8f9da28146106af57600080fd5b8063e67218cd14610642578063e90a182f14610655578063ee27a2f21461066857600080fd5b8063c37f68e2146105a3578063c5ebeaec146105d1578063c70920bc146105e4578063cfa99201146105ed578063db006a75146105f6578063dd62ed3e1461060957600080fd5b8063a6afed9511610156578063ae9d70b011610130578063ae9d70b01461056d578063b2a02ff114610575578063b3ab15fb14610588578063bd6d894d1461059b57600080fd5b8063a6afed9514610549578063a9059cbb14610551578063aa5af0fd1461056457600080fd5b8063836a1040146104ec578063852a12e3146104ff5780638bcd4016146105125780638f840ddd1461052557806395d89b411461052e57806395dd91931461053657600080fd5b8063371fd8e61161025c5780634fecab70116102155780636f307dc3116101ef5780636f307dc31461049557806370a08231146104a857806373acee98146104d15780637821a514146104d957600080fd5b80634fecab701461045c578063570ca7351461046f5780635bdcecb71461048257600080fd5b8063371fd8e6146103ff5780633af9e669146104125780633b1d21a21461042557806347bd37181461042d5780634914c008146104365780634dd18bf51461044957600080fd5b806318160ddd116102ae57806318160ddd1461037e578063182df0f5146103875780631c4469831461038f57806323b872dd146103a2578063********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", "sourceMap": "866:1190:176:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1644:18:180;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;6315:1043:178;;;;;;:::i;:::-;;:::i;:::-;;5158:221;;;;;;:::i;:::-;;:::i;:::-;;;1574:14:242;;1567:22;1549:41;;1537:2;1522:18;5158:221:178;1409:187:242;3944:298:179;;;:::i;1996:36:180:-;;;;;;;;;1747:25:242;;;1735:2;1720:18;1996:36:180;1601:177:242;5667:176:178;;;;;;:::i;:::-;;:::i;2406:26:180:-;;;;;;4471:116:178;;;:::i;2972:354:179:-;;;;;;:::i;:::-;;:::i;4915:194:178:-;;;;;;:::i;:::-;;:::i;1355:35:180:-;;;;;-1:-1:-1;;;;;1355:35:180;;;;;;-1:-1:-1;;;;;2728:32:242;;;2710:51;;2698:2;2683:18;1355:35:180;2548:219:242;1796:21:180;;;;;;;;;;;;2944:4:242;2932:17;;;2914:36;;2902:2;2887:18;1796:21:180;2772:184:242;3843:112:175;;;;;;:::i;:::-;;:::i;3026:232:178:-;;;;;;:::i;:::-;;:::i;4323:99::-;;;:::i;2244:27:180:-;;;;;;4205:179:175;;;;;;:::i;:::-;;:::i;3640:124:179:-;;;;;;:::i;:::-;;:::i;1513:27:180:-;;;;;-1:-1:-1;;;;;1513:27:180;;;1440:23;;;;;-1:-1:-1;;;;;1440:23:180;;;4004:152:175;;;;;;:::i;:::-;;:::i;1457:25::-;;;;;-1:-1:-1;;;;;1457:25:175;;;2858:119:178;;;;;;:::i;:::-;-1:-1:-1;;;;;2950:20:178;2924:7;2950:20;;;:13;:20;;;;;;;2858:119;5475:143;;;:::i;4433:96:175:-;;;;;;:::i;:::-;;:::i;3159:159::-;;;;;;:::i;:::-;;:::i;3519:123::-;;;;;;:::i;:::-;;:::i;2155:263:179:-;;;;;;:::i;:::-;;:::i;2321:28:180:-;;;;;;1719:20;;;:::i;4134:140:178:-;;;;;;:::i;:::-;;:::i;7900:77:180:-;;;:::i;4682:184:178:-;;;;;;:::i;:::-;;:::i;2168:26:180:-;;;;;;3848:237:178;;;:::i;6091:175::-;;;;;;:::i;:::-;;:::i;1430:99:179:-;;;;;;:::i;:::-;;:::i;5892:150:178:-;;;:::i;3352:206::-;;;;;;:::i;:::-;;:::i;:::-;;;;4860:25:242;;;4916:2;4901:18;;4894:34;;;;4944:18;;;4937:34;4848:2;4833:18;3352:206:178;4658:319:242;3691:103:175;;;;;;:::i;:::-;;:::i;2489:30:180:-;;;;;;2082:36;;;;;;3367:103:175;;;;;;:::i;:::-;;:::i;2652:150:178:-;;;;;;:::i;:::-;-1:-1:-1;;;;;2761:25:178;;;2735:7;2761:25;;;:18;:25;;;;;;;;:34;;;;;;;;;;;;;2652:150;2424:343:179;;;;;;:::i;:::-;;:::i;2874:190:175:-;;;;;;:::i;:::-;;:::i;2602:48:180:-;;;;;;1914:32;;;;;;;;-1:-1:-1;;;;;1914:32:180;;;1277:28;;;;;-1:-1:-1;;;;;1277:28:180;;;1650:231:179;;;;;;:::i;:::-;;:::i;3607:192:178:-;;;:::i;1644:18:180:-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;6315:1043:178:-;2356:21:48;:19;:21::i;:::-;6437:5:178::1;::::0;-1:-1:-1;;;;;6437:5:178::1;6423:10;:19;::::0;:95:::1;;-1:-1:-1::0;6446:13:178::1;::::0;6485:32:::1;::::0;;-1:-1:-1;;;6485:32:178;;;;-1:-1:-1;;;;;6446:13:178;;::::1;::::0;:26:::1;::::0;6473:10:::1;::::0;6446:13;;6485:30:::1;::::0;:32:::1;::::0;;::::1;::::0;::::1;::::0;;;;;;;;6446:13;6485:32:::1;;;;;;;;;::::0;::::1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;6446:72;::::0;-1:-1:-1;;;;;;6446:72:178::1;::::0;;;;;;-1:-1:-1;;;;;6529:32:242;;;6446:72:178::1;::::0;::::1;6511:51:242::0;6578:18;;;6571:34;6484:18;;6446:72:178::1;;;;;;;;;;;;;;;;;::::0;::::1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;6402:160;;;;-1:-1:-1::0;;;6402:160:178::1;;;;;;;;;;;;6573:17;:15;:17::i;:::-;6628:12;6609:15;4894::175::0;;;4803:113;6609:15:178::1;:31;;6601:70;;;;-1:-1:-1::0;;;6601:70:178::1;;;;;;;;;;;;6705:13;;6689:12;:29;;6681:68;;;;-1:-1:-1::0;;;6681:68:178::1;;;;;;;;;;;;6916:24;6959:12;6943:13;;:28;;;;:::i;:::-;7042:13;:32:::0;;;6916:55;-1:-1:-1;7191:49:178::1;7214:10;7227:12:::0;7191:14:::1;:49::i;:::-;7269:12;7250:15;;:31;;;;;;;:::i;:::-;::::0;;;-1:-1:-1;;7313:5:178::1;::::0;7297:54:::1;::::0;;7337:25:242;;;7393:2;7378:18;;7371:34;;;-1:-1:-1;;;;;7313:5:178;;::::1;::::0;7297:54:::1;::::0;7310:18:242;7297:54:178::1;;;;;;;6392:966;2398:20:48::0;1713:1;2924:7;:21;2744:208;2398:20;6315:1043:178;:::o;5158:221::-;5270:10;5235:4;5251:30;;;:18;:30;;;;;;;;-1:-1:-1;;;;;5251:39:178;;;;;;;;;;:48;;;5314:37;5235:4;;5251:39;;5314:37;;;;5293:6;1747:25:242;;1735:2;1720:18;;1601:177;5314:37:178;;;;;;;;-1:-1:-1;5368:4:178;5158:221;;;;;:::o;3944:298:179:-;4048:12;;-1:-1:-1;;;;;4048:12:179;4034:10;:26;4026:51;;;;-1:-1:-1;;;4026:51:179;;;;;;;;;;;;4143:12;;;;4135:20;;-1:-1:-1;;;;;;4135:20:179;;;-1:-1:-1;;;;;4143:12:179;;4135:20;;;;4201:34;;;3944:298::o;5667:176:178:-;5754:7;2356:21:48;:19;:21::i;:::-;5773:17:178::1;:15;:17::i;:::-;5807:29;5828:7;5807:20;:29::i;:::-;5800:36;;2398:20:48::0;1713:1;2924:7;:21;2744:208;2398:20;5667:176:178;;;:::o;4471:116::-;4533:7;4559:21;:19;:21::i;:::-;4552:28;;4471:116;:::o;2972:354:179:-;1231:5;;-1:-1:-1;;;;;1231:5:179;1217:10;:19;1209:44;;;;-1:-1:-1;;;1209:44:179;;;;;;;;;;;;3061:17:::1;:15;:17::i;:::-;3742:4:180;3097:24:179;:55;;3089:91;;;;-1:-1:-1::0;;;3089:91:179::1;;;;;;;;;;;;3213:21;::::0;3196:65:::1;::::0;;7337:25:242;;;7393:2;7378:18;;7371:34;;;3196:65:179::1;::::0;7310:18:242;3196:65:179::1;;;;;;;3271:21;:48:::0;2972:354::o;4915:194:178:-;5019:4;2356:21:48;:19;:21::i;:::-;5035:45:178::1;5051:10;5063:3;5068;5073:6;5035:15;:45::i;:::-;-1:-1:-1::0;5098:4:178::1;2398:20:48::0;1713:1;2924:7;:21;2744:208;2398:20;4915:194:178;;;;;:::o;3843:112:175:-;3897:7;3923:25;3930:11;3943:4;3923:6;:25::i;3026:232:178:-;3097:7;3116:23;3142:38;;;;;;;;3157:21;:19;:21::i;:::-;3142:38;;-1:-1:-1;;;;;3230:20:178;;;;;;:13;:20;;;;;;3116:64;;-1:-1:-1;3197:54:178;;3116:64;;3197:18;:54::i;4323:99::-;4374:7;4400:15;4894::175;;;4803:113;4205:179;4308:69;4319:10;4331:8;4341:11;4354:16;4372:4;4308:10;:69::i;:::-;4205:179;;;:::o;3640:124:179:-;1231:5;;-1:-1:-1;;;;;1231:5:179;1217:10;:19;1209:44;;;;-1:-1:-1;;;1209:44:179;;;;;;;;;;;;3727:12:::1;:30:::0;;-1:-1:-1;;;;;;3727:30:179::1;-1:-1:-1::0;;;;;3727:30:179;;;::::1;::::0;;;::::1;::::0;;3640:124::o;4004:152:175:-;4082:7;4108:41;4121:8;4131:11;4144:4;4108:12;:41::i;5475:143:178:-;5546:7;2356:21:48;:19;:21::i;:::-;5565:17:178::1;:15;:17::i;:::-;-1:-1:-1::0;5599:12:178::1;::::0;2398:20:48;1713:1;2924:7;:21;2744:208;2398:20;5475:143:178;:::o;4433:96:175:-;4499:23;4512:9;4499:12;:23::i;3159:159::-;3252:59;3258:10;3270:8;3280:10;3292:12;3306:4;3252:5;:59::i;3519:123::-;3586:49;3604:10;3616:12;3630:4;3586:17;:49::i;2155:263:179:-;1231:5;;-1:-1:-1;;;;;1231:5:179;1217:10;:19;1209:44;;;;-1:-1:-1;;;1209:44:179;;;;;;;;;;;;2244:17:::1;:15;:17::i;:::-;2368:43;2390:20;2368:21;:43::i;1719:20:180:-:0;;;;;;;:::i;4134:140:178:-;4212:7;4238:29;4259:7;4238:20;:29::i;7900:77:180:-;7953:17;:15;:17::i;:::-;7900:77::o;4682:184:178:-;4769:4;2356:21:48;:19;:21::i;:::-;4785:52:178::1;4801:10;4813;4825:3;4830:6;4785:15;:52::i;:::-;-1:-1:-1::0;4855:4:178::1;2398:20:48::0;1713:1;2924:7;:21;2744:208;3848:237:178;3955:17;;3910:7;;-1:-1:-1;;;;;3955:17:178;;;;;3936:51;4001:15;4894::175;;;4803:113;4001:15:178;4018:12;;4032:13;;4047:21;;3936:142;;-1:-1:-1;;;;;;3936:142:178;;;;;;;;;;7647:25:242;;;;7688:18;;;7681:34;;;;7731:18;;;7724:34;7774:18;;;7767:34;7619:19;;3936:142:178;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;6091:175::-;2356:21:48;:19;:21::i;:::-;6206:53:178::1;6213:10;6225;6237:8;6247:11;6206:6;:53::i;:::-;2398:20:48::0;1713:1;2924:7;:21;2744:208;1430:99:179;1231:5;;-1:-1:-1;;;;;1231:5:179;1217:10;:19;1209:44;;;;-1:-1:-1;;;1209:44:179;;;;;;;;;;;;1499:23:::1;1512:9;1499:12;:23::i;5892:150:178:-:0;5961:7;2356:21:48;:19;:21::i;:::-;5980:17:178::1;:15;:17::i;:::-;6014:21;:19;:21::i;:::-;6007:28;;2398:20:48::0;1713:1;2924:7;:21;2744:208;3352:206:178;-1:-1:-1;;;;;3474:22:178;;3429:7;3474:22;;;:13;:22;;;;;;3429:7;;;;3498:29;3488:7;3498:20;:29::i;:::-;3529:21;:19;:21::i;:::-;3466:85;;;;;;3352:206;;;;;:::o;3691:103:175:-;3748:39;3756:10;3768:12;3782:4;3748:7;:39::i;3367:103::-;3424:39;3432:10;3444:12;3458:4;3424:7;:39::i;:::-;;3367:103;:::o;2424:343:179:-;1231:5;;-1:-1:-1;;;;;1231:5:179;1217:10;:19;1209:44;;;;-1:-1:-1;;;1209:44:179;;;;;;;;;;;;2526:21:::1;::::0;;2557:35;;;;2640:11:::1;::::0;:15;2636:63:::1;;2671:17;:15;:17::i;:::-;2714:46;::::0;;7337:25:242;;;7393:2;7378:18;;7371:34;;;2714:46:179::1;::::0;7310:18:242;2714:46:179::1;;;;;;;2498:269;2424:343:::0;:::o;2874:190:175:-;1231:5:179;;-1:-1:-1;;;;;1231:5:179;1217:10;:19;1209:44;;;;-1:-1:-1;;;1209:44:179;;;;;;;;;;;;2979:10:175::1;::::0;-1:-1:-1;;;;;2979:10:175;;::::1;2961:28:::0;;::::1;::::0;2953:61:::1;;;;-1:-1:-1::0;;;2953:61:175::1;;;;;;;;;;;;3043:5;::::0;3024:33:::1;::::0;-1:-1:-1;;;;;3024:18:175;;::::1;::::0;3043:5:::1;3050:6:::0;3024:18:::1;:33::i;1650:231:179:-:0;1231:5;;-1:-1:-1;;;;;1231:5:179;1217:10;:19;1209:44;;;;-1:-1:-1;;;1209:44:179;;;;;;;;;;;;-1:-1:-1;;;;;1729:20:179;::::1;1721:48;;;;-1:-1:-1::0;;;1721:48:179::1;;;;;;;;;;;;1810:13;::::0;1785:48:::1;::::0;-1:-1:-1;;;;;1785:48:179;;::::1;::::0;1810:13:::1;::::0;1785:48:::1;::::0;1810:13:::1;::::0;1785:48:::1;1844:13;:30:::0;;-1:-1:-1;;;;;;1844:30:179::1;-1:-1:-1::0;;;;;1844:30:179;;;::::1;::::0;;;::::1;::::0;;1650:231::o;3607:192:178:-;3714:17;;3669:7;;-1:-1:-1;;;;;3714:17:178;;;;;3695:51;3747:15;4894::175;;;4803:113;3747:15:178;3764:12;;3778:13;;3695:97;;-1:-1:-1;;;;;;3695:97:178;;;;;;;;;;4860:25:242;;;;4901:18;;;4894:34;;;;4944:18;;;4937:34;4833:18;;3695:97:178;4658:319:242;2431:307:48;1755:1;2558:7;;:18;2554:86;;2599:30;;-1:-1:-1;;;2599:30:48;;;;;;;;;;;2554:86;1755:1;2714:7;:17;2431:307::o;10520:2592:180:-;10716:21;;8220:15;;10804:51;;;10800:64;;10857:7;;10520:2592::o;10800:64::-;10928:17;10948:15;4894::175;;;4803:113;10948:15:180;10996:12;;11042:13;;11092:11;;11231:17;;11212:91;;-1:-1:-1;;;11212:91:180;;;;;4860:25:242;;;4901:18;;;4894:34;;;4944:18;;;4937:34;;;10928:35:180;;-1:-1:-1;10996:12:180;;11042:13;;11092:11;;10973:20;;11231:17;;;-1:-1:-1;;;;;11231:17:180;;11212:51;;4833:18:242;;11212:91:180;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;11317:21;;11171:132;;-1:-1:-1;11317:25:180;11313:132;;11388:21;;11366:18;:43;;11358:76;;;;-1:-1:-1;;;11358:76:180;;;;;;;;;;;;11531:18;11552:50;11576:26;11552:21;:50;:::i;:::-;11531:71;;12083:31;12117:53;12122:35;;;;;;;;12137:18;12122:35;;;12159:10;12117:4;:53::i;:::-;12083:87;;12180:27;12210:54;12229:20;12251:12;12210:18;:54::i;:::-;12180:84;-1:-1:-1;12274:23:180;12300:34;12322:12;12180:84;12300:34;:::i;:::-;12274:60;;12344:24;12383:101;12409:38;;;;;;;;12424:21;;12409:38;;;12449:19;12470:13;12383:25;:101::i;:::-;12344:140;;12494:22;12519:83;12545:20;12567:16;12585;12519:25;:83::i;:::-;12799:21;:45;;;12854:11;:28;;;12892:12;:30;;;12932:13;:32;;;13026:79;;;7647:25:242;;;7703:2;7688:18;;7681:34;;;7731:18;;;7724:34;;;7789:2;7774:18;;7767:34;;;12854:28:180;;-1:-1:-1;13026:79:180;;7634:3:242;7619:19;13026:79:180;;;;;;;10556:2556;;;;;;;;;;;;;10520:2592::o;5911:146:175:-;6014:10;;6007:43;;-1:-1:-1;;;;;6014:10:175;6039:2;6043:6;6007:31;:43::i;7651:796:178:-;-1:-1:-1;;;;;7828:23:178;;7721:7;7828:23;;;:14;:23;;;;;8052:24;;:29;;8048:68;;-1:-1:-1;8104:1:178;;7651:796;-1:-1:-1;;7651:796:178:o;8048:68::-;8362:11;;8335:24;;8305:27;;8335:38;;;:::i;:::-;8305:68;;8412:14;:28;;;8390:19;:50;;;;:::i;:::-;8383:57;7651:796;-1:-1:-1;;;;7651:796:178:o;8623:772:180:-;8727:11;;8685:7;;8752:17;;;8748:641;;-1:-1:-1;;8920:27:180;;;8623:772::o;8748:641::-;9123:17;9143:15;4894::175;;;4803:113;9143:15:180;9123:35;;9172:36;9238:13;;9223:12;;9211:9;:24;;;;:::i;:::-;:40;;;;:::i;:::-;9172:79;-1:-1:-1;9265:20:180;9332:12;9289:39;1224:4:195;9172:79:180;9289:39;:::i;:::-;9288:56;;;;:::i;:::-;9265:79;8623:772;-1:-1:-1;;;;;8623:772:180:o;30140:1232:178:-;30260:8;;30242:81;;-1:-1:-1;;;30242:81:178;;30299:4;30242:81;;;8757:51:242;-1:-1:-1;;;;;8844:32:242;;;8824:18;;;8817:60;8913:32;;;8893:18;;;8886:60;8962:18;;;8955:34;;;30260:8:178;;;;30242:48;;8729:19:242;;30242:81:178;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;30349:3;-1:-1:-1;;;;;30342:10:178;:3;-1:-1:-1;;;;;30342:10:178;;30334:42;;;;-1:-1:-1;;;30334:42:178;;;;;;;;;;;;30451:25;30505:3;-1:-1:-1;;;;;30494:14:178;:7;-1:-1:-1;;;;;30494:14:178;;30490:165;;-1:-1:-1;;;30490:165:178;;;-1:-1:-1;;;;;;30612:23:178;;;;;;;:18;:23;;;;;;;;:32;;;;;;;;;;30490:165;30730:20;30753:26;30773:6;30753:17;:26;:::i;:::-;-1:-1:-1;;;;;30812:18:178;;30789:20;30812:18;;;:13;:18;;;;;;30730:49;;-1:-1:-1;30789:20:178;30812:27;;30833:6;;30812:27;:::i;:::-;-1:-1:-1;;;;;30872:18:178;;30849:20;30872:18;;;:13;:18;;;;;;30789:50;;-1:-1:-1;30849:20:178;30872:27;;30893:6;;30872:27;:::i;:::-;-1:-1:-1;;;;;31027:18:178;;;;;;;:13;:18;;;;;;:33;;;31070:18;;;;;;:33;;;30849:50;-1:-1:-1;;;31173:38:178;;31169:116;;-1:-1:-1;;;;;31227:23:178;;;;;;;:18;:23;;;;;;;;:32;;;;;;;;;:47;;;31169:116;31353:3;-1:-1:-1;;;;;31339:26:178;31348:3;-1:-1:-1;;;;;31339:26:178;-1:-1:-1;;;;;;;;;;;31358:6:178;31339:26;;;;1747:25:242;;1735:2;1720:18;;1601:177;31339:26:178;;;;;;;;30232:1140;;;;30140:1232;;;;:::o;11904:276::-;11989:7;2356:21:48;:19;:21::i;:::-;12008:17:178::1;:15;:17::i;:::-;12117:56;12125:10;12137;12149:11;12162:10;12117:7;:56::i;:::-;12110:63;;2398:20:48::0;1713:1;2924:7;:21;2744:208;1941:177:195;2022:7;2041:18;2062:15;2067:1;2070:6;2062:4;:15::i;:::-;2041:36;;2094:17;2103:7;2094:8;:17::i;13322:433:178:-;2356:21:48;:19;:21::i;:::-;13522:17:178::1;:15;:17::i;:::-;13558:16;-1:-1:-1::0;;;;;13550:40:178::1;;:42;;;;;;;;;;;;;;;;;;;;;;;;;;;;::::0;::::1;;;;;;;;;;;;::::0;::::1;;;;;;;;;13672:76;13684:10;13696:8;13706:11;13719:16;13737:10;13672:11;:76::i;:::-;2398:20:48::0;1713:1;2924:7;:21;2744:208;2398:20;13322:433:178;;;;;:::o;12496:326::-;12629:7;2356:21:48;:19;:21::i;:::-;12652:17:178::1;:15;:17::i;:::-;12761:54;12769:10;12781:8;12791:11;12804:10;12761:7;:54::i;:::-;12754:61;;2398:20:48::0;1713:1;2924:7;:21;2744:208;16152:1177:178;2356:21:48;:19;:21::i;:::-;16225:17:178::1;:15;:17::i;:::-;16296:24;16330:23:::0;16929:36:::1;16943:10;16955:9;16929:13;:36::i;:::-;16911:54;;16994:15;16975;;:34;;;;;;;:::i;:::-;::::0;;;-1:-1:-1;;17039:13:178::1;::::0;:31:::1;::::0;17055:15;;17039:31:::1;:::i;:::-;17144:13;:32:::0;;;17262:60:::1;::::0;;7337:25:242;;;7393:2;7378:18;;7371:34;;;17144:32:178;;-1:-1:-1;17276:10:178::1;::::0;17262:60:::1;::::0;7310:18:242;17262:60:178::1;;;;;;;16215:1114;;2398:20:48::0;1713:1;2924:7;:21;2744:208;8955:345:178;2356:21:48;:19;:21::i;:::-;9113:17:178::1;:15;:17::i;:::-;9233:60;9240:4;9246:8;9256:10;9268:12;9282:10;9233:6;:60::i;10386:267::-:0;2356:21:48;:19;:21::i;:::-;10498:17:178::1;:15;:17::i;:::-;10594:52;10611:4;10618:1;10621:12;10635:10;10594:8;:52::i;:::-;;2398:20:48::0;1713:1;2924:7;:21;2744:208;4518:399:179;4706:20;-1:-1:-1;;;;;4687:60:179;;:62;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;4679:98;;;;-1:-1:-1;;;4679:98:179;;;;;;;;;;;;4820:17;;4793:67;;-1:-1:-1;;;;;4793:67:179;;;;4820:17;;;;;4793:67;;;;;4870:17;:40;;-1:-1:-1;;;;;4870:40:179;;;;;-1:-1:-1;;;;;;4870:40:179;;;;;;;;;4518:399::o;14331:1659:178:-;14464:8;;14446:95;;-1:-1:-1;;;14446:95:178;;14500:4;14446:95;;;9231:51:242;-1:-1:-1;;;;;9318:32:242;;;9298:18;;;9291:60;9387:32;;;9367:18;;;9360:60;9456:32;;;9436:18;;;9429:60;14464:8:178;;;;14446:45;;9203:19:242;;14446:95:178;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;14572:10;-1:-1:-1;;;;;14560:22:178;:8;-1:-1:-1;;;;;14560:22:178;;14552:50;;;;-1:-1:-1;;;14552:50:178;;;;;;;;;;;;14883:27;14913:65;14918:11;14931:46;;;;;;;;3895:6:180;14931:46:178;;;14913:4;:65::i;:::-;14883:95;-1:-1:-1;14988:29:178;15020:33;14883:95;15020:11;:33;:::i;:::-;14988:65;;15063:23;15089:38;;;;;;;;15104:21;:19;:21::i;:::-;15089:38;;15063:64;-1:-1:-1;15137:27:178;15167:53;15063:64;15200:19;15167:18;:53::i;:::-;15137:83;;15230:24;15273:19;15257:13;;:35;;;;:::i;:::-;15478:13;:32;;;15534:11;;15230:62;;-1:-1:-1;15534:33:178;;15548:19;;15534:33;:::i;:::-;15520:11;:47;-1:-1:-1;;;;;15603:23:178;;;;;;:13;:23;;;;;;:37;;15629:11;;15603:37;:::i;:::-;-1:-1:-1;;;;;15577:23:178;;;;;;;:13;:23;;;;;;:63;;;;15678:25;;;;;;;:49;;15706:21;;15678:49;:::i;:::-;-1:-1:-1;;;;;15650:25:178;;;;;;;:13;:25;;;;;;;:77;;;;15779:53;;;;;;-1:-1:-1;;;;;;;;;;;15779:53:178;;;15810:21;1747:25:242;;1735:2;1720:18;;1601:177;15779:53:178;;;;;;;;15847:54;;1747:25:242;;;15874:4:178;;-1:-1:-1;;;;;15847:54:178;;;-1:-1:-1;;;;;;;;;;;15847:54:178;1735:2:242;1720:18;15847:54:178;;;;;;;15916:67;;;7337:25:242;;;7393:2;7378:18;;7371:34;;;15938:4:178;;15916:67;;7310:18:242;15916:67:178;;;;;;;14436:1554;;;;;14331:1659;;;;:::o;4923:215:179:-;5001:9;-1:-1:-1;;;;;4991:31:179;;:33;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;4983:69;;;;-1:-1:-1;;;4983:69:179;;;;;;;;;;;;5080:8;;5068:32;;-1:-1:-1;;;;;5068:32:179;;;;5080:8;;5068:32;;5080:8;;5068:32;5111:8;:20;;-1:-1:-1;;;;;;5111:20:179;-1:-1:-1;;;;;5111:20:179;;;;;;;;;;4923:215::o;10926:269:178:-;2356:21:48;:19;:21::i;:::-;11028:17:178::1;:15;:17::i;:::-;11124:64;11141:4;11156;11163:12;11177:10;11124:8;:64::i;9659:339::-:0;9784:24;2356:21:48;:19;:21::i;:::-;9824:17:178::1;:15;:17::i;:::-;9939:52;9956:4;9963:12;9977:1;9980:10;9939:8;:52::i;1303:160:43:-:0;1412:43;;-1:-1:-1;;;;;6529:32:242;;;1412:43:43;;;6511:51:242;6578:18;;;6571:34;;;1385:71:43;;1405:5;;1427:14;;;;;6484:18:242;;1412:43:43;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;1412:43:43;;;;;;;;;;;1385:19;:71::i;4640:134:195:-;-1:-1:-1;;;;;;;;;;;;4731:36:195;;;;;;;;4746:19;4751:1;:10;;;4763:1;4746:4;:19::i;:::-;4731:36;;4724:43;4640:134;-1:-1:-1;;;4640:134:195:o;2258:214::-;2362:7;2381:18;2402:15;2407:1;2410:6;2402:4;:15::i;:::-;2381:36;;2434:31;2439:17;2448:7;2439:8;:17::i;:::-;2458:6;2434:4;:31::i;20090:2043:178:-;20232:8;;20214:70;;-1:-1:-1;;;20214:70:178;;20268:4;20214:70;;;9953:51:242;-1:-1:-1;;;;;10040:32:242;;;10020:18;;;10013:60;20195:7:178;;20232:8;;20214:45;;9926:18:242;;20214:70:178;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;20374:26;20403:30;20424:8;20403:20;:30::i;:::-;20374:59;;20525:24;-1:-1:-1;;20552:11:178;:32;:67;;20608:11;20552:67;;;20587:18;20552:67;20525:94;;21182:25;21210:10;:70;;21264:16;21210:70;;;21223:38;21237:5;21244:16;21223:13;:38::i;:::-;21182:98;;21309:17;21290:15;;:36;;;;;;;:::i;:::-;;;;-1:-1:-1;21580:25:178;;-1:-1:-1;21608:38:178;21629:17;21608:18;:38;:::i;:::-;21580:66;;21656:23;21697:17;21682:12;;:32;;;;:::i;:::-;-1:-1:-1;;;;;21794:24:178;;;;;;;:14;:24;;;;;;;;;:54;;;21899:11;;21858:38;;;;:52;21920:12;:30;;;22008:83;;4860:25:242;;;4901:18;;;4894:34;;;4944:18;;;4937:34;;;21920:30:178;;-1:-1:-1;21794:24:178;22008:83;;;;;;4848:2:242;4833:18;22008:83:178;;;;;;;-1:-1:-1;22109:17:178;;20090:2043;-1:-1:-1;;;;;;;;20090:2043:178:o;1620:213:195:-;1803:12;;1677:7;;1803:23;;1224:4;;1803:23;:::i;17905:1773:178:-;18113:10;-1:-1:-1;;;;;18101:22:178;:8;-1:-1:-1;;;;;18101:22:178;;18093:50;;;;-1:-1:-1;;;18093:50:178;;;;;;;;;;;;18175:1;18161:11;:15;:51;;;;;-1:-1:-1;;18180:11:178;:32;;18161:51;18153:79;;;;-1:-1:-1;;;18153:79:178;;;;;;;;;;;;18261:8;;18243:105;;-1:-1:-1;;;18243:105:178;;18301:4;18243:105;;;8757:51:242;-1:-1:-1;;;;;8844:32:242;;;8824:18;;;8817:60;8913:32;;;8893:18;;;8886:60;8962:18;;;8955:34;;;18261:8:178;;;;18243:49;;8729:19:242;;18243:105:178;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;18433:20;8220:15:180;;8133:109;18433:20:178;18388:16;-1:-1:-1;;;;;18380:47:178;;:49;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:73;18359:155;;;;-1:-1:-1;;;18359:155:178;;;;;;;;;;;;18565:25;18593:54;18601:10;18613:8;18623:11;18636:10;18593:7;:54::i;:::-;18898:8;;18888:101;;-1:-1:-1;;;18888:101:178;;18565:82;;-1:-1:-1;18854:19:178;;-1:-1:-1;;;;;18898:8:178;;;;18888:49;;:101;;18946:4;;18953:16;;18565:82;;18888:101;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;19080:45;;-1:-1:-1;;;19080:45:178;;-1:-1:-1;;;;;2728:32:242;;;19080:45:178;;;2710:51:242;18854:135:178;;-1:-1:-1;18854:135:178;;19080:35;;;;;2683:18:242;;19080:45:178;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:60;;19072:97;;;;-1:-1:-1;;;19072:97:178;;;;;;;;;;;;19329:4;-1:-1:-1;;;;;19292:42:178;;;19288:226;;19350:56;19365:4;19372:10;19384:8;19394:11;19350:6;:56::i;:::-;19288:226;;;19437:66;;-1:-1:-1;;;19437:66:178;;-1:-1:-1;;;;;19437:31:178;;;;;:66;;19469:10;;19481:8;;19491:11;;19437:66;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;19288:226;19640:16;-1:-1:-1;;;;;19575:96:178;19603:8;-1:-1:-1;;;;;19575:96:178;19591:10;-1:-1:-1;;;;;19575:96:178;;19613:17;19659:11;19575:96;;;;;;7337:25:242;;;7393:2;7378:18;;7371:34;7325:2;7310:18;;7163:248;19575:96:178;;;;;;;;18083:1595;;17905:1773;;;;;:::o;5152:376:175:-;5290:10;;5283:43;;-1:-1:-1;;;5283:43:175;;5320:4;5283:43;;;2710:51:242;5240:7:175;;;;-1:-1:-1;;;;;5290:10:175;;;;5283:28;;2683:18:242;;5283:43:175;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;5343:10;;5259:67;;-1:-1:-1;5336:64:175;;-1:-1:-1;;;;;5343:10:175;5372:4;5386;5393:6;5336:35;:64::i;:::-;5440:10;;5433:43;;-1:-1:-1;;;5433:43:175;;5470:4;5433:43;;;2710:51:242;5410:20:175;;-1:-1:-1;;;;;5440:10:175;;5433:28;;2683:18:242;;5433:43:175;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;5410:66;-1:-1:-1;5493:28:175;5508:13;5410:66;5493:28;:::i;27269:2472:178:-;27426:8;;27408:67;;-1:-1:-1;;;27408:67:178;;27461:4;27408:67;;;9953:51:242;-1:-1:-1;;;;;10040:32:242;;;10020:18;;;10013:60;27426:8:178;;;;27408:44;;9926:18:242;;27408:67:178;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;27486:23;27512:38;;;;;;;;27527:21;:19;:21::i;:::-;27512:38;;27486:64;-1:-1:-1;28147:24:178;28174:10;:59;;28223:10;28174:59;;;28187:33;28201:6;28209:10;28187:13;:33::i;:::-;28147:86;;28262:16;28243:15;;:35;;;;;;;:::i;:::-;;;;-1:-1:-1;28464:18:178;;-1:-1:-1;28485:36:178;28490:16;28508:12;28485:4;:36::i;:::-;28464:57;;28553:12;28539:10;:26;;28531:59;;;;-1:-1:-1;;;28531:59:178;;;;;;;;;;;;28648:11;;28663:1;28648:16;28644:143;;28694:4;28680:11;:18;;;28712:25;;;:13;:25;;;:32;;;28758:18;;;;:::i;:::-;;;28644:143;29112:10;29098:11;;:24;;;;:::i;:::-;29084:11;:38;-1:-1:-1;;;;;29158:23:178;;;;;;:13;:23;;;;;;:36;;29184:10;;29158:36;:::i;:::-;-1:-1:-1;;;;;29132:23:178;;;;;;;:13;:23;;;;;;;;;:62;;;;29267:52;;7337:25:242;;;7378:18;;;7371:34;;;29132:23:178;;29267:52;;;;;;7310:18:242;29267:52:178;;;;;;;29334:45;;1747:25:242;;;-1:-1:-1;;;;;29334:45:178;;;29351:4;;-1:-1:-1;;;;;;;;;;;29334:45:178;1735:2:242;1720:18;29334:45:178;;;;;;;29447:8;;29429:58;;-1:-1:-1;;;29429:58:178;;29481:4;29429:58;;;2710:51:242;-1:-1:-1;;;;;29447:8:178;;;;29429:43;;2683:18:242;;29429:58:178;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;29586:8:178;;29576:58;;-1:-1:-1;;;29576:58:178;;-1:-1:-1;;;;;9971:32:242;;;29576:58:178;;;9953:51:242;29628:4:178;10020:18:242;;;10013:60;29559:14:178;;-1:-1:-1;29586:8:178;;;;-1:-1:-1;29576:35:178;;9926:18:242;;29576:58:178;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;29559:75;;29649:9;29644:91;;29684:8;;29674:50;;-1:-1:-1;;;29674:50:178;;-1:-1:-1;;;;;2728:32:242;;;29674:50:178;;;2710:51:242;29684:8:178;;;;29674:42;;2683:18:242;;29674:50:178;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;29644:91;27398:2343;;;;27269:2472;;;;;:::o;24152:2580::-;24294:20;24338:19;;;:42;;-1:-1:-1;24361:19:178;;24338:42;24330:70;;;;-1:-1:-1;;;24330:70:178;;;;;;;;;;;;24470:23;24496:38;;;;;;;;24511:21;:19;:21::i;:::-;24496:38;;24470:64;-1:-1:-1;24545:20:178;24616:18;;24612:741;;-1:-1:-1;24901:14:178;24944:48;24963:12;24901:14;24944:18;:48::i;:::-;24929:63;;24612:741;;;25265:34;25270:14;25286:12;25265:4;:34::i;:::-;25250:49;;25328:14;25313:29;;24612:741;25366:17;;:38;;;;-1:-1:-1;25387:17:178;;25366:38;25362:67;;;25413:16;;-1:-1:-1;;;25413:16:178;;;;;;;;;;;25362:67;25499:8;;25481:85;;-1:-1:-1;;;25481:85:178;;-1:-1:-1;;;;;25499:8:178;;;;25481:46;;:85;;25536:4;;25543:8;;25553:12;;25481:85;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;25604:12;25585:15;4894::175;;;4803:113;25585:15:178;:31;;25577:69;;;;-1:-1:-1;;;25577:69:178;;;;;;;;;;;;25993:12;25979:11;;:26;;;;:::i;:::-;25965:11;:40;-1:-1:-1;;;;;26041:23:178;;;;;;:13;:23;;;;;;:38;;26067:12;;26041:38;:::i;:::-;-1:-1:-1;;;;;26015:23:178;;;;;;:13;:23;;;;;:64;26449:54;;;;26465:38;26480:8;26490:12;26465:14;:38::i;:::-;26532:12;26513:15;;:31;;;;;;;:::i;:::-;;;;-1:-1:-1;;26619:47:178;;1747:25:242;;;26646:4:178;;-1:-1:-1;;;;;26619:47:178;;;-1:-1:-1;;;;;;;;;;;26619:47:178;1735:2:242;1720:18;26619:47:178;;;;;;;26681:44;;;7337:25:242;;;7393:2;7378:18;;7371:34;;;-1:-1:-1;;;;;26681:44:178;;;;;7310:18:242;26681:44:178;;;;;;;24320:2412;;24152:2580;;;;;;:::o;4780:125:195:-;4842:7;1224:4;4868:19;4873:1;4876;:10;;;4868:4;:19::i;:::-;:30;;;;:::i;22301:1845:178:-;22458:8;;22440:85;;-1:-1:-1;;;22440:85:178;;-1:-1:-1;;;;;22458:8:178;;;;22440:46;;:85;;22495:4;;22502:8;;22512:12;;22440:85;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;22563:12;22544:15;4894::175;;;4803:113;22544:15:178;:31;;22536:69;;;;-1:-1:-1;;;22536:69:178;;;;;;;;;;;;22846:26;22875:30;22896:8;22875:20;:30::i;:::-;22846:59;-1:-1:-1;22915:25:178;22943:33;22964:12;22846:59;22943:33;:::i;:::-;22915:61;;22986:23;23027:12;23012;;:27;;;;:::i;:::-;-1:-1:-1;;;;;23360:24:178;;;;;;:14;:24;;;;;:54;;;23465:11;;23424:38;;;;:52;23486:12;:30;;;22986:53;-1:-1:-1;23527:453:178;;;;23931:38;23946:8;23956:12;23931:14;:38::i;:::-;24008:12;23989:15;;:31;;;;;;;:::i;:::-;;;;-1:-1:-1;;24073:66:178;;;4860:25:242;;;4916:2;4901:18;;4894:34;;;4944:18;;;4937:34;;;-1:-1:-1;;;;;24073:66:178;;;;;4848:2:242;4833:18;24073:66:178;;;;;;;22430:1716;;;22301:1845;;;;:::o;4059:629:43:-;4478:23;4504:33;-1:-1:-1;;;;;4504:27:43;;4532:4;4504:27;:33::i;:::-;4478:59;;4551:10;:17;4572:1;4551:22;;:57;;;;;4589:10;4578:30;;;;;;;;;;;;:::i;:::-;4577:31;4551:57;4547:135;;;4631:40;;-1:-1:-1;;;4631:40:43;;-1:-1:-1;;;;;2728:32:242;;4631:40:43;;;2710:51:242;2683:18;;4631:40:43;;;;;;;;5375:97:195;5434:7;5460:5;5464:1;5460;:5;:::i;3955:97::-;4014:7;4040:5;4044:1;4040;:5;:::i;1702:188:43:-;1802:81;1822:5;1844;-1:-1:-1;;;;;1844:18:43;;1865:4;1871:2;1875:5;1829:53;;;;;;;;;;:::i;1802:81::-;1702:188;;;;:::o;5786:130:195:-;5848:7;5874:35;5879:17;5884:1;1224:4;5879;:17::i;:::-;5898:10;;5874:4;:35::i;2705:151:46:-;2780:12;2811:38;2833:6;2841:4;2847:1;2811:21;:38::i;6396:97:195:-;6455:7;6481:5;6485:1;6481;:5;:::i;3180:392:46:-;3279:12;3331:5;3307:21;:29;3303:108;;;3359:41;;-1:-1:-1;;;3359:41:46;;3394:4;3359:41;;;2710:51:242;2683:18;;3359:41:46;2548:219:242;3303:108:46;3421:12;3435:23;3462:6;-1:-1:-1;;;;;3462:11:46;3481:5;3488:4;3462:31;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3420:73;;;;3510:55;3537:6;3545:7;3554:10;3510:26;:55::i;:::-;3503:62;3180:392;-1:-1:-1;;;;;;3180:392:46:o;4625:582::-;4769:12;4798:7;4793:408;;4821:19;4829:10;4821:7;:19::i;:::-;4793:408;;;5045:17;;:22;:49;;;;-1:-1:-1;;;;;;5071:18:46;;;:23;5045:49;5041:119;;;5121:24;;-1:-1:-1;;;5121:24:46;;-1:-1:-1;;;;;2728:32:242;;5121:24:46;;;2710:51:242;2683:18;;5121:24:46;2548:219:242;5041:119:46;-1:-1:-1;5180:10:46;5173:17;;5743:516;5874:17;;:21;5870:383;;6102:10;6096:17;6158:15;6145:10;6141:2;6137:19;6130:44;5870:383;6225:17;;-1:-1:-1;;;6225:17:46;;;;;;;;;;;14:250:242;99:1;109:113;123:6;120:1;117:13;109:113;;;199:11;;;193:18;180:11;;;173:39;145:2;138:10;109:113;;;-1:-1:-1;;256:1:242;238:16;;231:27;14:250::o;269:396::-;418:2;407:9;400:21;381:4;450:6;444:13;493:6;488:2;477:9;473:18;466:34;509:79;581:6;576:2;565:9;561:18;556:2;548:6;544:15;509:79;:::i;:::-;649:2;628:15;-1:-1:-1;;624:29:242;609:45;;;;656:2;605:54;;269:396;-1:-1:-1;;269:396:242:o;670:226::-;729:6;782:2;770:9;761:7;757:23;753:32;750:52;;;798:1;795;788:12;750:52;-1:-1:-1;843:23:242;;670:226;-1:-1:-1;670:226:242:o;901:131::-;-1:-1:-1;;;;;976:31:242;;966:42;;956:70;;1022:1;1019;1012:12;1037:367;1105:6;1113;1166:2;1154:9;1145:7;1141:23;1137:32;1134:52;;;1182:1;1179;1172:12;1134:52;1221:9;1208:23;1240:31;1265:5;1240:31;:::i;:::-;1290:5;1368:2;1353:18;;;;1340:32;;-1:-1:-1;;;1037:367:242:o;1783:247::-;1842:6;1895:2;1883:9;1874:7;1870:23;1866:32;1863:52;;;1911:1;1908;1901:12;1863:52;1950:9;1937:23;1969:31;1994:5;1969:31;:::i;2035:508::-;2112:6;2120;2128;2181:2;2169:9;2160:7;2156:23;2152:32;2149:52;;;2197:1;2194;2187:12;2149:52;2236:9;2223:23;2255:31;2280:5;2255:31;:::i;:::-;2305:5;-1:-1:-1;2362:2:242;2347:18;;2334:32;2375:33;2334:32;2375:33;:::i;:::-;2035:508;;2427:7;;-1:-1:-1;;;2507:2:242;2492:18;;;;2479:32;;2035:508::o;2961:::-;3038:6;3046;3054;3107:2;3095:9;3086:7;3082:23;3078:32;3075:52;;;3123:1;3120;3113:12;3075:52;3162:9;3149:23;3181:31;3206:5;3181:31;:::i;:::-;3231:5;-1:-1:-1;3309:2:242;3294:18;;3281:32;;-1:-1:-1;3391:2:242;3376:18;;3363:32;3404:33;3363:32;3404:33;:::i;:::-;3456:7;3446:17;;;2961:508;;;;;:::o;4166:487::-;4243:6;4251;4259;4312:2;4300:9;4291:7;4287:23;4283:32;4280:52;;;4328:1;4325;4318:12;4280:52;4373:23;;;-1:-1:-1;4472:2:242;4457:18;;4444:32;4485:33;4444:32;4485:33;:::i;4982:388::-;5050:6;5058;5111:2;5099:9;5090:7;5086:23;5082:32;5079:52;;;5127:1;5124;5117:12;5079:52;5166:9;5153:23;5185:31;5210:5;5185:31;:::i;:::-;5235:5;-1:-1:-1;5292:2:242;5277:18;;5264:32;5305:33;5264:32;5305:33;:::i;:::-;5357:7;5347:17;;;4982:388;;;;;:::o;5763:380::-;5842:1;5838:12;;;;5885;;;5906:61;;5960:4;5952:6;5948:17;5938:27;;5906:61;6013:2;6005:6;6002:14;5982:18;5979:38;5976:161;;6059:10;6054:3;6050:20;6047:1;6040:31;6094:4;6091:1;6084:15;6122:4;6119:1;6112:15;5976:161;;5763:380;;;:::o;6148:184::-;6218:6;6271:2;6259:9;6250:7;6246:23;6242:32;6239:52;;;6287:1;6284;6277:12;6239:52;-1:-1:-1;6310:16:242;;6148:184;-1:-1:-1;6148:184:242:o;6616:277::-;6683:6;6736:2;6724:9;6715:7;6711:23;6707:32;6704:52;;;6752:1;6749;6742:12;6704:52;6784:9;6778:16;6837:5;6830:13;6823:21;6816:5;6813:32;6803:60;;6859:1;6856;6849:12;6898:127;6959:10;6954:3;6950:20;6947:1;6940:31;6990:4;6987:1;6980:15;7014:4;7011:1;7004:15;7030:128;7097:9;;;7118:11;;;7115:37;;;7132:18;;:::i;8001:125::-;8066:9;;;8087:10;;;8084:36;;;8100:18;;:::i;8131:168::-;8204:9;;;8235;;8252:15;;;8246:22;;8232:37;8222:71;;8273:18;;:::i;8304:217::-;8344:1;8370;8360:132;;8414:10;8409:3;8405:20;8402:1;8395:31;8449:4;8446:1;8439:15;8477:4;8474:1;8467:15;8360:132;-1:-1:-1;8506:9:242;;8304:217::o;10084:371::-;-1:-1:-1;;;;;10304:32:242;;;10286:51;;10373:32;;;;10368:2;10353:18;;10346:60;10437:2;10422:18;;10415:34;;;;10274:2;10259:18;;10084:371::o;10844:287::-;10973:3;11011:6;11005:13;11027:66;11086:6;11081:3;11074:4;11066:6;11062:17;11027:66;:::i;:::-;11109:16;;;;;10844:287;-1:-1:-1;;10844:287:242:o", "linkReferences": {}}, "methodIdentifiers": {"acceptAdmin()": "0e18b681", "accrualBlockTimestamp()": "cfa99201", "accrueInterest()": "a6afed95", "addReserves(uint256)": "7821a514", "admin()": "f851a440", "allowance(address,address)": "dd62ed3e", "approve(address,uint256)": "095ea7b3", "balanceOf(address)": "70a08231", "balanceOfUnderlying(address)": "3af9e669", "borrow(uint256)": "c5ebeaec", "borrowBalanceCurrent(address)": "17bfdfbc", "borrowBalanceStored(address)": "95dd9193", "borrowIndex()": "aa5af0fd", "borrowRateMaxMantissa()": "ee27a2f2", "borrowRatePerBlock()": "f8f9da28", "decimals()": "313ce567", "exchangeRateCurrent()": "bd6d894d", "exchangeRateStored()": "182df0f5", "getAccountSnapshot(address)": "c37f68e2", "getCash()": "3b1d21a2", "interestRateModel()": "f3fdb15a", "liquidate(address,uint256,address)": "4914c008", "mint(uint256,address,uint256)": "836a1040", "name()": "06fdde03", "operator()": "570ca735", "pendingAdmin()": "********", "redeem(uint256)": "db006a75", "redeemUnderlying(uint256)": "852a12e3", "reduceReserves(uint256)": "07e27959", "repay(uint256)": "371fd8e6", "repayBehalf(address,uint256)": "5bdcecb7", "reserveFactorMantissa()": "173b9904", "rolesOperator()": "4fecab70", "seize(address,address,uint256)": "b2a02ff1", "setBorrowRateMaxMantissa(uint256)": "e67218cd", "setInterestRateModel(address)": "8bcd4016", "setOperator(address)": "b3ab15fb", "setPendingAdmin(address)": "4dd18bf5", "setReserveFactor(uint256)": "1c446983", "setRolesOperator(address)": "f89416ee", "supplyRatePerBlock()": "ae9d70b0", "sweepToken(address,uint256)": "e90a182f", "symbol()": "95d89b41", "totalBorrows()": "47bd3718", "totalBorrowsCurrent()": "73acee98", "totalReserves()": "8f840ddd", "totalSupply()": "18160ddd", "totalUnderlying()": "c70920bc", "transfer(address,uint256)": "a9059cbb", "transferFrom(address,address,uint256)": "23b872dd", "underlying()": "6f307dc3"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"underlying_\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"operator_\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"interestRateModel_\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"initialExchangeRateMantissa_\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"name_\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"symbol_\",\"type\":\"string\"},{\"internalType\":\"uint8\",\"name\":\"decimals_\",\"type\":\"uint8\"},{\"internalType\":\"address payable\",\"name\":\"admin_\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"target\",\"type\":\"address\"}],\"name\":\"AddressEmptyCode\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"AddressInsufficientBalance\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"FailedInnerCall\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ReentrancyGuardReentrantCall\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"SafeERC20FailedOperation\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mErc20_TokenNotValid\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_AlreadyInitialized\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_BorrowCashNotAvailable\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_BorrowRateTooHigh\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_CollateralBlockTimestampNotValid\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_ExchangeRateNotValid\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_InvalidInput\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_LiquidateSeizeTooMuch\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_MarketMethodNotValid\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_MinAmountNotValid\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_OnlyAdmin\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_OnlyAdminOrRole\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_RedeemCashNotAvailable\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_RedeemEmpty\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_RedeemTransferOutNotPossible\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_ReserveCashNotAvailable\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_ReserveFactorTooHigh\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_SameChainOperationsAreDisabled\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_TransferNotValid\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"cashPrior\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"interestAccumulated\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"borrowIndex\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"totalBorrows\",\"type\":\"uint256\"}],\"name\":\"AccrueInterest\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"Approval\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"borrowAmount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"accountBorrows\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"totalBorrows\",\"type\":\"uint256\"}],\"name\":\"Borrow\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"liquidator\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"repayAmount\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"mTokenCollateral\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"seizeTokens\",\"type\":\"uint256\"}],\"name\":\"LiquidateBorrow\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"minter\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"mintAmount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"mintTokens\",\"type\":\"uint256\"}],\"name\":\"Mint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"oldVal\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"maxMantissa\",\"type\":\"uint256\"}],\"name\":\"NewBorrowRateMaxMantissa\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"oldInterestRateModel\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newInterestRateModel\",\"type\":\"address\"}],\"name\":\"NewMarketInterestRateModel\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"oldOperator\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOperator\",\"type\":\"address\"}],\"name\":\"NewOperator\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"oldReserveFactorMantissa\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"newReserveFactorMantissa\",\"type\":\"uint256\"}],\"name\":\"NewReserveFactor\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"oldRoles\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newRoles\",\"type\":\"address\"}],\"name\":\"NewRolesOperator\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"redeemer\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"redeemAmount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"redeemTokens\",\"type\":\"uint256\"}],\"name\":\"Redeem\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"payer\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"repayAmount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"accountBorrows\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"totalBorrows\",\"type\":\"uint256\"}],\"name\":\"RepayBorrow\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"benefactor\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"addAmount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"newTotalReserves\",\"type\":\"uint256\"}],\"name\":\"ReservesAdded\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"admin\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"reduceAmount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"newTotalReserves\",\"type\":\"uint256\"}],\"name\":\"ReservesReduced\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"_oldState\",\"type\":\"bool\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"_newState\",\"type\":\"bool\"}],\"name\":\"SameChainFlowStateUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"oldVerifier\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newVerifier\",\"type\":\"address\"}],\"name\":\"ZkVerifierUpdated\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"acceptAdmin\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"accrualBlockTimestamp\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"accrueInterest\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"addAmount\",\"type\":\"uint256\"}],\"name\":\"addReserves\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"admin\",\"outputs\":[{\"internalType\":\"address payable\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"}],\"name\":\"allowance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"approve\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"balanceOfUnderlying\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"borrowAmount\",\"type\":\"uint256\"}],\"name\":\"borrow\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"borrowBalanceCurrent\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"borrowBalanceStored\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"borrowIndex\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"borrowRateMaxMantissa\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"borrowRatePerBlock\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"decimals\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"exchangeRateCurrent\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"exchangeRateStored\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"getAccountSnapshot\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getCash\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"interestRateModel\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"repayAmount\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"mTokenCollateral\",\"type\":\"address\"}],\"name\":\"liquidate\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"mintAmount\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"minAmountOut\",\"type\":\"uint256\"}],\"name\":\"mint\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"name\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"operator\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"pendingAdmin\",\"outputs\":[{\"internalType\":\"address payable\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"redeemTokens\",\"type\":\"uint256\"}],\"name\":\"redeem\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"redeemAmount\",\"type\":\"uint256\"}],\"name\":\"redeemUnderlying\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"reduceAmount\",\"type\":\"uint256\"}],\"name\":\"reduceReserves\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"repayAmount\",\"type\":\"uint256\"}],\"name\":\"repay\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"repayAmount\",\"type\":\"uint256\"}],\"name\":\"repayBehalf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"reserveFactorMantissa\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"rolesOperator\",\"outputs\":[{\"internalType\":\"contract IRoles\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"liquidator\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"seizeTokens\",\"type\":\"uint256\"}],\"name\":\"seize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"maxMantissa\",\"type\":\"uint256\"}],\"name\":\"setBorrowRateMaxMantissa\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newInterestRateModel\",\"type\":\"address\"}],\"name\":\"setInterestRateModel\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_operator\",\"type\":\"address\"}],\"name\":\"setOperator\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address payable\",\"name\":\"newPendingAdmin\",\"type\":\"address\"}],\"name\":\"setPendingAdmin\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"newReserveFactorMantissa\",\"type\":\"uint256\"}],\"name\":\"setReserveFactor\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_roles\",\"type\":\"address\"}],\"name\":\"setRolesOperator\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"supplyRatePerBlock\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"contract IERC20\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"sweepToken\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"symbol\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalBorrows\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalBorrowsCurrent\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalReserves\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalSupply\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalUnderlying\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"dst\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"transfer\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"src\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"dst\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"transferFrom\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"underlying\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"AddressEmptyCode(address)\":[{\"details\":\"There's no code at `target` (it is not a contract).\"}],\"AddressInsufficientBalance(address)\":[{\"details\":\"The ETH balance of the account is not enough to perform the operation.\"}],\"FailedInnerCall()\":[{\"details\":\"A call to an address target failed. The target may have reverted.\"}],\"ReentrancyGuardReentrantCall()\":[{\"details\":\"Unauthorized reentrant call.\"}],\"SafeERC20FailedOperation(address)\":[{\"details\":\"An operation with an ERC20 token failed.\"}]},\"kind\":\"dev\",\"methods\":{\"acceptAdmin()\":{\"details\":\"Admin function for pending admin to accept role and update admin\"},\"addReserves(uint256)\":{\"params\":{\"addAmount\":\"The amount fo underlying token to add as reserves\"}},\"allowance(address,address)\":{\"params\":{\"owner\":\"The address of the token holder\",\"spender\":\"The address authorized to spend the tokens\"},\"returns\":{\"_0\":\"The current remaining number of tokens `spender` can spend\"}},\"approve(address,uint256)\":{\"params\":{\"amount\":\"The number of tokens to approve\",\"spender\":\"The address authorized to spend tokens\"},\"returns\":{\"_0\":\"Whether the approval was successful or not\"}},\"balanceOf(address)\":{\"params\":{\"account\":\"The account to check for\"}},\"balanceOfUnderlying(address)\":{\"params\":{\"owner\":\"The address to query the balance of underlying assets for\"},\"returns\":{\"_0\":\"The balance of underlying assets owned by `owner`\"}},\"borrow(uint256)\":{\"params\":{\"borrowAmount\":\"The amount of the underlying asset to borrow\"}},\"borrowBalanceCurrent(address)\":{\"params\":{\"account\":\"The address to query the borrow balance for\"},\"returns\":{\"_0\":\"The current borrow balance\"}},\"borrowBalanceStored(address)\":{\"params\":{\"account\":\"The address to query the stored borrow balance for\"},\"returns\":{\"_0\":\"The stored borrow balance\"}},\"borrowRatePerBlock()\":{\"returns\":{\"_0\":\"The current borrow rate per block, scaled by 1e18\"}},\"constructor\":{\"params\":{\"admin_\":\"Address of the administrator of this token\",\"decimals_\":\"ERC-20 decimal precision of this token\",\"initialExchangeRateMantissa_\":\"The initial exchange rate, scaled by 1e18\",\"interestRateModel_\":\"The address of the interest rate model\",\"name_\":\"ERC-20 name of this token\",\"operator_\":\"The address of the Operator\",\"symbol_\":\"ERC-20 symbol of this token\",\"underlying_\":\"The address of the underlying asset\"}},\"exchangeRateCurrent()\":{\"returns\":{\"_0\":\"The current exchange rate\"}},\"exchangeRateStored()\":{\"returns\":{\"_0\":\"The stored exchange rate\"}},\"getAccountSnapshot(address)\":{\"params\":{\"account\":\"The address to query the account snapshot for\"},\"returns\":{\"_0\":\"(token balance, borrow balance, exchange rate)\"}},\"getCash()\":{\"returns\":{\"_0\":\"The total amount of cash\"}},\"liquidate(address,uint256,address)\":{\"params\":{\"borrower\":\"The borrower of this mToken to be liquidated\",\"mTokenCollateral\":\"The market in which to seize collateral from the borrower\",\"repayAmount\":\"The amount of the underlying borrowed asset to repay\"}},\"mint(uint256,address,uint256)\":{\"details\":\"Accrues interest whether or not the operation succeeds, unless reverted\",\"params\":{\"minAmountOut\":\"The min amounts to be received\",\"mintAmount\":\"The amount of the underlying asset to supply\",\"receiver\":\"The mTokens receiver\"}},\"redeem(uint256)\":{\"details\":\"Accrues interest whether or not the operation succeeds, unless reverted\",\"params\":{\"redeemTokens\":\"The number of mTokens to redeem into underlying\"}},\"redeemUnderlying(uint256)\":{\"details\":\"Accrues interest whether or not the operation succeeds, unless reverted\",\"params\":{\"redeemAmount\":\"The amount of underlying to redeem\"}},\"reduceReserves(uint256)\":{\"params\":{\"reduceAmount\":\"Amount of reduction to reserves\"}},\"repay(uint256)\":{\"params\":{\"repayAmount\":\"The amount to repay, or type(uint256).max for the full outstanding amount\"}},\"repayBehalf(address,uint256)\":{\"params\":{\"borrower\":\"the account with the debt being payed off\",\"repayAmount\":\"The amount to repay, or type(uint256).max for the full outstanding amount\"}},\"seize(address,address,uint256)\":{\"details\":\"Will fail unless called by another mToken during the process of liquidation.  Its absolutely critical to use msg.sender as the borrowed mToken and not a parameter.\",\"params\":{\"borrower\":\"The account having collateral seized\",\"liquidator\":\"The account receiving seized collateral\",\"seizeTokens\":\"The number of mTokens to seize\"}},\"setInterestRateModel(address)\":{\"details\":\"Admin function to accrue interest and update the interest rate model\",\"params\":{\"newInterestRateModel\":\"the new interest rate model to use\"}},\"setOperator(address)\":{\"details\":\"Admin function to set a new operator\"},\"setPendingAdmin(address)\":{\"details\":\"Admin function to begin change of admin. The newPendingAdmin must call `_acceptAdmin` to finalize the transfer.\",\"params\":{\"newPendingAdmin\":\"New pending admin.\"}},\"setReserveFactor(uint256)\":{\"details\":\"Admin function to accrue interest and set a new reserve factor\"},\"setRolesOperator(address)\":{\"details\":\"Admin function to set a new operator\"},\"supplyRatePerBlock()\":{\"returns\":{\"_0\":\"The current supply rate per block, scaled by 1e18\"}},\"sweepToken(address,uint256)\":{\"params\":{\"token\":\"The address of the ERC-20 token to sweep\"}},\"totalBorrowsCurrent()\":{\"returns\":{\"_0\":\"The total amount of borrows\"}},\"transfer(address,uint256)\":{\"params\":{\"amount\":\"The number of tokens to transfer\",\"dst\":\"The address of the recipient\"},\"returns\":{\"_0\":\"Whether the transfer was successful or not\"}},\"transferFrom(address,address,uint256)\":{\"params\":{\"amount\":\"The number of tokens to transfer\",\"dst\":\"The address to which tokens are transferred\",\"src\":\"The address from which tokens are transferred\"},\"returns\":{\"_0\":\"Whether the transfer was successful or not\"}}},\"version\":1},\"userdoc\":{\"events\":{\"AccrueInterest(uint256,uint256,uint256,uint256)\":{\"notice\":\"Event emitted when interest is accrued\"},\"Approval(address,address,uint256)\":{\"notice\":\"EIP20 Approval event\"},\"Borrow(address,uint256,uint256,uint256)\":{\"notice\":\"Event emitted when underlying is borrowed\"},\"LiquidateBorrow(address,address,uint256,address,uint256)\":{\"notice\":\"Event emitted when a borrow is liquidated\"},\"Mint(address,address,uint256,uint256)\":{\"notice\":\"Event emitted when tokens are minted\"},\"NewBorrowRateMaxMantissa(uint256,uint256)\":{\"notice\":\"Event emitted when the borrow max mantissa is updated\"},\"NewMarketInterestRateModel(address,address)\":{\"notice\":\"Event emitted when interestRateModel is changed\"},\"NewOperator(address,address)\":{\"notice\":\"Event emitted when Operator is changed\"},\"NewReserveFactor(uint256,uint256)\":{\"notice\":\"Event emitted when the reserve factor is changed\"},\"NewRolesOperator(address,address)\":{\"notice\":\"Event emitted when rolesOperator is changed\"},\"Redeem(address,uint256,uint256)\":{\"notice\":\"Event emitted when tokens are redeemed\"},\"RepayBorrow(address,address,uint256,uint256,uint256)\":{\"notice\":\"Event emitted when a borrow is repaid\"},\"ReservesAdded(address,uint256,uint256)\":{\"notice\":\"Event emitted when the reserves are added\"},\"ReservesReduced(address,uint256,uint256)\":{\"notice\":\"Event emitted when the reserves are reduced\"},\"SameChainFlowStateUpdated(address,bool,bool)\":{\"notice\":\"Event emitted when same chain flow state is enabled or disabled\"},\"Transfer(address,address,uint256)\":{\"notice\":\"EIP20 Transfer event\"},\"ZkVerifierUpdated(address,address)\":{\"notice\":\"Event emitted when same chain flow state is enabled or disabled\"}},\"kind\":\"user\",\"methods\":{\"acceptAdmin()\":{\"notice\":\"Accepts transfer of admin rights. msg.sender must be pendingAdmin\"},\"accrualBlockTimestamp()\":{\"notice\":\"Block timestamp that interest was last accrued at\"},\"accrueInterest()\":{\"notice\":\"Accrues interest on the contract's outstanding loans\"},\"addReserves(uint256)\":{\"notice\":\"The sender adds to reserves.\"},\"admin()\":{\"notice\":\"Administrator for this contract\"},\"allowance(address,address)\":{\"notice\":\"Returns the current allowance the `spender` has from the `owner`\"},\"approve(address,uint256)\":{\"notice\":\"Approves `spender` to spend `amount` tokens on behalf of the caller\"},\"balanceOf(address)\":{\"notice\":\"Returns the value of tokens owned by `account`.\"},\"balanceOfUnderlying(address)\":{\"notice\":\"Returns the underlying asset balance of the `owner`\"},\"borrow(uint256)\":{\"notice\":\"Sender borrows assets from the protocol to their own address\"},\"borrowBalanceCurrent(address)\":{\"notice\":\"Returns the current borrow balance for `account`, accounting for interest\"},\"borrowBalanceStored(address)\":{\"notice\":\"Returns the stored borrow balance for `account`, without accruing interest\"},\"borrowIndex()\":{\"notice\":\"Accumulator of the total earned interest rate since the opening of the market\"},\"borrowRateMaxMantissa()\":{\"notice\":\"Maximum borrow rate that can ever be applied\"},\"borrowRatePerBlock()\":{\"notice\":\"Returns the current borrow rate per block\"},\"constructor\":{\"notice\":\"Constructs the new money market\"},\"decimals()\":{\"notice\":\"EIP-20 token decimals for this token\"},\"exchangeRateCurrent()\":{\"notice\":\"Returns the current exchange rate, with interest accrued\"},\"exchangeRateStored()\":{\"notice\":\"Returns the stored exchange rate, without accruing interest\"},\"getAccountSnapshot(address)\":{\"notice\":\"Returns the snapshot of account details for the given `account`\"},\"getCash()\":{\"notice\":\"Returns the total amount of available cash in the contract\"},\"interestRateModel()\":{\"notice\":\"Model which tells what the current interest rate should be\"},\"liquidate(address,uint256,address)\":{\"notice\":\"The sender liquidates the borrowers collateral.  The collateral seized is transferred to the liquidator.\"},\"mint(uint256,address,uint256)\":{\"notice\":\"Sender supplies assets into the market and receives mTokens in exchange\"},\"name()\":{\"notice\":\"EIP-20 token name for this token\"},\"operator()\":{\"notice\":\"Contract which oversees inter-mToken operations\"},\"pendingAdmin()\":{\"notice\":\"Pending administrator for this contract\"},\"redeem(uint256)\":{\"notice\":\"Sender redeems mTokens in exchange for the underlying asset\"},\"redeemUnderlying(uint256)\":{\"notice\":\"Sender redeems mTokens in exchange for a specified amount of underlying asset\"},\"reduceReserves(uint256)\":{\"notice\":\"Accrues interest and reduces reserves by transferring to admin\"},\"repay(uint256)\":{\"notice\":\"Sender repays their own borrow\"},\"repayBehalf(address,uint256)\":{\"notice\":\"Sender repays a borrow belonging to borrower\"},\"reserveFactorMantissa()\":{\"notice\":\"Fraction of interest currently set aside for reserves\"},\"rolesOperator()\":{\"notice\":\"Roles manager\"},\"seize(address,address,uint256)\":{\"notice\":\"Transfers collateral tokens (this market) to the liquidator.\"},\"setInterestRateModel(address)\":{\"notice\":\"accrues interest and updates the interest rate model using _setInterestRateModelFresh\"},\"setOperator(address)\":{\"notice\":\"Sets a new Operator for the market\"},\"setPendingAdmin(address)\":{\"notice\":\"Begins transfer of admin rights. The newPendingAdmin must call `_acceptAdmin` to finalize the transfer.\"},\"setReserveFactor(uint256)\":{\"notice\":\"accrues interest and sets a new reserve factor for the protocol using _setReserveFactorFresh\"},\"setRolesOperator(address)\":{\"notice\":\"Sets a new Operator for the market\"},\"supplyRatePerBlock()\":{\"notice\":\"Returns the current supply rate per block\"},\"sweepToken(address,uint256)\":{\"notice\":\"A public function to sweep accidental ERC-20 transfers to this contract. Tokens are sent to admin (timelock)\"},\"symbol()\":{\"notice\":\"EIP-20 token symbol for this token\"},\"totalBorrows()\":{\"notice\":\"Total amount of outstanding borrows of the underlying in this market\"},\"totalBorrowsCurrent()\":{\"notice\":\"Returns the total amount of borrows, accounting for interest\"},\"totalReserves()\":{\"notice\":\"Total amount of reserves of the underlying held in this market\"},\"totalSupply()\":{\"notice\":\"Returns the value of tokens in existence.\"},\"totalUnderlying()\":{\"notice\":\"Returns the amount of underlying tokens\"},\"transfer(address,uint256)\":{\"notice\":\"Transfers `amount` tokens to the `dst` address\"},\"transferFrom(address,address,uint256)\":{\"notice\":\"Transfers `amount` tokens from the `src` address to the `dst` address\"},\"underlying()\":{\"notice\":\"Underlying asset for this mToken\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/mToken/mErc20Immutable.sol\":\"mErc20Immutable\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xc6a8ff0ea489379b61faa647490411b80102578440ab9d84e9a957cc12164e70\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0ea104e577e63faea3b69c415637e99e755dcbf64c5833d7140c35a714d6d90c\",\"dweb:/ipfs/Qmau6x4Ns9XdyynRCNNp3RhLqijJjFm7z5fyZazfYFGYdq\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol\":{\"keccak256\":\"0x6008dabfe393240d73d7dd7688033f72740d570aa422254d29a7dce8568f3aff\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f5196ec75139918c6c7bb4251b36395e668f1fa6d206beba7e7520e74913940d\",\"dweb:/ipfs/QmSyqjksXxmm2mCG6qRd1yuwLykypkSVBbnBnGqJRcuJMi\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol\":{\"keccak256\":\"0x37bb49513c49c87c4642a891b13b63571bc87013dde806617aa1efb54605f386\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b3036b3a83b7c48f96641f2a9002b9f2dcb6a5958dd670894ada21ae8229b3d0\",\"dweb:/ipfs/QmUNfSBdoVtjhETaUJCYcaC7pTMgbhht926tJ2uXJbiVd3\"]},\"lib/openzeppelin-contracts/contracts/utils/Address.sol\":{\"keccak256\":\"0xaf28a975a78550e45f65e559a3ad6a5ad43b9b8a37366999abd1b7084eb70721\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b7bd24e224f67f65bfadf85dc2929fa965456bb2415478bd0125471b5ce35245\",\"dweb:/ipfs/QmRaydGr8BTHs1kvaZfsNU69pKzUAGFrvABn1KiRSbE51y\"]},\"lib/openzeppelin-contracts/contracts/utils/ReentrancyGuard.sol\":{\"keccak256\":\"0xf980daa263b661ab8ddee7d4fd833c7da7e7995e2c359ff1f17e67e4112f2236\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7448ab095d6940130bcf76ba47a2eab14148c83119523b93dd89f6d84edd6c02\",\"dweb:/ipfs/QmawrZ4voKQjH3oomXT3Kuheb3Mnmo2VvVpxg8Ne5UJUrd\"]},\"src/interfaces/IBlacklister.sol\":{\"keccak256\":\"0x47ed20135507a9d16473027cbee18de5d952846bada761c6248b91c75cd88be1\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://91a886b95dd578156f9b6cec3236e4751aae8a38e5d15e0d6045217c6fbd3d63\",\"dweb:/ipfs/QmRNjz7sGTuMkQNZhwwcg5DNN2eeqTZSAdZmxWxbx66CHU\"]},\"src/interfaces/IInterestRateModel.sol\":{\"keccak256\":\"0xdc8386be37a06d93fb0967be32e465f3b1f941ec9ce2f90984a5c70488cddb1e\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://de2bf96a7d0945f58b00e9fc867ac00fc4a09480e9e27c1a800604295320b769\",\"dweb:/ipfs/Qmf5sAgA58GjVxeipJNhP4euxwsZ9FS9CgsWTJ319UmCUE\"]},\"src/interfaces/IOperator.sol\":{\"keccak256\":\"0x0ae3d63eb07518da5a4fd6de7b013b6f55c87defb31900548378cffc70a51f72\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://1d9710c4ac43cb0563449aa519501b4ed18a331c90d0bc7eb79cdc56407bad8a\",\"dweb:/ipfs/Qmafe9Wup2GF9pD83CLWUrRait93vwvReqU1C7EHQhY7WV\"]},\"src/interfaces/IRoles.sol\":{\"keccak256\":\"0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c\",\"dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj\"]},\"src/interfaces/ImErc20.sol\":{\"keccak256\":\"0x86a1dcaf0d188107ceae7c5eb0911ac63feafbbeda6d0686e4f9950a51d17888\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://d7dd8e0fcfd68501d9f40e37e1c95d8cb653d65626aeeeb5492b73c0e967e6cb\",\"dweb:/ipfs/QmcadG9QuHtAX5qxTKzNr6FMaXSgijr542tFUVTLBkYhAF\"]},\"src/interfaces/ImToken.sol\":{\"keccak256\":\"0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36\",\"dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL\"]},\"src/mToken/mErc20.sol\":{\"keccak256\":\"0x46aa77f6808c1ca56c7d51b4822bc03d26d036151b4eeb324a3dbdc52bc90643\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://4681c86b025cb7d0872c003146b4e6d56b2e86387c832134f4224fc9d559218c\",\"dweb:/ipfs/QmWM9zVEmNWYSYEvGPd8jwSioG8zBC3hQnCEHbup1gCCLe\"]},\"src/mToken/mErc20Immutable.sol\":{\"keccak256\":\"0x20fc9e1acba9e145cfaad5cb4e86de3a3b35de9f1611bb7fca8bf9303087419d\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://7fecf95de1419da6fed36c0367b5a48ce9c9ca6137be56e40cc1a02ec4bb702a\",\"dweb:/ipfs/QmevE6TFUTc9UPfKMQJJidBaXDicTb1yLuVkxF3FNrhJxJ\"]},\"src/mToken/mToken.sol\":{\"keccak256\":\"0xeefa3394ae7a01c38bc97404ca6375a497e0bce2a8ae3f83feb4c5bd681aaf43\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://cd94a416031e33c169d36692a661899dc9697b8c543bdb8c051f70a3e7eced2c\",\"dweb:/ipfs/QmRBoo7LQ5nbwLpBjB9yEEib8RM5i9yQjfeE7FELHXvBBk\"]},\"src/mToken/mTokenConfiguration.sol\":{\"keccak256\":\"0x4ac1b80b93e3b13ce568f859c1fc4dd54360578d6055027819fee3e6d0b2b83f\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://e27a83d36a038105631a1ce53f92d225b9949a2cc6aed6d898c736850c10676a\",\"dweb:/ipfs/QmZhvhCfZsDxvyYJ1x9TNSfnHKrxpbgLq3qr7hJiL3XpZa\"]},\"src/mToken/mTokenStorage.sol\":{\"keccak256\":\"0x42236cd9e37c2eef2d317f05510bbe21cf5174813ee40f9a6fcbaf84d4fed2f7\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://2679166e1ca0e5d21294447566bba7549900a8f9a9b1882c8c2895a9751a1792\",\"dweb:/ipfs/Qmb49FVvre1He18e6ABHjNJ2yzCPdkYeyzSwMXixZ7fUw3\"]},\"src/utils/ExponentialNoError.sol\":{\"keccak256\":\"0xa97178117d8c32059d55dd2224e5514c54b31f896e565c3834cab3d7502b1bb6\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://1cecc0713709f57a5bb7f64d2fa06b9437a42752fa3fed5d3c0b19d82aa56ef4\",\"dweb:/ipfs/QmQqBCZkzmZxomXLr3b1tHUV55n8EPk3wAUsXgojYSpR41\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "underlying_", "type": "address"}, {"internalType": "address", "name": "operator_", "type": "address"}, {"internalType": "address", "name": "interestRateModel_", "type": "address"}, {"internalType": "uint256", "name": "initialExchangeRateMantissa_", "type": "uint256"}, {"internalType": "string", "name": "name_", "type": "string"}, {"internalType": "string", "name": "symbol_", "type": "string"}, {"internalType": "uint8", "name": "decimals_", "type": "uint8"}, {"internalType": "address payable", "name": "admin_", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "target", "type": "address"}], "type": "error", "name": "AddressEmptyCode"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "type": "error", "name": "AddressInsufficientBalance"}, {"inputs": [], "type": "error", "name": "FailedInnerCall"}, {"inputs": [], "type": "error", "name": "ReentrancyGuardReentrantCall"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "type": "error", "name": "SafeERC20FailedOperation"}, {"inputs": [], "type": "error", "name": "mErc20_TokenNotValid"}, {"inputs": [], "type": "error", "name": "mt_AlreadyInitialized"}, {"inputs": [], "type": "error", "name": "mt_BorrowCashNotAvailable"}, {"inputs": [], "type": "error", "name": "mt_BorrowRateTooHigh"}, {"inputs": [], "type": "error", "name": "mt_CollateralBlockTimestampNotValid"}, {"inputs": [], "type": "error", "name": "mt_ExchangeRateNotValid"}, {"inputs": [], "type": "error", "name": "mt_InvalidInput"}, {"inputs": [], "type": "error", "name": "mt_LiquidateSeizeTooMuch"}, {"inputs": [], "type": "error", "name": "mt_MarketMethodNotValid"}, {"inputs": [], "type": "error", "name": "mt_MinAmountNotValid"}, {"inputs": [], "type": "error", "name": "mt_OnlyAdmin"}, {"inputs": [], "type": "error", "name": "mt_OnlyAdminOrRole"}, {"inputs": [], "type": "error", "name": "mt_RedeemCashNotAvailable"}, {"inputs": [], "type": "error", "name": "mt_RedeemEmpty"}, {"inputs": [], "type": "error", "name": "mt_RedeemTransferOutNotPossible"}, {"inputs": [], "type": "error", "name": "mt_ReserveCashNotAvailable"}, {"inputs": [], "type": "error", "name": "mt_ReserveFactorTooHigh"}, {"inputs": [], "type": "error", "name": "mt_SameChainOperationsAreDisabled"}, {"inputs": [], "type": "error", "name": "mt_TransferNotValid"}, {"inputs": [{"internalType": "uint256", "name": "cashPrior", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "interestAccumulated", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "borrowIndex", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "totalBorrows", "type": "uint256", "indexed": false}], "type": "event", "name": "AccrueInterest", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address", "indexed": true}, {"internalType": "address", "name": "spender", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}], "type": "event", "name": "Approval", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "borrower", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "borrowAmount", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "accountBorrows", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "totalBorrows", "type": "uint256", "indexed": false}], "type": "event", "name": "Borrow", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "liquidator", "type": "address", "indexed": true}, {"internalType": "address", "name": "borrower", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "repayAmount", "type": "uint256", "indexed": false}, {"internalType": "address", "name": "mTokenCollateral", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "seizeTokens", "type": "uint256", "indexed": false}], "type": "event", "name": "LiquidateBorrow", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "minter", "type": "address", "indexed": true}, {"internalType": "address", "name": "receiver", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "mintAmount", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "mintTokens", "type": "uint256", "indexed": false}], "type": "event", "name": "Mint", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "oldVal", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "maxMantissa", "type": "uint256", "indexed": false}], "type": "event", "name": "NewBorrowRateMaxMantissa", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "oldInterestRateModel", "type": "address", "indexed": true}, {"internalType": "address", "name": "newInterestRateModel", "type": "address", "indexed": true}], "type": "event", "name": "NewMarketInterestRateModel", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "oldOperator", "type": "address", "indexed": true}, {"internalType": "address", "name": "newOperator", "type": "address", "indexed": true}], "type": "event", "name": "NewOperator", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "oldReserveFactorMantissa", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "newReserveFactorMantissa", "type": "uint256", "indexed": false}], "type": "event", "name": "NewReserveFactor", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "oldRoles", "type": "address", "indexed": true}, {"internalType": "address", "name": "newRoles", "type": "address", "indexed": true}], "type": "event", "name": "NewRolesOperator", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "redeemer", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "redeemAmount", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "redeemTokens", "type": "uint256", "indexed": false}], "type": "event", "name": "Redeem", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "payer", "type": "address", "indexed": true}, {"internalType": "address", "name": "borrower", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "repayAmount", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "accountBorrows", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "totalBorrows", "type": "uint256", "indexed": false}], "type": "event", "name": "RepayBorrow", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "benefactor", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "addAmount", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "newTotalReserves", "type": "uint256", "indexed": false}], "type": "event", "name": "ReservesAdded", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "admin", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "reduceAmount", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "newTotalReserves", "type": "uint256", "indexed": false}], "type": "event", "name": "ReservesReduced", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address", "indexed": true}, {"internalType": "bool", "name": "_oldState", "type": "bool", "indexed": false}, {"internalType": "bool", "name": "_newState", "type": "bool", "indexed": false}], "type": "event", "name": "SameChainFlowStateUpdated", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "from", "type": "address", "indexed": true}, {"internalType": "address", "name": "to", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}], "type": "event", "name": "Transfer", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "oldVerifier", "type": "address", "indexed": true}, {"internalType": "address", "name": "newVerifier", "type": "address", "indexed": true}], "type": "event", "name": "ZkVerifierUpdated", "anonymous": false}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "acceptAdmin"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "accrualBlockTimestamp", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "accrueInterest"}, {"inputs": [{"internalType": "uint256", "name": "addAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "addReserves"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "admin", "outputs": [{"internalType": "address payable", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "stateMutability": "view", "type": "function", "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "stateMutability": "view", "type": "function", "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "balanceOfUnderlying", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "borrowAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "borrow"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "borrowBalanceCurrent", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "borrowBalanceStored", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "borrowIndex", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "borrowRateMaxMantissa", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "borrowRatePerBlock", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "exchangeRateCurrent", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "exchangeRateStored", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getAccountSnapshot", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getCash", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "interestRateModel", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "borrower", "type": "address"}, {"internalType": "uint256", "name": "repayAmount", "type": "uint256"}, {"internalType": "address", "name": "mTokenCollateral", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "liquidate"}, {"inputs": [{"internalType": "uint256", "name": "mintAmount", "type": "uint256"}, {"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "uint256", "name": "minAmountOut", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "mint"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "operator", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "pendingAdmin", "outputs": [{"internalType": "address payable", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "uint256", "name": "redeemTokens", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "redeem"}, {"inputs": [{"internalType": "uint256", "name": "redeemAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "redeemUnderlying"}, {"inputs": [{"internalType": "uint256", "name": "reduceAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "reduceReserves"}, {"inputs": [{"internalType": "uint256", "name": "repayAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "repay", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "borrower", "type": "address"}, {"internalType": "uint256", "name": "repayAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "repayBehalf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "reserveFactorMantissa", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "rolesOperator", "outputs": [{"internalType": "contract IRoles", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "liquidator", "type": "address"}, {"internalType": "address", "name": "borrower", "type": "address"}, {"internalType": "uint256", "name": "seizeTokens", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "seize"}, {"inputs": [{"internalType": "uint256", "name": "maxMantissa", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "setBorrowRateMaxMantissa"}, {"inputs": [{"internalType": "address", "name": "newInterestRateModel", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setInterestRateModel"}, {"inputs": [{"internalType": "address", "name": "_operator", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setOperator"}, {"inputs": [{"internalType": "address payable", "name": "newPendingAdmin", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setPendingAdmin"}, {"inputs": [{"internalType": "uint256", "name": "newReserveFactorMantissa", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "setReserveFactor"}, {"inputs": [{"internalType": "address", "name": "_roles", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setRolesOperator"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "supplyRatePerBlock", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "contract IERC20", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "sweepToken"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "totalBorrows", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "totalBorrowsCurrent", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "totalReserves", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "totalUnderlying", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "dst", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "src", "type": "address"}, {"internalType": "address", "name": "dst", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "underlying", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}], "devdoc": {"kind": "dev", "methods": {"acceptAdmin()": {"details": "Admin function for pending admin to accept role and update admin"}, "addReserves(uint256)": {"params": {"addAmount": "The amount fo underlying token to add as reserves"}}, "allowance(address,address)": {"params": {"owner": "The address of the token holder", "spender": "The address authorized to spend the tokens"}, "returns": {"_0": "The current remaining number of tokens `spender` can spend"}}, "approve(address,uint256)": {"params": {"amount": "The number of tokens to approve", "spender": "The address authorized to spend tokens"}, "returns": {"_0": "Whether the approval was successful or not"}}, "balanceOf(address)": {"params": {"account": "The account to check for"}}, "balanceOfUnderlying(address)": {"params": {"owner": "The address to query the balance of underlying assets for"}, "returns": {"_0": "The balance of underlying assets owned by `owner`"}}, "borrow(uint256)": {"params": {"borrowAmount": "The amount of the underlying asset to borrow"}}, "borrowBalanceCurrent(address)": {"params": {"account": "The address to query the borrow balance for"}, "returns": {"_0": "The current borrow balance"}}, "borrowBalanceStored(address)": {"params": {"account": "The address to query the stored borrow balance for"}, "returns": {"_0": "The stored borrow balance"}}, "borrowRatePerBlock()": {"returns": {"_0": "The current borrow rate per block, scaled by 1e18"}}, "constructor": {"params": {"admin_": "Address of the administrator of this token", "decimals_": "ERC-20 decimal precision of this token", "initialExchangeRateMantissa_": "The initial exchange rate, scaled by 1e18", "interestRateModel_": "The address of the interest rate model", "name_": "ERC-20 name of this token", "operator_": "The address of the Operator", "symbol_": "ERC-20 symbol of this token", "underlying_": "The address of the underlying asset"}}, "exchangeRateCurrent()": {"returns": {"_0": "The current exchange rate"}}, "exchangeRateStored()": {"returns": {"_0": "The stored exchange rate"}}, "getAccountSnapshot(address)": {"params": {"account": "The address to query the account snapshot for"}, "returns": {"_0": "(token balance, borrow balance, exchange rate)"}}, "getCash()": {"returns": {"_0": "The total amount of cash"}}, "liquidate(address,uint256,address)": {"params": {"borrower": "The borrower of this mToken to be liquidated", "mTokenCollateral": "The market in which to seize collateral from the borrower", "repayAmount": "The amount of the underlying borrowed asset to repay"}}, "mint(uint256,address,uint256)": {"details": "Accrues interest whether or not the operation succeeds, unless reverted", "params": {"minAmountOut": "The min amounts to be received", "mintAmount": "The amount of the underlying asset to supply", "receiver": "The mTokens receiver"}}, "redeem(uint256)": {"details": "Accrues interest whether or not the operation succeeds, unless reverted", "params": {"redeemTokens": "The number of mTokens to redeem into underlying"}}, "redeemUnderlying(uint256)": {"details": "Accrues interest whether or not the operation succeeds, unless reverted", "params": {"redeemAmount": "The amount of underlying to redeem"}}, "reduceReserves(uint256)": {"params": {"reduceAmount": "Amount of reduction to reserves"}}, "repay(uint256)": {"params": {"repayAmount": "The amount to repay, or type(uint256).max for the full outstanding amount"}}, "repayBehalf(address,uint256)": {"params": {"borrower": "the account with the debt being payed off", "repayAmount": "The amount to repay, or type(uint256).max for the full outstanding amount"}}, "seize(address,address,uint256)": {"details": "Will fail unless called by another mToken during the process of liquidation.  Its absolutely critical to use msg.sender as the borrowed mToken and not a parameter.", "params": {"borrower": "The account having collateral seized", "liquidator": "The account receiving seized collateral", "seizeTokens": "The number of mTokens to seize"}}, "setInterestRateModel(address)": {"details": "Admin function to accrue interest and update the interest rate model", "params": {"newInterestRateModel": "the new interest rate model to use"}}, "setOperator(address)": {"details": "Admin function to set a new operator"}, "setPendingAdmin(address)": {"details": "Admin function to begin change of admin. The newPendingAdmin must call `_acceptAdmin` to finalize the transfer.", "params": {"newPendingAdmin": "New pending admin."}}, "setReserveFactor(uint256)": {"details": "Admin function to accrue interest and set a new reserve factor"}, "setRolesOperator(address)": {"details": "Admin function to set a new operator"}, "supplyRatePerBlock()": {"returns": {"_0": "The current supply rate per block, scaled by 1e18"}}, "sweepToken(address,uint256)": {"params": {"token": "The address of the ERC-20 token to sweep"}}, "totalBorrowsCurrent()": {"returns": {"_0": "The total amount of borrows"}}, "transfer(address,uint256)": {"params": {"amount": "The number of tokens to transfer", "dst": "The address of the recipient"}, "returns": {"_0": "Whether the transfer was successful or not"}}, "transferFrom(address,address,uint256)": {"params": {"amount": "The number of tokens to transfer", "dst": "The address to which tokens are transferred", "src": "The address from which tokens are transferred"}, "returns": {"_0": "Whether the transfer was successful or not"}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"acceptAdmin()": {"notice": "Accepts transfer of admin rights. msg.sender must be pendingAdmin"}, "accrualBlockTimestamp()": {"notice": "Block timestamp that interest was last accrued at"}, "accrueInterest()": {"notice": "Accrues interest on the contract's outstanding loans"}, "addReserves(uint256)": {"notice": "The sender adds to reserves."}, "admin()": {"notice": "Administrator for this contract"}, "allowance(address,address)": {"notice": "Returns the current allowance the `spender` has from the `owner`"}, "approve(address,uint256)": {"notice": "Approves `spender` to spend `amount` tokens on behalf of the caller"}, "balanceOf(address)": {"notice": "Returns the value of tokens owned by `account`."}, "balanceOfUnderlying(address)": {"notice": "Returns the underlying asset balance of the `owner`"}, "borrow(uint256)": {"notice": "Sender borrows assets from the protocol to their own address"}, "borrowBalanceCurrent(address)": {"notice": "Returns the current borrow balance for `account`, accounting for interest"}, "borrowBalanceStored(address)": {"notice": "Returns the stored borrow balance for `account`, without accruing interest"}, "borrowIndex()": {"notice": "Accumulator of the total earned interest rate since the opening of the market"}, "borrowRateMaxMantissa()": {"notice": "Maximum borrow rate that can ever be applied"}, "borrowRatePerBlock()": {"notice": "Returns the current borrow rate per block"}, "constructor": {"notice": "Constructs the new money market"}, "decimals()": {"notice": "EIP-20 token decimals for this token"}, "exchangeRateCurrent()": {"notice": "Returns the current exchange rate, with interest accrued"}, "exchangeRateStored()": {"notice": "Returns the stored exchange rate, without accruing interest"}, "getAccountSnapshot(address)": {"notice": "Returns the snapshot of account details for the given `account`"}, "getCash()": {"notice": "Returns the total amount of available cash in the contract"}, "interestRateModel()": {"notice": "Model which tells what the current interest rate should be"}, "liquidate(address,uint256,address)": {"notice": "The sender liquidates the borrowers collateral.  The collateral seized is transferred to the liquidator."}, "mint(uint256,address,uint256)": {"notice": "Sender supplies assets into the market and receives mTokens in exchange"}, "name()": {"notice": "EIP-20 token name for this token"}, "operator()": {"notice": "Contract which oversees inter-mToken operations"}, "pendingAdmin()": {"notice": "Pending administrator for this contract"}, "redeem(uint256)": {"notice": "Sender redeems mTokens in exchange for the underlying asset"}, "redeemUnderlying(uint256)": {"notice": "Sender redeems mTokens in exchange for a specified amount of underlying asset"}, "reduceReserves(uint256)": {"notice": "Accrues interest and reduces reserves by transferring to admin"}, "repay(uint256)": {"notice": "Sender repays their own borrow"}, "repayBehalf(address,uint256)": {"notice": "Sender repays a borrow belonging to borrower"}, "reserveFactorMantissa()": {"notice": "Fraction of interest currently set aside for reserves"}, "rolesOperator()": {"notice": "Roles manager"}, "seize(address,address,uint256)": {"notice": "Transfers collateral tokens (this market) to the liquidator."}, "setInterestRateModel(address)": {"notice": "accrues interest and updates the interest rate model using _setInterestRateModelFresh"}, "setOperator(address)": {"notice": "Sets a new Operator for the market"}, "setPendingAdmin(address)": {"notice": "Begins transfer of admin rights. The newPendingAdmin must call `_acceptAdmin` to finalize the transfer."}, "setReserveFactor(uint256)": {"notice": "accrues interest and sets a new reserve factor for the protocol using _setReserveFactorFresh"}, "setRolesOperator(address)": {"notice": "Sets a new Operator for the market"}, "supplyRatePerBlock()": {"notice": "Returns the current supply rate per block"}, "sweepToken(address,uint256)": {"notice": "A public function to sweep accidental ERC-20 transfers to this contract. Tokens are sent to admin (timelock)"}, "symbol()": {"notice": "EIP-20 token symbol for this token"}, "totalBorrows()": {"notice": "Total amount of outstanding borrows of the underlying in this market"}, "totalBorrowsCurrent()": {"notice": "Returns the total amount of borrows, accounting for interest"}, "totalReserves()": {"notice": "Total amount of reserves of the underlying held in this market"}, "totalSupply()": {"notice": "Returns the value of tokens in existence."}, "totalUnderlying()": {"notice": "Returns the amount of underlying tokens"}, "transfer(address,uint256)": {"notice": "Transfers `amount` tokens to the `dst` address"}, "transferFrom(address,address,uint256)": {"notice": "Transfers `amount` tokens from the `src` address to the `dst` address"}, "underlying()": {"notice": "Underlying asset for this mToken"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/mToken/mErc20Immutable.sol": "mErc20Immutable"}, "evmVersion": "london", "libraries": {}}, "sources": {"lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0xc6a8ff0ea489379b61faa647490411b80102578440ab9d84e9a957cc12164e70", "urls": ["bzz-raw://0ea104e577e63faea3b69c415637e99e755dcbf64c5833d7140c35a714d6d90c", "dweb:/ipfs/Qmau6x4Ns9XdyynRCNNp3RhLqijJjFm7z5fyZazfYFGYdq"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol": {"keccak256": "0x6008dabfe393240d73d7dd7688033f72740d570aa422254d29a7dce8568f3aff", "urls": ["bzz-raw://f5196ec75139918c6c7bb4251b36395e668f1fa6d206beba7e7520e74913940d", "dweb:/ipfs/QmSyqjksXxmm2mCG6qRd1yuwLykypkSVBbnBnGqJRcuJMi"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol": {"keccak256": "0x37bb49513c49c87c4642a891b13b63571bc87013dde806617aa1efb54605f386", "urls": ["bzz-raw://b3036b3a83b7c48f96641f2a9002b9f2dcb6a5958dd670894ada21ae8229b3d0", "dweb:/ipfs/QmUNfSBdoVtjhETaUJCYcaC7pTMgbhht926tJ2uXJbiVd3"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Address.sol": {"keccak256": "0xaf28a975a78550e45f65e559a3ad6a5ad43b9b8a37366999abd1b7084eb70721", "urls": ["bzz-raw://b7bd24e224f67f65bfadf85dc2929fa965456bb2415478bd0125471b5ce35245", "dweb:/ipfs/QmRaydGr8BTHs1kvaZfsNU69pKzUAGFrvABn1KiRSbE51y"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/ReentrancyGuard.sol": {"keccak256": "0xf980daa263b661ab8ddee7d4fd833c7da7e7995e2c359ff1f17e67e4112f2236", "urls": ["bzz-raw://7448ab095d6940130bcf76ba47a2eab14148c83119523b93dd89f6d84edd6c02", "dweb:/ipfs/QmawrZ4voKQjH3oomXT3Kuheb3Mnmo2VvVpxg8Ne5UJUrd"], "license": "MIT"}, "src/interfaces/IBlacklister.sol": {"keccak256": "0x47ed20135507a9d16473027cbee18de5d952846bada761c6248b91c75cd88be1", "urls": ["bzz-raw://91a886b95dd578156f9b6cec3236e4751aae8a38e5d15e0d6045217c6fbd3d63", "dweb:/ipfs/QmRNjz7sGTuMkQNZhwwcg5DNN2eeqTZSAdZmxWxbx66CHU"], "license": "BSL-1.1"}, "src/interfaces/IInterestRateModel.sol": {"keccak256": "0xdc8386be37a06d93fb0967be32e465f3b1f941ec9ce2f90984a5c70488cddb1e", "urls": ["bzz-raw://de2bf96a7d0945f58b00e9fc867ac00fc4a09480e9e27c1a800604295320b769", "dweb:/ipfs/Qmf5sAgA58GjVxeipJNhP4euxwsZ9FS9CgsWTJ319UmCUE"], "license": "BSL-1.1"}, "src/interfaces/IOperator.sol": {"keccak256": "0x0ae3d63eb07518da5a4fd6de7b013b6f55c87defb31900548378cffc70a51f72", "urls": ["bzz-raw://1d9710c4ac43cb0563449aa519501b4ed18a331c90d0bc7eb79cdc56407bad8a", "dweb:/ipfs/Qmafe9Wup2GF9pD83CLWUrRait93vwvReqU1C7EHQhY7WV"], "license": "BSL-1.1"}, "src/interfaces/IRoles.sol": {"keccak256": "0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365", "urls": ["bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c", "dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj"], "license": "BSL-1.1"}, "src/interfaces/ImErc20.sol": {"keccak256": "0x86a1dcaf0d188107ceae7c5eb0911ac63feafbbeda6d0686e4f9950a51d17888", "urls": ["bzz-raw://d7dd8e0fcfd68501d9f40e37e1c95d8cb653d65626aeeeb5492b73c0e967e6cb", "dweb:/ipfs/QmcadG9QuHtAX5qxTKzNr6FMaXSgijr542tFUVTLBkYhAF"], "license": "BSL-1.1"}, "src/interfaces/ImToken.sol": {"keccak256": "0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f", "urls": ["bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36", "dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL"], "license": "BSL-1.1"}, "src/mToken/mErc20.sol": {"keccak256": "0x46aa77f6808c1ca56c7d51b4822bc03d26d036151b4eeb324a3dbdc52bc90643", "urls": ["bzz-raw://4681c86b025cb7d0872c003146b4e6d56b2e86387c832134f4224fc9d559218c", "dweb:/ipfs/QmWM9zVEmNWYSYEvGPd8jwSioG8zBC3hQnCEHbup1gCCLe"], "license": "BSL-1.1"}, "src/mToken/mErc20Immutable.sol": {"keccak256": "0x20fc9e1acba9e145cfaad5cb4e86de3a3b35de9f1611bb7fca8bf9303087419d", "urls": ["bzz-raw://7fecf95de1419da6fed36c0367b5a48ce9c9ca6137be56e40cc1a02ec4bb702a", "dweb:/ipfs/QmevE6TFUTc9UPfKMQJJidBaXDicTb1yLuVkxF3FNrhJxJ"], "license": "BSL-1.1"}, "src/mToken/mToken.sol": {"keccak256": "0xeefa3394ae7a01c38bc97404ca6375a497e0bce2a8ae3f83feb4c5bd681aaf43", "urls": ["bzz-raw://cd94a416031e33c169d36692a661899dc9697b8c543bdb8c051f70a3e7eced2c", "dweb:/ipfs/QmRBoo7LQ5nbwLpBjB9yEEib8RM5i9yQjfeE7FELHXvBBk"], "license": "BSL-1.1"}, "src/mToken/mTokenConfiguration.sol": {"keccak256": "0x4ac1b80b93e3b13ce568f859c1fc4dd54360578d6055027819fee3e6d0b2b83f", "urls": ["bzz-raw://e27a83d36a038105631a1ce53f92d225b9949a2cc6aed6d898c736850c10676a", "dweb:/ipfs/QmZhvhCfZsDxvyYJ1x9TNSfnHKrxpbgLq3qr7hJiL3XpZa"], "license": "BSL-1.1"}, "src/mToken/mTokenStorage.sol": {"keccak256": "0x42236cd9e37c2eef2d317f05510bbe21cf5174813ee40f9a6fcbaf84d4fed2f7", "urls": ["bzz-raw://2679166e1ca0e5d21294447566bba7549900a8f9a9b1882c8c2895a9751a1792", "dweb:/ipfs/Qmb49FVvre1He18e6ABHjNJ2yzCPdkYeyzSwMXixZ7fUw3"], "license": "BSL-1.1"}, "src/utils/ExponentialNoError.sol": {"keccak256": "0xa97178117d8c32059d55dd2224e5514c54b31f896e565c3834cab3d7502b1bb6", "urls": ["bzz-raw://1cecc0713709f57a5bb7f64d2fa06b9437a42752fa3fed5d3c0b19d82aa56ef4", "dweb:/ipfs/QmQqBCZkzmZxomXLr3b1tHUV55n8EPk3wAUsXgojYSpR41"], "license": "BSL-1.1"}}, "version": 1}, "id": 176}