{"abi": [{"type": "function", "name": "CHAINS_MANAGER", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "GUARDIAN_BLACKLIST", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "GUARDIAN_BORROW_CAP", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "GUARDIAN_BRIDGE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "GUARDIAN_ORACLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "GUARDIAN_PAUSE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "GUARDIAN_RESERVE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "GUARDIAN_SUPPLY_CAP", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "PAUSE_MANAGER", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "PROOF_BATCH_FORWARDER", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "PROOF_FORWARDER", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "REBALANCER", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "REBALANCER_EOA", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "SEQUENCER", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "isAllowedFor", "inputs": [{"name": "_contract", "type": "address", "internalType": "address"}, {"name": "_role", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "error", "name": "Roles_InputNotValid", "inputs": []}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"CHAINS_MANAGER()": "e0482413", "GUARDIAN_BLACKLIST()": "ce848e13", "GUARDIAN_BORROW_CAP()": "265cbca9", "GUARDIAN_BRIDGE()": "ce287160", "GUARDIAN_ORACLE()": "7f3c8ff5", "GUARDIAN_PAUSE()": "bffdc2a4", "GUARDIAN_RESERVE()": "d71c72e0", "GUARDIAN_SUPPLY_CAP()": "9943ad67", "PAUSE_MANAGER()": "47164d3b", "PROOF_BATCH_FORWARDER()": "a1bd302d", "PROOF_FORWARDER()": "a8720195", "REBALANCER()": "9e106dc7", "REBALANCER_EOA()": "48a166e6", "SEQUENCER()": "75fd4ca9", "isAllowedFor(address,bytes32)": "38dd8c2c"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"Roles_InputNotValid\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"CHAINS_MANAGER\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"GUARDIAN_BLACKLIST\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"GUARDIAN_BORROW_CAP\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"GUARDIAN_BRIDGE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"GUARDIAN_ORACLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"GUARDIAN_PAUSE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"GUARDIAN_RESERVE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"GUARDIAN_SUPPLY_CAP\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"PAUSE_MANAGER\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"PROOF_BATCH_FORWARDER\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"PROOF_FORWARDER\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"REBALANCER\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"REBALANCER_EOA\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"SEQUENCER\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_contract\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"_role\",\"type\":\"bytes32\"}],\"name\":\"isAllowedFor\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{\"isAllowedFor(address,bytes32)\":{\"params\":{\"_contract\":\"the contract address\",\"_role\":\"the bytes32 role\"}}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"CHAINS_MANAGER()\":{\"notice\":\"Returns CHAINS_MANAGER role\"},\"GUARDIAN_BLACKLIST()\":{\"notice\":\"Returns GUARDIAN_BLACKLIST role\"},\"GUARDIAN_BORROW_CAP()\":{\"notice\":\"Returns GUARDIAN_BORROW_CAP role\"},\"GUARDIAN_BRIDGE()\":{\"notice\":\"Returns GUARDIAN_BRIDGE role\"},\"GUARDIAN_ORACLE()\":{\"notice\":\"Returns GUARDIAN_ORACLE role\"},\"GUARDIAN_PAUSE()\":{\"notice\":\"Returns GUARDIAN_PAUSE role\"},\"GUARDIAN_RESERVE()\":{\"notice\":\"Returns GUARDIAN_RESERVE role\"},\"GUARDIAN_SUPPLY_CAP()\":{\"notice\":\"Returns GUARDIAN_SUPPLY_CAP role\"},\"PAUSE_MANAGER()\":{\"notice\":\"Returns PAUSE_MANAGER role\"},\"PROOF_BATCH_FORWARDER()\":{\"notice\":\"Returns PROOF_BATCH_FORWARDER role\"},\"PROOF_FORWARDER()\":{\"notice\":\"Returns PROOF_FORWARDER role\"},\"REBALANCER()\":{\"notice\":\"Returns REBALANCER role\"},\"REBALANCER_EOA()\":{\"notice\":\"Returns REBALANCER_EOA role\"},\"SEQUENCER()\":{\"notice\":\"Returns SEQUENCER role\"},\"isAllowedFor(address,bytes32)\":{\"notice\":\"Returns allowance status for a contract and a role\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/IRoles.sol\":\"IRoles\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"src/interfaces/IRoles.sol\":{\"keccak256\":\"0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c\",\"dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "Roles_InputNotValid"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "CHAINS_MANAGER", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "GUARDIAN_BLACKLIST", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "GUARDIAN_BORROW_CAP", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "GUARDIAN_BRIDGE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "GUARDIAN_ORACLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "GUARDIAN_PAUSE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "GUARDIAN_RESERVE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "GUARDIAN_SUPPLY_CAP", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "PAUSE_MANAGER", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "PROOF_BATCH_FORWARDER", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "PROOF_FORWARDER", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "REBALANCER", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "REBALANCER_EOA", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "SEQUENCER", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "address", "name": "_contract", "type": "address"}, {"internalType": "bytes32", "name": "_role", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "isAllowedFor", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}], "devdoc": {"kind": "dev", "methods": {"isAllowedFor(address,bytes32)": {"params": {"_contract": "the contract address", "_role": "the bytes32 role"}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"CHAINS_MANAGER()": {"notice": "Returns CHAINS_MANAGER role"}, "GUARDIAN_BLACKLIST()": {"notice": "Returns GUARDIAN_BLACKLIST role"}, "GUARDIAN_BORROW_CAP()": {"notice": "Returns GUARDIAN_BORROW_CAP role"}, "GUARDIAN_BRIDGE()": {"notice": "Returns GUARDIAN_BRIDGE role"}, "GUARDIAN_ORACLE()": {"notice": "Returns GUARDIAN_ORACLE role"}, "GUARDIAN_PAUSE()": {"notice": "Returns GUARDIAN_PAUSE role"}, "GUARDIAN_RESERVE()": {"notice": "Returns GUARDIAN_RESERVE role"}, "GUARDIAN_SUPPLY_CAP()": {"notice": "Returns GUARDIAN_SUPPLY_CAP role"}, "PAUSE_MANAGER()": {"notice": "Returns PAUSE_MANAGER role"}, "PROOF_BATCH_FORWARDER()": {"notice": "Returns PROOF_BATCH_FORWARDER role"}, "PROOF_FORWARDER()": {"notice": "Returns PROOF_FORWARDER role"}, "REBALANCER()": {"notice": "Returns REBALANCER role"}, "REBALANCER_EOA()": {"notice": "Returns REBALANCER_EOA role"}, "SEQUENCER()": {"notice": "Returns SEQUENCER role"}, "isAllowedFor(address,bytes32)": {"notice": "Returns allowance status for a contract and a role"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/interfaces/IRoles.sol": "IRoles"}, "evmVersion": "london", "libraries": {}}, "sources": {"src/interfaces/IRoles.sol": {"keccak256": "0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365", "urls": ["bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c", "dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj"], "license": "BSL-1.1"}}, "version": 1}, "id": 143}