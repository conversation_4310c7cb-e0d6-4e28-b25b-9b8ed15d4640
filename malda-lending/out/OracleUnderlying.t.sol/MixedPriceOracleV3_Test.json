{"abi": [{"type": "function", "name": "IS_TEST", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "accountAssets", "inputs": [{"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "afterMTokenMint", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "view"}, {"type": "function", "name": "allMarkets", "inputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "beforeMTokenBorrow", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}, {"name": "borrower", "type": "address", "internalType": "address"}, {"name": "borrowAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "beforeMTokenLiquidate", "inputs": [{"name": "mTokenBorrowed", "type": "address", "internalType": "address"}, {"name": "mTokenCollateral", "type": "address", "internalType": "address"}, {"name": "borrower", "type": "address", "internalType": "address"}, {"name": "repayAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "view"}, {"type": "function", "name": "beforeMTokenMint", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}, {"name": "minter", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "beforeMTokenRedeem", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}, {"name": "redeemer", "type": "address", "internalType": "address"}, {"name": "redeemTokens", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "beforeMTokenRepay", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}, {"name": "borrower", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "beforeMTokenSeize", "inputs": [{"name": "mTokenCollateral", "type": "address", "internalType": "address"}, {"name": "mTokenBorrowed", "type": "address", "internalType": "address"}, {"name": "liquidator", "type": "address", "internalType": "address"}, {"name": "borrower", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "beforeMTokenTransfer", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}, {"name": "src", "type": "address", "internalType": "address"}, {"name": "dst", "type": "address", "internalType": "address"}, {"name": "transferTokens", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "beforeRebalancing", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "view"}, {"type": "function", "name": "blacklistOperator", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IBlacklister"}], "stateMutability": "view"}, {"type": "function", "name": "borrowCaps", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "checkMembership", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "mToken", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "checkOutflowVolumeLimit", "inputs": [{"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "claimMalda", "inputs": [{"name": "holder", "type": "address", "internalType": "address"}, {"name": "mTokens", "type": "address[]", "internalType": "address[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "claimMalda", "inputs": [{"name": "holders", "type": "address[]", "internalType": "address[]"}, {"name": "mTokens", "type": "address[]", "internalType": "address[]"}, {"name": "borrowers", "type": "bool", "internalType": "bool"}, {"name": "suppliers", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "claimMalda", "inputs": [{"name": "holder", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "closeFactorMantissa", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "cumulativeOutflowVolume", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "disable<PERSON><PERSON><PERSON><PERSON>", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "enterMarkets", "inputs": [{"name": "_mTokens", "type": "address[]", "internalType": "address[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "enterMarketsWithSender", "inputs": [{"name": "_account", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "excludeArtifacts", "inputs": [], "outputs": [{"name": "excludedArtifacts_", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "excludeContracts", "inputs": [], "outputs": [{"name": "excludedContracts_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "excludeSelectors", "inputs": [], "outputs": [{"name": "excludedSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzSelector[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "excludeSenders", "inputs": [], "outputs": [{"name": "excludedSenders_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "exitMarket", "inputs": [{"name": "_mToken", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "failed", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "getAccountLiquidity", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getAllMarkets", "inputs": [], "outputs": [{"name": "mTokens", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "getAssetsIn", "inputs": [{"name": "_user", "type": "address", "internalType": "address"}], "outputs": [{"name": "mTokens", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "getHypotheticalAccountLiquidity", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "mTokenModify", "type": "address", "internalType": "address"}, {"name": "redeemTokens", "type": "uint256", "internalType": "uint256"}, {"name": "borrowAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getUSDValueForAllMarkets", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "initialize", "inputs": [{"name": "_rolesOperator", "type": "address", "internalType": "address"}, {"name": "_blacklistOperator", "type": "address", "internalType": "address"}, {"name": "_rewardDistributor", "type": "address", "internalType": "address"}, {"name": "_admin", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "isDeprecated", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isMarketListed", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isOperator", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "pure"}, {"type": "function", "name": "isPaused", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}, {"name": "_type", "type": "uint8", "internalType": "enum ImTokenOperationTypes.OperationType"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "lastOutflowResetTimestamp", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "limitPerTimePeriod", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "liquidateCalculateSeizeTokens", "inputs": [{"name": "mTokenBorrowed", "type": "address", "internalType": "address"}, {"name": "mTokenCollateral", "type": "address", "internalType": "address"}, {"name": "actualRepayAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "liquidationIncentiveMantissa", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "markets", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "isListed", "type": "bool", "internalType": "bool"}, {"name": "collateralFactorMantissa", "type": "uint256", "internalType": "uint256"}, {"name": "isMalded", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "newOracleInBase", "inputs": [{"name": "usdPerQuotedToken", "type": "uint256", "internalType": "uint256"}, {"name": "usdPerBaseToken", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "contract MockChainlinkOracle"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "newUSDOracle", "inputs": [{"name": "usdPerToken", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "contract MockChainlinkOracle"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "oracleOperator", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "outflowResetTimeWindow", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "resetOutflowVolume", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "rewardDistributor", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "rolesOperator", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IRoles"}], "stateMutability": "view"}, {"type": "function", "name": "setCloseFactor", "inputs": [{"name": "newCloseFactorMantissa", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setCollateralFactor", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}, {"name": "newCollateralFactorMantissa", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setLiquidationIncentive", "inputs": [{"name": "market", "type": "address", "internalType": "address"}, {"name": "newLiquidationIncentiveMantissa", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setMarketBorrowCaps", "inputs": [{"name": "mTokens", "type": "address[]", "internalType": "address[]"}, {"name": "newBorrowCaps", "type": "uint256[]", "internalType": "uint256[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setMarketSupplyCaps", "inputs": [{"name": "mTokens", "type": "address[]", "internalType": "address[]"}, {"name": "newSupplyCaps", "type": "uint256[]", "internalType": "uint256[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setOutflowTimeLimitInUSD", "inputs": [{"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setOutflowVolumeTimeWindow", "inputs": [{"name": "newTimeWindow", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setPaused", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}, {"name": "_type", "type": "uint8", "internalType": "enum ImTokenOperationTypes.OperationType"}, {"name": "state", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setPriceO<PERSON>le", "inputs": [{"name": "newOracle", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setRewardDistributor", "inputs": [{"name": "newRewardDistributor", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setRolesOperator", "inputs": [{"name": "_roles", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setUp", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setWhitelistedUser", "inputs": [{"name": "user", "type": "address", "internalType": "address"}, {"name": "state", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "supplyCaps", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "supportMarket", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "targetArtifactSelectors", "inputs": [], "outputs": [{"name": "targetedArtifactSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzArtifactSelector[]", "components": [{"name": "artifact", "type": "string", "internalType": "string"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetArtifacts", "inputs": [], "outputs": [{"name": "targetedArtifacts_", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "targetContracts", "inputs": [], "outputs": [{"name": "targetedContracts_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "targetInterfaces", "inputs": [], "outputs": [{"name": "targetedInterfaces_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzInterface[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "artifacts", "type": "string[]", "internalType": "string[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetSelectors", "inputs": [], "outputs": [{"name": "targetedSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzSelector[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetSenders", "inputs": [], "outputs": [{"name": "targetedSenders_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "test_getPriceUSD", "inputs": [], "outputs": [], "stateMutability": "view"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "user<PERSON><PERSON><PERSON>sted", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "whitelistEnabled", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "event", "name": "ActionPaused", "inputs": [{"name": "mToken", "type": "address", "indexed": true, "internalType": "address"}, {"name": "_type", "type": "uint8", "indexed": false, "internalType": "enum ImTokenOperationTypes.OperationType"}, {"name": "state", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "Initialized", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "MarketEntered", "inputs": [{"name": "mToken", "type": "address", "indexed": true, "internalType": "address"}, {"name": "account", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "MarketExited", "inputs": [{"name": "mToken", "type": "address", "indexed": true, "internalType": "address"}, {"name": "account", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "MarketListed", "inputs": [{"name": "mToken", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "NewBorrowCap", "inputs": [{"name": "mToken", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newBorrowCap", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "NewCloseFactor", "inputs": [{"name": "oldCloseFactorMantissa", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "newCloseFactorMantissa", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "NewCollateralFactor", "inputs": [{"name": "mToken", "type": "address", "indexed": true, "internalType": "address"}, {"name": "oldCollateralFactorMantissa", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "newCollateralFactorMantissa", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "NewLiquidationIncentive", "inputs": [{"name": "market", "type": "address", "indexed": false, "internalType": "address"}, {"name": "oldLiquidationIncentiveMantissa", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "newLiquidationIncentiveMantissa", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "NewPriceOracle", "inputs": [{"name": "oldPriceOracle", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newPriceOracle", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "NewRewardDistributor", "inputs": [{"name": "oldRewardDistributor", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newRewardDistributor", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "NewRolesOperator", "inputs": [{"name": "oldRoles", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newRoles", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "NewSupplyCap", "inputs": [{"name": "mToken", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newBorrowCap", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "OutflowLimitUpdated", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "oldLimit", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "newLimit", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "OutflowTimeWindowUpdated", "inputs": [{"name": "oldWindow", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "newWindow", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "OutflowVolumeReset", "inputs": [], "anonymous": false}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "<PERSON>r<PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "user", "type": "address", "indexed": true, "internalType": "address"}, {"name": "state", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "WhitelistDisabled", "inputs": [], "anonymous": false}, {"type": "event", "name": "WhitelistEnabled", "inputs": [], "anonymous": false}, {"type": "event", "name": "log", "inputs": [{"name": "", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_address", "inputs": [{"name": "", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "int256[]", "indexed": false, "internalType": "int256[]"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "event", "name": "log_bytes", "inputs": [{"name": "", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "log_bytes32", "inputs": [{"name": "", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "log_int", "inputs": [{"name": "", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "log_named_address", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256[]", "indexed": false, "internalType": "int256[]"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "event", "name": "log_named_bytes", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "log_named_bytes32", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "log_named_decimal_int", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256", "indexed": false, "internalType": "int256"}, {"name": "decimals", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_named_decimal_uint", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "decimals", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_named_int", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "log_named_string", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_named_uint", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_string", "inputs": [{"name": "", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_uint", "inputs": [{"name": "", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "logs", "inputs": [{"name": "", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "error", "name": "InvalidInitialization", "inputs": []}, {"type": "error", "name": "NotInitializing", "inputs": []}, {"type": "error", "name": "Operator_AssetNotFound", "inputs": []}, {"type": "error", "name": "Operator_Deactivate_MarketBalanceOwed", "inputs": []}, {"type": "error", "name": "Operator_EmptyPrice", "inputs": []}, {"type": "error", "name": "Operator_InsufficientLiquidity", "inputs": []}, {"type": "error", "name": "Operator_InvalidBlacklistOperator", "inputs": []}, {"type": "error", "name": "Operator_InvalidCollateralFactor", "inputs": []}, {"type": "error", "name": "Operator_InvalidInput", "inputs": []}, {"type": "error", "name": "Operator_InvalidRewardDistributor", "inputs": []}, {"type": "error", "name": "Operator_InvalidRolesOperator", "inputs": []}, {"type": "error", "name": "Operator_MarketAlreadyListed", "inputs": []}, {"type": "error", "name": "Operator_MarketBorrowCapReached", "inputs": []}, {"type": "error", "name": "Operator_MarketNotListed", "inputs": []}, {"type": "error", "name": "Operator_MarketSupplyReached", "inputs": []}, {"type": "error", "name": "Operator_Mismatch", "inputs": []}, {"type": "error", "name": "Operator_OnlyAdmin", "inputs": []}, {"type": "error", "name": "Operator_OnlyAdminOrRole", "inputs": []}, {"type": "error", "name": "Operator_OracleUnderlyingFetchError", "inputs": []}, {"type": "error", "name": "Operator_OutflowVolumeReached", "inputs": []}, {"type": "error", "name": "Operator_Paused", "inputs": []}, {"type": "error", "name": "Operator_PriceFetchFailed", "inputs": []}, {"type": "error", "name": "Operator_RepayAmountNotValid", "inputs": []}, {"type": "error", "name": "Operator_RepayingTooMuch", "inputs": []}, {"type": "error", "name": "Operator_SenderMustBeToken", "inputs": []}, {"type": "error", "name": "Operator_UserBlacklisted", "inputs": []}, {"type": "error", "name": "Operator_UserNotWhitelisted", "inputs": []}, {"type": "error", "name": "Operator_WrongMarket", "inputs": []}, {"type": "error", "name": "OwnableInvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}], "bytecode": {"object": "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", "sourceMap": "1429:3960:233:-:0;;;;;3126:44:3;;;3166:4;-1:-1:-1;;3126:44:3;;;;;;;1065:26:14;;;;;;;;;;;1594:6:233;1570:30;;1632:1;1606:27;;;;1702:5;1682:25;;1735:2;1713:24;;1788:22;;;;1839:1;1816:24;;1919:28;;1953:31;;1991:24;;1429:3960;;;;;;;;;-1:-1:-1;1596:22:127;:20;:22::i;:::-;1429:3960:233;;7711:422:22;8870:21;7900:15;;;;;;;7896:76;;;7938:23;;-1:-1:-1;;;7938:23:22;;;;;;;;;;;7896:76;7985:14;;-1:-1:-1;;;;;7985:14:22;;;:34;7981:146;;8035:33;;-1:-1:-1;;;;;;8035:33:22;-1:-1:-1;;;;;8035:33:22;;;;;8087:29;;158:50:242;;;8087:29:22;;146:2:242;131:18;8087:29:22;;;;;;;7981:146;7760:373;7711:422::o;14:200:242:-;1429:3960:233;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x608060405234801561001057600080fd5b50600436106104805760003560e01c80638da5cb5b11610257578063c321fbcc11610146578063e44a429a116100c3578063f89416ee11610087578063f89416ee14610a37578063f8c8765e14610a4a578063f9f00f8914610a5d578063fa7626d414610a70578063fc2e0c2f14610a7d57600080fd5b8063e44a429a146109ec578063e8755446146109ff578063e92081b414610a08578063ede4edd014610a11578063f2fde38b14610a2457600080fd5b8063d6b0f4841161010a578063d6b0f484146109b8578063d99faea5146109c0578063dce15449146109c8578063ddf46254146109db578063e20c9f71146109e457600080fd5b8063c321fbcc14610964578063c488847b14610977578063cab4f84c1461098a578063cdfb2b4e1461099d578063d136af44146109a557600080fd5b8063acc2166a116101d4578063ba414fa611610198578063ba414fa614610910578063befca68414610918578063c04f31ff1461092b578063c0f1ee091461093e578063c29982381461095157600080fd5b8063acc2166a146108d2578063b0464fdc146108e5578063b0772d0b146108ed578063b50ce762146108f5578063b5508aa91461090857600080fd5b806394543c151161021b57806394543c1514610873578063973fd521146108865780639bd8f6e814610899578063a1809b95146108ac578063abfceffc146108bf57600080fd5b80638da5cb5b146107af5780638e8f294b146107b7578063916a17c61461080a578063918c89e31461081f578063929fe9a11461083257600080fd5b80634a584432116103735780635ec88c79116102f05780637048e31d116102b45780637048e31d14610763578063715018a614610776578063823307f21461077e57806385226c81146107915780638728d8a7146107a657600080fd5b80635ec88c791461070c57806366d9a9a01461071f5780636765dff91461073457806368f6f4b014610747578063700e12121461075a57600080fd5b806350795f8a1161033757806350795f8a146106be57806351fb012d146106d157806352a2548d146106de57806352d84d1e146106e6578063530e784f146106f957600080fd5b80634a584432146106485780634a675b34146106685780634e79238f1461067b5780634fe010a5146106a35780634fecab70146106ab57600080fd5b80631ed7831c116104015780633d98a1e5116103c55780633d98a1e5146105f25780633e5e3c231461061e5780633f7286f4146106265780634456eda21461062e57806344710fbe1461063557600080fd5b80631ed7831c146105825780631fbd27a5146105975780632ade3880146105aa5780632d57d487146105bf5780632e06d7b1146105d257600080fd5b806312348e961161044857806312348e961461052357806317bf120e14610536578063186db48f146105495780631c7818ac1461055c5780631e32bd9b1461056f57600080fd5b806302c3bcbb146104855780630a9254e4146104b85780630d126627146104c25780630d926fc8146104e557806311679ef7146104f8575b600080fd5b6104a5610493366004615498565b60096020526000908152604090205481565b6040519081526020015b60405180910390f35b6104c0610aa0565b005b6104d56104d03660046154c4565b61110a565b60405190151581526020016104af565b6104c06104f3366004615498565b611163565b60025461050b906001600160a01b031681565b6040516001600160a01b0390911681526020016104af565b6104c06105313660046154f9565b611292565b6104c0610544366004615512565b611317565b6104c06105573660046155af565b6114a5565b6104c061056a3660046156d2565b6116f0565b6104c061057d366004615722565b611758565b61058a611848565b6040516104af9190615763565b6104c06105a53660046157bd565b6118aa565b6105b26118bc565b6040516104af91906158ed565b60015461050b906001600160a01b031681565b6104a56105e0366004615498565b60046020526000908152604090205481565b6104d5610600366004615498565b6001600160a01b031660009081526006602052604090205460ff1690565b61058a6119fe565b61058a611a5e565b60016104d5565b6104c0610643366004615972565b611abe565b6104a5610656366004615498565b60086020526000908152604090205481565b6104c06106763660046159ab565b611b25565b61068e6106893660046159f4565b611d36565b604080519283526020830191909152016104af565b6104c0611d53565b60005461050b906001600160a01b031681565b6104c06106cc366004615722565b61208f565b6010546104d59060ff1681565b6104c0612416565b61050b6106f43660046154f9565b61244e565b6104c0610707366004615498565b612478565b61068e61071a366004615498565b612503565b61072761251d565b6040516104af9190615a80565b6104c0610742366004615b00565b61268a565b6104c0610755366004615498565b612992565b6104a5600c5481565b61050b6107713660046154f9565b6129db565b6104c0612a38565b6104c061078c3660046154f9565b612a4c565b610799612afe565b6040516104af9190615b51565b6104a5600b5481565b61050b612bce565b6107eb6107c5366004615498565b60066020526000908152604090208054600182015460039092015460ff91821692911683565b60408051931515845260208401929092521515908201526060016104af565b610812612bfc565b6040516104af9190615b64565b61050b61082d366004615bdd565b612ce2565b6104d5610840366004615bff565b6001600160a01b038082166000908152600660209081526040808320938616835260029093019052205460ff1692915050565b6104d5610881366004615498565b612d4b565b6104c0610894366004615498565b612d56565b6104c06108a7366004615c2d565b612dd7565b6104c06108ba366004615498565b612e53565b61058a6108cd366004615498565b612eb7565b600a5461050b906001600160a01b031681565b610812612f2d565b61058a613013565b6104c0610903366004615512565b613073565b61079961331f565b6104d56133ef565b6104c06109263660046154f9565b613493565b6104c0610939366004615c2d565b6134dc565b6104c061094c366004615bff565b613654565b6104c061095f366004615c59565b6137b8565b6104c0610972366004615bff565b613848565b6104a5610985366004615722565b613920565b6104c0610998366004615498565b613b4e565b6104c0613caf565b6104c06109b33660046155af565b613cef565b6104c0613f31565b6104a5613f6e565b61050b6109d6366004615c2d565b61404d565b6104a5600d5481565b61058a614085565b6104c06109fa366004615498565b6140e5565b6104a560035481565b6104a5600e5481565b6104c0610a1f366004615498565b6141a2565b6104c0610a32366004615498565b614461565b6104c0610a45366004615498565b6144a1565b6104c0610a58366004615b00565b61452b565b6104c0610a6b3660046154f9565b61471f565b6031546104d59060ff1681565b6104d5610a8b366004615498565b600f6020526000908152604090205460ff1681565b603554604051610aaf9061539d565b60408082526003908201526242544360e81b60608201526020810191909152608001604051809103906000f080158015610aed573d6000803e3d6000fd5b50603280546001600160a01b0319166001600160a01b0392909216919091179055603954604051610b1d9061539d565b60408082526003908201526208aa8960eb1b60608201526020810191909152608001604051809103906000f080158015610b5b573d6000803e3d6000fd5b50603680546001600160a01b0319166001600160a01b0392909216919091179055603d54604051610b8b9061539d565b6040808252600490820152635553444360e01b60608201526020810191909152608001604051809103906000f080158015610bca573d6000803e3d6000fd5b50603a80546001600160a01b0319166001600160a01b0392909216919091179055604154604051610bfa9061539d565b6040808252600590820152644c6172676560d81b60608201526020810191909152608001604051809103906000f080158015610c3a573d6000803e3d6000fd5b50603e80546001600160a01b0319166001600160a01b03928316179055603254604051911690610c69906153aa565b6001600160a01b039091168152602001604051809103906000f080158015610c95573d6000803e3d6000fd5b50603380546001600160a01b0319166001600160a01b03928316179055603654604051911690610cc4906153aa565b6001600160a01b039091168152602001604051809103906000f080158015610cf0573d6000803e3d6000fd5b50603780546001600160a01b0319166001600160a01b03928316179055603a54604051911690610d1f906153aa565b6001600160a01b039091168152602001604051809103906000f080158015610d4b573d6000803e3d6000fd5b50603b80546001600160a01b0319166001600160a01b03928316179055603e54604051911690610d7a906153aa565b6001600160a01b039091168152602001604051809103906000f080158015610da6573d6000803e3d6000fd5b50603f60006101000a8154816001600160a01b0302191690836001600160a01b031602179055506000610dda603c546129db565b90506000610dec603854603c54612ce2565b90506000610dfe603454603854612ce2565b604080516003808252608082019092529192509060009082816020015b6060815260200190600190039081610e1b57905050905060008267ffffffffffffffff811115610e4d57610e4d615620565b604051908082528060200260200182016040528015610eab57816020015b610e98604051806060016040528060006001600160a01b0316815260200160608152602001600081525090565b815260200190600190039081610e6b5790505b509050604051806040016040528060048152602001635553444360e01b81525082600081518110610ede57610ede615c9b565b60200260200101819052506040518060600160405280876001600160a01b03168152602001604051806040016040528060038152602001621554d160ea1b8152508152602001603d5481525081600081518110610f3d57610f3d615c9b565b60200260200101819052506040518060400160405280600381526020016208aa8960eb1b81525082600181518110610f7757610f77615c9b565b60200260200101819052506040518060600160405280866001600160a01b03168152602001604051806040016040528060048152602001635553444360e01b815250815260200160395481525081600181518110610fd757610fd7615c9b565b60200260200101819052506040518060400160405280600381526020016242544360e81b8152508260028151811061101157611011615c9b565b60200260200101819052506040518060600160405280856001600160a01b031681526020016040518060400160405280600381526020016208aa8960eb1b81525081526020016035548152508160028151811061107057611070615c9b565b60200260200101819052506000806064905083838383604051611092906153b7565b61109f9493929190615cb1565b604051809103906000f0801580156110bb573d6000803e3d6000fd5b5060318054610100600160a81b0319166101006001600160a01b039384168102919091179182905560028054919092049092166001600160a01b03199092169190911790555050505050505050565b6001600160a01b03821660009081526011602052604081208183600b81111561113557611135615d72565b600b81111561114657611146615d72565b815260208101919091526040016000205460ff1690505b92915050565b6001600160a01b038116600090815260096020526040902054801561128e576000826001600160a01b03166318160ddd6040518163ffffffff1660e01b8152600401602060405180830381865afa1580156111c2573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906111e69190615d88565b905060006040518060200160405280856001600160a01b031663182df0f56040518163ffffffff1660e01b8152600401602060405180830381865afa158015611233573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906112579190615d88565b905290506000611267828461476a565b90508381111561128a57604051635b025ead60e01b815260040160405180910390fd5b5050505b5050565b61129a61478a565b66b1a2bc2ec5000081101580156112b95750670c7d713b49da00008111155b6112d65760405163e5d0f96f60e01b815260040160405180910390fd5b60035460408051918252602082018390527f3b9670cf975d26958e754b57098eaa2ac914d8d2a31b83257997b9f346110fd9910160405180910390a1600355565b60015460405163fe575a8760e01b81526001600160a01b0380861660048301528592169063fe575a8790602401602060405180830381865afa158015611361573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906113859190615da1565b156113a35760405163711b123360e01b815260040160405180910390fd5b60015460405163fe575a8760e01b81526001600160a01b0380861660048301528592169063fe575a8790602401602060405180830381865afa1580156113ed573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906114119190615da1565b1561142f5760405163711b123360e01b815260040160405180910390fd5b6001600160a01b03861660009081526011602090815260408083206005845290915290205460ff1615611475576040516349fbea8b60e01b815260040160405180910390fd5b6114808686856147bc565b611489866148a2565b61149386866148fe565b61149d86856148fe565b505050505050565b6114ad612bce565b6001600160a01b0316336001600160a01b031614806115ab57506000546040805163265cbca960e01b815290516001600160a01b03909216916338dd8c2c913391849163265cbca99160048083019260209291908290030181865afa15801561151a573d6000803e3d6000fd5b505050506040513d601f19601f8201168201806040525081019061153e9190615d88565b6040516001600160e01b031960e085901b1681526001600160a01b0390921660048301526024820152604401602060405180830381865afa158015611587573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906115ab9190615da1565b6115c857604051636fd743bb60e11b815260040160405180910390fd5b828181158015906115d857508082145b6115f55760405163e5d0f96f60e01b815260040160405180910390fd5b60005b828110156116e75784848281811061161257611612615c9b565b905060200201356008600089898581811061162f5761162f615c9b565b90506020020160208101906116449190615498565b6001600160a01b0316815260208101919091526040016000205586868281811061167057611670615c9b565b90506020020160208101906116859190615498565b6001600160a01b03167f6f1951b2aad10f3fc81b86d91105b413a5b3f847a34bbc5ce1904201b14438f68686848181106116c1576116c1615c9b565b905060200201356040516116d791815260200190565b60405180910390a26001016115f8565b50505050505050565b60408051600180825281830190925260009160208083019080368337019050509050828160008151811061172657611726615c9b565b60200260200101906001600160a01b031690816001600160a01b0316815250506117538183600180614962565b505050565b601054829060ff161561179e576001600160a01b0381166000908152600f602052604090205460ff1661179e5760405163efc6ae3360e01b815260040160405180910390fd5b60015460405163fe575a8760e01b81526001600160a01b0380861660048301528592169063fe575a8790602401602060405180830381865afa1580156117e8573d6000803e3d6000fd5b505050506040513d601f19601f8201168201806040525081019061180c9190615da1565b1561182a5760405163711b123360e01b815260040160405180910390fd5b6118358585856147bc565b61183e856148a2565b61128a85856148fe565b606060288054806020026020016040519081016040528092919081815260200182805480156118a057602002820191906000526020600020905b81546001600160a01b03168152600190910190602001808311611882575b5050505050905090565b6118b684848484614962565b50505050565b60606030805480602002602001604051908101604052809291908181526020016000905b828210156119f557600084815260208082206040805180820182526002870290920180546001600160a01b03168352600181018054835181870281018701909452808452939591948681019491929084015b828210156119de57838290600052602060002001805461195190615dbe565b80601f016020809104026020016040519081016040528092919081815260200182805461197d90615dbe565b80156119ca5780601f1061199f576101008083540402835291602001916119ca565b820191906000526020600020905b8154815290600101906020018083116119ad57829003601f168201915b505050505081526020019060010190611932565b5050505081525050815260200190600101906118e0565b50505050905090565b6060602a8054806020026020016040519081016040528092919081815260200182805480156118a0576020028201919060005260206000209081546001600160a01b03168152600190910190602001808311611882575050505050905090565b606060298054806020026020016040519081016040528092919081815260200182805480156118a0576020028201919060005260206000209081546001600160a01b03168152600190910190602001808311611882575050505050905090565b611ac661478a565b6001600160a01b0382166000818152600f6020908152604091829020805460ff191685151590811790915591519182527f995d2434d36c9bceaab42c56efe51e8ffe41fd11cabefd9e2cb83700d8b2035e910160405180910390a25050565b8015611c5357611b33612bce565b6001600160a01b0316336001600160a01b03161480611c31575060005460408051632fff70a960e21b815290516001600160a01b03909216916338dd8c2c913391849163bffdc2a49160048083019260209291908290030181865afa158015611ba0573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190611bc49190615d88565b6040516001600160e01b031960e085901b1681526001600160a01b0390921660048301526024820152604401602060405180830381865afa158015611c0d573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190611c319190615da1565b611c4e57604051636fd743bb60e11b815260040160405180910390fd5b611c8c565b611c5b612bce565b6001600160a01b0316336001600160a01b031614611c8c5760405163444b2c2360e11b815260040160405180910390fd5b6001600160a01b0383166000908152601160205260408120829184600b811115611cb857611cb8615d72565b600b811115611cc957611cc9615d72565b815260200190815260200160002060006101000a81548160ff021916908315150217905550826001600160a01b03167f09a1b808af19f025701cfcd3cd019fba653e4d664d69902d7b44880a49e6822c8383604051611d29929190615df2565b60405180910390a2505050565b600080611d4586868686614ac4565b915091505b94509492505050565b60315460335460405163fc57d4df60e01b81526001600160a01b03918216600482015260009261010090049091169063fc57d4df90602401602060405180830381865afa158015611da8573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190611dcc9190615d88565b60315460375460405163fc57d4df60e01b81526001600160a01b039182166004820152929350600092610100909204169063fc57d4df90602401602060405180830381865afa158015611e23573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190611e479190615d88565b603154603b5460405163fc57d4df60e01b81526001600160a01b039182166004820152929350600092610100909204169063fc57d4df90602401602060405180830381865afa158015611e9e573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190611ec29190615d88565b9050611eee60405180604001604052806008815260200167627463507269636560c01b81525084614d85565b611f1860405180604001604052806008815260200167657468507269636560c01b81525083614d85565b611f436040518060400160405280600981526020016875736463507269636560b81b81525082614d85565b611f73836034546035546024611f599190615e39565b611f6490600a615f33565b611f6e9190615f3f565b614dca565b611f89826038546039546024611f599190615e39565b611f9f81603c54603d546024611f599190615e39565b611fee6034546305f5e100611fb49190615f3f565b603354611fcf906305f5e100906001600160a01b0316614e30565b604051806040016040528060018152602001604160f81b815250614ef6565b6120416038546305f5e1006120039190615f3f565b60375461202290670de0b6b3a7640000906001600160a01b0316614e30565b604051806040016040528060018152602001602160f91b815250614ef6565b611753603c546305f5e1006120569190615f3f565b603b5461207090620f4240906001600160a01b0316614e30565b604051806040016040528060018152602001604360f81b815250614ef6565b601054829060ff16156120d5576001600160a01b0381166000908152600f602052604090205460ff166120d55760405163efc6ae3360e01b815260040160405180910390fd5b60015460405163fe575a8760e01b81526001600160a01b0380861660048301528592169063fe575a8790602401602060405180830381865afa15801561211f573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906121439190615da1565b156121615760405163711b123360e01b815260040160405180910390fd5b6001600160a01b03851660009081526011602090815260408083206007845290915290205460ff16156121a7576040516349fbea8b60e01b815260040160405180910390fd5b6001600160a01b03851660009081526006602052604090205460ff166121e05760405163107732bd60e21b815260040160405180910390fd5b6001600160a01b038086166000908152600660209081526040808320938816835260029093019052205460ff1661228e57336001600160a01b0386161461223a57604051635db212ed60e11b815260040160405180910390fd5b6122448585614f5d565b6001600160a01b038086166000908152600660209081526040808320938816835260029093019052205460ff1661228e576040516361dc86b560e11b815260040160405180910390fd5b60025460405163fc57d4df60e01b81526001600160a01b0387811660048301529091169063fc57d4df90602401602060405180830381865afa1580156122d8573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906122fc9190615d88565b60000361231c57604051636eda0f7b60e01b815260040160405180910390fd5b6001600160a01b03851660009081526008602052604090205480156123d2576000866001600160a01b03166347bd37186040518163ffffffff1660e01b8152600401602060405180830381865afa15801561237b573d6000803e3d6000fd5b505050506040513d601f19601f8201168201806040525081019061239f9190615d88565b905060006123ad828761503d565b90508281106123cf5760405163c4047e0f60e01b815260040160405180910390fd5b50505b60006123e18688600088614ac4565b9150508015612403576040516384c1d20160e01b815260040160405180910390fd5b61240c87615050565b6116e78787615082565b61241e61478a565b6000600c8190556040517f6767d885d8956eb0b593fc8534cd27b3ec1213cbb5dc8e0004d25afd136286d49190a1565b6007818154811061245e57600080fd5b6000918252602090912001546001600160a01b0316905081565b61248061478a565b6001600160a01b0381166124a75760405163e5d0f96f60e01b815260040160405180910390fd5b6002546040516001600160a01b038084169216907fd52b2b9b7e9ee655fcb95d2e5b9e0c9f69e7ef2b8e9d2d0ea78402d576d22e2290600090a3600280546001600160a01b0319166001600160a01b0392909216919091179055565b600080612514836000806000614ac4565b91509150915091565b6060602d805480602002602001604051908101604052809291908181526020016000905b828210156119f5578382906000526020600020906002020160405180604001604052908160008201805461257490615dbe565b80601f01602080910402602001604051908101604052809291908181526020018280546125a090615dbe565b80156125ed5780601f106125c2576101008083540402835291602001916125ed565b820191906000526020600020905b8154815290600101906020018083116125d057829003601f168201915b505050505081526020016001820180548060200260200160405190810160405280929190818152602001828054801561267257602002820191906000526020600020906000905b82829054906101000a900460e01b6001600160e01b031916815260200190600401906020826003010492830192600103820291508084116126345790505b50505050508152505081526020019060010190612541565b60015460405163fe575a8760e01b81526001600160a01b0380851660048301528492169063fe575a8790602401602060405180830381865afa1580156126d4573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906126f89190615da1565b156127165760405163711b123360e01b815260040160405180910390fd5b60015460405163fe575a8760e01b81526001600160a01b0380851660048301528492169063fe575a8790602401602060405180830381865afa158015612760573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906127849190615da1565b156127a25760405163711b123360e01b815260040160405180910390fd5b6001600160a01b03861660009081526011602090815260408083206004845290915290205460ff161580156127fc57506001600160a01b03851660009081526011602090815260408083206004845290915290205460ff16155b612819576040516349fbea8b60e01b815260040160405180910390fd5b6001600160a01b03851660009081526006602052604090205460ff166128525760405163107732bd60e21b815260040160405180910390fd5b6001600160a01b03861660009081526006602052604090205460ff1661288b5760405163107732bd60e21b815260040160405180910390fd5b846001600160a01b031663570ca7356040518163ffffffff1660e01b8152600401602060405180830381865afa1580156128c9573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906128ed9190615f56565b6001600160a01b0316866001600160a01b031663570ca7356040518163ffffffff1660e01b8152600401602060405180830381865afa158015612934573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906129589190615f56565b6001600160a01b03161461297f57604051632025588560e21b815260040160405180910390fd5b612988866148a2565b61149386846148fe565b6001600160a01b0381166000908152601160209081526040808320600b845290915290205460ff16156129d8576040516349fbea8b60e01b815260040160405180910390fd5b50565b60425460009081836129ee83600a615f33565b6129f89190615f3f565b90508082604051612a08906153c4565b9182526020820152604001604051809103906000f080158015612a2f573d6000803e3d6000fd5b50949350505050565b612a4061478a565b612a4a60006150bc565b565b3360009081526006602052604090205460ff16612a7c5760405163107732bd60e21b815260040160405180910390fd5b600b54156129d857600e54600d54612a949190615f73565b421115612aa5576000600c5542600d555b6000612ab18233614e30565b9050600b5481600c54612ac49190615f73565b1115612ae357604051630fb1f67d60e31b815260040160405180910390fd5b80600c6000828254612af59190615f73565b90915550505050565b6060602c805480602002602001604051908101604052809291908181526020016000905b828210156119f5578382906000526020600020018054612b4190615dbe565b80601f0160208091040260200160405190810160405280929190818152602001828054612b6d90615dbe565b8015612bba5780601f10612b8f57610100808354040283529160200191612bba565b820191906000526020600020905b815481529060010190602001808311612b9d57829003601f168201915b505050505081526020019060010190612b22565b7f9016d09d72d40fdae2fd8ceac6b6234c7706214fd39c1cd1e609a0528c199300546001600160a01b031690565b6060602f805480602002602001604051908101604052809291908181526020016000905b828210156119f55760008481526020908190206040805180820182526002860290920180546001600160a01b03168352600181018054835181870281018701909452808452939491938583019392830182828015612cca57602002820191906000526020600020906000905b82829054906101000a900460e01b6001600160e01b03191681526020019060040190602082600301049283019260010382029150808411612c8c5790505b50505050508152505081526020019060010190612c20565b604254600090818385612cf684600a615f33565b612d009190615f3f565b612d0a9190615f86565b90508082604051612d1a906153c4565b9182526020820152604001604051809103906000f080158015612d41573d6000803e3d6000fd5b5095945050505050565b600061115d8261512d565b601054819060ff1615612d9c576001600160a01b0381166000908152600f602052604090205460ff16612d9c5760405163efc6ae3360e01b815260040160405180910390fd5b336000908152600660205260409020805460ff16612dcd5760405163107732bd60e21b815260040160405180910390fd5b6117533384614f5d565b612ddf61478a565b6001600160a01b0382166000818152600460209081526040918290205482519384529083015281018290527f27341352c0a718639d6e997adf9031500037bcdf0e1a0396469ea59fdd815ed89060600160405180910390a16001600160a01b03909116600090815260046020526040902055565b612e5b61478a565b600a546040516001600160a01b038084169216907f8ddca872a7a62d68235cff1a03badc845dc3007cfaa6145379f7bf3452ecb9b990600090a3600a80546001600160a01b0319166001600160a01b0392909216919091179055565b6001600160a01b038116600090815260056020908152604091829020805483518184028101840190945280845260609392830182828015612f2157602002820191906000526020600020905b81546001600160a01b03168152600190910190602001808311612f03575b50505050509050919050565b6060602e805480602002602001604051908101604052809291908181526020016000905b828210156119f55760008481526020908190206040805180820182526002860290920180546001600160a01b03168352600181018054835181870281018701909452808452939491938583019392830182828015612ffb57602002820191906000526020600020906000905b82829054906101000a900460e01b6001600160e01b03191681526020019060040190602082600301049283019260010382029150808411612fbd5790505b50505050508152505081526020019060010190612f51565b606060078054806020026020016040519081016040528092919081815260200182805480156118a0576020028201919060005260206000209081546001600160a01b03168152600190910190602001808311611882575050505050905090565b601054829060ff16156130b9576001600160a01b0381166000908152600f602052604090205460ff166130b95760405163efc6ae3360e01b815260040160405180910390fd5b60015460405163fe575a8760e01b81526001600160a01b0380861660048301528592169063fe575a8790602401602060405180830381865afa158015613103573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906131279190615da1565b156131455760405163711b123360e01b815260040160405180910390fd5b6001600160a01b0386166000908152601160209081526040808320600a845290915290205460ff161561318b576040516349fbea8b60e01b815260040160405180910390fd5b6001600160a01b03861660009081526006602052604090205460ff166131c45760405163107732bd60e21b815260040160405180910390fd5b6001600160a01b03851660009081526006602052604090205460ff166131fd5760405163107732bd60e21b815260040160405180910390fd5b6040516395dd919360e01b81526001600160a01b038581166004830152600091908816906395dd919390602401602060405180830381865afa158015613247573d6000803e3d6000fd5b505050506040513d601f19601f8201168201806040525081019061326b9190615d88565b90506132768761512d565b156132a1578381101561329c57604051634d6cf7c760e11b815260040160405180910390fd5b6116e7565b60006132b1866000806000614ac4565b915050600081116132d5576040516384c1d20160e01b815260040160405180910390fd5b60006132f160405180602001604052806003548152508461476a565b905080861115613314576040516324517cf760e21b815260040160405180910390fd5b505050505050505050565b6060602b805480602002602001604051908101604052809291908181526020016000905b828210156119f557838290600052602060002001805461336290615dbe565b80601f016020809104026020016040519081016040528092919081815260200182805461338e90615dbe565b80156133db5780601f106133b0576101008083540402835291602001916133db565b820191906000526020600020905b8154815290600101906020018083116133be57829003601f168201915b505050505081526020019060010190613343565b601a5460009060ff16156134075750601a5460ff1690565b604051630667f9d760e41b8152737109709ecfa91a80626ff3989d68f67f5b1dd12d600482018190526519985a5b195960d21b602483015260009163667f9d7090604401602060405180830381865afa158015613468573d6000803e3d6000fd5b505050506040513d601f19601f8201168201806040525081019061348c9190615d88565b1415905090565b61349b61478a565b600e5460408051918252602082018390527f2f88a7c1e5b2f610ab1117e131a892f8047394a1040484d1d6e82b4b945d458f910160405180910390a1600e55565b6134e461478a565b6001600160a01b0382166000908152600660205260409020805460ff1661351e5760405163107732bd60e21b815260040160405180910390fd5b60408051602080820183528482528251908101909252670c7d713b49da000082529061354c81835190511090565b1561356a57604051630264240760e11b815260040160405180910390fd5b83158015906135e4575060025460405163fc57d4df60e01b81526001600160a01b0387811660048301529091169063fc57d4df90602401602060405180830381865afa1580156135be573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906135e29190615d88565b155b1561360257604051636eda0f7b60e01b815260040160405180910390fd5b600183015460408051918252602082018690526001600160a01b038716917f70483e6592cd5182d45ac970e05bc62cdcc90e9d8ef2c2dbe686cf383bcd7fc5910160405180910390a250506001015550565b601054819060ff161561369a576001600160a01b0381166000908152600f602052604090205460ff1661369a5760405163efc6ae3360e01b815260040160405180910390fd5b60015460405163fe575a8760e01b81526001600160a01b0380851660048301528492169063fe575a8790602401602060405180830381865afa1580156136e4573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906137089190615da1565b156137265760405163711b123360e01b815260040160405180910390fd5b6001600160a01b03841660009081526011602090815260408083206006845290915290205460ff161561376c576040516349fbea8b60e01b815260040160405180910390fd5b6001600160a01b03841660009081526006602052604090205460ff166137a55760405163107732bd60e21b815260040160405180910390fd5b6137ae846148a2565b6118b684846148fe565b601054339060ff16156137fe576001600160a01b0381166000908152600f602052604090205460ff166137fe5760405163efc6ae3360e01b815260040160405180910390fd5b8160005b8181101561128a57600085858381811061381e5761381e615c9b565b90506020020160208101906138339190615498565b905061383f8133614f5d565b50600101613802565b601054819060ff161561388e576001600160a01b0381166000908152600f602052604090205460ff1661388e5760405163efc6ae3360e01b815260040160405180910390fd5b6001600160a01b03831660009081526011602090815260408083206008845290915290205460ff16156138d4576040516349fbea8b60e01b815260040160405180910390fd5b6001600160a01b03831660009081526006602052604090205460ff1661390d5760405163107732bd60e21b815260040160405180910390fd5b61391683615050565b6117538383615082565b60025460405163fc57d4df60e01b81526001600160a01b038581166004830152600092839291169063fc57d4df90602401602060405180830381865afa15801561396e573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906139929190615d88565b60025460405163fc57d4df60e01b81526001600160a01b0387811660048301529293506000929091169063fc57d4df90602401602060405180830381865afa1580156139e2573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190613a069190615d88565b9050600082118015613a185750600081115b613a3557604051637107178f60e01b815260040160405180910390fd5b6000856001600160a01b031663182df0f56040518163ffffffff1660e01b8152600401602060405180830381865afa158015613a75573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190613a999190615d88565b9050613ab16040518060200160405280600081525090565b6040805160208082018352600080835283518083018552818152845180840186526001600160a01b038d16835260048452918590205482528451928301909452878252919291613b00916151f3565b9250613b286040518060200160405280878152506040518060200160405280878152506151f3565b9150613b34838361523b565b9050613b40818961476a565b9a9950505050505050505050565b613b5661478a565b6001600160a01b03811660009081526006602052604090205460ff1615613b8f57604051625297eb60e11b815260040160405180910390fd5b6001600160a01b03811660009081526006602052604081208054600160ff19918216811783556003830180549092169091558101829055905b600754811015613c2957826001600160a01b031660078281548110613bef57613bef615c9b565b6000918252602090912001546001600160a01b031603613c2157604051625297eb60e11b815260040160405180910390fd5b600101613bc8565b50600780546001810182556000919091527fa66cc928b5edb82af9bd49922954155ab7b0942694bea4ce44661d9a8736c6880180546001600160a01b0319166001600160a01b0384169081179091556040519081527fcf583bb0c569eb967f806b11601c4cb93c10310485c67add5f8362c2f212321f9060200160405180910390a15050565b613cb761478a565b6010805460ff191660011790556040517fe5e5846f783279948f6ec5faad38318cde86fe5be7ea845ede56d62f16c3743490600090a1565b613cf7612bce565b6001600160a01b0316336001600160a01b03161480613df5575060005460408051639943ad6760e01b815290516001600160a01b03909216916338dd8c2c9133918491639943ad679160048083019260209291908290030181865afa158015613d64573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190613d889190615d88565b6040516001600160e01b031960e085901b1681526001600160a01b0390921660048301526024820152604401602060405180830381865afa158015613dd1573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190613df59190615da1565b613e1257604051636fd743bb60e11b815260040160405180910390fd5b82818115801590613e2257508082145b613e3f5760405163e5d0f96f60e01b815260040160405180910390fd5b60005b828110156116e757848482818110613e5c57613e5c615c9b565b9050602002013560096000898985818110613e7957613e79615c9b565b9050602002016020810190613e8e9190615498565b6001600160a01b03168152602081019190915260400160002055868682818110613eba57613eba615c9b565b9050602002016020810190613ecf9190615498565b6001600160a01b03167f9e0ad9cee10bdf36b7fbd38910c0bdff0f275ace679b45b922381c2723d676f8868684818110613f0b57613f0b615c9b565b90506020020135604051613f2191815260200190565b60405180910390a2600101613e42565b613f3961478a565b6010805460ff191690556040517f212c6e1d3045c9581ef0adf2504dbb1d137f52f38162ccf77a16c69d14eba5c390600090a1565b60008060005b60075481101561404757600060078281548110613f9357613f93615c9b565b6000918252602090912001546001600160a01b03169050613fb38161512d565b15613fbe5750613f74565b6000816001600160a01b031663c70920bc6040518163ffffffff1660e01b8152600401602060405180830381865afa158015613ffe573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906140229190615d88565b905061402e8183614e30565b6140389085615f73565b93508260010192505050613f74565b50919050565b6005602052816000526040600020818154811061406957600080fd5b6000918252602090912001546001600160a01b03169150829050565b606060278054806020026020016040519081016040528092919081815260200182805480156118a0576020028201919060005260206000209081546001600160a01b03168152600190910190602001808311611882575050505050905090565b60408051600180825281830190925260009160208083019080368337019050509050818160008151811061411b5761411b615c9b565b60200260200101906001600160a01b031690816001600160a01b03168152505061128e81600780548060200260200160405190810160405280929190818152602001828054801561419557602002820191906000526020600020905b81546001600160a01b03168152600190910190602001808311614177575b5050505050600180614962565b6001600160a01b0381166000908152600660209081526040808320338452600281019092529091205460ff166141d6575050565b6040516361bfb47160e11b815233600482015260009081906001600160a01b0385169063c37f68e290602401606060405180830381865afa15801561421f573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906142439190615fa8565b50915091508060001461426957604051630c75ffbd60e41b815260040160405180910390fd5b6142748433846147bc565b3360009081526002840160209081526040808320805460ff1916905560058252808320805482518185028101850190935280835291929091908301828280156142e657602002820191906000526020600020905b81546001600160a01b031681526001909101906020018083116142c8575b5050835193945083925060009150505b8281101561434057876001600160a01b031684828151811061431a5761431a615c9b565b60200260200101516001600160a01b03160361433857809150614340565b6001016142f6565b5081811061436157604051632e31529960e21b815260040160405180910390fd5b3360009081526005602052604090208054819061438090600190615e39565b8154811061439057614390615c9b565b9060005260206000200160009054906101000a90046001600160a01b03168183815481106143c0576143c0615c9b565b9060005260206000200160006101000a8154816001600160a01b0302191690836001600160a01b03160217905550808054806143fe576143fe615fd6565b600082815260208120820160001990810180546001600160a01b031916905590910190915560405133916001600160a01b038b16917fe699a64c18b07ac5b7301aa273f36a2287239eb9501d81950672794afba29a0d9190a35050505050505050565b61446961478a565b6001600160a01b03811661449857604051631e4fbdf760e01b8152600060048201526024015b60405180910390fd5b6129d8816150bc565b6144a961478a565b6001600160a01b0381166144d05760405163e5d0f96f60e01b815260040160405180910390fd5b600080546040516001600160a01b03808516939216917f9859cd0a756b5f08366068b791448fb837581d3b8afc097914d88edbc7bff2a391a3600080546001600160a01b0319166001600160a01b0392909216919091179055565b7ff0c57e16840df040f15088dc2f81fe391c3923bec73e23a9662efc9c229c6a008054600160401b810460ff16159067ffffffffffffffff166000811580156145715750825b905060008267ffffffffffffffff16600114801561458e5750303b155b90508115801561459c575080155b156145ba5760405163f92ee8a960e01b815260040160405180910390fd5b845467ffffffffffffffff1916600117855583156145e457845460ff60401b1916600160401b1785555b6001600160a01b03891661460b576040516377cdc2b960e11b815260040160405180910390fd5b6001600160a01b0388166146325760405163a00108e360e01b815260040160405180910390fd5b6001600160a01b0387166146595760405163044e4f4b60e51b815260040160405180910390fd5b6001600160a01b0386166146805760405163e5d0f96f60e01b815260040160405180910390fd5b61468986615275565b600080546001600160a01b03808c166001600160a01b0319928316178355600180548c8316908416179055600a8054918b1691909216179055610e10600e5542600d55600b55831561331457845460ff60401b19168555604051600181527fc7f505b2f371ae2175ee4913f4499e1f2633a7b5936321eed1cdaeb6115181d29060200160405180910390a1505050505050505050565b61472761478a565b600b54604080519182526020820183905233917f2800dd60f12c0bee63cd1a581080d66f36f2d862ee33e1aaa473fc310df44ca6910160405180910390a2600b55565b6000806147778484615286565b9050614782816152ae565b949350505050565b33614793612bce565b6001600160a01b031614612a4a5760405163118cdaa760e01b815233600482015260240161448f565b6001600160a01b03831660009081526011602090815260408083206009845290915290205460ff1615614802576040516349fbea8b60e01b815260040160405180910390fd5b6001600160a01b03831660009081526006602052604090205460ff1661483b5760405163107732bd60e21b815260040160405180910390fd5b6001600160a01b038084166000908152600660209081526040808320938616835260029093019052205460ff1661487157505050565b60006148808385846000614ac4565b91505080156118b6576040516384c1d20160e01b815260040160405180910390fd5b600a5460405163743597df60e11b81526001600160a01b0383811660048301529091169063e86b2fbe906024015b600060405180830381600087803b1580156148ea57600080fd5b505af115801561128a573d6000803e3d6000fd5b600a54604051631169274f60e21b81526001600160a01b0384811660048301528381166024830152909116906345a49d3c906044015b600060405180830381600087803b15801561494e57600080fd5b505af115801561149d573d6000803e3d6000fd5b825160005b81811015614a6557600085828151811061498357614983615c9b565b6020908102919091018101516001600160a01b0381166000908152600690925260409091205490915060ff166149cc5760405163107732bd60e21b815260040160405180910390fd5b8415614a14576149db81615050565b60005b8751811015614a1257614a0a828983815181106149fd576149fd615c9b565b6020026020010151615082565b6001016149de565b505b8315614a5c57614a23816148a2565b60005b8751811015614a5a57614a5282898381518110614a4557614a45615c9b565b60200260200101516148fe565b600101614a26565b505b50600101614967565b50600a5460405163318d9e5d60e01b81526001600160a01b039091169063318d9e5d90614a96908890600401615763565b600060405180830381600087803b158015614ab057600080fd5b505af1158015613314573d6000803e3d6000fd5b600080614acf6153d1565b6001600160a01b038716600090815260056020526040812054905b81811015614d3b576001600160a01b0389166000908152600560205260408120805483908110614b1c57614b1c615c9b565b6000918252602090912001546040516361bfb47160e11b81526001600160a01b038c811660048301529091169150819063c37f68e290602401606060405180830381865afa158015614b72573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190614b969190615fa8565b6080870190815260608701919091526040808701929092528151602081810184526001600160a01b0385811660008181526006845286902060010154845260c08a0193909352845191820185529251815260e0880152600254925163fc57d4df60e01b8152600481019190915291169063fc57d4df90602401602060405180830381865afa158015614c2c573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190614c509190615d88565b60a08501819052600003614c77576040516368959e3d60e11b815260040160405180910390fd5b604080516020810190915260a0850151815261010085015260c084015160e0850151614cb191614ca6916151f3565b8561010001516151f3565b610120850181905260408501518551614ccb9291906152c6565b845261010084015160608501516020860151614ce89291906152c6565b60208501526001600160a01b03808a1690821603614d3257614d148461012001518986602001516152c6565b60208501819052610100850151614d2c9189906152c6565b60208501525b50600101614aea565b50602082015182511115614d675760208201518251614d5a9190615e39565b6000935093505050611d4a565b81516020830151600091614d7a91615e39565b935093505050611d4a565b61128e8282604051602401614d9b929190615fec565b60408051601f198184030181529190526020810180516001600160e01b0316632d839cb360e21b1790526152e7565b60405163260a5b1560e21b81526004810183905260248101829052737109709ecfa91a80626ff3989d68f67f5b1dd12d906398296c549060440160006040518083038186803b158015614e1c57600080fd5b505afa15801561149d573d6000803e3d6000fd5b60025460405163fc57d4df60e01b81526001600160a01b038381166004830152600092839291169063fc57d4df90602401602060405180830381865afa158015614e7e573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190614ea29190615d88565b905080600003614ec5576040516368959e3d60e11b815260040160405180910390fd5b60408051602081019091528181526402540be400614ee386836152f0565b614eed9190615f86565b95945050505050565b6040516388b44c8560e01b8152737109709ecfa91a80626ff3989d68f67f5b1dd12d906388b44c8590614f319086908690869060040161600e565b60006040518083038186803b158015614f4957600080fd5b505afa1580156116e7573d6000803e3d6000fd5b6001600160a01b0382166000908152600660205260409020805460ff16614f975760405163107732bd60e21b815260040160405180910390fd5b6001600160a01b038216600090815260028201602052604090205460ff16611753576001600160a01b0380831660008181526002840160209081526040808320805460ff191660019081179091556005835281842080549182018155845291832090910180549488166001600160a01b031990951685179055519192917f3ab23ab0d51cccc0c3085aec51f99228625aa1a922b3a8ca89a26b0f2027a1a59190a3505050565b60006150498284615f73565b9392505050565b600a54604051635ce65fe960e01b81526001600160a01b03838116600483015290911690635ce65fe9906024016148d0565b600a546040516375c7940360e11b81526001600160a01b03848116600483015283811660248301529091169063eb8f280690604401614934565b7f9016d09d72d40fdae2fd8ceac6b6234c7706214fd39c1cd1e609a0528c19930080546001600160a01b031981166001600160a01b03848116918217845560405192169182907f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e090600090a3505050565b6001600160a01b03811660009081526006602052604081206001015415801561517a57506001600160a01b03821660009081526011602090815260408083206007845290915290205460ff165b801561115d5750816001600160a01b031663173b99046040518163ffffffff1660e01b8152600401602060405180830381865afa1580156151bf573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906151e39190615d88565b670de0b6b3a76400001492915050565b6040805160208101909152600081526040518060200160405280670de0b6b3a764000061522886600001518660000151615313565b6152329190615f86565b90529392505050565b604080516020810190915260008152604051806020016040528061523261526e8660000151670de0b6b3a7640000615313565b855161531f565b61527d61532b565b6129d881615374565b6040805160208101909152600081526040518060200160405280615232856000015185615313565b805160009061115d90670de0b6b3a764000090615f86565b6000806152d38585615286565b9050614eed6152e1826152ae565b8461503d565b6129d88161537c565b6000670de0b6b3a7640000615309848460000151615313565b6150499190615f86565b60006150498284615f3f565b60006150498284615f86565b7ff0c57e16840df040f15088dc2f81fe391c3923bec73e23a9662efc9c229c6a0054600160401b900460ff16612a4a57604051631afcd79f60e31b815260040160405180910390fd5b61446961532b565b60006a636f6e736f6c652e6c6f679050600080835160208501845afa505050565b6104398061602e83390190565b61011d8061646783390190565b6119428061658483390190565b61012080617ec683390190565b60405180610140016040528060008152602001600081526020016000815260200160008152602001600081526020016000815260200161541d6040518060200160405280600081525090565b81526020016154386040518060200160405280600081525090565b81526020016154536040518060200160405280600081525090565b815260200161546e6040518060200160405280600081525090565b905290565b6001600160a01b03811681146129d857600080fd5b803561549381615473565b919050565b6000602082840312156154aa57600080fd5b813561504981615473565b8035600c811061549357600080fd5b600080604083850312156154d757600080fd5b82356154e281615473565b91506154f0602084016154b5565b90509250929050565b60006020828403121561550b57600080fd5b5035919050565b6000806000806080858703121561552857600080fd5b843561553381615473565b9350602085013561554381615473565b9250604085013561555381615473565b9396929550929360600135925050565b60008083601f84011261557557600080fd5b50813567ffffffffffffffff81111561558d57600080fd5b6020830191508360208260051b85010111156155a857600080fd5b9250929050565b600080600080604085870312156155c557600080fd5b843567ffffffffffffffff8111156155dc57600080fd5b6155e887828801615563565b909550935050602085013567ffffffffffffffff81111561560857600080fd5b61561487828801615563565b95989497509550505050565b634e487b7160e01b600052604160045260246000fd5b600082601f83011261564757600080fd5b813567ffffffffffffffff81111561566157615661615620565b8060051b604051601f19603f830116810181811067ffffffffffffffff8211171561568e5761568e615620565b6040529182526020818501810192908101868411156156ac57600080fd5b6020860192505b83831015612d41576156c483615488565b8152602092830192016156b3565b600080604083850312156156e557600080fd5b82356156f081615473565b9150602083013567ffffffffffffffff81111561570c57600080fd5b61571885828601615636565b9150509250929050565b60008060006060848603121561573757600080fd5b833561574281615473565b9250602084013561575281615473565b929592945050506040919091013590565b602080825282518282018190526000918401906040840190835b818110156157a45783516001600160a01b031683526020938401939092019160010161577d565b509095945050505050565b80151581146129d857600080fd5b600080600080608085870312156157d357600080fd5b843567ffffffffffffffff8111156157ea57600080fd5b6157f687828801615636565b945050602085013567ffffffffffffffff81111561581357600080fd5b61581f87828801615636565b9350506040850135615830816157af565b91506060850135615840816157af565b939692955090935050565b6000815180845260005b8181101561587157602081850181015186830182015201615855565b506000602082860101526020601f19601f83011685010191505092915050565b600082825180855260208501945060208160051b8301016020850160005b838110156158e157601f198584030188526158cb83835161584b565b60209889019890935091909101906001016158af565b50909695505050505050565b6000602082016020835280845180835260408501915060408160051b86010192506020860160005b8281101561596657868503603f19018452815180516001600160a01b0316865260209081015160409187018290529061595090870182615891565b9550506020938401939190910190600101615915565b50929695505050505050565b6000806040838503121561598557600080fd5b823561599081615473565b915060208301356159a0816157af565b809150509250929050565b6000806000606084860312156159c057600080fd5b83356159cb81615473565b92506159d9602085016154b5565b915060408401356159e9816157af565b809150509250925092565b60008060008060808587031215615a0a57600080fd5b8435615a1581615473565b93506020850135615a2581615473565b93969395505050506040820135916060013590565b600081518084526020840193506020830160005b82811015615a765781516001600160e01b031916865260209586019590910190600101615a4e565b5093949350505050565b6000602082016020835280845180835260408501915060408160051b86010192506020860160005b8281101561596657603f198786030184528151805160408752615ace604088018261584b565b9050602082015191508681036020880152615ae98183615a3a565b965050506020938401939190910190600101615aa8565b60008060008060808587031215615b1657600080fd5b8435615b2181615473565b93506020850135615b3181615473565b92506040850135615b4181615473565b9150606085013561584081615473565b6020815260006150496020830184615891565b6000602082016020835280845180835260408501915060408160051b86010192506020860160005b8281101561596657868503603f19018452815180516001600160a01b03168652602090810151604091870182905290615bc790870182615a3a565b9550506020938401939190910190600101615b8c565b60008060408385031215615bf057600080fd5b50508035926020909101359150565b60008060408385031215615c1257600080fd5b8235615c1d81615473565b915060208301356159a081615473565b60008060408385031215615c4057600080fd5b8235615c4b81615473565b946020939093013593505050565b60008060208385031215615c6c57600080fd5b823567ffffffffffffffff811115615c8357600080fd5b615c8f85828601615563565b90969095509350505050565b634e487b7160e01b600052603260045260246000fd5b608081526000615cc46080830187615891565b828103602084015280865180835260208301915060208160051b8401016020890160005b83811015615d4757858303601f19018552815180516001600160a01b03168452602080820151606091860182905290615d239086018261584b565b60409283015195909201949094526020958601959093509190910190600101615ce8565b50506001600160a01b03881660408701529350615d6392505050565b82606083015295945050505050565b634e487b7160e01b600052602160045260246000fd5b600060208284031215615d9a57600080fd5b5051919050565b600060208284031215615db357600080fd5b8151615049816157af565b600181811c90821680615dd257607f821691505b60208210810361404757634e487b7160e01b600052602260045260246000fd5b60408101600c8410615e1457634e487b7160e01b600052602160045260246000fd5b92815290151560209091015290565b634e487b7160e01b600052601160045260246000fd5b8181038181111561115d5761115d615e23565b6001815b6001841115615e8757808504811115615e6b57615e6b615e23565b6001841615615e7957908102905b60019390931c928002615e50565b935093915050565b600082615e9e5750600161115d565b81615eab5750600061115d565b8160018114615ec15760028114615ecb57615ee7565b600191505061115d565b60ff841115615edc57615edc615e23565b50506001821b61115d565b5060208310610133831016604e8410600b8410161715615f0a575081810a61115d565b615f176000198484615e4c565b8060001904821115615f2b57615f2b615e23565b029392505050565b60006150498383615e8f565b808202811582820484141761115d5761115d615e23565b600060208284031215615f6857600080fd5b815161504981615473565b8082018082111561115d5761115d615e23565b600082615fa357634e487b7160e01b600052601260045260246000fd5b500490565b600080600060608486031215615fbd57600080fd5b5050815160208301516040909301519094929350919050565b634e487b7160e01b600052603160045260246000fd5b604081526000615fff604083018561584b565b90508260208301529392505050565b838152826020820152606060408201526000614eed606083018461584b56fe608060405234801561001057600080fd5b5060405161043938038061043983398101604081905261002f9161005b565b600061003b83826101b4565b5060015550610272565b634e487b7160e01b600052604160045260246000fd5b6000806040838503121561006e57600080fd5b82516001600160401b0381111561008457600080fd5b8301601f8101851361009557600080fd5b80516001600160401b038111156100ae576100ae610045565b604051601f8201601f19908116603f011681016001600160401b03811182821017156100dc576100dc610045565b6040528181528282016020018710156100f457600080fd5b60005b82811015610113576020818501810151838301820152016100f7565b50600060209282018301529401519395939450505050565b600181811c9082168061013f57607f821691505b60208210810361015f57634e487b7160e01b600052602260045260246000fd5b50919050565b601f8211156101af57806000526020600020601f840160051c8101602085101561018c5750805b601f840160051c820191505b818110156101ac5760008155600101610198565b50505b505050565b81516001600160401b038111156101cd576101cd610045565b6101e1816101db845461012b565b84610165565b6020601f82116001811461021557600083156101fd5750848201515b600019600385901b1c1916600184901b1784556101ac565b600084815260208120601f198516915b828110156102455787850151825560209485019460019092019101610225565b50848210156102635786840151600019600387901b60f8161c191681555b50505050600190811b01905550565b6101b8806102816000396000f3fe608060405234801561001057600080fd5b50600436106100365760003560e01c8063313ce5671461003b57806395d89b4114610057575b600080fd5b61004460015481565b6040519081526020015b60405180910390f35b61005f61006c565b60405161004e91906100fa565b6000805461007990610148565b80601f01602080910402602001604051908101604052809291908181526020018280546100a590610148565b80156100f25780601f106100c7576101008083540402835291602001916100f2565b820191906000526020600020905b8154815290600101906020018083116100d557829003601f168201915b505050505081565b602081526000825180602084015260005b81811015610128576020818601810151604086840101520161010b565b506000604082850101526040601f19601f83011684010191505092915050565b600181811c9082168061015c57607f821691505b60208210810361017c57634e487b7160e01b600052602260045260246000fd5b5091905056fea26469706673582212202693a32444b2a480754439eabaf276328465443b559f8c98acc61ead4a7318b364736f6c634300081c00336080604052348015600f57600080fd5b5060405161011d38038061011d833981016040819052602c916050565b600080546001600160a01b0319166001600160a01b0392909216919091179055607e565b600060208284031215606157600080fd5b81516001600160a01b0381168114607757600080fd5b9392505050565b60918061008c6000396000f3fe6080604052348015600f57600080fd5b506004361060285760003560e01c80636f307dc314602d575b600080fd5b600054603f906001600160a01b031681565b6040516001600160a01b03909116815260200160405180910390f3fea2646970667358221220765bb4f7437af37db8b3a1441e51df2c6296b0af2ce406cf245a4cad531fdcab64736f6c634300081c003360c060405234801561001057600080fd5b5060405161194238038061194283398101604081905261002f91610312565b6001600160a01b03821660a05260005b84518110156100e05783818151811061005a5761005a61040c565b602002602001015160008683815181106100765761007661040c565b602002602001015160405161008b9190610422565b90815260405160209181900382019020825181546001600160a01b0319166001600160a01b039091161781559082015160018201906100ca90826104c7565b506040919091015160029091015560010161003f565b5060805250610585915050565b634e487b7160e01b600052604160045260246000fd5b604051606081016001600160401b0381118282101715610125576101256100ed565b60405290565b604051601f8201601f191681016001600160401b0381118282101715610153576101536100ed565b604052919050565b60006001600160401b03821115610174576101746100ed565b5060051b60200190565b60005b83811015610199578181015183820152602001610181565b50506000910152565b600082601f8301126101b357600080fd5b81516001600160401b038111156101cc576101cc6100ed565b6101df601f8201601f191660200161012b565b8181528460208386010111156101f457600080fd5b61020582602083016020870161017e565b949350505050565b80516001600160a01b038116811461022457600080fd5b919050565b600082601f83011261023a57600080fd5b815161024d6102488261015b565b61012b565b8082825260208201915060208360051b86010192508583111561026f57600080fd5b602085015b838110156103085780516001600160401b0381111561029257600080fd5b86016060818903601f190112156102a857600080fd5b6102b0610103565b6102bc6020830161020d565b815260408201516001600160401b038111156102d757600080fd5b6102e68a6020838601016101a2565b6020838101919091526060939093015160408301525084529283019201610274565b5095945050505050565b6000806000806080858703121561032857600080fd5b84516001600160401b0381111561033e57600080fd5b8501601f8101871361034f57600080fd5b805161035d6102488261015b565b8082825260208201915060208360051b85010192508983111561037f57600080fd5b602084015b838110156103c05780516001600160401b038111156103a257600080fd5b6103b18c6020838901016101a2565b84525060209283019201610384565b506020890151909750925050506001600160401b038111156103e157600080fd5b6103ed87828801610229565b9350506103fc6040860161020d565b6060959095015193969295505050565b634e487b7160e01b600052603260045260246000fd5b6000825161043481846020870161017e565b9190910192915050565b600181811c9082168061045257607f821691505b60208210810361047257634e487b7160e01b600052602260045260246000fd5b50919050565b601f8211156104c257806000526020600020601f840160051c8101602085101561049f5750805b601f840160051c820191505b818110156104bf57600081556001016104ab565b50505b505050565b81516001600160401b038111156104e0576104e06100ed565b6104f4816104ee845461043e565b84610478565b6020601f82116001811461052857600083156105105750848201515b600019600385901b1c1916600184901b1784556104bf565b600084815260208120601f198516915b828110156105585787850151825560209485019460019092019101610538565b50848210156105765786840151600019600387901b60f8161c191681555b50505050600190811b01905550565b60805160a0516113776105cb6000396000818160f7015281816102de0152818161030e015281816104da015261050a01526000818160920152610ba301526113776000f3fe608060405234801561001057600080fd5b50600436106100885760003560e01c80635ef7fbad1161005b5780635ef7fbad14610144578063909d048114610166578063c86e86851461017b578063fc57d4df1461018e57600080fd5b806324c9477a1461008d5780632ab77c57146100c7578063392f5f64146100f257806341976e0914610131575b600080fd5b6100b47f000000000000000000000000000000000000000000000000000000000000000081565b6040519081526020015b60405180910390f35b6100b46100d5366004610cbb565b805160208183018101805160018252928201919093012091525481565b6101197f000000000000000000000000000000000000000000000000000000000000000081565b6040516001600160a01b0390911681526020016100be565b6100b461013f366004610d10565b6101a1565b610157610152366004610cbb565b61021c565b6040516100be93929190610d7d565b610179610174366004610db1565b6102dc565b005b610179610189366004610e6f565b6104d8565b6100b461019c366004610d10565b610667565b600080826001600160a01b03166395d89b416040518163ffffffff1660e01b8152600401600060405180830381865afa1580156101e2573d6000803e3d6000fd5b505050506040513d6000823e601f3d908101601f1916820160405261020a9190810190610eb4565b905061021581610847565b9392505050565b8051602081830181018051600082529282019190930120915280546001820180546001600160a01b03909216929161025390610f22565b80601f016020809104026020016040519081016040528092919081815260200182805461027f90610f22565b80156102cc5780601f106102a1576101008083540402835291602001916102cc565b820191906000526020600020905b8154815290600101906020018083116102af57829003601f168201915b5050505050908060020154905083565b7f00000000000000000000000000000000000000000000000000000000000000006001600160a01b03166338dd8c2c337f00000000000000000000000000000000000000000000000000000000000000006001600160a01b0316637f3c8ff56040518163ffffffff1660e01b8152600401602060405180830381865afa15801561036a573d6000803e3d6000fd5b505050506040513d601f19601f8201168201806040525081019061038e9190610f5c565b6040516001600160e01b031960e085901b1681526001600160a01b0390921660048301526024820152604401602060405180830381865afa1580156103d7573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906103fb9190610f75565b610418576040516307eb074d60e01b815260040160405180910390fd5b80516001600160a01b031661043f576040516204ea5f60e61b815260040160405180910390fd5b806000836040516104509190610f97565b90815260405160209181900382019020825181546001600160a01b0319166001600160a01b0390911617815590820151600182019061048f9082611002565b50604091820151600290910155517fcd168315f4476cc88255c37eac3c06949db1e48f500390e24f9b988fc2a95497906104cc90849084906110c1565b60405180910390a15050565b7f00000000000000000000000000000000000000000000000000000000000000006001600160a01b03166338dd8c2c337f00000000000000000000000000000000000000000000000000000000000000006001600160a01b0316637f3c8ff56040518163ffffffff1660e01b8152600401602060405180830381865afa158015610566573d6000803e3d6000fd5b505050506040513d601f19601f8201168201806040525081019061058a9190610f5c565b6040516001600160e01b031960e085901b1681526001600160a01b0390921660048301526024820152604401602060405180830381865afa1580156105d3573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906105f79190610f75565b610614576040516307eb074d60e01b815260040160405180910390fd5b806001836040516106259190610f97565b9081526020016040518091039020819055507e6ebc2a07d9cbde1f5c02f38e4224748a98286f396f9329f8c9bf03b4af1ed582826040516104cc929190611119565b600080826001600160a01b0316636f307dc36040518163ffffffff1660e01b8152600401602060405180830381865afa1580156106a8573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906106cc919061113b565b6001600160a01b03166395d89b416040518163ffffffff1660e01b8152600401600060405180830381865afa158015610709573d6000803e3d6000fd5b505050506040513d6000823e601f3d908101601f191682016040526107319190810190610eb4565b9050600080826040516107449190610f97565b9081526040805191829003602090810183206060840190925281546001600160a01b031683526001820180549184019161077d90610f22565b80601f01602080910402602001604051908101604052809291908181526020018280546107a990610f22565b80156107f65780601f106107cb576101008083540402835291602001916107f6565b820191906000526020600020905b8154815290600101906020018083116107d957829003601f168201915b505050505081526020016002820154815250509050600061081683610847565b905081604001516012610829919061116e565b61083490600a611268565b61083e9082611274565b95945050505050565b60008060008360405161085a9190610f97565b9081526040805191829003602090810183206060840190925281546001600160a01b031683526001820180549184019161089390610f22565b80601f01602080910402602001604051908101604052809291908181526020018280546108bf90610f22565b801561090c5780601f106108e15761010080835404028352916020019161090c565b820191906000526020600020905b8154815290600101906020018083116108ef57829003601f168201915b50505050508152602001600282015481525050905060008061092e85846109e4565b9092509050600061094082601261116e565b61094b90600a611268565b6109559084611274565b604051621554d160ea1b60208201529091506023016040516020818303038152906040528051906020012084602001516040516020016109959190610f97565b604051602081830303815290604052805190602001201461083e57670de0b6b3a76400006109c68560200151610847565b6109d09083611274565b6109da919061128b565b9695505050505050565b805160009081906001600160a01b0316610a385760405162461bcd60e51b81526020600482015260116024820152701b5a5cdcda5b99c81c1c9a58d951995959607a1b604482015260640160405180910390fd5b600083600001519050600080826001600160a01b031663feaf968c6040518163ffffffff1660e01b815260040160a060405180830381865afa158015610a82573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190610aa691906112cc565b5093505092505060008213610ace57604051630cdda18b60e31b815260040160405180910390fd5b610ad787610b75565b610ae1824261116e565b10610aff576040516353a4bc9f60e11b815260040160405180910390fd5b6000836001600160a01b031663313ce5676040518163ffffffff1660e01b8152600401602060405180830381865afa158015610b3f573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190610b63919061131e565b929550505060ff169150509250929050565b600080600183604051610b889190610f97565b908152602001604051809103902054905060008111610bc7577f0000000000000000000000000000000000000000000000000000000000000000610215565b92915050565b634e487b7160e01b600052604160045260246000fd5b6040516060810167ffffffffffffffff81118282101715610c0657610c06610bcd565b60405290565b604051601f8201601f1916810167ffffffffffffffff81118282101715610c3557610c35610bcd565b604052919050565b600067ffffffffffffffff821115610c5757610c57610bcd565b50601f01601f191660200190565b600082601f830112610c7657600080fd5b8135610c89610c8482610c3d565b610c0c565b818152846020838601011115610c9e57600080fd5b816020850160208301376000918101602001919091529392505050565b600060208284031215610ccd57600080fd5b813567ffffffffffffffff811115610ce457600080fd5b610cf084828501610c65565b949350505050565b6001600160a01b0381168114610d0d57600080fd5b50565b600060208284031215610d2257600080fd5b813561021581610cf8565b60005b83811015610d48578181015183820152602001610d30565b50506000910152565b60008151808452610d69816020860160208601610d2d565b601f01601f19169290920160200192915050565b6001600160a01b0384168152606060208201819052600090610da190830185610d51565b9050826040830152949350505050565b60008060408385031215610dc457600080fd5b823567ffffffffffffffff811115610ddb57600080fd5b610de785828601610c65565b925050602083013567ffffffffffffffff811115610e0457600080fd5b830160608186031215610e1657600080fd5b610e1e610be3565b8135610e2981610cf8565b8152602082013567ffffffffffffffff811115610e4557600080fd5b610e5187828501610c65565b60208301525060409182013591810191909152919491935090915050565b60008060408385031215610e8257600080fd5b823567ffffffffffffffff811115610e9957600080fd5b610ea585828601610c65565b95602094909401359450505050565b600060208284031215610ec657600080fd5b815167ffffffffffffffff811115610edd57600080fd5b8201601f81018413610eee57600080fd5b8051610efc610c8482610c3d565b818152856020838501011115610f1157600080fd5b61083e826020830160208601610d2d565b600181811c90821680610f3657607f821691505b602082108103610f5657634e487b7160e01b600052602260045260246000fd5b50919050565b600060208284031215610f6e57600080fd5b5051919050565b600060208284031215610f8757600080fd5b8151801515811461021557600080fd5b60008251610fa9818460208701610d2d565b9190910192915050565b601f821115610ffd57806000526020600020601f840160051c81016020851015610fda5750805b601f840160051c820191505b81811015610ffa5760008155600101610fe6565b50505b505050565b815167ffffffffffffffff81111561101c5761101c610bcd565b6110308161102a8454610f22565b84610fb3565b6020601f821160018114611064576000831561104c5750848201515b600019600385901b1c1916600184901b178455610ffa565b600084815260208120601f198516915b828110156110945787850151825560209485019460019092019101611074565b50848210156110b25786840151600019600387901b60f8161c191681555b50505050600190811b01905550565b6040815260006110d46040830185610d51565b828103602084015260018060a01b0384511681526020840151606060208301526111016060830182610d51565b90506040850151604083015280925050509392505050565b60408152600061112c6040830185610d51565b90508260208301529392505050565b60006020828403121561114d57600080fd5b815161021581610cf8565b634e487b7160e01b600052601160045260246000fd5b81810381811115610bc757610bc7611158565b6001815b60018411156111bc578085048111156111a0576111a0611158565b60018416156111ae57908102905b60019390931c928002611185565b935093915050565b6000826111d357506001610bc7565b816111e057506000610bc7565b81600181146111f657600281146112005761121c565b6001915050610bc7565b60ff84111561121157611211611158565b50506001821b610bc7565b5060208310610133831016604e8410600b841016171561123f575081810a610bc7565b61124c6000198484611181565b806000190482111561126057611260611158565b029392505050565b600061021583836111c4565b8082028115828204841417610bc757610bc7611158565b6000826112a857634e487b7160e01b600052601260045260246000fd5b500490565b805169ffffffffffffffffffff811681146112c757600080fd5b919050565b600080600080600060a086880312156112e457600080fd5b6112ed866112ad565b60208701516040880151606089015192975090955093509150611312608087016112ad565b90509295509295909350565b60006020828403121561133057600080fd5b815160ff8116811461021557600080fdfea26469706673582212207c51b1792eda1c731bc114d9622c20afbedd6fcd1cd8311d37b00911b5a5d33364736f6c634300081c00336080604052348015600f57600080fd5b50604051610120380380610120833981016040819052602c916039565b600191909155600055605c565b60008060408385031215604b57600080fd5b505080516020909101519092909150565b60b68061006a6000396000f3fe6080604052348015600f57600080fd5b506004361060325760003560e01c8063313ce567146037578063feaf968c146052575b600080fd5b603f60005481565b6040519081526020015b60405180910390f35b60018054604080518381526020810192909252429082018190526060820152608081019190915260a001604956fea2646970667358221220ca99a3f5f27a64810ceb2c0137cc0982f2dfccca26dfccf2a9ea4992f7bb164f64736f6c634300081c0033a26469706673582212200c3026939ce5dc04bffa4a8e262131e15aa53e3816ebee2ef1179bb4d63ff14b64736f6c634300081c0033", "sourceMap": "1429:3960:233:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2357:45:128;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;687:25:242;;;675:2;660:18;2357:45:128;;;;;;;;2612:1858:233;;;:::i;:::-;;12968:197:127;;;;;;:::i;:::-;;:::i;:::-;;;1404:14:242;;1397:22;1379:41;;1367:2;1352:18;12968:197:127;1239:187:242;21936:539:127;;;;;;:::i;:::-;;:::i;1539:29:128:-;;;;;-1:-1:-1;;;;;1539:29:128;;;;;;-1:-1:-1;;;;;1704:32:242;;;1686:51;;1674:2;1659:18;1539:29:128;1540:203:242;4339:394:127;;;;;;:::i;:::-;;:::i;20868:546::-;;;;;;:::i;:::-;;:::i;9828:709::-;;;;;;:::i;:::-;;:::i;19263:215::-;;;;;;:::i;:::-;;:::i;22534:347::-;;;;;;:::i;:::-;;:::i;2907:134:7:-;;;:::i;:::-;;;;;;;:::i;19817:205:127:-;;;;;;:::i;:::-;;:::i;3823:151:7:-;;;:::i;:::-;;;;;;;:::i;1450:37:128:-;;;;;-1:-1:-1;;;;;1450:37:128;;;1706:63;;;;;;:::i;:::-;;;;;;;;;;;;;;13940:126:127;;;;;;:::i;:::-;-1:-1:-1;;;;;14035:15:127;14012:4;14035:15;;;:7;:15;;;;;:24;;;;13940:126;3684:133:7;;;:::i;3385:141::-;;;:::i;12829:88:127:-;12906:4;12829:88;;2913:162;;;;;;:::i;:::-;;:::i;2260:45:128:-;;;;;;:::i;:::-;;;;;;;;;;;;;;11678:540:127;;;;;;:::i;:::-;;:::i;14336:310::-;;;;;;:::i;:::-;;:::i;:::-;;;;11391:25:242;;;11447:2;11432:18;;11425:34;;;;11364:18;14336:310:127;11217:248:242;4476:911:233;;;:::i;1371:27:128:-;;;;;-1:-1:-1;;;;;1371:27:128;;;22940:1400:127;;;;;;:::i;:::-;;:::i;2975:28:128:-;;;;;;;;;8369:128:127;;;:::i;2181:27:128:-;;;;;;:::i;:::-;;:::i;3907:228:127:-;;;;;;:::i;:::-;;:::i;14117:168::-;;;;;;:::i;:::-;;:::i;3193:186:7:-;;;:::i;:::-;;;;;;;:::i;26067:888:127:-;;;;;;:::i;:::-;;:::i;20655:154::-;;;;;;:::i;:::-;;:::i;2623:38:128:-;;;;;;2064:239:233;;;;;;:::i;:::-;;:::i;3155:101:21:-;;;:::i;8599:846:127:-;;;;;;:::i;:::-;;:::i;3047:140:7:-;;;:::i;:::-;;;;;;;:::i;2538:33:128:-;;;;;;2441:144:21;;;:::i;2066:55:128:-;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;14707:14:242;;14700:22;14682:41;;14754:2;14739:18;;14732:34;;;;14809:14;14802:22;14782:18;;;14775:50;14670:2;14655:18;2066:55:128;14492:339:242;3532:146:7;;;:::i;:::-;;;;;;;:::i;2309:297:233:-;;;;;;:::i;:::-;;:::i;13405:153:127:-;;;;;;:::i;:::-;-1:-1:-1;;;;;13509:15:127;;;13486:4;13509:15;;;:7;:15;;;;;;;;:42;;;;;:33;;;;:42;;;;;;13405:153;;;;;13768:121;;;;;;:::i;:::-;;:::i;16684:322::-;;;;;;:::i;:::-;;:::i;6321:424::-;;;;;;:::i;:::-;;:::i;12380:361::-;;;;;;:::i;:::-;;:::i;13216:138::-;;;;;;:::i;:::-;;:::i;2454:32:128:-;;;;;-1:-1:-1;;;;;2454:32:128;;;2754:147:7;;;:::i;13609:108:127:-;;;:::i;24837:1171::-;;;;;;:::i;:::-;;:::i;2459:141:7:-;;;:::i;1243:204:2:-;;;:::i;7768:211:127:-;;;;;;:::i;:::-;;:::i;5008:1109::-;;;;;;:::i;:::-;;:::i;21473:404::-;;;;;;:::i;:::-;;:::i;16285:348::-;;;;;;:::i;:::-;;:::i;24399:379::-;;;;;;:::i;:::-;;:::i;14697:1498::-;;;;;;:::i;:::-;;:::i;6978:667::-;;;;;;:::i;:::-;;:::i;3134:119::-;;;:::i;10919:708::-;;;;;;:::i;:::-;;:::i;3313:122::-;;;:::i;20093:503::-;;;:::i;1871:50:128:-;;;;;;:::i;:::-;;:::i;2713:40::-;;;;;;2606:142:7;;;:::i;18852:199:127:-;;;;;;:::i;:::-;;:::i;1620:34:128:-;;;;;;2832:37;;;;;;17057:1653:127;;;;;;:::i;:::-;;:::i;3405:215:21:-;;;;;;:::i;:::-;;:::i;3556:237:127:-;;;;;;:::i;:::-;;:::i;1631:779::-;;;;;;:::i;:::-;;:::i;8123:187::-;;;;;;:::i;:::-;;:::i;1065:26:14:-;;;;;;;;;2921:47:128;;;;;;:::i;:::-;;;;;;;;;;;;;;;;2612:1858:233;2674:15;;2652:38;;;;;:::i;:::-;17660:2:242;17642:21;;;17699:1;17679:18;;;17672:29;-1:-1:-1;;;17732:2:242;17717:18;;17710:33;17810:4;17795:20;;17788:36;;;;17775:3;17760:19;2652:38:233;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;2646:3:233;:44;;-1:-1:-1;;;;;;2646:44:233;-1:-1:-1;;;;;2646:44:233;;;;;;;;;;2728:11;;2706:34;;;;;:::i;:::-;18065:2:242;18047:21;;;18104:1;18084:18;;;18077:29;-1:-1:-1;;;18137:2:242;18122:18;;18115:33;18215:4;18200:20;;18193:36;;;;18180:3;18165:19;2706:34:233;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;2700:3:233;:40;;-1:-1:-1;;;;;;2700:40:233;-1:-1:-1;;;;;2700:40:233;;;;;;;;;;2780:12;;2757:36;;;;;:::i;:::-;18470:2:242;18452:21;;;18509:1;18489:18;;;18482:29;-1:-1:-1;;;18542:2:242;18527:18;;18520:34;18621:4;18606:20;;18599:36;;;;18586:3;18571:19;2757:36:233;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;2750:4:233;:43;;-1:-1:-1;;;;;;2750:43:233;-1:-1:-1;;;;;2750:43:233;;;;;;;;;;2848:18;;2824:43;;;;;:::i;:::-;18876:2:242;18858:21;;;18915:1;18895:18;;;18888:29;-1:-1:-1;;;18948:2:242;18933:18;;18926:35;19028:4;19013:20;;19006:36;;;;18993:3;18978:19;2824:43:233;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;2803:18:233;:64;;-1:-1:-1;;;;;;2803:64:233;-1:-1:-1;;;;;2803:64:233;;;;;;2909:3;;2885:29;;2909:3;;;2885:29;;;:::i;:::-;-1:-1:-1;;;;;1704:32:242;;;1686:51;;1674:2;1659:18;2885:29:233;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;2878:4:233;:36;;-1:-1:-1;;;;;;2878:36:233;-1:-1:-1;;;;;2878:36:233;;;;;;2955:3;;2931:29;;2955:3;;;2931:29;;;:::i;:::-;-1:-1:-1;;;;;1704:32:242;;;1686:51;;1674:2;1659:18;2931:29:233;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;2924:4:233;:36;;-1:-1:-1;;;;;;2924:36:233;-1:-1:-1;;;;;2924:36:233;;;;;;3002:4;;2978:30;;3002:4;;;2978:30;;;:::i;:::-;-1:-1:-1;;;;;1704:32:242;;;1686:51;;1674:2;1659:18;2978:30:233;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;2970:5:233;:38;;-1:-1:-1;;;;;;2970:38:233;-1:-1:-1;;;;;2970:38:233;;;;;;3064:18;;3040:44;;3064:18;;;3040:44;;;:::i;:::-;-1:-1:-1;;;;;1704:32:242;;;1686:51;;1674:2;1659:18;3040:44:233;;;;;;;;;;;;;;;;;;;;;;;3018:19;;:66;;;;;-1:-1:-1;;;;;3018:66:233;;;;;-1:-1:-1;;;;;3018:66:233;;;;;;3095:36;3134:24;3147:10;;3134:12;:24::i;:::-;3095:63;;3168:36;3207:38;3223:9;;3234:10;;3207:15;:38::i;:::-;3168:77;;3255:35;3293:41;3309:13;;3324:9;;3293:15;:41::i;:::-;3403:24;;;3366:1;3403:24;;;;;;;;;3255:79;;-1:-1:-1;3366:1:233;3345:18;;3366:1;3403:24;;;;;;;;;;;;;;;;;;;;3377:50;;3437:44;3518:10;3484:45;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3484:45:233;;;;;;;;;;;;;;;;;3437:92;;3540:19;;;;;;;;;;;;;-1:-1:-1;;;3540:19:233;;;:7;3548:1;3540:10;;;;;;;;:::i;:::-;;;;;;:19;;;;3582:166;;;;;;;;3645:16;-1:-1:-1;;;;;3582:166:233;;;;;;;;;;;;;;;;;;-1:-1:-1;;;3582:166:233;;;;;;;3725:12;;3582:166;;;3569:7;3577:1;3569:10;;;;;;;;:::i;:::-;;;;;;:179;;;;3759:18;;;;;;;;;;;;;-1:-1:-1;;;3759:18:233;;;:7;3767:1;3759:10;;;;;;;;:::i;:::-;;;;;;:18;;;;3800:166;;;;;;;;3863:16;-1:-1:-1;;;;;3800:166:233;;;;;;;;;;;;;;;;;;-1:-1:-1;;;3800:166:233;;;;;;;3944:11;;3800:166;;;3787:7;3795:1;3787:10;;;;;;;;:::i;:::-;;;;;;:179;;;;3977:18;;;;;;;;;;;;;-1:-1:-1;;;3977:18:233;;;:7;3985:1;3977:10;;;;;;;;:::i;:::-;;;;;;:18;;;;4018:168;;;;;;;;4081:15;-1:-1:-1;;;;;4018:168:233;;;;;;;;;;;;;;;;;;-1:-1:-1;;;4018:168:233;;;;;;;4160:15;;4018:168;;;4005:7;4013:1;4005:10;;;;;;;;:::i;:::-;;;;;;:181;;;;4197:13;4233:23;4259:3;4233:29;;4315:7;4324;4333:5;4340:15;4292:64;;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;4273:16:233;:83;;-1:-1:-1;;;;;;4273:83:233;;-1:-1:-1;;;;;4273:83:233;;;;;;;;;;;;;4421:14;:42;;4446:16;;;;;;;-1:-1:-1;;;;;;4421:42:233;;;;;;;;;-1:-1:-1;;;;;;;;2612:1858:233:o;12968:197:127:-;-1:-1:-1;;;;;13136:15:127;;13109:4;13136:15;;;:7;:15;;;;;13109:4;13152:5;13136:22;;;;;;;;:::i;:::-;;;;;;;;;:::i;:::-;;;;;;;;;;;;-1:-1:-1;13136:22:127;;;;;-1:-1:-1;12968:197:127;;;;;:::o;21936:539::-;-1:-1:-1;;;;;22030:18:127;;22010:17;22030:18;;;:10;:18;;;;;;22124:14;;22120:349;;22154:19;22184:6;-1:-1:-1;;;;;22176:27:127;;:29;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;22154:51;;22219:23;22245:53;;;;;;;;22268:6;-1:-1:-1;;;;;22260:34:127;;:36;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;22245:53;;22219:79;-1:-1:-1;22312:19:127;22334:45;22219:79;22367:11;22334:18;:45::i;:::-;22312:67;;22416:9;22401:11;:24;;22393:65;;;;-1:-1:-1;;;22393:65:127;;;;;;;;;;;;22140:329;;;22120:349;22000:475;21936:539;:::o;4339:394::-;2334:13:21;:11;:13::i;:::-;3875:7:128::1;4445:22:127;:51;;:106;;;;;4005:6:128;4500:22:127;:51;;4445:106;4424:174;;;;-1:-1:-1::0;;;4424:174:127::1;;;;;;;;;;;;4628:19;::::0;4613:59:::1;::::0;;11391:25:242;;;11447:2;11432:18;;11425:34;;;4613:59:127::1;::::0;11364:18:242;4613:59:127::1;;;;;;;4682:19;:44:::0;4339:394::o;20868:546::-;2656:17;;:37;;-1:-1:-1;;;2656:37:127;;-1:-1:-1;;;;;1704:32:242;;;2656:37:127;;;1686:51:242;20999:3:127;;2656:17;;:31;;1659:18:242;;2656:37:127;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2655:38;2646:76;;;;-1:-1:-1;;;2646:76:127;;;;;;;;;;;;2656:17:::1;::::0;:37:::1;::::0;-1:-1:-1;;;2656:37:127;;-1:-1:-1;;;;;1704:32:242;;;2656:37:127::1;::::0;::::1;1686:51:242::0;21021:3:127;;2656:17:::1;::::0;:31:::1;::::0;1659:18:242;;2656:37:127::1;;;;;;;;;;;;;;;;;::::0;::::1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2655:38;2646:76;;;;-1:-1:-1::0;;;2646:76:127::1;;;;;;;;;;;;-1:-1:-1::0;;;;;21045:15:127;::::2;;::::0;;;:7:::2;:15;::::0;;;;;;;21061:22:::2;21045:39:::0;;;;;;;;::::2;;21044:40;21036:68;;;;-1:-1:-1::0;;;21036:68:127::2;;;;;;;;;;;;21193:42;21207:6;21215:3;21220:14;21193:13;:42::i;:::-;21282:31;21306:6;21282:23;:31::i;:::-;21323:37;21348:6;21356:3;21323:24;:37::i;:::-;21370;21395:6;21403:3;21370:24;:37::i;:::-;2732:1:::1;20868:546:::0;;;;;:::o;9828:709::-;9973:7;:5;:7::i;:::-;-1:-1:-1;;;;;9959:21:127;:10;-1:-1:-1;;;;;9959:21:127;;:100;;;-1:-1:-1;9984:13:127;;10023:35;;;-1:-1:-1;;;10023:35:127;;;;-1:-1:-1;;;;;9984:13:127;;;;:26;;10011:10;;9984:13;;10023:33;;:35;;;;;;;;;;;;;;9984:13;10023:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;9984:75;;-1:-1:-1;;;;;;9984:75:127;;;;;;;-1:-1:-1;;;;;21651:32:242;;;9984:75:127;;;21633:51:242;21700:18;;;21693:34;21606:18;;9984:75:127;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;9938:171;;;;-1:-1:-1;;;9938:171:127;;;;;;;;;;;;10141:7;10189:13;10228:15;;;;;:46;;;10261:13;10247:10;:27;10228:46;10220:80;;;;-1:-1:-1;;;10220:80:127;;;;;;;;;;;;10316:9;10311:220;10331:10;10327:1;:14;10311:220;;;10383:13;;10397:1;10383:16;;;;;;;:::i;:::-;;;;;;;10358:10;:22;10369:7;;10377:1;10369:10;;;;;;;:::i;:::-;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;10358:22:127;;;;;;;;;;;;-1:-1:-1;10358:22:127;:41;10431:7;;10439:1;10431:10;;;;;;;:::i;:::-;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;10418:42:127;;10443:13;;10457:1;10443:16;;;;;;;:::i;:::-;;;;;;;10418:42;;;;687:25:242;;675:2;660:18;;541:177;10418:42:127;;;;;;;;10503:3;;10311:220;;;;9928:609;;9828:709;;;;:::o;19263:215::-;19380:16;;;19394:1;19380:16;;;;;;;;;19353:24;;19380:16;;;;;;;;;;;-1:-1:-1;19380:16:127;19353:43;;19419:6;19406:7;19414:1;19406:10;;;;;;;;:::i;:::-;;;;;;:19;-1:-1:-1;;;;;19406:19:127;;;-1:-1:-1;;;;;19406:19:127;;;;;19435:36;19442:7;19451;19460:4;19466;19435:6;:36::i;:::-;19343:135;19263:215;;:::o;22534:347::-;2469:16;;22652:8;;2469:16;;2465:108;;;-1:-1:-1;;;;;2509:21:127;;;;;;:15;:21;;;;;;;;2501:61;;;;-1:-1:-1;;;2501:61:127;;;;;;;;;;;;2656:17:::1;::::0;:37:::1;::::0;-1:-1:-1;;;2656:37:127;;-1:-1:-1;;;;;1704:32:242;;;2656:37:127::1;::::0;::::1;1686:51:242::0;22679:8:127;;2656:17:::1;::::0;:31:::1;::::0;1659:18:242;;2656:37:127::1;;;;;;;;;;;;;;;;;::::0;::::1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2655:38;2646:76;;;;-1:-1:-1::0;;;2646:76:127::1;;;;;;;;;;;;22699:45:::2;22713:6;22721:8;22731:12;22699:13;:45::i;:::-;22791:31;22815:6;22791:23;:31::i;:::-;22832:42;22857:6;22865:8;22832:24;:42::i;2907:134:7:-:0;2954:33;3018:16;2999:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2999:35:7;;;;;;;;;;;;;;;;;;;;;;;2907:134;:::o;19817:205:127:-;19969:46;19976:7;19985;19994:9;20005;19969:6;:46::i;:::-;19817:205;;;;:::o;3823:151:7:-;3872:42;3948:19;3926:41;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3926:41:7;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3823:151;:::o;3684:133::-;3730:33;3794:16;3775:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3775:35:7;;;;;;;;;;;;;;;;;;;;;;3684:133;:::o;3385:141::-;3433:35;3501:18;3480:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3480:39:7;;;;;;;;;;;;;;;;;;;;;;3385:141;:::o;2913:162:127:-;2334:13:21;:11;:13::i;:::-;-1:-1:-1;;;;;2996:21:127;::::1;;::::0;;;:15:::1;:21;::::0;;;;;;;;:29;;-1:-1:-1;;2996:29:127::1;::::0;::::1;;::::0;;::::1;::::0;;;3040:28;;1379:41:242;;;3040:28:127::1;::::0;1352:18:242;3040:28:127::1;;;;;;;2913:162:::0;;:::o;11678:540::-;11791:5;11787:335;;;11851:7;:5;:7::i;:::-;-1:-1:-1;;;;;11837:21:127;:10;-1:-1:-1;;;;;11837:21:127;;:95;;;-1:-1:-1;11862:13:127;;11901:30;;;-1:-1:-1;;;11901:30:127;;;;-1:-1:-1;;;;;11862:13:127;;;;:26;;11889:10;;11862:13;;11901:28;;:30;;;;;;;;;;;;;;11862:13;11901:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;11862:70;;-1:-1:-1;;;;;;11862:70:127;;;;;;;-1:-1:-1;;;;;21651:32:242;;;11862:70:127;;;21633:51:242;21700:18;;;21693:34;21606:18;;11862:70:127;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;11812:178;;;;-1:-1:-1;;;11812:178:127;;;;;;;;;;;;11787:335;;;12081:7;:5;:7::i;:::-;-1:-1:-1;;;;;12067:21:127;:10;-1:-1:-1;;;;;12067:21:127;;12059:52;;;;-1:-1:-1;;;12059:52:127;;;;;;;;;;;;-1:-1:-1;;;;;12132:15:127;;;;;;:7;:15;;;;;12157:5;;12148;12132:22;;;;;;;;:::i;:::-;;;;;;;;;:::i;:::-;;;;;;;;;;;;;:30;;;;;;;;;;;;;;;;;;12190:6;-1:-1:-1;;;;;12177:34:127;;12198:5;12205;12177:34;;;;;;;:::i;:::-;;;;;;;;11678:540;;;:::o;14336:310::-;14521:7;14530;14556:83;14589:7;14598:12;14612;14626;14556:32;:83::i;:::-;14549:90;;;;14336:310;;;;;;;;:::o;4476:911:233:-;4545:16;;4589:4;;4545:50;;-1:-1:-1;;;4545:50:233;;-1:-1:-1;;;;;4589:4:233;;;4545:50;;;1686:51:242;4526:16:233;;4545;;;;;;;:35;;1659:18:242;;4545:50:233;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;4624:16;;4668:4;;4624:50;;-1:-1:-1;;;4624:50:233;;-1:-1:-1;;;;;4668:4:233;;;4624:50;;;1686:51:242;4526:69:233;;-1:-1:-1;4605:16:233;;4624;;;;;;:35;;1659:18:242;;4624:50:233;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;4704:16;;4748:5;;4704:51;;-1:-1:-1;;;4704:51:233;;-1:-1:-1;;;;;4748:5:233;;;4704:51;;;1686::242;4605:69:233;;-1:-1:-1;4684:17:233;;4704:16;;;;;;:35;;1659:18:242;;4704:51:233;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;4684:71;;4766:33;;;;;;;;;;;;;;-1:-1:-1;;;4766:33:233;;;4790:8;4766:11;:33::i;:::-;4809;;;;;;;;;;;;;;-1:-1:-1;;;4809:33:233;;;4833:8;4809:11;:33::i;:::-;4852:35;;;;;;;;;;;;;;-1:-1:-1;;;4852:35:233;;;4877:9;4852:11;:35::i;:::-;4897:64;4906:8;4947:13;;4928:15;;4923:2;:20;;;;:::i;:::-;4916:28;;:2;:28;:::i;:::-;:44;;;;:::i;:::-;4897:8;:64::i;:::-;4971:56;4980:8;5017:9;;5002:11;;4997:2;:16;;;;:::i;4971:56::-;5037:59;5046:9;5085:10;;5069:12;;5064:2;:17;;;;:::i;5037:59::-;5107:86;5116:13;;5132:3;5116:19;;;;:::i;:::-;5181:4;;5137:50;;5168:3;;-1:-1:-1;;;;;5181:4:233;5137:30;:50::i;:::-;5107:86;;;;;;;;;;;;;-1:-1:-1;;;5107:86:233;;;:8;:86::i;:::-;5203:83;5212:9;;5224:3;5212:15;;;;:::i;:::-;5274:4;;5229:51;;5260:4;;-1:-1:-1;;;;;5274:4:233;5229:30;:51::i;:::-;5203:83;;;;;;;;;;;;;-1:-1:-1;;;5203:83:233;;;:8;:83::i;:::-;5296:84;5305:10;;5318:3;5305:16;;;;:::i;:::-;5367:5;;5323:51;;5354:3;;-1:-1:-1;;;;;5367:5:233;5323:30;:51::i;:::-;5296:84;;;;;;;;;;;;;-1:-1:-1;;;5296:84:233;;;:8;:84::i;22940:1400:127:-;2469:16;;23058:8;;2469:16;;2465:108;;;-1:-1:-1;;;;;2509:21:127;;;;;;:15;:21;;;;;;;;2501:61;;;;-1:-1:-1;;;2501:61:127;;;;;;;;;;;;2656:17:::1;::::0;:37:::1;::::0;-1:-1:-1;;;2656:37:127;;-1:-1:-1;;;;;1704:32:242;;;2656:37:127::1;::::0;::::1;1686:51:242::0;23085:8:127;;2656:17:::1;::::0;:31:::1;::::0;1659:18:242;;2656:37:127::1;;;;;;;;;;;;;;;;;::::0;::::1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2655:38;2646:76;;;;-1:-1:-1::0;;;2646:76:127::1;;;;;;;;;;;;-1:-1:-1::0;;;;;23114:15:127;::::2;;::::0;;;:7:::2;:15;::::0;;;;;;;23130:20:::2;23114:37:::0;;;;;;;;::::2;;23113:38;23105:66;;;;-1:-1:-1::0;;;23105:66:127::2;;;;;;;;;;;;-1:-1:-1::0;;;;;23189:15:127;::::2;;::::0;;;:7:::2;:15;::::0;;;;:24;::::2;;23181:61;;;;-1:-1:-1::0;;;23181:61:127::2;;;;;;;;;;;;-1:-1:-1::0;;;;;23258:15:127;;::::2;;::::0;;;:7:::2;:15;::::0;;;;;;;:43;;::::2;::::0;;:33:::2;::::0;;::::2;:43:::0;;;;::::2;;23253:272;;23325:10;-1:-1:-1::0;;;;;23325:20:127;::::2;;23317:59;;;;-1:-1:-1::0;;;23317:59:127::2;;;;;;;;;;;;23391:33;23407:6;23415:8;23391:15;:33::i;:::-;-1:-1:-1::0;;;;;23446:15:127;;::::2;;::::0;;;:7:::2;:15;::::0;;;;;;;:43;;::::2;::::0;;:33:::2;::::0;;::::2;:43:::0;;;;::::2;;23438:76;;;;-1:-1:-1::0;;;23438:76:127::2;;;;;;;;;;;;23559:14;::::0;23543:58:::2;::::0;-1:-1:-1;;;23543:58:127;;-1:-1:-1;;;;;1704:32:242;;;23543:58:127::2;::::0;::::2;1686:51:242::0;23559:14:127;;::::2;::::0;23543:50:::2;::::0;1659:18:242;;23543:58:127::2;;;;;;;;;;;;;;;;;::::0;::::2;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;23605:1;23543:63:::0;23535:95:::2;;;;-1:-1:-1::0;;;23535:95:127::2;;;;;;;;;;;;-1:-1:-1::0;;;;;23661:18:127;::::2;23641:17;23661:18:::0;;;:10:::2;:18;::::0;;;;;23755:14;;23751:257:::2;;23785:20;23816:6;-1:-1:-1::0;;;;;23808:28:127::2;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;::::0;::::2;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;23785:53;;23852:24;23879:32;23884:12;23898;23879:4;:32::i;:::-;23852:59;;23952:9;23933:16;:28;23925:72;;;;-1:-1:-1::0;;;23925:72:127::2;;;;;;;;;;;;23771:237;;23751:257;24048:17;24069:67;24102:8;24112:6;24120:1;24123:12;24069:32;:67::i;:::-;24045:91:::0;-1:-1:-1;;24154:14:127;;24146:57:::2;;;;-1:-1:-1::0;;;24146:57:127::2;;;;;;;;;;;;24250:31;24274:6;24250:23;:31::i;:::-;24291:42;24316:6;24324:8;24291:24;:42::i;8369:128::-:0;2334:13:21;:11;:13::i;:::-;8454:1:127::1;8428:23;:27:::0;;;8470:20:::1;::::0;::::1;::::0;8454:1;8470:20:::1;8369:128::o:0;2181:27:128:-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2181:27:128;;-1:-1:-1;2181:27:128;:::o;3907:228:127:-;2334:13:21;:11;:13::i;:::-;-1:-1:-1;;;;;3987:23:127;::::1;3979:57;;;;-1:-1:-1::0;;;3979:57:127::1;;;;;;;;;;;;4066:14;::::0;4051:41:::1;::::0;-1:-1:-1;;;;;4051:41:127;;::::1;::::0;4066:14:::1;::::0;4051:41:::1;::::0;4066:14:::1;::::0;4051:41:::1;4102:14;:26:::0;;-1:-1:-1;;;;;;4102:26:127::1;-1:-1:-1::0;;;;;4102:26:127;;;::::1;::::0;;;::::1;::::0;;3907:228::o;14117:168::-;14184:7;14193;14219:59;14252:7;14269:1;14273;14276;14219:32;:59::i;:::-;14212:66;;;;14117:168;;;:::o;3193:186:7:-;3249:56;3346:26;3317:55;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3317:55:7;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;26067:888:127;2656:17;;:37;;-1:-1:-1;;;2656:37:127;;-1:-1:-1;;;;;1704:32:242;;;2656:37:127;;;1686:51:242;26241:10:127;;2656:17;;:31;;1659:18:242;;2656:37:127;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2655:38;2646:76;;;;-1:-1:-1;;;2646:76:127;;;;;;;;;;;;2656:17:::1;::::0;:37:::1;::::0;-1:-1:-1;;;2656:37:127;;-1:-1:-1;;;;;1704:32:242;;;2656:37:127::1;::::0;::::1;1686:51:242::0;26278:8:127;;2656:17:::1;::::0;:31:::1;::::0;1659:18:242;;2656:37:127::1;;;;;;;;;;;;;;;;;::::0;::::1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2655:38;2646:76;;;;-1:-1:-1::0;;;2646:76:127::1;;;;;;;;;;;;-1:-1:-1::0;;;;;26324:25:127;::::2;;::::0;;;:7:::2;:25;::::0;;;;;;;26350:19:::2;26324:46:::0;;;;;;;;::::2;;26323:47;:96:::0;::::2;;;-1:-1:-1::0;;;;;;26375:23:127;::::2;;::::0;;;:7:::2;:23;::::0;;;;;;;26399:19:::2;26375:44:::0;;;;;;;;::::2;;26374:45;26323:96;26302:158;;;;-1:-1:-1::0;;;26302:158:127::2;;;;;;;;;;;;-1:-1:-1::0;;;;;26478:23:127;::::2;;::::0;;;:7:::2;:23;::::0;;;;:32;::::2;;26470:69;;;;-1:-1:-1::0;;;26470:69:127::2;;;;;;;;;;;;-1:-1:-1::0;;;;;26557:25:127;::::2;;::::0;;;:7:::2;:25;::::0;;;;:34;::::2;;26549:71;;;;-1:-1:-1::0;;;26549:71:127::2;;;;;;;;;;;;26686:14;-1:-1:-1::0;;;;;26678:32:127::2;;:34;;;;;;;;;;;;;;;;;;;;;;;;;;;;::::0;::::2;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1::0;;;;;26638:74:127::2;26646:16;-1:-1:-1::0;;;;;26638:34:127::2;;:36;;;;;;;;;;;;;;;;;;;;;;;;;;;;::::0;::::2;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1::0;;;;;26638:74:127::2;;26630:104;;;;-1:-1:-1::0;;;26630:104:127::2;;;;;;;;;;;;26781:41;26805:16;26781:23;:41::i;:::-;26832:52;26857:16;26875:8;26832:24;:52::i;20655:154::-:0;-1:-1:-1;;;;;20740:15:127;;;;;;:7;:15;;;;;;;;20756:25;20740:42;;;;;;;;;;20739:43;20731:71;;;;-1:-1:-1;;;20731:71:127;;;;;;;;;;;;20655:154;:::o;2064:239:233:-;2173:12;;2123:19;;;2228:11;2211:14;2173:12;2211:2;:14;:::i;:::-;:28;;;;:::i;:::-;2195:44;;2280:5;2287:8;2256:40;;;;;:::i;:::-;11391:25:242;;;11447:2;11432:18;;11425:34;11379:2;11364:18;2256:40:233;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;2249:47:233;2064:239;-1:-1:-1;;;;2064:239:233:o;3155:101:21:-;2334:13;:11;:13::i;:::-;3219:30:::1;3246:1;3219:18;:30::i;:::-;3155:101::o:0;8599:846:127:-;8683:10;8675:19;;;;:7;:19;;;;;:28;;;8667:65;;;;-1:-1:-1;;;8667:65:127;;;;;;;;;;;;8807:18;;:22;8803:636;;8939:22;;8911:25;;:50;;;;:::i;:::-;8893:15;:68;8889:195;;;9007:1;8981:23;:27;9054:15;9026:25;:43;8889:195;9144:19;9166:50;9197:6;9205:10;9166:30;:50::i;:::-;9144:72;;9318:18;;9304:11;9278:23;;:37;;;;:::i;:::-;:58;9274:102;;;9345:31;;-1:-1:-1;;;9345:31:127;;;;;;;;;;;9274:102;9417:11;9390:23;;:38;;;;;;;:::i;:::-;;;;-1:-1:-1;;;8599:846:127;:::o;3047:140:7:-;3095:34;3162:18;3141:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2441:144:21;1313:22;2570:8;-1:-1:-1;;;;;2570:8:21;;2441:144::o;3532:146:7:-;3580:40;3653:18;3632:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3632:39:7;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3632:39:7;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2309:297:233;2452:12;;2402:19;;;2527:15;2507:17;2490:14;2452:12;2490:2;:14;:::i;:::-;:34;;;;:::i;:::-;:52;;;;:::i;:::-;2474:68;;2583:5;2590:8;2559:40;;;;;:::i;:::-;11391:25:242;;;11447:2;11432:18;;11425:34;11379:2;11364:18;2559:40:233;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;2552:47:233;2309:297;-1:-1:-1;;;;;2309:297:233:o;13768:121:127:-;13838:4;13861:21;13875:6;13861:13;:21::i;16684:322::-;2469:16;;16768:8;;2469:16;;2465:108;;;-1:-1:-1;;;;;2509:21:127;;;;;;:15;:21;;;;;;;;2501:61;;;;-1:-1:-1;;;2501:61:127;;;;;;;;;;;;16879:10:::1;16833:35;16871:19:::0;;;:7:::1;:19;::::0;;;;16908:15;;::::1;;16900:52;;;;-1:-1:-1::0;;;16900:52:127::1;;;;;;;;;;;;16962:37;16978:10;16990:8;16962:15;:37::i;6321:424::-:0;2334:13:21;:11;:13::i;:::-;-1:-1:-1;;;;;6533:36:127;::::1;;::::0;;;:28:::1;:36;::::0;;;;;;;;;6501:102;;25228:51:242;;;25295:18;;;25288:34;25338:18;;25331:34;;;6501:102:127::1;::::0;25216:2:242;25201:18;6501:102:127::1;;;;;;;-1:-1:-1::0;;;;;6668:36:127;;::::1;;::::0;;;:28:::1;:36;::::0;;;;:70;6321:424::o;12380:361::-;2334:13:21;:11;:13::i;:::-;12576:17:127::1;::::0;12555:61:::1;::::0;-1:-1:-1;;;;;12555:61:127;;::::1;::::0;12576:17:::1;::::0;12555:61:::1;::::0;12576:17:::1;::::0;12555:61:::1;12694:17;:40:::0;;-1:-1:-1;;;;;;12694:40:127::1;-1:-1:-1::0;;;;;12694:40:127;;;::::1;::::0;;;::::1;::::0;;12380:361::o;13216:138::-;-1:-1:-1;;;;;13327:20:127;;;;;;:13;:20;;;;;;;;;13320:27;;;;;;;;;;;;;;;;;13284:24;;13320:27;;;13327:20;13320:27;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;13320:27:127;;;;;;;;;;;;;;;;;;;;;;;13216:138;;;:::o;2754:147:7:-;2803:40;2876:18;2855:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2855:39:7;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2855:39:7;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;13609:108:127;13657:24;13700:10;13693:17;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;13693:17:127;;;;;;;;;;;;;;;;;;;;;;13609:108;:::o;24837:1171::-;2469:16;;25034:8;;2469:16;;2465:108;;;-1:-1:-1;;;;;2509:21:127;;;;;;:15;:21;;;;;;;;2501:61;;;;-1:-1:-1;;;2501:61:127;;;;;;;;;;;;2656:17:::1;::::0;:37:::1;::::0;-1:-1:-1;;;2656:37:127;;-1:-1:-1;;;;;1704:32:242;;;2656:37:127::1;::::0;::::1;1686:51:242::0;25061:8:127;;2656:17:::1;::::0;:31:::1;::::0;1659:18:242;;2656:37:127::1;;;;;;;;;;;;;;;;;::::0;::::1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2655:38;2646:76;;;;-1:-1:-1::0;;;2646:76:127::1;;;;;;;;;;;;-1:-1:-1::0;;;;;25090:23:127;::::2;;::::0;;;:7:::2;:23;::::0;;;;;;;25114::::2;25090:48:::0;;;;;;;;::::2;;25089:49;25081:77;;;;-1:-1:-1::0;;;25081:77:127::2;;;;;;;;;;;;-1:-1:-1::0;;;;;25176:23:127;::::2;;::::0;;;:7:::2;:23;::::0;;;;:32;::::2;;25168:69;;;;-1:-1:-1::0;;;25168:69:127::2;;;;;;;;;;;;-1:-1:-1::0;;;;;25255:25:127;::::2;;::::0;;;:7:::2;:25;::::0;;;;:34;::::2;;25247:71;;;;-1:-1:-1::0;;;25247:71:127::2;;;;;;;;;;;;25353:53;::::0;-1:-1:-1;;;25353:53:127;;-1:-1:-1;;;;;1704:32:242;;;25353:53:127::2;::::0;::::2;1686:51:242::0;25329:21:127::2;::::0;25353:43;;::::2;::::0;::::2;::::0;1659:18:242;;25353:53:127::2;;;;;;;;;;;;;;;;;::::0;::::2;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;25329:77;;25421:29;25435:14;25421:13;:29::i;:::-;25417:585;;;25491:11;25474:13;:28;;25466:69;;;;-1:-1:-1::0;;;25466:69:127::2;;;;;;;;;;;;25417:585;;;25569:17;25590:60;25623:8;25641:1;25645::::0;25648::::2;25590:32;:60::i;:::-;25566:84;;;25684:1;25672:9;:13;25664:56;;;;-1:-1:-1::0;;;25664:56:127::2;;;;;;;;;;;;25827:16;25846:71;25865:36;;;;;;;;25880:19;;25865:36;;::::0;25903:13:::2;25846:18;:71::i;:::-;25827:90;;25954:8;25939:11;:23;;25931:60;;;;-1:-1:-1::0;;;25931:60:127::2;;;;;;;;;;;;25552:450;;25071:937;2582:1:::1;24837:1171:::0;;;;;:::o;2459:141:7:-;2508:34;2575:18;2554:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1243:204:2;1302:7;;1282:4;;1302:7;;1298:143;;;-1:-1:-1;1332:7:2;;;;;1243:204::o;1298:143::-;1377:39;;-1:-1:-1;;;1377:39:2;;:7;:39;;;21633:51:242;;;-1:-1:-1;;;21700:18:242;;;21693:34;1428:1:2;;1377:7;;21606:18:242;;1377:39:2;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:53;;1370:60;;1243:204;:::o;7768:211:127:-;2334:13:21;:11;:13::i;:::-;7886:22:127::1;::::0;7861:63:::1;::::0;;11391:25:242;;;11447:2;11432:18;;11425:34;;;7861:63:127::1;::::0;11364:18:242;7861:63:127::1;;;;;;;7934:22;:38:::0;7768:211::o;5008:1109::-;2334:13:21;:11;:13::i;:::-;-1:-1:-1;;;;;5192:24:127;::::1;5154:35;5192:24:::0;;;:7:::1;:24;::::0;;;;5234:15;;::::1;;5226:52;;;;-1:-1:-1::0;;;5226:52:127::1;;;;;;;;;;;;5325:44;::::0;;::::1;::::0;;::::1;::::0;;;;;5445:47;;;;::::1;::::0;;;4141:6:128::1;5445:47:127::0;;5325:44;5512:46:::1;5445:47:::0;5325:44;2669:14:195;2653:13;;:30;;2551:139;5512:46:127::1;5511:47;5503:92;;;;-1:-1:-1::0;;;5503:92:127::1;;;;;;;;;;;;5610:32:::0;;;::::1;::::0;:99:::1;;-1:-1:-1::0;5662:14:127::1;::::0;5646:58:::1;::::0;-1:-1:-1;;;5646:58:127;;-1:-1:-1;;;;;1704:32:242;;;5646:58:127::1;::::0;::::1;1686:51:242::0;5662:14:127;;::::1;::::0;5646:50:::1;::::0;1659:18:242;;5646:58:127::1;;;;;;;;;;;;;;;;;::::0;::::1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:63:::0;5610:99:::1;5606:158;;;5732:21;;-1:-1:-1::0;;;5732:21:127::1;;;;;;;;;;;5606:158;5890:31;::::0;::::1;::::0;5862:89:::1;::::0;;11391:25:242;;;11447:2;11432:18;;11425:34;;;-1:-1:-1;;;;;5862:89:127;::::1;::::0;::::1;::::0;11364:18:242;5862:89:127::1;;;;;;;-1:-1:-1::0;;6049:31:127::1;;:61:::0;-1:-1:-1;5008:1109:127:o;21473:404::-;2469:16;;21565:6;;2469:16;;2465:108;;;-1:-1:-1;;;;;2509:21:127;;;;;;:15;:21;;;;;;;;2501:61;;;;-1:-1:-1;;;2501:61:127;;;;;;;;;;;;2656:17:::1;::::0;:37:::1;::::0;-1:-1:-1;;;2656:37:127;;-1:-1:-1;;;;;1704:32:242;;;2656:37:127::1;::::0;::::1;1686:51:242::0;21590:6:127;;2656:17:::1;::::0;:31:::1;::::0;1659:18:242;;2656:37:127::1;;;;;;;;;;;;;;;;;::::0;::::1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2655:38;2646:76;;;;-1:-1:-1::0;;;2646:76:127::1;;;;;;;;;;;;-1:-1:-1::0;;;;;21617:15:127;::::2;;::::0;;;:7:::2;:15;::::0;;;;;;;21633:18:::2;21617:35:::0;;;;;;;;::::2;;21616:36;21608:64;;;;-1:-1:-1::0;;;21608:64:127::2;;;;;;;;;;;;-1:-1:-1::0;;;;;21690:15:127;::::2;;::::0;;;:7:::2;:15;::::0;;;;:24;::::2;;21682:61;;;;-1:-1:-1::0;;;21682:61:127::2;;;;;;;;;;;;21789:31;21813:6;21789:23;:31::i;:::-;21830:40;21855:6;21863;21830:24;:40::i;16285:348::-:0;2469:16;;16370:10;;2469:16;;2465:108;;;-1:-1:-1;;;;;2509:21:127;;;;;;:15;:21;;;;;;;;2501:61;;;;-1:-1:-1;;;2501:61:127;;;;;;;;;;;;16406:8;16392:11:::1;16431:196;16455:3;16451:1;:7;16431:196;;;16475:16;16494:8;;16503:1;16494:11;;;;;;;:::i;:::-;;;;;;;;;;;;;;:::i;:::-;16475:30;;16519:37;16535:8;16545:10;16519:15;:37::i;:::-;-1:-1:-1::0;16599:3:127::1;;16431:196;;24399:379:::0;2469:16;;24485:8;;2469:16;;2465:108;;;-1:-1:-1;;;;;2509:21:127;;;;;;:15;:21;;;;;;;;2501:61;;;;-1:-1:-1;;;2501:61:127;;;;;;;;;;;;-1:-1:-1;;;;;24514:15:127;::::1;;::::0;;;:7:::1;:15;::::0;;;;;;;24530:19:::1;24514:36:::0;;;;;;;;::::1;;24513:37;24505:65;;;;-1:-1:-1::0;;;24505:65:127::1;;;;;;;;;;;;-1:-1:-1::0;;;;;24588:15:127;::::1;;::::0;;;:7:::1;:15;::::0;;;;:24;::::1;;24580:61;;;;-1:-1:-1::0;;;24580:61:127::1;;;;;;;;;;;;24688:31;24712:6;24688:23;:31::i;:::-;24729:42;24754:6;24762:8;24729:24;:42::i;14697:1498::-:0;15000:14;;14984:66;;-1:-1:-1;;;14984:66:127;;-1:-1:-1;;;;;1704:32:242;;;14984:66:127;;;1686:51:242;14860:7:127;;;;15000:14;;;14984:50;;1659:18:242;;14984:66:127;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;15110:14;;15094:68;;-1:-1:-1;;;15094:68:127;;-1:-1:-1;;;;;1704:32:242;;;15094:68:127;;;1686:51:242;14952:98:127;;-1:-1:-1;15060:31:127;;15110:14;;;;15094:50;;1659:18:242;;15094:68:127;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;15060:102;;15204:1;15180:21;:25;:56;;;;;15235:1;15209:23;:27;15180:56;15172:94;;;;-1:-1:-1;;;15172:94:127;;;;;;;;;;;;15652:28;15691:16;-1:-1:-1;;;;;15683:44:127;;:46;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;15652:77;;15740:20;-1:-1:-1;;;;;;;;;;;;;;15740:20:127;-1:-1:-1;;;;;;;;;;;;;;;;;;;;;;;15858:63:127;;;;;;;-1:-1:-1;;;;;15873:46:127;;;;:28;:46;;;;;;;15858:63;;15923:38;;;;;;;;;;;-1:-1:-1;;;15840:131:127;;:4;:131::i;:::-;15828:143;;15995:85;16000:40;;;;;;;;16015:23;16000:40;;;16042:37;;;;;;;;16057:20;16042:37;;;15995:4;:85::i;:::-;15981:99;;16098:28;16103:9;16114:11;16098:4;:28::i;:::-;16090:36;;16144:44;16163:5;16170:17;16144:18;:44::i;:::-;16137:51;14697:1498;-1:-1:-1;;;;;;;;;;14697:1498:127:o;6978:667::-;2334:13:21;:11;:13::i;:::-;-1:-1:-1;;;;;7055:24:127;::::1;;::::0;;;:7:::1;:24;::::0;;;;:33;::::1;;7054:34;7046:75;;;;-1:-1:-1::0;;;7046:75:127::1;;;;;;;;;;;;-1:-1:-1::0;;;;;7232:15:127;::::1;7191:38;7232:15:::0;;;:7:::1;:15;::::0;;;;7257:25;;7278:4:::1;-1:-1:-1::0;;7257:25:127;;::::1;::::0;::::1;::::0;;7292:18:::1;::::0;::::1;:26:::0;;;;::::1;::::0;;;7328:34;::::1;:38:::0;;;7232:15;7377:193:::1;7401:10;:17:::0;7397:21;::::1;7377:193;;;7460:6;-1:-1:-1::0;;;;;7443:23:127::1;:10;7454:1;7443:13;;;;;;;;:::i;:::-;;::::0;;;::::1;::::0;;;::::1;::::0;-1:-1:-1;;;;;7443:13:127::1;:23:::0;7435:64:::1;;;;-1:-1:-1::0;;;7435:64:127::1;;;;;;;;;;;;7542:3;;7377:193;;;-1:-1:-1::0;7579:10:127::1;:23:::0;;::::1;::::0;::::1;::::0;;-1:-1:-1;7579:23:127;;;;;::::1;::::0;;-1:-1:-1;;;;;;7579:23:127::1;-1:-1:-1::0;;;;;7579:23:127;::::1;::::0;;::::1;::::0;;;7618:20:::1;::::0;1686:51:242;;;7618:20:127::1;::::0;1674:2:242;1659:18;7618:20:127::1;;;;;;;7036:609;6978:667:::0;:::o;3134:119::-;2334:13:21;:11;:13::i;:::-;3190:16:127::1;:23:::0;;-1:-1:-1;;3190:23:127::1;3209:4;3190:23;::::0;;3228:18:::1;::::0;::::1;::::0;3190:16:::1;::::0;3228:18:::1;3134:119::o:0;10919:708::-;11064:7;:5;:7::i;:::-;-1:-1:-1;;;;;11050:21:127;:10;-1:-1:-1;;;;;11050:21:127;;:100;;;-1:-1:-1;11075:13:127;;11114:35;;;-1:-1:-1;;;11114:35:127;;;;-1:-1:-1;;;;;11075:13:127;;;;:26;;11102:10;;11075:13;;11114:33;;:35;;;;;;;;;;;;;;11075:13;11114:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;11075:75;;-1:-1:-1;;;;;;11075:75:127;;;;;;;-1:-1:-1;;;;;21651:32:242;;;11075:75:127;;;21633:51:242;21700:18;;;21693:34;21606:18;;11075:75:127;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;11029:171;;;;-1:-1:-1;;;11029:171:127;;;;;;;;;;;;11232:7;11280:13;11318:15;;;;;:46;;;11351:13;11337:10;:27;11318:46;11310:80;;;;-1:-1:-1;;;11310:80:127;;;;;;;;;;;;11406:9;11401:220;11421:10;11417:1;:14;11401:220;;;11473:13;;11487:1;11473:16;;;;;;;:::i;:::-;;;;;;;11448:10;:22;11459:7;;11467:1;11459:10;;;;;;;:::i;:::-;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;11448:22:127;;;;;;;;;;;;-1:-1:-1;11448:22:127;:41;11521:7;;11529:1;11521:10;;;;;;;:::i;:::-;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;11508:42:127;;11533:13;;11547:1;11533:16;;;;;;;:::i;:::-;;;;;;;11508:42;;;;687:25:242;;675:2;660:18;;541:177;11508:42:127;;;;;;;;11593:3;;11401:220;;3313:122;2334:13:21;:11;:13::i;:::-;3370:16:127::1;:24:::0;;-1:-1:-1;;3370:24:127::1;::::0;;3409:19:::1;::::0;::::1;::::0;3389:5:::1;::::0;3409:19:::1;3313:122::o:0;20093:503::-;20152:7;20171:11;20197:9;20192:378;20212:10;:17;20208:21;;20192:378;;;20246:15;20272:10;20283:1;20272:13;;;;;;;;:::i;:::-;;;;;;;;;;;-1:-1:-1;;;;;20272:13:127;;-1:-1:-1;20304:31:127;20272:13;20304;:31::i;:::-;20300:45;;;20337:8;20192:378;;20300:45;20359:25;20387:7;-1:-1:-1;;;;;20387:23:127;;:25;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;20359:53;;20433:67;20464:17;20491:7;20433:30;:67::i;:::-;20426:74;;;;:::i;:::-;;;20542:3;;;;;20232:338;;20192:378;;;-1:-1:-1;20586:3:127;20093:503;-1:-1:-1;20093:503:127:o;1871:50:128:-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;1871:50:128;;-1:-1:-1;1871:50:128;;-1:-1:-1;1871:50:128:o;2606:142:7:-;2655:35;2723:18;2702:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2702:39:7;;;;;;;;;;;;;;;;;;;;;;2606:142;:::o;18852:199:127:-;18943:16;;;18957:1;18943:16;;;;;;;;;18916:24;;18943:16;;;;;;;;;;;-1:-1:-1;18943:16:127;18916:43;;18982:6;18969:7;18977:1;18969:10;;;;;;;;:::i;:::-;;;;;;:19;-1:-1:-1;;;;;18969:19:127;;;-1:-1:-1;;;;;18969:19:127;;;;;19005:39;19012:7;19021:10;19005:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;19005:39:127;;;;;;;;;;;;;;;;;;;;;19033:4;19039;19005:6;:39::i;17057:1653::-;-1:-1:-1;;;;;17166:16:127;;17122:41;17166:16;;;:7;:16;;;;;;;;17295:10;17264:42;;:30;;;:42;;;;;;;;;17259:56;;17308:7;17057:1653;:::o;17259:56::-;17447:47;;-1:-1:-1;;;17447:47:127;;17483:10;17447:47;;;1686:51:242;17404:18:127;;;;-1:-1:-1;;;;;17447:35:127;;;;;1659:18:242;;17447:47:127;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;17403:91;;;;;17513:10;17527:1;17513:15;17505:65;;;;-1:-1:-1;;;17505:65:127;;;;;;;;;;;;17608:46;17622:7;17631:10;17643;17608:13;:46::i;:::-;17756:10;17725:42;;;;:30;;;:42;;;;;;;;17718:49;;-1:-1:-1;;17718:49:127;;;17922:13;:25;;;;;17889:58;;;;;;;;;;;;;;;;;;;17922:25;;17889:58;;;17922:25;17889:58;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;17889:58:127;;;;;;;;;;;;;;;;-1:-1:-1;;17971:20:127;;17889:58;;-1:-1:-1;17971:20:127;;-1:-1:-1;17957:11:127;;-1:-1:-1;;18035:213:127;18055:3;18051:1;:7;18035:213;;;18099:7;-1:-1:-1;;;;;18079:27:127;:13;18093:1;18079:16;;;;;;;;:::i;:::-;;;;;;;-1:-1:-1;;;;;18079:27:127;;18075:103;;18139:1;18126:14;;18158:5;;18075:103;18220:3;;18035:213;;;;18375:3;18362:10;:16;18354:51;;;;-1:-1:-1;;;18354:51:127;;;;;;;;;;;;18549:10;18504:28;18535:25;;;:13;:25;;;;;18606:17;;18535:25;;18606:21;;18626:1;;18606:21;:::i;:::-;18595:33;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;-1:-1:-1;;;;;18595:33:127;18570:10;18581;18570:22;;;;;;;;:::i;:::-;;;;;;;;;:58;;;;;-1:-1:-1;;;;;18570:58:127;;;;;-1:-1:-1;;;;;18570:58:127;;;;;;18638:10;:16;;;;;;;:::i;:::-;;;;;;;;;;-1:-1:-1;;18638:16:127;;;;;-1:-1:-1;;;;;;18638:16:127;;;;;;;;;18670:33;;18692:10;;-1:-1:-1;;;;;18670:33:127;;;;;18638:16;18670:33;17112:1598;;;;;;;17057:1653;:::o;3405:215:21:-;2334:13;:11;:13::i;:::-;-1:-1:-1;;;;;3489:22:21;::::1;3485:91;;3534:31;::::0;-1:-1:-1;;;3534:31:21;;3562:1:::1;3534:31;::::0;::::1;1686:51:242::0;1659:18;;3534:31:21::1;;;;;;;;3485:91;3585:28;3604:8;3585:18;:28::i;3556:237:127:-:0;2334:13:21;:11;:13::i;:::-;-1:-1:-1;;;;;3635:20:127;::::1;3627:54;;;;-1:-1:-1::0;;;3627:54:127::1;;;;;;;;;;;;3722:13;::::0;;3697:48:::1;::::0;-1:-1:-1;;;;;3697:48:127;;::::1;::::0;3722:13;::::1;::::0;3697:48:::1;::::0;::::1;3756:13;:30:::0;;-1:-1:-1;;;;;;3756:30:127::1;-1:-1:-1::0;;;;;3756:30:127;;;::::1;::::0;;;::::1;::::0;;3556:237::o;1631:779::-;8870:21:22;4302:15;;-1:-1:-1;;;4302:15:22;;;;4301:16;;4348:14;;4158:30;4726:16;;:34;;;;;4746:14;4726:34;4706:54;;4770:17;4790:11;:16;;4805:1;4790:16;:50;;;;-1:-1:-1;4818:4:22;4810:25;:30;4790:50;4770:70;;4856:12;4855:13;:30;;;;;4873:12;4872:13;4855:30;4851:91;;;4908:23;;-1:-1:-1;;;4908:23:22;;;;;;;;;;;4851:91;4951:18;;-1:-1:-1;;4951:18:22;4968:1;4951:18;;;4979:67;;;;5013:22;;-1:-1:-1;;;;5013:22:22;-1:-1:-1;;;5013:22:22;;;4979:67;-1:-1:-1;;;;;1784:28:127;::::1;1776:70;;;;-1:-1:-1::0;;;1776:70:127::1;;;;;;;;;;;;-1:-1:-1::0;;;;;1864:32:127;::::1;1856:78;;;;-1:-1:-1::0;;;1856:78:127::1;;;;;;;;;;;;-1:-1:-1::0;;;;;1952:32:127;::::1;1944:78;;;;-1:-1:-1::0;;;1944:78:127::1;;;;;;;;;;;;-1:-1:-1::0;;;;;2040:20:127;::::1;2032:54;;;;-1:-1:-1::0;;;2032:54:127::1;;;;;;;;;;;;2096:22;2111:6;2096:14;:22::i;:::-;2128:13;:38:::0;;-1:-1:-1;;;;;2128:38:127;;::::1;-1:-1:-1::0;;;;;;2128:38:127;;::::1;;::::0;;;2176:52;;;;::::1;::::0;;::::1;;::::0;;2238:17:::1;:38:::0;;;;::::1;::::0;;;::::1;;::::0;;2311:7:::1;2286:22;:32:::0;2356:15:::1;2328:25;:43:::0;2381:18:::1;:22:::0;5066:101:22;;;;5100:23;;-1:-1:-1;;;;5100:23:22;;;5142:14;;-1:-1:-1;26122:50:242;;5142:14:22;;26110:2:242;26095:18;5142:14:22;;;;;;;4092:1081;;;;;1631:779:127;;;;:::o;8123:187::-;2334:13:21;:11;:13::i;:::-;8239:18:127::1;::::0;8207:59:::1;::::0;;11391:25:242;;;11447:2;11432:18;;11425:34;;;8227:10:127::1;::::0;8207:59:::1;::::0;11364:18:242;8207:59:127::1;;;;;;;8276:18;:27:::0;8123:187::o;1941:177:195:-;2022:7;2041:18;2062:15;2067:1;2070:6;2062:4;:15::i;:::-;2041:36;;2094:17;2103:7;2094:8;:17::i;:::-;2087:24;1941:177;-1:-1:-1;;;;1941:177:195:o;2658:162:21:-;966:10:23;2717:7:21;:5;:7::i;:::-;-1:-1:-1;;;;;2717:23:21;;2713:101;;2763:40;;-1:-1:-1;;;2763:40:21;;966:10:23;2763:40:21;;;1686:51:242;1659:18;;2763:40:21;1540:203:242;27869:602:127;-1:-1:-1;;;;;27980:15:127;;;;;;:7;:15;;;;;;;;27996:20;27980:37;;;;;;;;;;27979:38;27971:66;;;;-1:-1:-1;;;27971:66:127;;;;;;;;;;;;-1:-1:-1;;;;;28055:15:127;;;;;;:7;:15;;;;;:24;;;28047:61;;;;-1:-1:-1;;;28047:61:127;;;;;;;;;;;;-1:-1:-1;;;;;28217:15:127;;;;;;;:7;:15;;;;;;;;:43;;;;;:33;;;;:43;;;;;;28212:57;;27869:602;;;:::o;28212:57::-;28309:17;28330:67;28363:8;28373:6;28381:12;28395:1;28330:32;:67::i;:::-;28306:91;-1:-1:-1;;28415:14:127;;28407:57;;;;-1:-1:-1;;;28407:57:127;;;;;;;;;;;31303:137;31389:17;;31370:63;;-1:-1:-1;;;31370:63:127;;-1:-1:-1;;;;;1704:32:242;;;31370:63:127;;;1686:51:242;31389:17:127;;;;31370:55;;1659:18:242;;31370:63:127;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;31951:163;32056:17;;32037:70;;-1:-1:-1;;;32037:70:127;;-1:-1:-1;;;;;26375:32:242;;;32037:70:127;;;26357:51:242;26444:32;;;26424:18;;;26417:60;32056:17:127;;;;32037:52;;26330:18:242;;32037:70:127;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;32609:1112;32741:14;;32727:11;32765:887;32785:3;32781:1;:7;32765:887;;;32805:14;32822:7;32830:1;32822:10;;;;;;;;:::i;:::-;;;;;;;;;;;;-1:-1:-1;;;;;32854:15:127;;;;;;:7;:15;;;;;;;:24;32822:10;;-1:-1:-1;32854:24:127;;32846:61;;;;-1:-1:-1;;;32846:61:127;;;;;;;;;;;;32925:9;32921:324;;;32954:40;32986:6;32954:23;:40::i;:::-;33017:9;33012:219;33036:7;:14;33032:1;:18;33012:219;;;33075:53;33108:6;33117:7;33125:1;33117:10;;;;;;;;:::i;:::-;;;;;;;33075:24;:53::i;:::-;33187:3;;33012:219;;;;32921:324;33262:9;33258:324;;;33291:40;33323:6;33291:23;:40::i;:::-;33354:9;33349:219;33373:7;:14;33369:1;:18;33349:219;;;33412:53;33445:6;33454:7;33462:1;33454:10;;;;;;;;:::i;:::-;;;;;;;33412:24;:53::i;:::-;33524:3;;33349:219;;;;33258:324;-1:-1:-1;33624:3:127;;32765:887;;;-1:-1:-1;33681:17:127;;33662:52;;-1:-1:-1;;;33662:52:127;;-1:-1:-1;;;;;33681:17:127;;;;33662:43;;:52;;33706:7;;33662:52;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;28477:2678;28662:7;28671;28690:37;;:::i;:::-;-1:-1:-1;;;;;28789:22:127;;28775:11;28789:22;;;:13;:22;;;;;:29;;28828:2087;28848:3;28844:1;:7;28828:2087;;;-1:-1:-1;;;;;28885:22:127;;28868:14;28885:22;;;:13;:22;;;;;:25;;28908:1;;28885:25;;;;;;:::i;:::-;;;;;;;;;;;29078:43;;-1:-1:-1;;;29078:43:127;;-1:-1:-1;;;;;1704:32:242;;;29078:43:127;;;1686:51:242;28885:25:127;;;;-1:-1:-1;28885:25:127;;29078:34;;1659:18:242;;29078:43:127;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;29033:25;;;28992:129;;;29013:18;;;28992:129;;;;28993:18;;;;28992:129;;;;29160:57;;-1:-1:-1;29160:57:127;;;;;-1:-1:-1;;;;;29175:15:127;;;-1:-1:-1;29175:15:127;;;:7;:15;;;;;:40;;;29160:57;;29136:21;;;:81;;;;29251:42;;;;;;;29266:25;;29251:42;;29231:17;;;:62;29404:14;;29388:58;;-1:-1:-1;;;29388:58:127;;;;;1686:51:242;;;;29404:14:127;;;29388:50;;1659:18:242;;29388:58:127;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;29361:24;;;:85;;;29496:1;29468:29;29460:77;;;;-1:-1:-1;;;29460:77:127;;;;;;;;;;;;29571:41;;;;;;;;;29586:24;;;;29571:41;;29552:16;;;:60;29751:21;;;;29774:17;;;;29741:70;;29746:46;;:4;:46::i;:::-;29794:4;:16;;;29741:4;:70::i;:::-;29720:18;;;:91;;;29955:18;;;;29975;;29909:85;;29720:91;29955:18;29909:25;:85::i;:::-;29888:106;;30146:16;;;;30164:18;;;;30184:25;;;;30120:90;;30146:16;30164:18;30120:25;:90::i;:::-;30076:25;;;:134;-1:-1:-1;;;;;30295:22:127;;;;;;;30291:554;;30490:86;30516:4;:18;;;30536:12;30550:4;:25;;;30490;:86::i;:::-;30442:25;;;:134;;;30772:16;;;;30746:84;;30790:12;;30746:25;:84::i;:::-;30698:25;;;:132;30291:554;-1:-1:-1;30887:3:127;;28828:2087;;;-1:-1:-1;30950:25:127;;;;30929:18;;:46;30925:224;;;31020:25;;;;30999:18;;:46;;31020:25;30999:46;:::i;:::-;31047:1;30991:58;;;;;;;;30925:224;31119:18;;31091:25;;;;31088:1;;31091:46;;;:::i;:::-;31080:58;;;;;;;;7139:145:16;7206:71;7269:2;7273;7222:54;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;7222:54:16;;;;;;;;;;;;;;-1:-1:-1;;;;;7222:54:16;-1:-1:-1;;;7222:54:16;;;7206:15;:71::i;2270:110:2:-;2349:24;;-1:-1:-1;;;2349:24:2;;;;;11391:25:242;;;11432:18;;;11425:34;;;2349:11:2;;;;11364:18:242;;2349:24:2;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;27001:412:127;27161:14;;27145:58;;-1:-1:-1;;;27145:58:127;;-1:-1:-1;;;;;1704:32:242;;;27145:58:127;;;1686:51:242;27096:7:127;;;;27161:14;;;27145:50;;1659:18:242;;27145:58:127;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;27115:88;;27221:19;27244:1;27221:24;27213:72;;;;-1:-1:-1;;;27213:72:127;;;;;;;;;;;;27321:36;;;;;;;;;;;;27402:4;27374:25;27379:6;27321:36;27374:4;:25::i;:::-;:32;;;;:::i;:::-;27367:39;27001:412;-1:-1:-1;;;;;27001:412:127:o;2386:134:2:-;2484:29;;-1:-1:-1;;;2484:29:2;;:11;;;;:29;;2496:4;;2502:5;;2509:3;;2484:29;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;27419:444:127;-1:-1:-1;;;;;27541:16:127;;27497:41;27541:16;;;:7;:16;;;;;27575:21;;;;27567:58;;;;-1:-1:-1;;;27567:58:127;;;;;;;;;;;;-1:-1:-1;;;;;27641:40:127;;;;;;:30;;;:40;;;;;;;;27636:221;;-1:-1:-1;;;;;27697:40:127;;;;;;;:30;;;:40;;;;;;;;:47;;-1:-1:-1;;27697:47:127;27740:4;27697:47;;;;;;27758:13;:23;;;;;:37;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;;27758:37:127;;;;;;;27814:32;27697:40;;27758:37;27814:32;;27697:40;27814:32;27487:376;27419:444;;:::o;3955:97:195:-;4014:7;4040:5;4044:1;4040;:5;:::i;:::-;4033:12;3955:97;-1:-1:-1;;;3955:97:195:o;31588:137:127:-;31674:17;;31655:63;;-1:-1:-1;;;31655:63:127;;-1:-1:-1;;;;;1704:32:242;;;31655:63:127;;;1686:51:242;31674:17:127;;;;31655:55;;1659:18:242;;31655:63:127;1540:203:242;32440:163:127;32545:17;;32526:70;;-1:-1:-1;;;32526:70:127;;-1:-1:-1;;;;;26375:32:242;;;32526:70:127;;;26357:51:242;26444:32;;;26424:18;;;26417:60;32545:17:127;;;;32526:52;;26330:18:242;;32526:70:127;26183:300:242;3774:248:21;1313:22;3923:8;;-1:-1:-1;;;;;;3941:19:21;;-1:-1:-1;;;;;3941:19:21;;;;;;;;3975:40;;3923:8;;;;;3975:40;;3847:24;;3975:40;3837:185;;3774:248;:::o;33727:240:127:-;-1:-1:-1;;;;;33811:15:127;;33788:4;33811:15;;;:7;:15;;;;;:40;;;:45;:86;;;;-1:-1:-1;;;;;;33860:15:127;;;;;;:7;:15;;;;;;;;33876:20;33860:37;;;;;;;;;;33811:86;:149;;;;;33921:6;-1:-1:-1;;;;;33913:37:127;;:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;33956:4;33913:47;33804:156;33727:240;-1:-1:-1;;33727:240:127:o;4477:157:195:-;-1:-1:-1;;;;;;;;;;;;4571:56:195;;;;;;;;1224:4;4586:28;4591:1;:10;;;4603:1;:10;;;4586:4;:28::i;:::-;:39;;;;:::i;:::-;4571:56;;4564:63;4477:157;-1:-1:-1;;;4477:157:195:o;5478:162::-;-1:-1:-1;;;;;;;;;;;;5572:61:195;;;;;;;;5587:44;5592:26;5597:1;:10;;;1224:4;5592;:26::i;:::-;5620:10;;5587:4;:44::i;1847:127:21:-;6931:20:22;:18;:20::i;:::-;1929:38:21::1;1954:12;1929:24;:38::i;4640:134:195:-:0;-1:-1:-1;;;;;;;;;;;;4731:36:195;;;;;;;;4746:19;4751:1;:10;;;4763:1;4746:4;:19::i;1620:213::-;1803:12;;1677:7;;1803:23;;1224:4;;1803:23;:::i;2258:214::-;2362:7;2381:18;2402:15;2407:1;2410:6;2402:4;:15::i;:::-;2381:36;;2434:31;2439:17;2448:7;2439:8;:17::i;:::-;2458:6;2434:4;:31::i;851:129:16:-;922:51;965:7;934:29;922:51::i;4780:125:195:-;4842:7;1224:4;4868:19;4873:1;4876;:10;;;4868:4;:19::i;:::-;:30;;;;:::i;5375:97::-;5434:7;5460:5;5464:1;5460;:5;:::i;6396:97::-;6455:7;6481:5;6485:1;6481;:5;:::i;7084:141:22:-;8870:21;8560:40;-1:-1:-1;;;8560:40:22;;;;7146:73;;7191:17;;-1:-1:-1;;;7191:17:22;;;;;;;;;;;1980:235:21;6931:20:22;:18;:20::i;180:463:16:-;265:22;131:42;265:40;;594:1;571;541:7;535:14;510:2;501:7;497:16;461:14;434:5;402:211;381:246;367:270;180:463;:::o;-1:-1:-1:-;;;;;;;;:::o;:::-;;;;;;;;:::o;:::-;;;;;;;;:::o;:::-;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;14:131:242:-;-1:-1:-1;;;;;89:31:242;;79:42;;69:70;;135:1;132;125:12;150:134;218:20;;247:31;218:20;247:31;:::i;:::-;150:134;;;:::o;289:247::-;348:6;401:2;389:9;380:7;376:23;372:32;369:52;;;417:1;414;407:12;369:52;456:9;443:23;475:31;500:5;475:31;:::i;723:155::-;802:20;;851:2;841:13;;831:41;;868:1;865;858:12;883:351;970:6;978;1031:2;1019:9;1010:7;1006:23;1002:32;999:52;;;1047:1;1044;1037:12;999:52;1086:9;1073:23;1105:31;1130:5;1105:31;:::i;:::-;1155:5;-1:-1:-1;1179:49:242;1224:2;1209:18;;1179:49;:::i;:::-;1169:59;;883:351;;;;;:::o;1748:226::-;1807:6;1860:2;1848:9;1839:7;1835:23;1831:32;1828:52;;;1876:1;1873;1866:12;1828:52;-1:-1:-1;1921:23:242;;1748:226;-1:-1:-1;1748:226:242:o;1979:650::-;2065:6;2073;2081;2089;2142:3;2130:9;2121:7;2117:23;2113:33;2110:53;;;2159:1;2156;2149:12;2110:53;2198:9;2185:23;2217:31;2242:5;2217:31;:::i;:::-;2267:5;-1:-1:-1;2324:2:242;2309:18;;2296:32;2337:33;2296:32;2337:33;:::i;:::-;2389:7;-1:-1:-1;2448:2:242;2433:18;;2420:32;2461:33;2420:32;2461:33;:::i;:::-;1979:650;;;;-1:-1:-1;2513:7:242;;2593:2;2578:18;2565:32;;-1:-1:-1;;1979:650:242:o;2634:367::-;2697:8;2707:6;2761:3;2754:4;2746:6;2742:17;2738:27;2728:55;;2779:1;2776;2769:12;2728:55;-1:-1:-1;2802:20:242;;2845:18;2834:30;;2831:50;;;2877:1;2874;2867:12;2831:50;2914:4;2906:6;2902:17;2890:29;;2974:3;2967:4;2957:6;2954:1;2950:14;2942:6;2938:27;2934:38;2931:47;2928:67;;;2991:1;2988;2981:12;2928:67;2634:367;;;;;:::o;3006:768::-;3128:6;3136;3144;3152;3205:2;3193:9;3184:7;3180:23;3176:32;3173:52;;;3221:1;3218;3211:12;3173:52;3261:9;3248:23;3294:18;3286:6;3283:30;3280:50;;;3326:1;3323;3316:12;3280:50;3365:70;3427:7;3418:6;3407:9;3403:22;3365:70;:::i;:::-;3454:8;;-1:-1:-1;3339:96:242;-1:-1:-1;;3542:2:242;3527:18;;3514:32;3571:18;3558:32;;3555:52;;;3603:1;3600;3593:12;3555:52;3642:72;3706:7;3695:8;3684:9;3680:24;3642:72;:::i;:::-;3006:768;;;;-1:-1:-1;3733:8:242;-1:-1:-1;;;;3006:768:242:o;3779:127::-;3840:10;3835:3;3831:20;3828:1;3821:31;3871:4;3868:1;3861:15;3895:4;3892:1;3885:15;3911:906;3965:5;4018:3;4011:4;4003:6;3999:17;3995:27;3985:55;;4036:1;4033;4026:12;3985:55;4076:6;4063:20;4106:18;4098:6;4095:30;4092:56;;;4128:18;;:::i;:::-;4174:6;4171:1;4167:14;4210:2;4204:9;4273:2;4269:7;4264:2;4260;4256:11;4252:25;4244:6;4240:38;4344:6;4332:10;4329:22;4308:18;4296:10;4293:34;4290:62;4287:88;;;4355:18;;:::i;:::-;4391:2;4384:22;4441;;;4491:4;4523:15;;;4519:26;;;4441:22;4479:17;;4557:15;;;4554:35;;;4585:1;4582;4575:12;4554:35;4621:4;4613:6;4609:17;4598:28;;4635:152;4651:6;4646:3;4643:15;4635:152;;;4719:23;4738:3;4719:23;:::i;:::-;4707:36;;4772:4;4668:14;;;;4763;4635:152;;4822:483;4915:6;4923;4976:2;4964:9;4955:7;4951:23;4947:32;4944:52;;;4992:1;4989;4982:12;4944:52;5031:9;5018:23;5050:31;5075:5;5050:31;:::i;:::-;5100:5;-1:-1:-1;5156:2:242;5141:18;;5128:32;5183:18;5172:30;;5169:50;;;5215:1;5212;5205:12;5169:50;5238:61;5291:7;5282:6;5271:9;5267:22;5238:61;:::i;:::-;5228:71;;;4822:483;;;;;:::o;5310:508::-;5387:6;5395;5403;5456:2;5444:9;5435:7;5431:23;5427:32;5424:52;;;5472:1;5469;5462:12;5424:52;5511:9;5498:23;5530:31;5555:5;5530:31;:::i;:::-;5580:5;-1:-1:-1;5637:2:242;5622:18;;5609:32;5650:33;5609:32;5650:33;:::i;:::-;5310:508;;5702:7;;-1:-1:-1;;;5782:2:242;5767:18;;;;5754:32;;5310:508::o;5823:637::-;6013:2;6025:21;;;6095:13;;5998:18;;;6117:22;;;5965:4;;6196:15;;;6170:2;6155:18;;;5965:4;6239:195;6253:6;6250:1;6247:13;6239:195;;;6318:13;;-1:-1:-1;;;;;6314:39:242;6302:52;;6383:2;6409:15;;;;6374:12;;;;6350:1;6268:9;6239:195;;;-1:-1:-1;6451:3:242;;5823:637;-1:-1:-1;;;;;5823:637:242:o;6465:118::-;6551:5;6544:13;6537:21;6530:5;6527:32;6517:60;;6573:1;6570;6563:12;6588:855;6718:6;6726;6734;6742;6795:3;6783:9;6774:7;6770:23;6766:33;6763:53;;;6812:1;6809;6802:12;6763:53;6852:9;6839:23;6885:18;6877:6;6874:30;6871:50;;;6917:1;6914;6907:12;6871:50;6940:61;6993:7;6984:6;6973:9;6969:22;6940:61;:::i;:::-;6930:71;;;7054:2;7043:9;7039:18;7026:32;7083:18;7073:8;7070:32;7067:52;;;7115:1;7112;7105:12;7067:52;7138:63;7193:7;7182:8;7171:9;7167:24;7138:63;:::i;:::-;7128:73;;;7251:2;7240:9;7236:18;7223:32;7264:28;7286:5;7264:28;:::i;:::-;7311:5;-1:-1:-1;7368:2:242;7353:18;;7340:32;7381:30;7340:32;7381:30;:::i;:::-;6588:855;;;;-1:-1:-1;6588:855:242;;-1:-1:-1;;6588:855:242:o;7448:400::-;7490:3;7528:5;7522:12;7555:6;7550:3;7543:19;7580:1;7590:139;7604:6;7601:1;7598:13;7590:139;;;7712:4;7697:13;;;7693:24;;7687:31;7667:11;;;7663:22;;7656:63;7619:12;7590:139;;;7594:3;7774:1;7767:4;7758:6;7753:3;7749:16;7745:27;7738:38;7837:4;7830:2;7826:7;7821:2;7813:6;7809:15;7805:29;7800:3;7796:39;7792:50;7785:57;;;7448:400;;;;:::o;7853:579::-;7905:3;7936;7968:5;7962:12;7995:6;7990:3;7983:19;8027:4;8022:3;8018:14;8011:21;;8085:4;8075:6;8072:1;8068:14;8061:5;8057:26;8053:37;8124:4;8117:5;8113:16;8147:1;8157:249;8171:6;8168:1;8165:13;8157:249;;;8258:2;8254:7;8246:5;8240:4;8236:16;8232:30;8227:3;8220:43;8284:38;8317:4;8308:6;8302:13;8284:38;:::i;:::-;8357:4;8382:14;;;;8276:46;;-1:-1:-1;8345:17:242;;;;;8193:1;8186:9;8157:249;;;-1:-1:-1;8422:4:242;;7853:579;-1:-1:-1;;;;;;7853:579:242:o;8437:1033::-;8641:4;8689:2;8678:9;8674:18;8719:2;8708:9;8701:21;8742:6;8777;8771:13;8808:6;8800;8793:22;8846:2;8835:9;8831:18;8824:25;;8908:2;8898:6;8895:1;8891:14;8880:9;8876:30;8872:39;8858:53;;8946:2;8938:6;8934:15;8967:1;8977:464;8991:6;8988:1;8985:13;8977:464;;;9056:22;;;-1:-1:-1;;9052:36:242;9040:49;;9112:13;;9157:9;;-1:-1:-1;;;;;9153:35:242;9138:51;;9236:2;9228:11;;;9222:18;9277:2;9260:15;;;9253:27;;;9222:18;9303:58;;9345:15;;9222:18;9303:58;:::i;:::-;9293:68;-1:-1:-1;;9396:2:242;9419:12;;;;9384:15;;;;;9013:1;9006:9;8977:464;;;-1:-1:-1;9458:6:242;;8437:1033;-1:-1:-1;;;;;;8437:1033:242:o;9705:382::-;9770:6;9778;9831:2;9819:9;9810:7;9806:23;9802:32;9799:52;;;9847:1;9844;9837:12;9799:52;9886:9;9873:23;9905:31;9930:5;9905:31;:::i;:::-;9955:5;-1:-1:-1;10012:2:242;9997:18;;9984:32;10025:30;9984:32;10025:30;:::i;:::-;10074:7;10064:17;;;9705:382;;;;;:::o;10092:486::-;10185:6;10193;10201;10254:2;10242:9;10233:7;10229:23;10225:32;10222:52;;;10270:1;10267;10260:12;10222:52;10309:9;10296:23;10328:31;10353:5;10328:31;:::i;:::-;10378:5;-1:-1:-1;10402:49:242;10447:2;10432:18;;10402:49;:::i;:::-;10392:59;;10503:2;10492:9;10488:18;10475:32;10516:30;10538:7;10516:30;:::i;:::-;10565:7;10555:17;;;10092:486;;;;;:::o;10583:629::-;10669:6;10677;10685;10693;10746:3;10734:9;10725:7;10721:23;10717:33;10714:53;;;10763:1;10760;10753:12;10714:53;10802:9;10789:23;10821:31;10846:5;10821:31;:::i;:::-;10871:5;-1:-1:-1;10928:2:242;10913:18;;10900:32;10941:33;10900:32;10941:33;:::i;:::-;10583:629;;10993:7;;-1:-1:-1;;;;11073:2:242;11058:18;;11045:32;;11176:2;11161:18;11148:32;;10583:629::o;11694:446::-;11746:3;11784:5;11778:12;11811:6;11806:3;11799:19;11843:4;11838:3;11834:14;11827:21;;11882:4;11875:5;11871:16;11905:1;11915:200;11929:6;11926:1;11923:13;11915:200;;;11994:13;;-1:-1:-1;;;;;;11990:40:242;11978:53;;12060:4;12051:14;;;;12088:17;;;;11951:1;11944:9;11915:200;;;-1:-1:-1;12131:3:242;;11694:446;-1:-1:-1;;;;11694:446:242:o;12145:1143::-;12363:4;12411:2;12400:9;12396:18;12441:2;12430:9;12423:21;12464:6;12499;12493:13;12530:6;12522;12515:22;12568:2;12557:9;12553:18;12546:25;;12630:2;12620:6;12617:1;12613:14;12602:9;12598:30;12594:39;12580:53;;12668:2;12660:6;12656:15;12689:1;12699:560;12713:6;12710:1;12707:13;12699:560;;;12806:2;12802:7;12790:9;12782:6;12778:22;12774:36;12769:3;12762:49;12840:6;12834:13;12886:2;12880:9;12917:2;12909:6;12902:18;12947:48;12991:2;12983:6;12979:15;12965:12;12947:48;:::i;:::-;12933:62;;13044:2;13040;13036:11;13030:18;13008:40;;13097:6;13089;13085:19;13080:2;13072:6;13068:15;13061:44;13128:51;13172:6;13156:14;13128:51;:::i;:::-;13118:61;-1:-1:-1;;;13214:2:242;13237:12;;;;13202:15;;;;;12735:1;12728:9;12699:560;;13293:671;13379:6;13387;13395;13403;13456:3;13444:9;13435:7;13431:23;13427:33;13424:53;;;13473:1;13470;13463:12;13424:53;13512:9;13499:23;13531:31;13556:5;13531:31;:::i;:::-;13581:5;-1:-1:-1;13638:2:242;13623:18;;13610:32;13651:33;13610:32;13651:33;:::i;:::-;13703:7;-1:-1:-1;13762:2:242;13747:18;;13734:32;13775:33;13734:32;13775:33;:::i;:::-;13827:7;-1:-1:-1;13886:2:242;13871:18;;13858:32;13899:33;13858:32;13899:33;:::i;14207:280::-;14406:2;14395:9;14388:21;14369:4;14426:55;14477:2;14466:9;14462:18;14454:6;14426:55;:::i;14836:1031::-;15038:4;15086:2;15075:9;15071:18;15116:2;15105:9;15098:21;15139:6;15174;15168:13;15205:6;15197;15190:22;15243:2;15232:9;15228:18;15221:25;;15305:2;15295:6;15292:1;15288:14;15277:9;15273:30;15269:39;15255:53;;15343:2;15335:6;15331:15;15364:1;15374:464;15388:6;15385:1;15382:13;15374:464;;;15453:22;;;-1:-1:-1;;15449:36:242;15437:49;;15509:13;;15554:9;;-1:-1:-1;;;;;15550:35:242;15535:51;;15633:2;15625:11;;;15619:18;15674:2;15657:15;;;15650:27;;;15619:18;15700:58;;15742:15;;15619:18;15700:58;:::i;:::-;15690:68;-1:-1:-1;;15793:2:242;15816:12;;;;15781:15;;;;;15410:1;15403:9;15374:464;;15872:346;15940:6;15948;16001:2;15989:9;15980:7;15976:23;15972:32;15969:52;;;16017:1;16014;16007:12;15969:52;-1:-1:-1;;16062:23:242;;;16182:2;16167:18;;;16154:32;;-1:-1:-1;15872:346:242:o;16223:388::-;16291:6;16299;16352:2;16340:9;16331:7;16327:23;16323:32;16320:52;;;16368:1;16365;16358:12;16320:52;16407:9;16394:23;16426:31;16451:5;16426:31;:::i;:::-;16476:5;-1:-1:-1;16533:2:242;16518:18;;16505:32;16546:33;16505:32;16546:33;:::i;16616:367::-;16684:6;16692;16745:2;16733:9;16724:7;16720:23;16716:32;16713:52;;;16761:1;16758;16751:12;16713:52;16800:9;16787:23;16819:31;16844:5;16819:31;:::i;:::-;16869:5;16947:2;16932:18;;;;16919:32;;-1:-1:-1;;;16616:367:242:o;16988:437::-;17074:6;17082;17135:2;17123:9;17114:7;17110:23;17106:32;17103:52;;;17151:1;17148;17141:12;17103:52;17191:9;17178:23;17224:18;17216:6;17213:30;17210:50;;;17256:1;17253;17246:12;17210:50;17295:70;17357:7;17348:6;17337:9;17333:22;17295:70;:::i;:::-;17384:8;;17269:96;;-1:-1:-1;16988:437:242;-1:-1:-1;;;;16988:437:242:o;19053:127::-;19114:10;19109:3;19105:20;19102:1;19095:31;19145:4;19142:1;19135:15;19169:4;19166:1;19159:15;19185:1463;19578:3;19567:9;19560:22;19541:4;19605:56;19656:3;19645:9;19641:19;19633:6;19605:56;:::i;:::-;19709:9;19701:6;19697:22;19692:2;19681:9;19677:18;19670:50;19740:6;19775;19769:13;19806:6;19798;19791:22;19841:2;19833:6;19829:15;19822:22;;19900:2;19890:6;19887:1;19883:14;19875:6;19871:27;19867:36;19938:2;19930:6;19926:15;19959:1;19969:548;19983:6;19980:1;19977:13;19969:548;;;20048:19;;;-1:-1:-1;;20044:33:242;20032:46;;20101:13;;20146:9;;-1:-1:-1;;;;;20142:35:242;20127:51;;20225:2;20217:11;;;20211:18;20266:4;20249:15;;;20242:29;;;20211:18;20298:50;;20330:17;;20211:18;20298:50;:::i;:::-;20401:4;20393:13;;;20387:20;20368:17;;;;20361:47;;;;20472:2;20495:12;;;;20284:64;;-1:-1:-1;20460:15:242;;;;;20005:1;19998:9;19969:548;;;-1:-1:-1;;;;;;;1497:31:242;;20591:4;20576:20;;1485:44;20534:6;-1:-1:-1;20549:48:242;;-1:-1:-1;;;1431:104:242;20549:48;20635:6;20628:4;20617:9;20613:20;20606:36;19185:1463;;;;;;;:::o;20653:127::-;20714:10;20709:3;20705:20;20702:1;20695:31;20745:4;20742:1;20735:15;20769:4;20766:1;20759:15;20785:230;20855:6;20908:2;20896:9;20887:7;20883:23;20879:32;20876:52;;;20924:1;20921;20914:12;20876:52;-1:-1:-1;20969:16:242;;20785:230;-1:-1:-1;20785:230:242:o;21020:245::-;21087:6;21140:2;21128:9;21119:7;21115:23;21111:32;21108:52;;;21156:1;21153;21146:12;21108:52;21188:9;21182:16;21207:28;21229:5;21207:28;:::i;21738:380::-;21817:1;21813:12;;;;21860;;;21881:61;;21935:4;21927:6;21923:17;21913:27;;21881:61;21988:2;21980:6;21977:14;21957:18;21954:38;21951:161;;22034:10;22029:3;22025:20;22022:1;22015:31;22069:4;22066:1;22059:15;22097:4;22094:1;22087:15;22123:429;22296:2;22281:18;;22329:2;22318:14;;22308:145;;22375:10;22370:3;22366:20;22363:1;22356:31;22410:4;22407:1;22400:15;22438:4;22435:1;22428:15;22308:145;22462:25;;;22530:14;;22523:22;22518:2;22503:18;;;22496:50;22123:429;:::o;22557:127::-;22618:10;22613:3;22609:20;22606:1;22599:31;22649:4;22646:1;22639:15;22673:4;22670:1;22663:15;22689:128;22756:9;;;22777:11;;;22774:37;;;22791:18;;:::i;22822:375::-;22910:1;22928:5;22942:249;22963:1;22953:8;22950:15;22942:249;;;23013:4;23008:3;23004:14;22998:4;22995:24;22992:50;;;23022:18;;:::i;:::-;23072:1;23062:8;23058:16;23055:49;;;23086:16;;;;23055:49;23169:1;23165:16;;;;;23125:15;;22942:249;;;22822:375;;;;;;:::o;23202:902::-;23251:5;23281:8;23271:80;;-1:-1:-1;23322:1:242;23336:5;;23271:80;23370:4;23360:76;;-1:-1:-1;23407:1:242;23421:5;;23360:76;23452:4;23470:1;23465:59;;;;23538:1;23533:174;;;;23445:262;;23465:59;23495:1;23486:10;;23509:5;;;23533:174;23570:3;23560:8;23557:17;23554:43;;;23577:18;;:::i;:::-;-1:-1:-1;;23633:1:242;23619:16;;23692:5;;23445:262;;23791:2;23781:8;23778:16;23772:3;23766:4;23763:13;23759:36;23753:2;23743:8;23740:16;23735:2;23729:4;23726:12;23722:35;23719:77;23716:203;;;-1:-1:-1;23828:19:242;;;23904:5;;23716:203;23951:42;-1:-1:-1;;23976:8:242;23970:4;23951:42;:::i;:::-;24029:6;24025:1;24021:6;24017:19;24008:7;24005:32;24002:58;;;24040:18;;:::i;:::-;24078:20;;23202:902;-1:-1:-1;;;23202:902:242:o;24109:131::-;24169:5;24198:36;24225:8;24219:4;24198:36;:::i;24245:168::-;24318:9;;;24349;;24366:15;;;24360:22;;24346:37;24336:71;;24387:18;;:::i;24418:251::-;24488:6;24541:2;24529:9;24520:7;24516:23;24512:32;24509:52;;;24557:1;24554;24547:12;24509:52;24589:9;24583:16;24608:31;24633:5;24608:31;:::i;24674:125::-;24739:9;;;24760:10;;;24757:36;;;24773:18;;:::i;24804:217::-;24844:1;24870;24860:132;;24914:10;24909:3;24905:20;24902:1;24895:31;24949:4;24946:1;24939:15;24977:4;24974:1;24967:15;24860:132;-1:-1:-1;25006:9:242;;24804:217::o;25376:456::-;25464:6;25472;25480;25533:2;25521:9;25512:7;25508:23;25504:32;25501:52;;;25549:1;25546;25539:12;25501:52;-1:-1:-1;;25594:16:242;;25700:2;25685:18;;25679:25;25796:2;25781:18;;;25775:25;25594:16;;25679:25;;-1:-1:-1;25775:25:242;25376:456;-1:-1:-1;25376:456:242:o;25837:127::-;25898:10;25893:3;25889:20;25886:1;25879:31;25929:4;25926:1;25919:15;25953:4;25950:1;25943:15;26488:291;26665:2;26654:9;26647:21;26628:4;26685:45;26726:2;26715:9;26711:18;26703:6;26685:45;:::i;:::-;26677:53;;26766:6;26761:2;26750:9;26746:18;26739:34;26488:291;;;;;:::o;26784:362::-;26989:6;26978:9;26971:25;27032:6;27027:2;27016:9;27012:18;27005:34;27075:2;27070;27059:9;27055:18;27048:30;26952:4;27095:45;27136:2;27125:9;27121:18;27113:6;27095:45;:::i", "linkReferences": {}}, "methodIdentifiers": {"IS_TEST()": "fa7626d4", "accountAssets(address,uint256)": "dce15449", "afterMTokenMint(address)": "0d926fc8", "allMarkets(uint256)": "52d84d1e", "beforeMTokenBorrow(address,address,uint256)": "50795f8a", "beforeMTokenLiquidate(address,address,address,uint256)": "b50ce762", "beforeMTokenMint(address,address)": "c0f1ee09", "beforeMTokenRedeem(address,address,uint256)": "1e32bd9b", "beforeMTokenRepay(address,address)": "c321fbcc", "beforeMTokenSeize(address,address,address,address)": "6765dff9", "beforeMTokenTransfer(address,address,address,uint256)": "17bf120e", "beforeRebalancing(address)": "68f6f4b0", "blacklistOperator()": "2d57d487", "borrowCaps(address)": "4a584432", "checkMembership(address,address)": "929fe9a1", "checkOutflowVolumeLimit(uint256)": "823307f2", "claimMalda(address)": "e44a429a", "claimMalda(address,address[])": "1c7818ac", "claimMalda(address[],address[],bool,bool)": "1fbd27a5", "closeFactorMantissa()": "e8755446", "cumulativeOutflowVolume()": "700e1212", "disableWhitelist()": "d6b0f484", "enableWhitelist()": "cdfb2b4e", "enterMarkets(address[])": "c2998238", "enterMarketsWithSender(address)": "973fd521", "excludeArtifacts()": "b5508aa9", "excludeContracts()": "e20c9f71", "excludeSelectors()": "b0464fdc", "excludeSenders()": "1ed7831c", "exitMarket(address)": "ede4edd0", "failed()": "ba414fa6", "getAccountLiquidity(address)": "5ec88c79", "getAllMarkets()": "b0772d0b", "getAssetsIn(address)": "abfceffc", "getHypotheticalAccountLiquidity(address,address,uint256,uint256)": "4e79238f", "getUSDValueForAllMarkets()": "d99faea5", "initialize(address,address,address,address)": "f8c8765e", "isDeprecated(address)": "94543c15", "isMarketListed(address)": "3d98a1e5", "isOperator()": "4456eda2", "isPaused(address,uint8)": "0d126627", "lastOutflowResetTimestamp()": "ddf46254", "limitPerTimePeriod()": "8728d8a7", "liquidateCalculateSeizeTokens(address,address,uint256)": "c488847b", "liquidationIncentiveMantissa(address)": "2e06d7b1", "markets(address)": "8e8f294b", "newOracleInBase(uint256,uint256)": "918c89e3", "newUSDOracle(uint256)": "7048e31d", "oracleOperator()": "11679ef7", "outflowResetTimeWindow()": "e92081b4", "owner()": "8da5cb5b", "renounceOwnership()": "715018a6", "resetOutflowVolume()": "52a2548d", "rewardDistributor()": "acc2166a", "rolesOperator()": "4fecab70", "setCloseFactor(uint256)": "12348e96", "setCollateralFactor(address,uint256)": "c04f31ff", "setLiquidationIncentive(address,uint256)": "9bd8f6e8", "setMarketBorrowCaps(address[],uint256[])": "186db48f", "setMarketSupplyCaps(address[],uint256[])": "d136af44", "setOutflowTimeLimitInUSD(uint256)": "f9f00f89", "setOutflowVolumeTimeWindow(uint256)": "befca684", "setPaused(address,uint8,bool)": "4a675b34", "setPriceOracle(address)": "530e784f", "setRewardDistributor(address)": "a1809b95", "setRolesOperator(address)": "f89416ee", "setUp()": "0a9254e4", "setWhitelistedUser(address,bool)": "44710fbe", "supplyCaps(address)": "02c3bcbb", "supportMarket(address)": "cab4f84c", "targetArtifactSelectors()": "66d9a9a0", "targetArtifacts()": "85226c81", "targetContracts()": "3f7286f4", "targetInterfaces()": "2ade3880", "targetSelectors()": "916a17c6", "targetSenders()": "3e5e3c23", "test_getPriceUSD()": "4fe010a5", "transferOwnership(address)": "f2fde38b", "userWhitelisted(address)": "fc2e0c2f", "whitelistEnabled()": "51fb012d"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"InvalidInitialization\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotInitializing\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_AssetNotFound\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_Deactivate_MarketBalanceOwed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_EmptyPrice\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_InsufficientLiquidity\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_InvalidBlacklistOperator\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_InvalidCollateralFactor\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_InvalidInput\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_InvalidRewardDistributor\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_InvalidRolesOperator\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_MarketAlreadyListed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_MarketBorrowCapReached\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_MarketNotListed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_MarketSupplyReached\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_Mismatch\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_OnlyAdmin\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_OnlyAdminOrRole\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_OracleUnderlyingFetchError\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_OutflowVolumeReached\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_Paused\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_PriceFetchFailed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_RepayAmountNotValid\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_RepayingTooMuch\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_SenderMustBeToken\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_UserBlacklisted\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_UserNotWhitelisted\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_WrongMarket\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"OwnableInvalidOwner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"OwnableUnauthorizedAccount\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"enum ImTokenOperationTypes.OperationType\",\"name\":\"_type\",\"type\":\"uint8\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"state\",\"type\":\"bool\"}],\"name\":\"ActionPaused\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"version\",\"type\":\"uint64\"}],\"name\":\"Initialized\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"MarketEntered\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"MarketExited\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"}],\"name\":\"MarketListed\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"newBorrowCap\",\"type\":\"uint256\"}],\"name\":\"NewBorrowCap\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"oldCloseFactorMantissa\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"newCloseFactorMantissa\",\"type\":\"uint256\"}],\"name\":\"NewCloseFactor\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"oldCollateralFactorMantissa\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"newCollateralFactorMantissa\",\"type\":\"uint256\"}],\"name\":\"NewCollateralFactor\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"market\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"oldLiquidationIncentiveMantissa\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"newLiquidationIncentiveMantissa\",\"type\":\"uint256\"}],\"name\":\"NewLiquidationIncentive\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"oldPriceOracle\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newPriceOracle\",\"type\":\"address\"}],\"name\":\"NewPriceOracle\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"oldRewardDistributor\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newRewardDistributor\",\"type\":\"address\"}],\"name\":\"NewRewardDistributor\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"oldRoles\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newRoles\",\"type\":\"address\"}],\"name\":\"NewRolesOperator\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"newBorrowCap\",\"type\":\"uint256\"}],\"name\":\"NewSupplyCap\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"oldLimit\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"newLimit\",\"type\":\"uint256\"}],\"name\":\"OutflowLimitUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"oldWindow\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"newWindow\",\"type\":\"uint256\"}],\"name\":\"OutflowTimeWindowUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[],\"name\":\"OutflowVolumeReset\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"state\",\"type\":\"bool\"}],\"name\":\"UserWhitelisted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[],\"name\":\"WhitelistDisabled\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[],\"name\":\"WhitelistEnabled\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"log\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"log_address\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"val\",\"type\":\"uint256[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int256[]\",\"name\":\"val\",\"type\":\"int256[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"val\",\"type\":\"address[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"log_bytes\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"name\":\"log_bytes32\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"\",\"type\":\"int256\"}],\"name\":\"log_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"val\",\"type\":\"address\"}],\"name\":\"log_named_address\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"val\",\"type\":\"uint256[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256[]\",\"name\":\"val\",\"type\":\"int256[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"val\",\"type\":\"address[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"val\",\"type\":\"bytes\"}],\"name\":\"log_named_bytes\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"val\",\"type\":\"bytes32\"}],\"name\":\"log_named_bytes32\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"val\",\"type\":\"int256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"log_named_decimal_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"val\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"log_named_decimal_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"val\",\"type\":\"int256\"}],\"name\":\"log_named_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"val\",\"type\":\"string\"}],\"name\":\"log_named_string\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"val\",\"type\":\"uint256\"}],\"name\":\"log_named_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"log_string\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"log_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"logs\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"IS_TEST\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"accountAssets\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"}],\"name\":\"afterMTokenMint\",\"outputs\":[],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"allMarkets\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"borrowAmount\",\"type\":\"uint256\"}],\"name\":\"beforeMTokenBorrow\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mTokenBorrowed\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"mTokenCollateral\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"repayAmount\",\"type\":\"uint256\"}],\"name\":\"beforeMTokenLiquidate\",\"outputs\":[],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"minter\",\"type\":\"address\"}],\"name\":\"beforeMTokenMint\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"redeemer\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"redeemTokens\",\"type\":\"uint256\"}],\"name\":\"beforeMTokenRedeem\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"}],\"name\":\"beforeMTokenRepay\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mTokenCollateral\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"mTokenBorrowed\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"liquidator\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"}],\"name\":\"beforeMTokenSeize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"src\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"dst\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"transferTokens\",\"type\":\"uint256\"}],\"name\":\"beforeMTokenTransfer\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"}],\"name\":\"beforeRebalancing\",\"outputs\":[],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"blacklistOperator\",\"outputs\":[{\"internalType\":\"contract IBlacklister\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"borrowCaps\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"}],\"name\":\"checkMembership\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"checkOutflowVolumeLimit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"holder\",\"type\":\"address\"},{\"internalType\":\"address[]\",\"name\":\"mTokens\",\"type\":\"address[]\"}],\"name\":\"claimMalda\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"holders\",\"type\":\"address[]\"},{\"internalType\":\"address[]\",\"name\":\"mTokens\",\"type\":\"address[]\"},{\"internalType\":\"bool\",\"name\":\"borrowers\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"suppliers\",\"type\":\"bool\"}],\"name\":\"claimMalda\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"holder\",\"type\":\"address\"}],\"name\":\"claimMalda\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"closeFactorMantissa\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"cumulativeOutflowVolume\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"disableWhitelist\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"enableWhitelist\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"_mTokens\",\"type\":\"address[]\"}],\"name\":\"enterMarkets\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_account\",\"type\":\"address\"}],\"name\":\"enterMarketsWithSender\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeArtifacts\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"excludedArtifacts_\",\"type\":\"string[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeContracts\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"excludedContracts_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzSelector[]\",\"name\":\"excludedSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeSenders\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"excludedSenders_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_mToken\",\"type\":\"address\"}],\"name\":\"exitMarket\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"failed\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"getAccountLiquidity\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getAllMarkets\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"mTokens\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_user\",\"type\":\"address\"}],\"name\":\"getAssetsIn\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"mTokens\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"mTokenModify\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"redeemTokens\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"borrowAmount\",\"type\":\"uint256\"}],\"name\":\"getHypotheticalAccountLiquidity\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getUSDValueForAllMarkets\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_rolesOperator\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_blacklistOperator\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_rewardDistributor\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_admin\",\"type\":\"address\"}],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"}],\"name\":\"isDeprecated\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"}],\"name\":\"isMarketListed\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"isOperator\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"internalType\":\"enum ImTokenOperationTypes.OperationType\",\"name\":\"_type\",\"type\":\"uint8\"}],\"name\":\"isPaused\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"lastOutflowResetTimestamp\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"limitPerTimePeriod\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mTokenBorrowed\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"mTokenCollateral\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"actualRepayAmount\",\"type\":\"uint256\"}],\"name\":\"liquidateCalculateSeizeTokens\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"liquidationIncentiveMantissa\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"markets\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"isListed\",\"type\":\"bool\"},{\"internalType\":\"uint256\",\"name\":\"collateralFactorMantissa\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"isMalded\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"usdPerQuotedToken\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"usdPerBaseToken\",\"type\":\"uint256\"}],\"name\":\"newOracleInBase\",\"outputs\":[{\"internalType\":\"contract MockChainlinkOracle\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"usdPerToken\",\"type\":\"uint256\"}],\"name\":\"newUSDOracle\",\"outputs\":[{\"internalType\":\"contract MockChainlinkOracle\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"oracleOperator\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"outflowResetTimeWindow\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"resetOutflowVolume\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"rewardDistributor\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"rolesOperator\",\"outputs\":[{\"internalType\":\"contract IRoles\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"newCloseFactorMantissa\",\"type\":\"uint256\"}],\"name\":\"setCloseFactor\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"newCollateralFactorMantissa\",\"type\":\"uint256\"}],\"name\":\"setCollateralFactor\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"market\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"newLiquidationIncentiveMantissa\",\"type\":\"uint256\"}],\"name\":\"setLiquidationIncentive\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"mTokens\",\"type\":\"address[]\"},{\"internalType\":\"uint256[]\",\"name\":\"newBorrowCaps\",\"type\":\"uint256[]\"}],\"name\":\"setMarketBorrowCaps\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"mTokens\",\"type\":\"address[]\"},{\"internalType\":\"uint256[]\",\"name\":\"newSupplyCaps\",\"type\":\"uint256[]\"}],\"name\":\"setMarketSupplyCaps\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"setOutflowTimeLimitInUSD\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"newTimeWindow\",\"type\":\"uint256\"}],\"name\":\"setOutflowVolumeTimeWindow\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"internalType\":\"enum ImTokenOperationTypes.OperationType\",\"name\":\"_type\",\"type\":\"uint8\"},{\"internalType\":\"bool\",\"name\":\"state\",\"type\":\"bool\"}],\"name\":\"setPaused\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOracle\",\"type\":\"address\"}],\"name\":\"setPriceOracle\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newRewardDistributor\",\"type\":\"address\"}],\"name\":\"setRewardDistributor\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_roles\",\"type\":\"address\"}],\"name\":\"setRolesOperator\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"setUp\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"state\",\"type\":\"bool\"}],\"name\":\"setWhitelistedUser\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"supplyCaps\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"}],\"name\":\"supportMarket\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetArtifactSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"string\",\"name\":\"artifact\",\"type\":\"string\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzArtifactSelector[]\",\"name\":\"targetedArtifactSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetArtifacts\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"targetedArtifacts_\",\"type\":\"string[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetContracts\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"targetedContracts_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetInterfaces\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"string[]\",\"name\":\"artifacts\",\"type\":\"string[]\"}],\"internalType\":\"struct StdInvariant.FuzzInterface[]\",\"name\":\"targetedInterfaces_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzSelector[]\",\"name\":\"targetedSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetSenders\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"targetedSenders_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_getPriceUSD\",\"outputs\":[],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"userWhitelisted\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"whitelistEnabled\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"InvalidInitialization()\":[{\"details\":\"The contract is already initialized.\"}],\"NotInitializing()\":[{\"details\":\"The contract is not initializing.\"}],\"OwnableInvalidOwner(address)\":[{\"details\":\"The owner is not a valid owner account. (eg. `address(0)`)\"}],\"OwnableUnauthorizedAccount(address)\":[{\"details\":\"The caller account is not authorized to perform an operation.\"}]},\"events\":{\"Initialized(uint64)\":{\"details\":\"Triggered when the contract has been initialized or reinitialized.\"}},\"kind\":\"dev\",\"methods\":{\"afterMTokenMint(address)\":{\"params\":{\"mToken\":\"Asset being minted\"}},\"beforeMTokenBorrow(address,address,uint256)\":{\"params\":{\"borrowAmount\":\"The amount of underlying the account would borrow\",\"borrower\":\"The account which would borrow the asset\",\"mToken\":\"The market to verify the borrow against\"}},\"beforeMTokenLiquidate(address,address,address,uint256)\":{\"params\":{\"borrower\":\"The address of the borrower\",\"mTokenBorrowed\":\"Asset which was borrowed by the borrower\",\"mTokenCollateral\":\"Asset which was used as collateral and will be seized\",\"repayAmount\":\"The amount of underlying being repaid\"}},\"beforeMTokenMint(address,address)\":{\"params\":{\"mToken\":\"The market to verify the mint against\",\"minter\":\"The account which would get the minted tokens\"}},\"beforeMTokenRedeem(address,address,uint256)\":{\"params\":{\"mToken\":\"The market to verify the redeem against\",\"redeemTokens\":\"The number of mTokens to exchange for the underlying asset in the market\",\"redeemer\":\"The account which would redeem the tokens\"}},\"beforeMTokenRepay(address,address)\":{\"params\":{\"borrower\":\"The account which would borrowed the asset\",\"mToken\":\"The market to verify the repay against\"}},\"beforeMTokenSeize(address,address,address,address)\":{\"params\":{\"borrower\":\"The address of the borrower\",\"liquidator\":\"The address repaying the borrow and seizing the collateral\",\"mTokenBorrowed\":\"Asset which was borrowed by the borrower\",\"mTokenCollateral\":\"Asset which was used as collateral and will be seized\"}},\"beforeMTokenTransfer(address,address,address,uint256)\":{\"params\":{\"dst\":\"The account which receives the tokens\",\"mToken\":\"The market to verify the transfer against\",\"src\":\"The account which sources the tokens\",\"transferTokens\":\"The number of mTokens to transfer\"}},\"beforeRebalancing(address)\":{\"params\":{\"mToken\":\"The market to verify the transfer against\"}},\"checkMembership(address,address)\":{\"params\":{\"account\":\"The address of the account to check\",\"mToken\":\"The mToken to check\"},\"returns\":{\"_0\":\"True if the account is in the asset, otherwise false.\"}},\"checkOutflowVolumeLimit(uint256)\":{\"params\":{\"amount\":\"The new limit\"}},\"claimMalda(address)\":{\"params\":{\"holder\":\"The address to claim MALDA for\"}},\"claimMalda(address,address[])\":{\"params\":{\"holder\":\"The address to claim MALDA for\",\"mTokens\":\"The list of markets to claim MALDA in\"}},\"claimMalda(address[],address[],bool,bool)\":{\"params\":{\"borrowers\":\"Whether or not to claim MALDA earned by borrowing\",\"holders\":\"The addresses to claim MALDA for\",\"mTokens\":\"The list of markets to claim MALDA in\",\"suppliers\":\"Whether or not to claim MALDA earned by supplying\"}},\"enterMarkets(address[])\":{\"params\":{\"_mTokens\":\"The list of addresses of the mToken markets to be enabled\"}},\"enterMarketsWithSender(address)\":{\"params\":{\"_account\":\"The account to add for\"}},\"exitMarket(address)\":{\"details\":\"Sender must not have an outstanding borrow balance in the asset,  or be providing necessary collateral for an outstanding borrow.\",\"params\":{\"_mToken\":\"The address of the asset to be removed\"}},\"getAccountLiquidity(address)\":{\"returns\":{\"_0\":\"account liquidity in excess of collateral requirements,          account shortfall below collateral requirements)\"}},\"getAssetsIn(address)\":{\"params\":{\"_user\":\"The address of the account to pull assets for\"},\"returns\":{\"mTokens\":\"A dynamic list with the assets the account has entered\"}},\"getHypotheticalAccountLiquidity(address,address,uint256,uint256)\":{\"params\":{\"account\":\"The account to determine liquidity for\",\"borrowAmount\":\"The amount of underlying to hypothetically borrow\",\"mTokenModify\":\"The market to hypothetically redeem/borrow in\",\"redeemTokens\":\"The number of tokens to hypothetically redeem\"},\"returns\":{\"_0\":\"hypothetical account liquidity in excess of collateral requirements,         hypothetical account shortfall below collateral requirements)\"}},\"isDeprecated(address)\":{\"details\":\"All borrows in a deprecated mToken market can be immediately liquidated\",\"params\":{\"mToken\":\"The market to check if deprecated\"}},\"isPaused(address,uint8)\":{\"params\":{\"_type\":\"the operation type\",\"mToken\":\"The mToken to check\"}},\"liquidateCalculateSeizeTokens(address,address,uint256)\":{\"details\":\"Used in liquidation (called in mTokenBorrowed.liquidate)\",\"params\":{\"actualRepayAmount\":\"The amount of mTokenBorrowed underlying to convert into mTokenCollateral tokens\",\"mTokenBorrowed\":\"The address of the borrowed mToken\",\"mTokenCollateral\":\"The address of the collateral mToken\"},\"returns\":{\"_0\":\"number of mTokenCollateral tokens to be seized in a liquidation\"}},\"owner()\":{\"details\":\"Returns the address of the current owner.\"},\"renounceOwnership()\":{\"details\":\"Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner.\"},\"setCloseFactor(uint256)\":{\"details\":\"Admin function to set closeFactor\",\"params\":{\"newCloseFactorMantissa\":\"New close factor, scaled by 1e18\"}},\"setCollateralFactor(address,uint256)\":{\"details\":\"Admin function to set per-market collateralFactor\",\"params\":{\"mToken\":\"The market to set the factor on\",\"newCollateralFactorMantissa\":\"The new collateral factor, scaled by 1e18\"}},\"setLiquidationIncentive(address,uint256)\":{\"details\":\"Admin function to set liquidationIncentive\",\"params\":{\"newLiquidationIncentiveMantissa\":\"New liquidationIncentive scaled by 1e18\"}},\"setMarketBorrowCaps(address[],uint256[])\":{\"params\":{\"mTokens\":\"The addresses of the markets (tokens) to change the borrow caps for\",\"newBorrowCaps\":\"The new borrow cap values in underlying to be set. A value of 0 corresponds to unlimited borrowing.\"}},\"setMarketSupplyCaps(address[],uint256[])\":{\"params\":{\"mTokens\":\"The addresses of the markets (tokens) to change the supply caps for\",\"newSupplyCaps\":\"The new supply cap values in underlying to be set. A value of 0 corresponds to unlimited supplying.\"}},\"setOutflowTimeLimitInUSD(uint256)\":{\"details\":\"when 0, it means there's no limit\",\"params\":{\"amount\":\"The new limit\"}},\"setOutflowVolumeTimeWindow(uint256)\":{\"params\":{\"newTimeWindow\":\"The new reset time window\"}},\"setPaused(address,uint8,bool)\":{\"params\":{\"_type\":\"The pause operation type\",\"mToken\":\"The market token address\",\"state\":\"The pause operation status\"}},\"setPriceOracle(address)\":{\"details\":\"Admin function to set a new price oracle\"},\"setRewardDistributor(address)\":{\"params\":{\"newRewardDistributor\":\"The address of the new Reward Distributor\"}},\"setRolesOperator(address)\":{\"details\":\"Admin function to set a new operator\"},\"setWhitelistedUser(address,bool)\":{\"params\":{\"state\":\"The new staate\",\"user\":\"The user address\"}},\"supportMarket(address)\":{\"details\":\"Admin function to set isListed and add support for the market\",\"params\":{\"mToken\":\"The address of the market (token) to list\"}},\"transferOwnership(address)\":{\"details\":\"Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner.\"}},\"version\":1},\"userdoc\":{\"events\":{\"ActionPaused(address,uint8,bool)\":{\"notice\":\"Emitted when pause status is changed\"},\"MarketEntered(address,address)\":{\"notice\":\"Emitted when an account enters a market\"},\"MarketExited(address,address)\":{\"notice\":\"Emitted when an account exits a market\"},\"MarketListed(address)\":{\"notice\":\"Emitted when an admin supports a market\"},\"NewBorrowCap(address,uint256)\":{\"notice\":\"Emitted when borrow cap for a mToken is changed\"},\"NewCloseFactor(uint256,uint256)\":{\"notice\":\"Emitted Emitted when close factor is changed by admin\"},\"NewCollateralFactor(address,uint256,uint256)\":{\"notice\":\"Emitted when a collateral factor is changed by admin\"},\"NewLiquidationIncentive(address,uint256,uint256)\":{\"notice\":\"Emitted when liquidation incentive is changed by admin\"},\"NewPriceOracle(address,address)\":{\"notice\":\"Emitted when price oracle is changed\"},\"NewRewardDistributor(address,address)\":{\"notice\":\"Emitted when reward distributor is changed\"},\"NewRolesOperator(address,address)\":{\"notice\":\"Event emitted when rolesOperator is changed\"},\"NewSupplyCap(address,uint256)\":{\"notice\":\"Emitted when supply cap for a mToken is changed\"},\"OutflowLimitUpdated(address,uint256,uint256)\":{\"notice\":\"Event emitted when outflow limit is updated\"},\"OutflowTimeWindowUpdated(uint256,uint256)\":{\"notice\":\"Event emitted when outflow reset time window is updated\"},\"OutflowVolumeReset()\":{\"notice\":\"Event emitted when outflow volume has been reset\"},\"UserWhitelisted(address,bool)\":{\"notice\":\"Emitted when user whitelist status is changed\"}},\"kind\":\"user\",\"methods\":{\"accountAssets(address,uint256)\":{\"notice\":\"Per-account mapping of \\\"assets you are in\\\", capped by maxAssets\"},\"afterMTokenMint(address)\":{\"notice\":\"Validates mint and reverts on rejection. May emit logs.\"},\"allMarkets(uint256)\":{\"notice\":\"A list of all markets\"},\"beforeMTokenBorrow(address,address,uint256)\":{\"notice\":\"Checks if the account should be allowed to borrow the underlying asset of the given market\"},\"beforeMTokenLiquidate(address,address,address,uint256)\":{\"notice\":\"Checks if the liquidation should be allowed to occur\"},\"beforeMTokenMint(address,address)\":{\"notice\":\"Checks if the account should be allowed to mint tokens in the given market\"},\"beforeMTokenRedeem(address,address,uint256)\":{\"notice\":\"Checks if the account should be allowed to redeem tokens in the given market\"},\"beforeMTokenRepay(address,address)\":{\"notice\":\"Checks if the account should be allowed to repay a borrow in the given market\"},\"beforeMTokenSeize(address,address,address,address)\":{\"notice\":\"Checks if the seizing of assets should be allowed to occur\"},\"beforeMTokenTransfer(address,address,address,uint256)\":{\"notice\":\"Checks if the account should be allowed to transfer tokens in the given market\"},\"beforeRebalancing(address)\":{\"notice\":\"Checks if the account should be allowed to rebalance tokens\"},\"blacklistOperator()\":{\"notice\":\"Blacklist\"},\"borrowCaps(address)\":{\"notice\":\"Borrow caps enforced by borrowAllowed for each mToken address. Defaults to zero which corresponds to unlimited borrowing.\"},\"checkMembership(address,address)\":{\"notice\":\"Returns whether the given account is entered in the given asset\"},\"checkOutflowVolumeLimit(uint256)\":{\"notice\":\"Verifies outflow volule limit\"},\"claimMalda(address)\":{\"notice\":\"Claim all the MALDA accrued by holder in all markets\"},\"claimMalda(address,address[])\":{\"notice\":\"Claim all the MALDA accrued by holder in the specified markets\"},\"claimMalda(address[],address[],bool,bool)\":{\"notice\":\"Claim all MALDA accrued by the holders\"},\"closeFactorMantissa()\":{\"notice\":\"Multiplier used to calculate the maximum repayAmount when liquidating a borrow\"},\"cumulativeOutflowVolume()\":{\"notice\":\"Should return outflow volume\"},\"disableWhitelist()\":{\"notice\":\"Disable user whitelist\"},\"enableWhitelist()\":{\"notice\":\"Enable user whitelist\"},\"enterMarkets(address[])\":{\"notice\":\"Add assets to be included in account liquidity calculation\"},\"enterMarketsWithSender(address)\":{\"notice\":\"Add asset (msg.sender) to be included in account liquidity calculation\"},\"exitMarket(address)\":{\"notice\":\"Removes asset from sender's account liquidity calculation\"},\"getAccountLiquidity(address)\":{\"notice\":\"Determine the current account liquidity wrt collateral requirements\"},\"getAllMarkets()\":{\"notice\":\"A list of all markets\"},\"getAssetsIn(address)\":{\"notice\":\"Returns the assets an account has entered\"},\"getHypotheticalAccountLiquidity(address,address,uint256,uint256)\":{\"notice\":\"Determine what the account liquidity would be if the given amounts were redeemed/borrowed\"},\"getUSDValueForAllMarkets()\":{\"notice\":\"Returns USD value for all markets\"},\"isDeprecated(address)\":{\"notice\":\"Returns true if the given mToken market has been deprecated\"},\"isMarketListed(address)\":{\"notice\":\"Returns true/false\"},\"isOperator()\":{\"notice\":\"Should return true\"},\"isPaused(address,uint8)\":{\"notice\":\"Returns if operation is paused\"},\"lastOutflowResetTimestamp()\":{\"notice\":\"Should return last reset time for outflow check\"},\"limitPerTimePeriod()\":{\"notice\":\"Should return outflow limit\"},\"liquidateCalculateSeizeTokens(address,address,uint256)\":{\"notice\":\"Calculate number of tokens of collateral asset to seize given an underlying amount\"},\"liquidationIncentiveMantissa(address)\":{\"notice\":\"Multiplier representing the discount on collateral that a liquidator receives\"},\"markets(address)\":{\"notice\":\"Official mapping of mTokens -> Market metadata\"},\"oracleOperator()\":{\"notice\":\"Oracle which gives the price of any given asset\"},\"outflowResetTimeWindow()\":{\"notice\":\"Should return the outflow volume time window\"},\"resetOutflowVolume()\":{\"notice\":\"Resets outflow volume\"},\"rewardDistributor()\":{\"notice\":\"Reward Distributor to markets supply and borrow (including protocol token)\"},\"rolesOperator()\":{\"notice\":\"Roles\"},\"setCloseFactor(uint256)\":{\"notice\":\"Sets the closeFactor used when liquidating borrows\"},\"setCollateralFactor(address,uint256)\":{\"notice\":\"Sets the collateralFactor for a market\"},\"setLiquidationIncentive(address,uint256)\":{\"notice\":\"Sets liquidationIncentive\"},\"setMarketBorrowCaps(address[],uint256[])\":{\"notice\":\"Set the given borrow caps for the given mToken markets. Borrowing that brings total borrows to or above borrow cap will revert.\"},\"setMarketSupplyCaps(address[],uint256[])\":{\"notice\":\"Set the given supply caps for the given mToken markets. Supplying that brings total supply to or above supply cap will revert.\"},\"setOutflowTimeLimitInUSD(uint256)\":{\"notice\":\"Sets outflow volume limit\"},\"setOutflowVolumeTimeWindow(uint256)\":{\"notice\":\"Sets outflow volume time window\"},\"setPaused(address,uint8,bool)\":{\"notice\":\"Set pause for a specific operation\"},\"setPriceOracle(address)\":{\"notice\":\"Sets a new price oracle\"},\"setRewardDistributor(address)\":{\"notice\":\"Admin function to change the Reward Distributor\"},\"setRolesOperator(address)\":{\"notice\":\"Sets a new Operator for the market\"},\"setWhitelistedUser(address,bool)\":{\"notice\":\"Sets user whitelist status\"},\"supplyCaps(address)\":{\"notice\":\"Supply caps enforced by supplyAllowed for each mToken address. Defaults to zero which corresponds to unlimited supplying.\"},\"supportMarket(address)\":{\"notice\":\"Add the market to the markets mapping and set it as listed\"},\"userWhitelisted(address)\":{\"notice\":\"Returns true/false for user\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"test/unit/oracle/OracleUnderlying.t.sol\":\"MixedPriceOracleV3_Test\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"lib/forge-std/src/Base.sol\":{\"keccak256\":\"0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d\",\"dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z\"]},\"lib/forge-std/src/StdAssertions.sol\":{\"keccak256\":\"0x4584f551c3a875c26423c1e50a77179bc2f9c9c1ee6d0f0c6be0f89ca5ee8270\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ef118876a862b60ba8a6612b8b3f3a6c226e11a0071a2e9695f021586d1bfdbe\",\"dweb:/ipfs/QmXd2vD91PkvoFWasFkgbDi89PctmgsHVxGvGNyhjmRK2b\"]},\"lib/forge-std/src/StdChains.sol\":{\"keccak256\":\"0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351\",\"dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm\"]},\"lib/forge-std/src/StdCheats.sol\":{\"keccak256\":\"0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41\",\"dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK\"]},\"lib/forge-std/src/StdConstants.sol\":{\"keccak256\":\"0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc\",\"dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r\"]},\"lib/forge-std/src/StdError.sol\":{\"keccak256\":\"0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6\",\"dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj\"]},\"lib/forge-std/src/StdInvariant.sol\":{\"keccak256\":\"0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391\",\"dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5\"]},\"lib/forge-std/src/StdJson.sol\":{\"keccak256\":\"0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974\",\"dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3\"]},\"lib/forge-std/src/StdMath.sol\":{\"keccak256\":\"0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92\",\"dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC\"]},\"lib/forge-std/src/StdStorage.sol\":{\"keccak256\":\"0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57\",\"dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ\"]},\"lib/forge-std/src/StdStyle.sol\":{\"keccak256\":\"0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8\",\"dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK\"]},\"lib/forge-std/src/StdToml.sol\":{\"keccak256\":\"0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3\",\"dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8\"]},\"lib/forge-std/src/StdUtils.sol\":{\"keccak256\":\"0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138\",\"dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776\"]},\"lib/forge-std/src/Test.sol\":{\"keccak256\":\"0x3dda6083a83dfa3e8526e97bcc28e862ee2442dd58fe94d5c426d65b8e38f73c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://33f8c02e4dabdab86a6825125856446a8657eacd712318b51b7818e4a6f6e3f4\",\"dweb:/ipfs/QmRKSjVnrk54yr8wTK2e6QxRjiuba2H8HJSKunHAkdo7RG\"]},\"lib/forge-std/src/Vm.sol\":{\"keccak256\":\"0xaae88142a348064677cf81169ea0ca706be3e99e2a0f13a01878ff8d52cf6b25\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://da4fb0ed1b31381c7e60ee386acc72796e8a8325c83739ec9a3678e2f841073c\",\"dweb:/ipfs/Qmb4MU6FpAi8Wt5kUQ3wa7ooHnfMVbdd9zuvnqbTehLmxJ\"]},\"lib/forge-std/src/console.sol\":{\"keccak256\":\"0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57\",\"dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP\"]},\"lib/forge-std/src/console2.sol\":{\"keccak256\":\"0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d\",\"dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ\"]},\"lib/forge-std/src/interfaces/IMulticall3.sol\":{\"keccak256\":\"0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0\",\"dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2\"]},\"lib/forge-std/src/safeconsole.sol\":{\"keccak256\":\"0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab\",\"dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol\":{\"keccak256\":\"0xc163fcf9bb10138631a9ba5564df1fa25db9adff73bd9ee868a8ae1858fe093a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9706d43a0124053d9880f6e31a59f31bc0a6a3dc1acd66ce0a16e1111658c5f6\",\"dweb:/ipfs/QmUFmfowzkRwGtDu36cXV9SPTBHJ3n7dG9xQiK5B28jTf2\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol\":{\"keccak256\":\"0x631188737069917d2f909d29ce62c4d48611d326686ba6683e26b72a23bfac0b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7a61054ae84cd6c4d04c0c4450ba1d6de41e27e0a2c4f1bcdf58f796b401c609\",\"dweb:/ipfs/QmUvtdp7X1mRVyC3CsHrtPbgoqWaXHp3S1ZR24tpAQYJWM\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol\":{\"keccak256\":\"0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9\",\"dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/ReentrancyGuardUpgradeable.sol\":{\"keccak256\":\"0xb44e086e941292cdc7f440de51478493894ef0b1aeccb0c4047445919f667f74\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://942dad22fbdc1669f025540ba63aa3ccfad5f8458fc5d4525b31ebf272e7af45\",\"dweb:/ipfs/Qmdo4X2M82aM3AMoW2kf2jhYkSCyC4T1pHNd6obdsDFnAB\"]},\"lib/openzeppelin-contracts/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol\":{\"keccak256\":\"0x60c65f701957fdd6faea1acb0bb45825791d473693ed9ecb34726fdfaa849dd7\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ea290300e0efc4d901244949dc4d877fd46e6c5e43dc2b26620e8efab3ab803f\",\"dweb:/ipfs/QmcLLJppxKeJWqHxE2CUkcfhuRTgHSn8J4kijcLa5MYhSt\"]},\"lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol\":{\"keccak256\":\"0xbfb6695731de677140fbf76c772ab08c4233a122fb51ac28ac120fc49bbbc4ec\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://68f8fded7cc318efa15874b7c6a983fe17a4a955d72d240353a9a4ca1e1b824c\",\"dweb:/ipfs/QmdcmBL9Qo4Tk3Dby4wFYabGyot9JNeLPxpSXZUgUm92BV\"]},\"lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Utils.sol\":{\"keccak256\":\"0x06a78f9b3ee3e6d0eb4e4cd635ba49960bea34cac1db8c0a27c75f2319f1fd65\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://547d21aa17f4f3f1a1a7edf7167beff8dd9496a0348d5588f15cc8a4b29d052a\",\"dweb:/ipfs/QmT16JtRQSWNpLo9W23jr6CzaMuTAcQcjJJcdRd8HLJ6cE\"]},\"lib/openzeppelin-contracts/contracts/proxy/Proxy.sol\":{\"keccak256\":\"0xc3f2ec76a3de8ed7a7007c46166f5550c72c7709e3fc7e8bb3111a7191cdedbd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e73efb4c2ca655882dc237c6b4f234a9bd36d97159d8fcaa837eb01171f726ac\",\"dweb:/ipfs/QmTNnnv7Gu5fs5G1ZMh7Fexp8N4XUs3XrNAngjcxgiss3e\"]},\"lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol\":{\"keccak256\":\"0xc59a78b07b44b2cf2e8ab4175fca91e8eca1eee2df7357b8d2a8833e5ea1f64c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5aa4f07e65444784c29cd7bfcc2341b34381e4e5b5da9f0c5bd00d7f430e66fa\",\"dweb:/ipfs/QmWRMh4Q9DpaU9GvsiXmDdoNYMyyece9if7hnfLz7uqzWM\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol\":{\"keccak256\":\"0xc3e1fa9d1987f8d349dfb4d6fe93bf2ca014b52ba335cfac30bfe71e357e6f80\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c5703ccdeb7b1d685e375ed719117e9edf2ab4bc544f24f23b0d50ec82257229\",\"dweb:/ipfs/QmTdwkbQq7owpCiyuzE7eh5LrD2ddrBCZ5WHVsWPi1RrTS\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xc6a8ff0ea489379b61faa647490411b80102578440ab9d84e9a957cc12164e70\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0ea104e577e63faea3b69c415637e99e755dcbf64c5833d7140c35a714d6d90c\",\"dweb:/ipfs/Qmau6x4Ns9XdyynRCNNp3RhLqijJjFm7z5fyZazfYFGYdq\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xaa761817f6cd7892fcf158b3c776b34551cde36f48ff9703d53898bc45a94ea2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0ad7c8d4d08938c8dfc43d75a148863fb324b80cf53e0a36f7e5a4ac29008850\",\"dweb:/ipfs/QmcrhfPgVNf5mkdhQvy1pMv51TFokD3Y4Wa5WZhFqVh8UV\"]},\"lib/openzeppelin-contracts/contracts/utils/Address.sol\":{\"keccak256\":\"0xaf28a975a78550e45f65e559a3ad6a5ad43b9b8a37366999abd1b7084eb70721\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b7bd24e224f67f65bfadf85dc2929fa965456bb2415478bd0125471b5ce35245\",\"dweb:/ipfs/QmRaydGr8BTHs1kvaZfsNU69pKzUAGFrvABn1KiRSbE51y\"]},\"lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol\":{\"keccak256\":\"0x32ba59b4b7299237c8ba56319110989d7978a039faf754793064e967e5894418\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1ae50c8b562427df610cc4540c9bf104acca7ef8e2dcae567ae7e52272281e9c\",\"dweb:/ipfs/QmTHiadFCSJUPpRjNegc5SahmeU8bAoY8i9Aq6tVscbcKR\"]},\"src/Operator/Operator.sol\":{\"keccak256\":\"0x1486ed6af6f40f0e045403ac9e35bc045ae7031d9ecac7d1f0358f78ff83b469\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://e136048d46ae9b4384f561e16dfd4638e68db987007d2059aaf19011dcfc8f65\",\"dweb:/ipfs/QmcyrVa45W2qYtd9UuJz9C89M3xKzKzACDQqVMAk4hvPeq\"]},\"src/Operator/OperatorStorage.sol\":{\"keccak256\":\"0x3d9e7f2bc64ab9b5de39d4a58f0a72ac0f86c3b95a6318dd03a7b8acde166783\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://dc0d5e026e00f5f5f92cded7704bb602b06f387370ddd7ee0d75111099b01f3a\",\"dweb:/ipfs/QmQfZzADQpBuvTHJpbHz6P4fGhaGWvQgkU8qMkKULCLdjQ\"]},\"src/Roles.sol\":{\"keccak256\":\"0xaa5bbf73d5d2ba654b7f0fe960ae9e17282d959fc559d09c2222b78dad1911b0\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://e683df4c25f2f8f725915d68e8ee9e0871c0d93ad592b0ca985b1829e460cabc\",\"dweb:/ipfs/QmTsQgUKXCtzucNF9DSq1wKVfeqSUeoKPMFCkM8hNLxTmV\"]},\"src/blacklister/Blacklister.sol\":{\"keccak256\":\"0xfa0b52f7467330267cf6ed2d17615b56a75a8a477a5a6c7670ebd0f4636a4e7c\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://d4b9df3910bdba55615acdf7fbef0772b4a7746d9f5f2c01b3ac41b42bd5d6c4\",\"dweb:/ipfs/QmWbo7tK6A57dQiWzX5rgWuQTXo1EGuTvNmG57A93rJHuA\"]},\"src/interest/JumpRateModelV4.sol\":{\"keccak256\":\"0xa93081b79a997a666c96497334d721b37abfd0f193c1031e9f7f2e1b0823c37b\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://b640c34eedc95215fc74b43637256f52acb95052a946a952402b18516a67f7d5\",\"dweb:/ipfs/QmeSemgHAutRfCAttB1mTQ2AYXQpRj1Eq7haGS8QgnTHUa\"]},\"src/interfaces/IBlacklister.sol\":{\"keccak256\":\"0x47ed20135507a9d16473027cbee18de5d952846bada761c6248b91c75cd88be1\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://91a886b95dd578156f9b6cec3236e4751aae8a38e5d15e0d6045217c6fbd3d63\",\"dweb:/ipfs/QmRNjz7sGTuMkQNZhwwcg5DNN2eeqTZSAdZmxWxbx66CHU\"]},\"src/interfaces/IDefaultAdapter.sol\":{\"keccak256\":\"0xbf7e882eeb81776c7be55110bb171c65d166bafeb71d828c085b139bed5735c8\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://7e139fb3ddd0623189493679e73fd42c4e505531502d55e1e699789fa3c1451a\",\"dweb:/ipfs/Qma3XsUVPffiGXZ7epTqMyNJKuh87xrFhqCTwQXznEccU6\"]},\"src/interfaces/IInterestRateModel.sol\":{\"keccak256\":\"0xdc8386be37a06d93fb0967be32e465f3b1f941ec9ce2f90984a5c70488cddb1e\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://de2bf96a7d0945f58b00e9fc867ac00fc4a09480e9e27c1a800604295320b769\",\"dweb:/ipfs/Qmf5sAgA58GjVxeipJNhP4euxwsZ9FS9CgsWTJ319UmCUE\"]},\"src/interfaces/IOperator.sol\":{\"keccak256\":\"0x0ae3d63eb07518da5a4fd6de7b013b6f55c87defb31900548378cffc70a51f72\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://1d9710c4ac43cb0563449aa519501b4ed18a331c90d0bc7eb79cdc56407bad8a\",\"dweb:/ipfs/Qmafe9Wup2GF9pD83CLWUrRait93vwvReqU1C7EHQhY7WV\"]},\"src/interfaces/IOracleOperator.sol\":{\"keccak256\":\"0xcc2848c75dd5e1531c2b958746cdd76a3b2b329be98fdca4311219a12a6b409e\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://337bdabf03ac279d95ba96aca60105932c7af5c15fdb4b21ab1c369509310fbc\",\"dweb:/ipfs/QmNYrNYNUwWWSrPwHdarhxt1ShdH1dy8B94M49TZ7XvmK1\"]},\"src/interfaces/IRewardDistributor.sol\":{\"keccak256\":\"0xd39c4db6e2a85e90db82e20685d4b235966d993c3407e1c0fb52add6421b86df\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://e865fe1569ec1ac79cd924a9aa2e2b43754555e510526f6cd030395e9923f01d\",\"dweb:/ipfs/QmXcshgzCxLKBZJYGuQviEamruhmwXWNJ3ZYLxazWBahuc\"]},\"src/interfaces/IRoles.sol\":{\"keccak256\":\"0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c\",\"dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj\"]},\"src/interfaces/ImToken.sol\":{\"keccak256\":\"0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36\",\"dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL\"]},\"src/interfaces/external/poh/IPohVerifier.sol\":{\"keccak256\":\"0xce93e2d734074add2a6d36867d59fd24e2d44ca48c524bd8dddabd4163038205\",\"license\":\"AGPL-3.0\",\"urls\":[\"bzz-raw://9e94dc14721e8873d15d7032afa3d2b5fea76e9c4029698aa489f2ea2edcedf6\",\"dweb:/ipfs/QmR55sQx36Ub7wUjDeemUSNotdmYCkK8AmUEYbRezF5Xhy\"]},\"src/oracles/MixedPriceOracleV3.sol\":{\"keccak256\":\"0x2a12b509ba518e116b6c1136575e04a2c2bfdd5402730f6746cfabbbe5e6549e\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://2417f80a6dfc814e73df5362226cd89db15ea2314effd80dd16bcb87595ea755\",\"dweb:/ipfs/QmcRPazSFBvLKUqo3SCYhNkzuDuQZHWcWjwTzxV88E6own\"]},\"src/rewards/RewardDistributor.sol\":{\"keccak256\":\"0x8d5c3e5e5050121d4f1310479a2cadde7dc97ff8a57115021cafb2032aaf50c2\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://e200d18803bb0680b3007c79c23a8a7e31f054ce6e4f36c376f7b43419679322\",\"dweb:/ipfs/QmV7CRA5HSB89fwCVF7VWGArZnWoH2BB1heXu9SaMkbL9H\"]},\"src/utils/ExponentialNoError.sol\":{\"keccak256\":\"0xa97178117d8c32059d55dd2224e5514c54b31f896e565c3834cab3d7502b1bb6\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://1cecc0713709f57a5bb7f64d2fa06b9437a42752fa3fed5d3c0b19d82aa56ef4\",\"dweb:/ipfs/QmQqBCZkzmZxomXLr3b1tHUV55n8EPk3wAUsXgojYSpR41\"]},\"test/Base_Unit_Test.t.sol\":{\"keccak256\":\"0x2f61ec9614bbfeed1c1f8555f20fb0d4c584dc3452369b53417ab3fba144f330\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://0f7a32bd42554d42a3062092b80a6a0fb90bea17a3cad2c12863688b811e6b4c\",\"dweb:/ipfs/QmZNFWWTyTFEjSErQAErkmEdzAPf2Eyeb2ir4fgRqQ5bKJ\"]},\"test/mocks/ERC20Mock.sol\":{\"keccak256\":\"0x86e2d4e622d3172aff95ad8436b1af1213c7be008fbbe1e74f29577ea0afd92f\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://2e2a534d2bf62fd7be511ac74e372b1d1e4cc08033fba742950785315553e3fb\",\"dweb:/ipfs/Qme24GSF4BFzuHETCfPzUafV1bsmVHVvbqQPNxDoU8AafR\"]},\"test/mocks/OracleMock.sol\":{\"keccak256\":\"0xb749ba9d217b9aedc9010690022b31672eeacf0927f43efe9769ae886f4ce855\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://5167ca5dcd31bc0ae6dcc22e43276e8f746b018d7d9c2edc332d1160024f6328\",\"dweb:/ipfs/QmVdgBmaBntj9TMV4z65bga1uuN2YkfUbLNN9Hsmvu4x6f\"]},\"test/unit/oracle/OracleUnderlying.t.sol\":{\"keccak256\":\"0xea57d5cab5b035dcaeb069acd24fbcbb688c67984b88a316fadef1e08e9559a0\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://2119fb80a4cf3d696f6e51e020dcad0080fe4dd028e04184116f50dbb76210a0\",\"dweb:/ipfs/QmaFw7HyXE2gH47hYkCy7Wyd1EUUrMoPSC6NKzN5ZMRrsv\"]},\"test/utils/Constants.sol\":{\"keccak256\":\"0xa2611aa14c45b8ea8b276aacad47d78f33908fab8c6ed0ff35cef76fd41c695b\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://f60db39c1ce5c7260361664fec9d731d3ba329055a0810d83491ce54ad7d2a01\",\"dweb:/ipfs/QmSzv3VXBa6q6bowzAfZ4Afcp4UWwGUKJFB72xV6MYyCNn\"]},\"test/utils/Events.sol\":{\"keccak256\":\"0xb0b41707dca3af9d783239cb5c96a2e9347e03b5529c944565ac9de2f33ae82a\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://e8fad0826e747465c9208ad6a7d52cd50205972cfea7fa8a206be750cf1e8a80\",\"dweb:/ipfs/QmR5mWoVf2ZcETLJVuCMHvWPBfQ3CNxD8Gx8Endms5AwmR\"]},\"test/utils/Helpers.sol\":{\"keccak256\":\"0xa59b1e23b76c632e72c93dbd612c9279b2cad6d8915c31c04e62af0d46becf4d\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://2a7d815eeebeea85ec405483ec6d55a61f1a984d68d3a6020d3444915aa6610c\",\"dweb:/ipfs/QmQ6qWmTAdWnnursoU4F2pYCa3tpTtS2qjPFht1kWT2KDT\"]},\"test/utils/Types.sol\":{\"keccak256\":\"0x696166d23b74196cb6a66bbd72f25024bb251be99ab2a6d8c9ba86f5b47f22d6\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://a302c977aea4ccbc54de408575dd5b52b00b9d62512da0d7eb71edb46eff1366\",\"dweb:/ipfs/QmUjRq9fjukqZL59ABU2Xp6KfR21sPvdBVcWWzjrMLxpzP\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "InvalidInitialization"}, {"inputs": [], "type": "error", "name": "NotInitializing"}, {"inputs": [], "type": "error", "name": "Operator_AssetNotFound"}, {"inputs": [], "type": "error", "name": "Operator_Deactivate_MarketBalanceOwed"}, {"inputs": [], "type": "error", "name": "Operator_EmptyPrice"}, {"inputs": [], "type": "error", "name": "Operator_InsufficientLiquidity"}, {"inputs": [], "type": "error", "name": "Operator_InvalidBlacklistOperator"}, {"inputs": [], "type": "error", "name": "Operator_InvalidCollateralFactor"}, {"inputs": [], "type": "error", "name": "Operator_InvalidInput"}, {"inputs": [], "type": "error", "name": "Operator_InvalidRewardDistributor"}, {"inputs": [], "type": "error", "name": "Operator_InvalidRolesOperator"}, {"inputs": [], "type": "error", "name": "Operator_MarketAlreadyListed"}, {"inputs": [], "type": "error", "name": "Operator_MarketBorrowCapReached"}, {"inputs": [], "type": "error", "name": "Operator_MarketNotListed"}, {"inputs": [], "type": "error", "name": "Operator_MarketSupplyReached"}, {"inputs": [], "type": "error", "name": "Operator_Mismatch"}, {"inputs": [], "type": "error", "name": "Operator_OnlyAdmin"}, {"inputs": [], "type": "error", "name": "Operator_OnlyAdminOrRole"}, {"inputs": [], "type": "error", "name": "Operator_OracleUnderlyingFetchError"}, {"inputs": [], "type": "error", "name": "Operator_OutflowVolumeReached"}, {"inputs": [], "type": "error", "name": "Operator_Paused"}, {"inputs": [], "type": "error", "name": "Operator_PriceFetchFailed"}, {"inputs": [], "type": "error", "name": "Operator_RepayAmountNotValid"}, {"inputs": [], "type": "error", "name": "Operator_RepayingTooMuch"}, {"inputs": [], "type": "error", "name": "Operator_SenderMustBeToken"}, {"inputs": [], "type": "error", "name": "Operator_UserBlacklisted"}, {"inputs": [], "type": "error", "name": "Operator_UserNotWhitelisted"}, {"inputs": [], "type": "error", "name": "Operator_WrongMarket"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "type": "error", "name": "OwnableInvalidOwner"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "type": "error", "name": "OwnableUnauthorizedAccount"}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address", "indexed": true}, {"internalType": "enum ImTokenOperationTypes.OperationType", "name": "_type", "type": "uint8", "indexed": false}, {"internalType": "bool", "name": "state", "type": "bool", "indexed": false}], "type": "event", "name": "ActionPaused", "anonymous": false}, {"inputs": [{"internalType": "uint64", "name": "version", "type": "uint64", "indexed": false}], "type": "event", "name": "Initialized", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address", "indexed": true}, {"internalType": "address", "name": "account", "type": "address", "indexed": true}], "type": "event", "name": "MarketEntered", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address", "indexed": true}, {"internalType": "address", "name": "account", "type": "address", "indexed": true}], "type": "event", "name": "MarketExited", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address", "indexed": false}], "type": "event", "name": "MarketListed", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "newBorrowCap", "type": "uint256", "indexed": false}], "type": "event", "name": "NewBorrowCap", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "oldCloseFactorMantissa", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "newCloseFactorMantissa", "type": "uint256", "indexed": false}], "type": "event", "name": "NewCloseFactor", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "oldCollateralFactorMantissa", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "newCollateralFactorMantissa", "type": "uint256", "indexed": false}], "type": "event", "name": "NewCollateralFactor", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "market", "type": "address", "indexed": false}, {"internalType": "uint256", "name": "oldLiquidationIncentiveMantissa", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "newLiquidationIncentiveMantissa", "type": "uint256", "indexed": false}], "type": "event", "name": "NewLiquidationIncentive", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "oldPriceOracle", "type": "address", "indexed": true}, {"internalType": "address", "name": "newPriceOracle", "type": "address", "indexed": true}], "type": "event", "name": "NewPriceOracle", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "oldRewardDistributor", "type": "address", "indexed": true}, {"internalType": "address", "name": "newRewardDistributor", "type": "address", "indexed": true}], "type": "event", "name": "NewRewardDistributor", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "oldRoles", "type": "address", "indexed": true}, {"internalType": "address", "name": "newRoles", "type": "address", "indexed": true}], "type": "event", "name": "NewRolesOperator", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "newBorrowCap", "type": "uint256", "indexed": false}], "type": "event", "name": "NewSupplyCap", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "oldLimit", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "newLimit", "type": "uint256", "indexed": false}], "type": "event", "name": "OutflowLimitUpdated", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "oldWindow", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "newWindow", "type": "uint256", "indexed": false}], "type": "event", "name": "OutflowTimeWindowUpdated", "anonymous": false}, {"inputs": [], "type": "event", "name": "OutflowVolumeReset", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "previousOwner", "type": "address", "indexed": true}, {"internalType": "address", "name": "new<PERSON>wner", "type": "address", "indexed": true}], "type": "event", "name": "OwnershipTransferred", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "user", "type": "address", "indexed": true}, {"internalType": "bool", "name": "state", "type": "bool", "indexed": false}], "type": "event", "name": "<PERSON>r<PERSON><PERSON><PERSON><PERSON>", "anonymous": false}, {"inputs": [], "type": "event", "name": "WhitelistDisabled", "anonymous": false}, {"inputs": [], "type": "event", "name": "WhitelistEnabled", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "", "type": "string", "indexed": false}], "type": "event", "name": "log", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "", "type": "address", "indexed": false}], "type": "event", "name": "log_address", "anonymous": false}, {"inputs": [{"internalType": "uint256[]", "name": "val", "type": "uint256[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "int256[]", "name": "val", "type": "int256[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "address[]", "name": "val", "type": "address[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes", "indexed": false}], "type": "event", "name": "log_bytes", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32", "indexed": false}], "type": "event", "name": "log_bytes32", "anonymous": false}, {"inputs": [{"internalType": "int256", "name": "", "type": "int256", "indexed": false}], "type": "event", "name": "log_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "address", "name": "val", "type": "address", "indexed": false}], "type": "event", "name": "log_named_address", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256[]", "name": "val", "type": "uint256[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256[]", "name": "val", "type": "int256[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "address[]", "name": "val", "type": "address[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "bytes", "name": "val", "type": "bytes", "indexed": false}], "type": "event", "name": "log_named_bytes", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "bytes32", "name": "val", "type": "bytes32", "indexed": false}], "type": "event", "name": "log_named_bytes32", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256", "name": "val", "type": "int256", "indexed": false}, {"internalType": "uint256", "name": "decimals", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_decimal_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256", "name": "val", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "decimals", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_decimal_uint", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256", "name": "val", "type": "int256", "indexed": false}], "type": "event", "name": "log_named_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "string", "name": "val", "type": "string", "indexed": false}], "type": "event", "name": "log_named_string", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256", "name": "val", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_uint", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "", "type": "string", "indexed": false}], "type": "event", "name": "log_string", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256", "indexed": false}], "type": "event", "name": "log_uint", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes", "indexed": false}], "type": "event", "name": "logs", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "IS_TEST", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "accountAssets", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}], "stateMutability": "view", "type": "function", "name": "afterMTokenMint"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "allMarkets", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}, {"internalType": "address", "name": "borrower", "type": "address"}, {"internalType": "uint256", "name": "borrowAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "beforeMTokenBorrow"}, {"inputs": [{"internalType": "address", "name": "mTokenBorrowed", "type": "address"}, {"internalType": "address", "name": "mTokenCollateral", "type": "address"}, {"internalType": "address", "name": "borrower", "type": "address"}, {"internalType": "uint256", "name": "repayAmount", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "beforeMTokenLiquidate"}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}, {"internalType": "address", "name": "minter", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "beforeMTokenMint"}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}, {"internalType": "address", "name": "redeemer", "type": "address"}, {"internalType": "uint256", "name": "redeemTokens", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "beforeMTokenRedeem"}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}, {"internalType": "address", "name": "borrower", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "beforeMTokenRepay"}, {"inputs": [{"internalType": "address", "name": "mTokenCollateral", "type": "address"}, {"internalType": "address", "name": "mTokenBorrowed", "type": "address"}, {"internalType": "address", "name": "liquidator", "type": "address"}, {"internalType": "address", "name": "borrower", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "beforeMTokenSeize"}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}, {"internalType": "address", "name": "src", "type": "address"}, {"internalType": "address", "name": "dst", "type": "address"}, {"internalType": "uint256", "name": "transferTokens", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "beforeMTokenTransfer"}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}], "stateMutability": "view", "type": "function", "name": "beforeRebalancing"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "blacklistOperator", "outputs": [{"internalType": "contract IBlacklister", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "borrowCaps", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "address", "name": "mToken", "type": "address"}], "stateMutability": "view", "type": "function", "name": "checkMembership", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "checkOutflowVolumeLimit"}, {"inputs": [{"internalType": "address", "name": "holder", "type": "address"}, {"internalType": "address[]", "name": "mTokens", "type": "address[]"}], "stateMutability": "nonpayable", "type": "function", "name": "claimMalda"}, {"inputs": [{"internalType": "address[]", "name": "holders", "type": "address[]"}, {"internalType": "address[]", "name": "mTokens", "type": "address[]"}, {"internalType": "bool", "name": "borrowers", "type": "bool"}, {"internalType": "bool", "name": "suppliers", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "claimMalda"}, {"inputs": [{"internalType": "address", "name": "holder", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "claimMalda"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "closeFactorMantissa", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "cumulativeOutflowVolume", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "disable<PERSON><PERSON><PERSON><PERSON>"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"inputs": [{"internalType": "address[]", "name": "_mTokens", "type": "address[]"}], "stateMutability": "nonpayable", "type": "function", "name": "enterMarkets"}, {"inputs": [{"internalType": "address", "name": "_account", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "enterMarketsWithSender"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeArtifacts", "outputs": [{"internalType": "string[]", "name": "excludedArtifacts_", "type": "string[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeContracts", "outputs": [{"internalType": "address[]", "name": "excludedContracts_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzSelector[]", "name": "excludedSelectors_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeSenders", "outputs": [{"internalType": "address[]", "name": "excludedSenders_", "type": "address[]"}]}, {"inputs": [{"internalType": "address", "name": "_mToken", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "exitMarket"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "failed", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getAccountLiquidity", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getAllMarkets", "outputs": [{"internalType": "address[]", "name": "mTokens", "type": "address[]"}]}, {"inputs": [{"internalType": "address", "name": "_user", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getAssetsIn", "outputs": [{"internalType": "address[]", "name": "mTokens", "type": "address[]"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "address", "name": "mTokenModify", "type": "address"}, {"internalType": "uint256", "name": "redeemTokens", "type": "uint256"}, {"internalType": "uint256", "name": "borrowAmount", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getHypotheticalAccountLiquidity", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getUSDValueForAllMarkets", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "_rolesOperator", "type": "address"}, {"internalType": "address", "name": "_blacklistOperator", "type": "address"}, {"internalType": "address", "name": "_rewardDistributor", "type": "address"}, {"internalType": "address", "name": "_admin", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "initialize"}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isDeprecated", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isMarketListed", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "pure", "type": "function", "name": "isOperator", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}, {"internalType": "enum ImTokenOperationTypes.OperationType", "name": "_type", "type": "uint8"}], "stateMutability": "view", "type": "function", "name": "isPaused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "lastOutflowResetTimestamp", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "limitPerTimePeriod", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "mTokenBorrowed", "type": "address"}, {"internalType": "address", "name": "mTokenCollateral", "type": "address"}, {"internalType": "uint256", "name": "actualRepayAmount", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "liquidateCalculateSeizeTokens", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "liquidationIncentiveMantissa", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "markets", "outputs": [{"internalType": "bool", "name": "isListed", "type": "bool"}, {"internalType": "uint256", "name": "collateralFactorMantissa", "type": "uint256"}, {"internalType": "bool", "name": "isMalded", "type": "bool"}]}, {"inputs": [{"internalType": "uint256", "name": "usdPerQuotedToken", "type": "uint256"}, {"internalType": "uint256", "name": "usdPerBaseToken", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "newOracleInBase", "outputs": [{"internalType": "contract MockChainlinkOracle", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "uint256", "name": "usdPerToken", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "newUSDOracle", "outputs": [{"internalType": "contract MockChainlinkOracle", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "oracleOperator", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "outflowResetTimeWindow", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "renounceOwnership"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "resetOutflowVolume"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "rewardDistributor", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "rolesOperator", "outputs": [{"internalType": "contract IRoles", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "uint256", "name": "newCloseFactorMantissa", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "setCloseFactor"}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}, {"internalType": "uint256", "name": "newCollateralFactorMantissa", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "setCollateralFactor"}, {"inputs": [{"internalType": "address", "name": "market", "type": "address"}, {"internalType": "uint256", "name": "newLiquidationIncentiveMantissa", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "setLiquidationIncentive"}, {"inputs": [{"internalType": "address[]", "name": "mTokens", "type": "address[]"}, {"internalType": "uint256[]", "name": "newBorrowCaps", "type": "uint256[]"}], "stateMutability": "nonpayable", "type": "function", "name": "setMarketBorrowCaps"}, {"inputs": [{"internalType": "address[]", "name": "mTokens", "type": "address[]"}, {"internalType": "uint256[]", "name": "newSupplyCaps", "type": "uint256[]"}], "stateMutability": "nonpayable", "type": "function", "name": "setMarketSupplyCaps"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "setOutflowTimeLimitInUSD"}, {"inputs": [{"internalType": "uint256", "name": "newTimeWindow", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "setOutflowVolumeTimeWindow"}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}, {"internalType": "enum ImTokenOperationTypes.OperationType", "name": "_type", "type": "uint8"}, {"internalType": "bool", "name": "state", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "setPaused"}, {"inputs": [{"internalType": "address", "name": "newOracle", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setPriceO<PERSON>le"}, {"inputs": [{"internalType": "address", "name": "newRewardDistributor", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setRewardDistributor"}, {"inputs": [{"internalType": "address", "name": "_roles", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setRolesOperator"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "setUp"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "bool", "name": "state", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "setWhitelistedUser"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "supplyCaps", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "supportMarket"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetArtifactSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzArtifactSelector[]", "name": "targetedArtifactSelectors_", "type": "tuple[]", "components": [{"internalType": "string", "name": "artifact", "type": "string"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetArtifacts", "outputs": [{"internalType": "string[]", "name": "targetedArtifacts_", "type": "string[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetContracts", "outputs": [{"internalType": "address[]", "name": "targetedContracts_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetInterfaces", "outputs": [{"internalType": "struct StdInvariant.FuzzInterface[]", "name": "targetedInterfaces_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "string[]", "name": "artifacts", "type": "string[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzSelector[]", "name": "targetedSelectors_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetSenders", "outputs": [{"internalType": "address[]", "name": "targetedSenders_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "test_getPriceUSD"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "transferOwnership"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "user<PERSON><PERSON><PERSON>sted", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "whitelistEnabled", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}], "devdoc": {"kind": "dev", "methods": {"afterMTokenMint(address)": {"params": {"mToken": "Asset being minted"}}, "beforeMTokenBorrow(address,address,uint256)": {"params": {"borrowAmount": "The amount of underlying the account would borrow", "borrower": "The account which would borrow the asset", "mToken": "The market to verify the borrow against"}}, "beforeMTokenLiquidate(address,address,address,uint256)": {"params": {"borrower": "The address of the borrower", "mTokenBorrowed": "Asset which was borrowed by the borrower", "mTokenCollateral": "Asset which was used as collateral and will be seized", "repayAmount": "The amount of underlying being repaid"}}, "beforeMTokenMint(address,address)": {"params": {"mToken": "The market to verify the mint against", "minter": "The account which would get the minted tokens"}}, "beforeMTokenRedeem(address,address,uint256)": {"params": {"mToken": "The market to verify the redeem against", "redeemTokens": "The number of mTokens to exchange for the underlying asset in the market", "redeemer": "The account which would redeem the tokens"}}, "beforeMTokenRepay(address,address)": {"params": {"borrower": "The account which would borrowed the asset", "mToken": "The market to verify the repay against"}}, "beforeMTokenSeize(address,address,address,address)": {"params": {"borrower": "The address of the borrower", "liquidator": "The address repaying the borrow and seizing the collateral", "mTokenBorrowed": "Asset which was borrowed by the borrower", "mTokenCollateral": "Asset which was used as collateral and will be seized"}}, "beforeMTokenTransfer(address,address,address,uint256)": {"params": {"dst": "The account which receives the tokens", "mToken": "The market to verify the transfer against", "src": "The account which sources the tokens", "transferTokens": "The number of mTokens to transfer"}}, "beforeRebalancing(address)": {"params": {"mToken": "The market to verify the transfer against"}}, "checkMembership(address,address)": {"params": {"account": "The address of the account to check", "mToken": "The mToken to check"}, "returns": {"_0": "True if the account is in the asset, otherwise false."}}, "checkOutflowVolumeLimit(uint256)": {"params": {"amount": "The new limit"}}, "claimMalda(address)": {"params": {"holder": "The address to claim MALDA for"}}, "claimMalda(address,address[])": {"params": {"holder": "The address to claim MALDA for", "mTokens": "The list of markets to claim MALDA in"}}, "claimMalda(address[],address[],bool,bool)": {"params": {"borrowers": "Whether or not to claim MALDA earned by borrowing", "holders": "The addresses to claim MALDA for", "mTokens": "The list of markets to claim MALDA in", "suppliers": "Whether or not to claim MALDA earned by supplying"}}, "enterMarkets(address[])": {"params": {"_mTokens": "The list of addresses of the mToken markets to be enabled"}}, "enterMarketsWithSender(address)": {"params": {"_account": "The account to add for"}}, "exitMarket(address)": {"details": "Sender must not have an outstanding borrow balance in the asset,  or be providing necessary collateral for an outstanding borrow.", "params": {"_mToken": "The address of the asset to be removed"}}, "getAccountLiquidity(address)": {"returns": {"_0": "account liquidity in excess of collateral requirements,          account shortfall below collateral requirements)"}}, "getAssetsIn(address)": {"params": {"_user": "The address of the account to pull assets for"}, "returns": {"mTokens": "A dynamic list with the assets the account has entered"}}, "getHypotheticalAccountLiquidity(address,address,uint256,uint256)": {"params": {"account": "The account to determine liquidity for", "borrowAmount": "The amount of underlying to hypothetically borrow", "mTokenModify": "The market to hypothetically redeem/borrow in", "redeemTokens": "The number of tokens to hypothetically redeem"}, "returns": {"_0": "hypothetical account liquidity in excess of collateral requirements,         hypothetical account shortfall below collateral requirements)"}}, "isDeprecated(address)": {"details": "All borrows in a deprecated mToken market can be immediately liquidated", "params": {"mToken": "The market to check if deprecated"}}, "isPaused(address,uint8)": {"params": {"_type": "the operation type", "mToken": "The mToken to check"}}, "liquidateCalculateSeizeTokens(address,address,uint256)": {"details": "Used in liquidation (called in mTokenBorrowed.liquidate)", "params": {"actualRepayAmount": "The amount of mTokenBorrowed underlying to convert into mTokenCollateral tokens", "mTokenBorrowed": "The address of the borrowed mToken", "mTokenCollateral": "The address of the collateral mToken"}, "returns": {"_0": "number of mTokenCollateral tokens to be seized in a liquidation"}}, "owner()": {"details": "Returns the address of the current owner."}, "renounceOwnership()": {"details": "Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner."}, "setCloseFactor(uint256)": {"details": "Admin function to set closeFactor", "params": {"newCloseFactorMantissa": "New close factor, scaled by 1e18"}}, "setCollateralFactor(address,uint256)": {"details": "Admin function to set per-market collateralFactor", "params": {"mToken": "The market to set the factor on", "newCollateralFactorMantissa": "The new collateral factor, scaled by 1e18"}}, "setLiquidationIncentive(address,uint256)": {"details": "Admin function to set liquidationIncentive", "params": {"newLiquidationIncentiveMantissa": "New liquidationIncentive scaled by 1e18"}}, "setMarketBorrowCaps(address[],uint256[])": {"params": {"mTokens": "The addresses of the markets (tokens) to change the borrow caps for", "newBorrowCaps": "The new borrow cap values in underlying to be set. A value of 0 corresponds to unlimited borrowing."}}, "setMarketSupplyCaps(address[],uint256[])": {"params": {"mTokens": "The addresses of the markets (tokens) to change the supply caps for", "newSupplyCaps": "The new supply cap values in underlying to be set. A value of 0 corresponds to unlimited supplying."}}, "setOutflowTimeLimitInUSD(uint256)": {"details": "when 0, it means there's no limit", "params": {"amount": "The new limit"}}, "setOutflowVolumeTimeWindow(uint256)": {"params": {"newTimeWindow": "The new reset time window"}}, "setPaused(address,uint8,bool)": {"params": {"_type": "The pause operation type", "mToken": "The market token address", "state": "The pause operation status"}}, "setPriceOracle(address)": {"details": "Admin function to set a new price oracle"}, "setRewardDistributor(address)": {"params": {"newRewardDistributor": "The address of the new Reward Distributor"}}, "setRolesOperator(address)": {"details": "Admin function to set a new operator"}, "setWhitelistedUser(address,bool)": {"params": {"state": "The new staate", "user": "The user address"}}, "supportMarket(address)": {"details": "Admin function to set isListed and add support for the market", "params": {"mToken": "The address of the market (token) to list"}}, "transferOwnership(address)": {"details": "Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"accountAssets(address,uint256)": {"notice": "Per-account mapping of \"assets you are in\", capped by maxAssets"}, "afterMTokenMint(address)": {"notice": "Validates mint and reverts on rejection. May emit logs."}, "allMarkets(uint256)": {"notice": "A list of all markets"}, "beforeMTokenBorrow(address,address,uint256)": {"notice": "Checks if the account should be allowed to borrow the underlying asset of the given market"}, "beforeMTokenLiquidate(address,address,address,uint256)": {"notice": "Checks if the liquidation should be allowed to occur"}, "beforeMTokenMint(address,address)": {"notice": "Checks if the account should be allowed to mint tokens in the given market"}, "beforeMTokenRedeem(address,address,uint256)": {"notice": "Checks if the account should be allowed to redeem tokens in the given market"}, "beforeMTokenRepay(address,address)": {"notice": "Checks if the account should be allowed to repay a borrow in the given market"}, "beforeMTokenSeize(address,address,address,address)": {"notice": "Checks if the seizing of assets should be allowed to occur"}, "beforeMTokenTransfer(address,address,address,uint256)": {"notice": "Checks if the account should be allowed to transfer tokens in the given market"}, "beforeRebalancing(address)": {"notice": "Checks if the account should be allowed to rebalance tokens"}, "blacklistOperator()": {"notice": "Blacklist"}, "borrowCaps(address)": {"notice": "Borrow caps enforced by borrowAllowed for each mToken address. Defaults to zero which corresponds to unlimited borrowing."}, "checkMembership(address,address)": {"notice": "Returns whether the given account is entered in the given asset"}, "checkOutflowVolumeLimit(uint256)": {"notice": "Verifies outflow volule limit"}, "claimMalda(address)": {"notice": "Claim all the MALDA accrued by holder in all markets"}, "claimMalda(address,address[])": {"notice": "Claim all the MALDA accrued by holder in the specified markets"}, "claimMalda(address[],address[],bool,bool)": {"notice": "Claim all MALDA accrued by the holders"}, "closeFactorMantissa()": {"notice": "Multiplier used to calculate the maximum repayAmount when liquidating a borrow"}, "cumulativeOutflowVolume()": {"notice": "Should return outflow volume"}, "disableWhitelist()": {"notice": "Disable user whitelist"}, "enableWhitelist()": {"notice": "Enable user whitelist"}, "enterMarkets(address[])": {"notice": "Add assets to be included in account liquidity calculation"}, "enterMarketsWithSender(address)": {"notice": "Add asset (msg.sender) to be included in account liquidity calculation"}, "exitMarket(address)": {"notice": "Removes asset from sender's account liquidity calculation"}, "getAccountLiquidity(address)": {"notice": "Determine the current account liquidity wrt collateral requirements"}, "getAllMarkets()": {"notice": "A list of all markets"}, "getAssetsIn(address)": {"notice": "Returns the assets an account has entered"}, "getHypotheticalAccountLiquidity(address,address,uint256,uint256)": {"notice": "Determine what the account liquidity would be if the given amounts were redeemed/borrowed"}, "getUSDValueForAllMarkets()": {"notice": "Returns USD value for all markets"}, "isDeprecated(address)": {"notice": "Returns true if the given mToken market has been deprecated"}, "isMarketListed(address)": {"notice": "Returns true/false"}, "isOperator()": {"notice": "Should return true"}, "isPaused(address,uint8)": {"notice": "Returns if operation is paused"}, "lastOutflowResetTimestamp()": {"notice": "Should return last reset time for outflow check"}, "limitPerTimePeriod()": {"notice": "Should return outflow limit"}, "liquidateCalculateSeizeTokens(address,address,uint256)": {"notice": "Calculate number of tokens of collateral asset to seize given an underlying amount"}, "liquidationIncentiveMantissa(address)": {"notice": "Multiplier representing the discount on collateral that a liquidator receives"}, "markets(address)": {"notice": "Official mapping of mTokens -> Market metadata"}, "oracleOperator()": {"notice": "Oracle which gives the price of any given asset"}, "outflowResetTimeWindow()": {"notice": "Should return the outflow volume time window"}, "resetOutflowVolume()": {"notice": "Resets outflow volume"}, "rewardDistributor()": {"notice": "Reward Distributor to markets supply and borrow (including protocol token)"}, "rolesOperator()": {"notice": "Roles"}, "setCloseFactor(uint256)": {"notice": "Sets the closeFactor used when liquidating borrows"}, "setCollateralFactor(address,uint256)": {"notice": "Sets the collateralFactor for a market"}, "setLiquidationIncentive(address,uint256)": {"notice": "Sets liquidationIncentive"}, "setMarketBorrowCaps(address[],uint256[])": {"notice": "Set the given borrow caps for the given mToken markets. Borrowing that brings total borrows to or above borrow cap will revert."}, "setMarketSupplyCaps(address[],uint256[])": {"notice": "Set the given supply caps for the given mToken markets. Supplying that brings total supply to or above supply cap will revert."}, "setOutflowTimeLimitInUSD(uint256)": {"notice": "Sets outflow volume limit"}, "setOutflowVolumeTimeWindow(uint256)": {"notice": "Sets outflow volume time window"}, "setPaused(address,uint8,bool)": {"notice": "Set pause for a specific operation"}, "setPriceOracle(address)": {"notice": "Sets a new price oracle"}, "setRewardDistributor(address)": {"notice": "Admin function to change the Reward Distributor"}, "setRolesOperator(address)": {"notice": "Sets a new Operator for the market"}, "setWhitelistedUser(address,bool)": {"notice": "Sets user whitelist status"}, "supplyCaps(address)": {"notice": "Supply caps enforced by supplyAllowed for each mToken address. Defaults to zero which corresponds to unlimited supplying."}, "supportMarket(address)": {"notice": "Add the market to the markets mapping and set it as listed"}, "userWhitelisted(address)": {"notice": "Returns true/false for user"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"test/unit/oracle/OracleUnderlying.t.sol": "MixedPriceOracleV3_Test"}, "evmVersion": "london", "libraries": {}}, "sources": {"lib/forge-std/src/Base.sol": {"keccak256": "0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd", "urls": ["bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d", "dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z"], "license": "MIT"}, "lib/forge-std/src/StdAssertions.sol": {"keccak256": "0x4584f551c3a875c26423c1e50a77179bc2f9c9c1ee6d0f0c6be0f89ca5ee8270", "urls": ["bzz-raw://ef118876a862b60ba8a6612b8b3f3a6c226e11a0071a2e9695f021586d1bfdbe", "dweb:/ipfs/QmXd2vD91PkvoFWasFkgbDi89PctmgsHVxGvGNyhjmRK2b"], "license": "MIT"}, "lib/forge-std/src/StdChains.sol": {"keccak256": "0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e", "urls": ["bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351", "dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm"], "license": "MIT"}, "lib/forge-std/src/StdCheats.sol": {"keccak256": "0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746", "urls": ["bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41", "dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK"], "license": "MIT"}, "lib/forge-std/src/StdConstants.sol": {"keccak256": "0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534", "urls": ["bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc", "dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r"], "license": "MIT"}, "lib/forge-std/src/StdError.sol": {"keccak256": "0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77", "urls": ["bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6", "dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj"], "license": "MIT"}, "lib/forge-std/src/StdInvariant.sol": {"keccak256": "0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d", "urls": ["bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391", "dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5"], "license": "MIT"}, "lib/forge-std/src/StdJson.sol": {"keccak256": "0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500", "urls": ["bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974", "dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3"], "license": "MIT"}, "lib/forge-std/src/StdMath.sol": {"keccak256": "0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2", "urls": ["bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92", "dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC"], "license": "MIT"}, "lib/forge-std/src/StdStorage.sol": {"keccak256": "0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc", "urls": ["bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57", "dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ"], "license": "MIT"}, "lib/forge-std/src/StdStyle.sol": {"keccak256": "0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d", "urls": ["bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8", "dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK"], "license": "MIT"}, "lib/forge-std/src/StdToml.sol": {"keccak256": "0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861", "urls": ["bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3", "dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8"], "license": "MIT"}, "lib/forge-std/src/StdUtils.sol": {"keccak256": "0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737", "urls": ["bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138", "dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776"], "license": "MIT"}, "lib/forge-std/src/Test.sol": {"keccak256": "0x3dda6083a83dfa3e8526e97bcc28e862ee2442dd58fe94d5c426d65b8e38f73c", "urls": ["bzz-raw://33f8c02e4dabdab86a6825125856446a8657eacd712318b51b7818e4a6f6e3f4", "dweb:/ipfs/QmRKSjVnrk54yr8wTK2e6QxRjiuba2H8HJSKunHAkdo7RG"], "license": "MIT"}, "lib/forge-std/src/Vm.sol": {"keccak256": "0xaae88142a348064677cf81169ea0ca706be3e99e2a0f13a01878ff8d52cf6b25", "urls": ["bzz-raw://da4fb0ed1b31381c7e60ee386acc72796e8a8325c83739ec9a3678e2f841073c", "dweb:/ipfs/Qmb4MU6FpAi8Wt5kUQ3wa7ooHnfMVbdd9zuvnqbTehLmxJ"], "license": "MIT OR Apache-2.0"}, "lib/forge-std/src/console.sol": {"keccak256": "0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5", "urls": ["bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57", "dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP"], "license": "MIT"}, "lib/forge-std/src/console2.sol": {"keccak256": "0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f", "urls": ["bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d", "dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ"], "license": "MIT"}, "lib/forge-std/src/interfaces/IMulticall3.sol": {"keccak256": "0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a", "urls": ["bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0", "dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2"], "license": "MIT"}, "lib/forge-std/src/safeconsole.sol": {"keccak256": "0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11", "urls": ["bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab", "dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol": {"keccak256": "0xc163fcf9bb10138631a9ba5564df1fa25db9adff73bd9ee868a8ae1858fe093a", "urls": ["bzz-raw://9706d43a0124053d9880f6e31a59f31bc0a6a3dc1acd66ce0a16e1111658c5f6", "dweb:/ipfs/QmUFmfowzkRwGtDu36cXV9SPTBHJ3n7dG9xQiK5B28jTf2"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol": {"keccak256": "0x631188737069917d2f909d29ce62c4d48611d326686ba6683e26b72a23bfac0b", "urls": ["bzz-raw://7a61054ae84cd6c4d04c0c4450ba1d6de41e27e0a2c4f1bcdf58f796b401c609", "dweb:/ipfs/QmUvtdp7X1mRVyC3CsHrtPbgoqWaXHp3S1ZR24tpAQYJWM"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol": {"keccak256": "0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397", "urls": ["bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9", "dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/ReentrancyGuardUpgradeable.sol": {"keccak256": "0xb44e086e941292cdc7f440de51478493894ef0b1aeccb0c4047445919f667f74", "urls": ["bzz-raw://942dad22fbdc1669f025540ba63aa3ccfad5f8458fc5d4525b31ebf272e7af45", "dweb:/ipfs/Qmdo4X2M82aM3AMoW2kf2jhYkSCyC4T1pHNd6obdsDFnAB"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/access/Ownable.sol": {"keccak256": "0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb", "urls": ["bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6", "dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol": {"keccak256": "0x60c65f701957fdd6faea1acb0bb45825791d473693ed9ecb34726fdfaa849dd7", "urls": ["bzz-raw://ea290300e0efc4d901244949dc4d877fd46e6c5e43dc2b26620e8efab3ab803f", "dweb:/ipfs/QmcLLJppxKeJWqHxE2CUkcfhuRTgHSn8J4kijcLa5MYhSt"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol": {"keccak256": "0xbfb6695731de677140fbf76c772ab08c4233a122fb51ac28ac120fc49bbbc4ec", "urls": ["bzz-raw://68f8fded7cc318efa15874b7c6a983fe17a4a955d72d240353a9a4ca1e1b824c", "dweb:/ipfs/QmdcmBL9Qo4Tk3Dby4wFYabGyot9JNeLPxpSXZUgUm92BV"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Utils.sol": {"keccak256": "0x06a78f9b3ee3e6d0eb4e4cd635ba49960bea34cac1db8c0a27c75f2319f1fd65", "urls": ["bzz-raw://547d21aa17f4f3f1a1a7edf7167beff8dd9496a0348d5588f15cc8a4b29d052a", "dweb:/ipfs/QmT16JtRQSWNpLo9W23jr6CzaMuTAcQcjJJcdRd8HLJ6cE"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol": {"keccak256": "0xc3f2ec76a3de8ed7a7007c46166f5550c72c7709e3fc7e8bb3111a7191cdedbd", "urls": ["bzz-raw://e73efb4c2ca655882dc237c6b4f234a9bd36d97159d8fcaa837eb01171f726ac", "dweb:/ipfs/QmTNnnv7Gu5fs5G1ZMh7Fexp8N4XUs3XrNAngjcxgiss3e"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol": {"keccak256": "0xc59a78b07b44b2cf2e8ab4175fca91e8eca1eee2df7357b8d2a8833e5ea1f64c", "urls": ["bzz-raw://5aa4f07e65444784c29cd7bfcc2341b34381e4e5b5da9f0c5bd00d7f430e66fa", "dweb:/ipfs/QmWRMh4Q9DpaU9GvsiXmDdoNYMyyece9if7hnfLz7uqzWM"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol": {"keccak256": "0xc3e1fa9d1987f8d349dfb4d6fe93bf2ca014b52ba335cfac30bfe71e357e6f80", "urls": ["bzz-raw://c5703ccdeb7b1d685e375ed719117e9edf2ab4bc544f24f23b0d50ec82257229", "dweb:/ipfs/QmTdwkbQq7owpCiyuzE7eh5LrD2ddrBCZ5WHVsWPi1RrTS"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0xc6a8ff0ea489379b61faa647490411b80102578440ab9d84e9a957cc12164e70", "urls": ["bzz-raw://0ea104e577e63faea3b69c415637e99e755dcbf64c5833d7140c35a714d6d90c", "dweb:/ipfs/Qmau6x4Ns9XdyynRCNNp3RhLqijJjFm7z5fyZazfYFGYdq"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0xaa761817f6cd7892fcf158b3c776b34551cde36f48ff9703d53898bc45a94ea2", "urls": ["bzz-raw://0ad7c8d4d08938c8dfc43d75a148863fb324b80cf53e0a36f7e5a4ac29008850", "dweb:/ipfs/QmcrhfPgVNf5mkdhQvy1pMv51TFokD3Y4Wa5WZhFqVh8UV"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Address.sol": {"keccak256": "0xaf28a975a78550e45f65e559a3ad6a5ad43b9b8a37366999abd1b7084eb70721", "urls": ["bzz-raw://b7bd24e224f67f65bfadf85dc2929fa965456bb2415478bd0125471b5ce35245", "dweb:/ipfs/QmRaydGr8BTHs1kvaZfsNU69pKzUAGFrvABn1KiRSbE51y"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol": {"keccak256": "0x32ba59b4b7299237c8ba56319110989d7978a039faf754793064e967e5894418", "urls": ["bzz-raw://1ae50c8b562427df610cc4540c9bf104acca7ef8e2dcae567ae7e52272281e9c", "dweb:/ipfs/QmTHiadFCSJUPpRjNegc5SahmeU8bAoY8i9Aq6tVscbcKR"], "license": "MIT"}, "src/Operator/Operator.sol": {"keccak256": "0x1486ed6af6f40f0e045403ac9e35bc045ae7031d9ecac7d1f0358f78ff83b469", "urls": ["bzz-raw://e136048d46ae9b4384f561e16dfd4638e68db987007d2059aaf19011dcfc8f65", "dweb:/ipfs/QmcyrVa45W2qYtd9UuJz9C89M3xKzKzACDQqVMAk4hvPeq"], "license": "BSL-1.1"}, "src/Operator/OperatorStorage.sol": {"keccak256": "0x3d9e7f2bc64ab9b5de39d4a58f0a72ac0f86c3b95a6318dd03a7b8acde166783", "urls": ["bzz-raw://dc0d5e026e00f5f5f92cded7704bb602b06f387370ddd7ee0d75111099b01f3a", "dweb:/ipfs/QmQfZzADQpBuvTHJpbHz6P4fGhaGWvQgkU8qMkKULCLdjQ"], "license": "BSL-1.1"}, "src/Roles.sol": {"keccak256": "0xaa5bbf73d5d2ba654b7f0fe960ae9e17282d959fc559d09c2222b78dad1911b0", "urls": ["bzz-raw://e683df4c25f2f8f725915d68e8ee9e0871c0d93ad592b0ca985b1829e460cabc", "dweb:/ipfs/QmTsQgUKXCtzucNF9DSq1wKVfeqSUeoKPMFCkM8hNLxTmV"], "license": "BSL-1.1"}, "src/blacklister/Blacklister.sol": {"keccak256": "0xfa0b52f7467330267cf6ed2d17615b56a75a8a477a5a6c7670ebd0f4636a4e7c", "urls": ["bzz-raw://d4b9df3910bdba55615acdf7fbef0772b4a7746d9f5f2c01b3ac41b42bd5d6c4", "dweb:/ipfs/QmWbo7tK6A57dQiWzX5rgWuQTXo1EGuTvNmG57A93rJHuA"], "license": "BSL-1.1"}, "src/interest/JumpRateModelV4.sol": {"keccak256": "0xa93081b79a997a666c96497334d721b37abfd0f193c1031e9f7f2e1b0823c37b", "urls": ["bzz-raw://b640c34eedc95215fc74b43637256f52acb95052a946a952402b18516a67f7d5", "dweb:/ipfs/QmeSemgHAutRfCAttB1mTQ2AYXQpRj1Eq7haGS8QgnTHUa"], "license": "BSL-1.1"}, "src/interfaces/IBlacklister.sol": {"keccak256": "0x47ed20135507a9d16473027cbee18de5d952846bada761c6248b91c75cd88be1", "urls": ["bzz-raw://91a886b95dd578156f9b6cec3236e4751aae8a38e5d15e0d6045217c6fbd3d63", "dweb:/ipfs/QmRNjz7sGTuMkQNZhwwcg5DNN2eeqTZSAdZmxWxbx66CHU"], "license": "BSL-1.1"}, "src/interfaces/IDefaultAdapter.sol": {"keccak256": "0xbf7e882eeb81776c7be55110bb171c65d166bafeb71d828c085b139bed5735c8", "urls": ["bzz-raw://7e139fb3ddd0623189493679e73fd42c4e505531502d55e1e699789fa3c1451a", "dweb:/ipfs/Qma3XsUVPffiGXZ7epTqMyNJKuh87xrFhqCTwQXznEccU6"], "license": "BSL-1.1"}, "src/interfaces/IInterestRateModel.sol": {"keccak256": "0xdc8386be37a06d93fb0967be32e465f3b1f941ec9ce2f90984a5c70488cddb1e", "urls": ["bzz-raw://de2bf96a7d0945f58b00e9fc867ac00fc4a09480e9e27c1a800604295320b769", "dweb:/ipfs/Qmf5sAgA58GjVxeipJNhP4euxwsZ9FS9CgsWTJ319UmCUE"], "license": "BSL-1.1"}, "src/interfaces/IOperator.sol": {"keccak256": "0x0ae3d63eb07518da5a4fd6de7b013b6f55c87defb31900548378cffc70a51f72", "urls": ["bzz-raw://1d9710c4ac43cb0563449aa519501b4ed18a331c90d0bc7eb79cdc56407bad8a", "dweb:/ipfs/Qmafe9Wup2GF9pD83CLWUrRait93vwvReqU1C7EHQhY7WV"], "license": "BSL-1.1"}, "src/interfaces/IOracleOperator.sol": {"keccak256": "0xcc2848c75dd5e1531c2b958746cdd76a3b2b329be98fdca4311219a12a6b409e", "urls": ["bzz-raw://337bdabf03ac279d95ba96aca60105932c7af5c15fdb4b21ab1c369509310fbc", "dweb:/ipfs/QmNYrNYNUwWWSrPwHdarhxt1ShdH1dy8B94M49TZ7XvmK1"], "license": "BSL-1.1"}, "src/interfaces/IRewardDistributor.sol": {"keccak256": "0xd39c4db6e2a85e90db82e20685d4b235966d993c3407e1c0fb52add6421b86df", "urls": ["bzz-raw://e865fe1569ec1ac79cd924a9aa2e2b43754555e510526f6cd030395e9923f01d", "dweb:/ipfs/QmXcshgzCxLKBZJYGuQviEamruhmwXWNJ3ZYLxazWBahuc"], "license": "BSL-1.1"}, "src/interfaces/IRoles.sol": {"keccak256": "0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365", "urls": ["bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c", "dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj"], "license": "BSL-1.1"}, "src/interfaces/ImToken.sol": {"keccak256": "0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f", "urls": ["bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36", "dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL"], "license": "BSL-1.1"}, "src/interfaces/external/poh/IPohVerifier.sol": {"keccak256": "0xce93e2d734074add2a6d36867d59fd24e2d44ca48c524bd8dddabd4163038205", "urls": ["bzz-raw://9e94dc14721e8873d15d7032afa3d2b5fea76e9c4029698aa489f2ea2edcedf6", "dweb:/ipfs/QmR55sQx36Ub7wUjDeemUSNotdmYCkK8AmUEYbRezF5Xhy"], "license": "AGPL-3.0"}, "src/oracles/MixedPriceOracleV3.sol": {"keccak256": "0x2a12b509ba518e116b6c1136575e04a2c2bfdd5402730f6746cfabbbe5e6549e", "urls": ["bzz-raw://2417f80a6dfc814e73df5362226cd89db15ea2314effd80dd16bcb87595ea755", "dweb:/ipfs/QmcRPazSFBvLKUqo3SCYhNkzuDuQZHWcWjwTzxV88E6own"], "license": "BSL-1.1"}, "src/rewards/RewardDistributor.sol": {"keccak256": "0x8d5c3e5e5050121d4f1310479a2cadde7dc97ff8a57115021cafb2032aaf50c2", "urls": ["bzz-raw://e200d18803bb0680b3007c79c23a8a7e31f054ce6e4f36c376f7b43419679322", "dweb:/ipfs/QmV7CRA5HSB89fwCVF7VWGArZnWoH2BB1heXu9SaMkbL9H"], "license": "BSL-1.1"}, "src/utils/ExponentialNoError.sol": {"keccak256": "0xa97178117d8c32059d55dd2224e5514c54b31f896e565c3834cab3d7502b1bb6", "urls": ["bzz-raw://1cecc0713709f57a5bb7f64d2fa06b9437a42752fa3fed5d3c0b19d82aa56ef4", "dweb:/ipfs/QmQqBCZkzmZxomXLr3b1tHUV55n8EPk3wAUsXgojYSpR41"], "license": "BSL-1.1"}, "test/Base_Unit_Test.t.sol": {"keccak256": "0x2f61ec9614bbfeed1c1f8555f20fb0d4c584dc3452369b53417ab3fba144f330", "urls": ["bzz-raw://0f7a32bd42554d42a3062092b80a6a0fb90bea17a3cad2c12863688b811e6b4c", "dweb:/ipfs/QmZNFWWTyTFEjSErQAErkmEdzAPf2Eyeb2ir4fgRqQ5bKJ"], "license": "BSL-1.1"}, "test/mocks/ERC20Mock.sol": {"keccak256": "0x86e2d4e622d3172aff95ad8436b1af1213c7be008fbbe1e74f29577ea0afd92f", "urls": ["bzz-raw://2e2a534d2bf62fd7be511ac74e372b1d1e4cc08033fba742950785315553e3fb", "dweb:/ipfs/Qme24GSF4BFzuHETCfPzUafV1bsmVHVvbqQPNxDoU8AafR"], "license": "BSL-1.1"}, "test/mocks/OracleMock.sol": {"keccak256": "0xb749ba9d217b9aedc9010690022b31672eeacf0927f43efe9769ae886f4ce855", "urls": ["bzz-raw://5167ca5dcd31bc0ae6dcc22e43276e8f746b018d7d9c2edc332d1160024f6328", "dweb:/ipfs/QmVdgBmaBntj9TMV4z65bga1uuN2YkfUbLNN9Hsmvu4x6f"], "license": "BSL-1.1"}, "test/unit/oracle/OracleUnderlying.t.sol": {"keccak256": "0xea57d5cab5b035dcaeb069acd24fbcbb688c67984b88a316fadef1e08e9559a0", "urls": ["bzz-raw://2119fb80a4cf3d696f6e51e020dcad0080fe4dd028e04184116f50dbb76210a0", "dweb:/ipfs/QmaFw7HyXE2gH47hYkCy7Wyd1EUUrMoPSC6NKzN5ZMRrsv"], "license": "BSL-1.1"}, "test/utils/Constants.sol": {"keccak256": "0xa2611aa14c45b8ea8b276aacad47d78f33908fab8c6ed0ff35cef76fd41c695b", "urls": ["bzz-raw://f60db39c1ce5c7260361664fec9d731d3ba329055a0810d83491ce54ad7d2a01", "dweb:/ipfs/QmSzv3VXBa6q6bowzAfZ4Afcp4UWwGUKJFB72xV6MYyCNn"], "license": "BSL-1.1"}, "test/utils/Events.sol": {"keccak256": "0xb0b41707dca3af9d783239cb5c96a2e9347e03b5529c944565ac9de2f33ae82a", "urls": ["bzz-raw://e8fad0826e747465c9208ad6a7d52cd50205972cfea7fa8a206be750cf1e8a80", "dweb:/ipfs/QmR5mWoVf2ZcETLJVuCMHvWPBfQ3CNxD8Gx8Endms5AwmR"], "license": "BSL-1.1"}, "test/utils/Helpers.sol": {"keccak256": "0xa59b1e23b76c632e72c93dbd612c9279b2cad6d8915c31c04e62af0d46becf4d", "urls": ["bzz-raw://2a7d815eeebeea85ec405483ec6d55a61f1a984d68d3a6020d3444915aa6610c", "dweb:/ipfs/QmQ6qWmTAdWnnursoU4F2pYCa3tpTtS2qjPFht1kWT2KDT"], "license": "BSL-1.1"}, "test/utils/Types.sol": {"keccak256": "0x696166d23b74196cb6a66bbd72f25024bb251be99ab2a6d8c9ba86f5b47f22d6", "urls": ["bzz-raw://a302c977aea4ccbc54de408575dd5b52b00b9d62512da0d7eb71edb46eff1366", "dweb:/ipfs/QmUjRq9fjukqZL59ABU2Xp6KfR21sPvdBVcWWzjrMLxpzP"], "license": "BSL-1.1"}}, "version": 1}, "id": 233}