{"abi": [{"type": "constructor", "inputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "blacklist", "inputs": [{"name": "user", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "getBlacklistedAddresses", "inputs": [], "outputs": [{"name": "", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "initialize", "inputs": [{"name": "_owner", "type": "address", "internalType": "address payable"}, {"name": "_roles", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "isBlacklisted", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "rolesOperator", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IRoles"}], "stateMutability": "view"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "unblacklist", "inputs": [{"name": "user", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "Blacklisted", "inputs": [{"name": "user", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Initialized", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Unblacklisted", "inputs": [{"name": "user", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "Blacklister_AlreadyBlacklisted", "inputs": []}, {"type": "error", "name": "Blacklister_NotAllowed", "inputs": []}, {"type": "error", "name": "Blacklister_NotBlacklisted", "inputs": []}, {"type": "error", "name": "InvalidInitialization", "inputs": []}, {"type": "error", "name": "NotInitializing", "inputs": []}, {"type": "error", "name": "OwnableInvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}], "bytecode": {"object": "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", "sourceMap": "846:2114:130:-:0;;;1291:53;;;;;;;;;-1:-1:-1;1315:22:130;:20;:22::i;:::-;846:2114;;7711:422:22;8870:21;7900:15;;;;;;;7896:76;;;7938:23;;-1:-1:-1;;;7938:23:22;;;;;;;;;;;7896:76;7985:14;;-1:-1:-1;;;;;7985:14:22;;;:34;7981:146;;8035:33;;-1:-1:-1;;;;;;8035:33:22;-1:-1:-1;;;;;8035:33:22;;;;;8087:29;;158:50:242;;;8087:29:22;;146:2:242;131:18;8087:29:22;;;;;;;7981:146;7760:373;7711:422::o;14:200:242:-;846:2114:130;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "846:2114:130:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1772:116;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;1350:180;;;;;;:::i;:::-;;:::i;:::-;;1045:27;;;;;-1:-1:-1;;;;;1045:27:130;;;;;;-1:-1:-1;;;;;1397:32:242;;;1379:51;;1367:2;1352:18;1045:27:130;1217:219:242;3155:101:21;;;:::i;2125:262:130:-;;;;;;:::i;:::-;;:::i;2441:144:21:-;;;:::i;3405:215::-;;;;;;:::i;:::-;;:::i;1936:183:130:-;;;;;;:::i;:::-;;:::i;948:45::-;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;2074:14:242;;2067:22;2049:41;;2037:2;2022:18;948:45:130;1909:187:242;1772:116:130;1830:16;1865;1858:23;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;1858:23:130;;;;;;;;;;;;;;;;;;;;;;;1772:116;:::o;1350:180::-;8870:21:22;4302:15;;-1:-1:-1;;;4302:15:22;;;;4301:16;;4348:14;;4158:30;4726:16;;:34;;;;;4746:14;4726:34;4706:54;;4770:17;4790:11;:16;;4805:1;4790:16;:50;;;;-1:-1:-1;4818:4:22;4810:25;:30;4790:50;4770:70;;4856:12;4855:13;:30;;;;;4873:12;4872:13;4855:30;4851:91;;;4908:23;;-1:-1:-1;;;4908:23:22;;;;;;;;;;;4851:91;4951:18;;-1:-1:-1;;4951:18:22;4968:1;4951:18;;;4979:67;;;;5013:22;;-1:-1:-1;;;;5013:22:22;-1:-1:-1;;;5013:22:22;;;4979:67;1461:22:130::1;1476:6;1461:14;:22::i;:::-;1493:13;:30:::0;;-1:-1:-1;;;;;;1493:30:130::1;-1:-1:-1::0;;;;;1493:30:130;::::1;;::::0;;5066:101:22;;;;5100:23;;-1:-1:-1;;;;5100:23:22;;;5142:14;;-1:-1:-1;2254:50:242;;5142:14:22;;2242:2:242;2227:18;5142:14:22;;;;;;;5066:101;4092:1081;;;;;1350:180:130;;:::o;3155:101:21:-;2334:13;:11;:13::i;:::-;3219:30:::1;3246:1;3219:18;:30::i;:::-;3155:101::o:0;2125:262:130:-;1599:7;:5;:7::i;:::-;-1:-1:-1;;;;;1585:21:130;:10;-1:-1:-1;;;;;1585:21:130;;:99;;;-1:-1:-1;1610:13:130;;1649:34;;;-1:-1:-1;;;1649:34:130;;;;-1:-1:-1;;;;;1610:13:130;;;;:26;;1637:10;;1610:13;;1649:32;;:34;;;;;;;;;;;;;;1610:13;1649:34;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1610:74;;-1:-1:-1;;;;;;1610:74:130;;;;;;;-1:-1:-1;;;;;2696:32:242;;;1610:74:130;;;2678:51:242;2745:18;;;2738:34;2651:18;;1610:74:130;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1577:134;;;;-1:-1:-1;;;1577:134:130;;;;;;;;;;;;-1:-1:-1;;;;;2213:19:130;::::1;:13;:19:::0;;;::::1;::::0;;;;;;;::::1;;2208:61;;2241:28;;-1:-1:-1::0;;;2241:28:130::1;;;;;;;;;;;2208:61;-1:-1:-1::0;;;;;2279:19:130;::::1;2301:5;2279:19:::0;;;::::1;::::0;;;;;;:27;;-1:-1:-1;;2279:27:130::1;::::0;;2316:30:::1;2293:4:::0;2316:24:::1;:30::i;:::-;2361:19;::::0;-1:-1:-1;;;;;2361:19:130;::::1;::::0;::::1;::::0;;;::::1;2125:262:::0;:::o;2441:144:21:-;1313:22;2570:8;-1:-1:-1;;;;;2570:8:21;;2441:144::o;3405:215::-;2334:13;:11;:13::i;:::-;-1:-1:-1;;;;;3489:22:21;::::1;3485:91;;3534:31;::::0;-1:-1:-1;;;3534:31:21;;3562:1:::1;3534:31;::::0;::::1;1379:51:242::0;1352:18;;3534:31:21::1;;;;;;;;3485:91;3585:28;3604:8;3585:18;:28::i;:::-;3405:215:::0;:::o;1936:183:130:-;1599:7;:5;:7::i;:::-;-1:-1:-1;;;;;1585:21:130;:10;-1:-1:-1;;;;;1585:21:130;;:99;;;-1:-1:-1;1610:13:130;;1649:34;;;-1:-1:-1;;;1649:34:130;;;;-1:-1:-1;;;;;1610:13:130;;;;:26;;1637:10;;1610:13;;1649:32;;:34;;;;;;;;;;;;;;1610:13;1649:34;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1610:74;;-1:-1:-1;;;;;;1610:74:130;;;;;;;-1:-1:-1;;;;;2696:32:242;;;1610:74:130;;;2678:51:242;2745:18;;;2738:34;2651:18;;1610:74:130;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1577:134;;;;-1:-1:-1;;;1577:134:130;;;;;;;;;;;;-1:-1:-1;;;;;2021:19:130;::::1;:13;:19:::0;;;::::1;::::0;;;;;;;::::1;;2017:64;;;2049:32;;-1:-1:-1::0;;;2049:32:130::1;;;;;;;;;;;2017:64;2091:21;2107:4;2091:15;:21::i;1847:127:21:-:0;6931:20:22;:18;:20::i;:::-;1929:38:21::1;1954:12;1929:24;:38::i;2658:162::-:0;966:10:23;2717:7:21;:5;:7::i;:::-;-1:-1:-1;;;;;2717:23:21;;2713:101;;2763:40;;-1:-1:-1;;;2763:40:21;;966:10:23;2763:40:21;;;1379:51:242;1352:18;;2763:40:21;1217:219:242;3774:248:21;1313:22;3923:8;;-1:-1:-1;;;;;;3941:19:21;;-1:-1:-1;;;;;3941:19:21;;;;;;;;3975:40;;3923:8;;;;;3975:40;;3847:24;;3975:40;3837:185;;3774:248;:::o;2608:350:130:-;2689:16;:23;2675:11;2722:230;2742:3;2738:1;:7;2722:230;;;2793:4;-1:-1:-1;;;;;2770:27:130;:16;2787:1;2770:19;;;;;;;;:::i;:::-;;;;;;;;;;;-1:-1:-1;;;;;2770:19:130;:27;2766:176;;2839:16;2856:7;2839:16;2856:3;:7;:::i;:::-;2839:25;;;;;;;;:::i;:::-;;;;;;;;;;;;2817:19;;-1:-1:-1;;;;;2839:25:130;;;;2834:1;;2817:19;;;;;;:::i;:::-;;;;;;;;;:47;;;;;-1:-1:-1;;;;;2817:47:130;;;;;-1:-1:-1;;;;;2817:47:130;;;;;;2882:16;:22;;;;;;;:::i;:::-;;;;;;;;;;-1:-1:-1;;2882:22:130;;;;;-1:-1:-1;;;;;;2882:22:130;;;;;;2722:230;2665:293;2608:350;:::o;2766:176::-;2747:3;;2722:230;;;;2665:293;2608:350;:::o;2438:160::-;-1:-1:-1;;;;;2496:19:130;;:13;:19;;;;;;;;;;;:26;;-1:-1:-1;;2496:26:130;2518:4;2496:26;;;;;;2532:27;;;;;;;;;;;;;;-1:-1:-1;;;;;;2532:27:130;;;;;2574:17;;;2496:13;2574:17;2438:160;:::o;7084:141:22:-;8870:21;8560:40;-1:-1:-1;;;8560:40:22;;;;7146:73;;7191:17;;-1:-1:-1;;;7191:17:22;;;;;;;;;;;1980:235:21;6931:20:22;:18;:20::i;14:637:242:-;204:2;216:21;;;286:13;;189:18;;;308:22;;;156:4;;387:15;;;361:2;346:18;;;156:4;430:195;444:6;441:1;438:13;430:195;;;509:13;;-1:-1:-1;;;;;505:39:242;493:52;;574:2;600:15;;;;565:12;;;;541:1;459:9;430:195;;;-1:-1:-1;642:3:242;;14:637;-1:-1:-1;;;;;14:637:242:o;656:139::-;-1:-1:-1;;;;;739:31:242;;729:42;;719:70;;785:1;782;775:12;800:412;876:6;884;937:2;925:9;916:7;912:23;908:32;905:52;;;953:1;950;943:12;905:52;992:9;979:23;1011:39;1044:5;1011:39;:::i;:::-;1069:5;-1:-1:-1;1126:2:242;1111:18;;1098:32;1139:41;1098:32;1139:41;:::i;:::-;1199:7;1189:17;;;800:412;;;;;:::o;1441:255::-;1500:6;1553:2;1541:9;1532:7;1528:23;1524:32;1521:52;;;1569:1;1566;1559:12;1521:52;1608:9;1595:23;1627:39;1660:5;1627:39;:::i;:::-;1685:5;1441:255;-1:-1:-1;;;1441:255:242:o;2315:184::-;2385:6;2438:2;2426:9;2417:7;2413:23;2409:32;2406:52;;;2454:1;2451;2444:12;2406:52;-1:-1:-1;2477:16:242;;2315:184;-1:-1:-1;2315:184:242:o;2783:277::-;2850:6;2903:2;2891:9;2882:7;2878:23;2874:32;2871:52;;;2919:1;2916;2909:12;2871:52;2951:9;2945:16;3004:5;2997:13;2990:21;2983:5;2980:32;2970:60;;3026:1;3023;3016:12;3065:127;3126:10;3121:3;3117:20;3114:1;3107:31;3157:4;3154:1;3147:15;3181:4;3178:1;3171:15;3197:225;3264:9;;;3285:11;;;3282:134;;;3338:10;3333:3;3329:20;3326:1;3319:31;3373:4;3370:1;3363:15;3401:4;3398:1;3391:15;3282:134;3197:225;;;;:::o;3427:127::-;3488:10;3483:3;3479:20;3476:1;3469:31;3519:4;3516:1;3509:15;3543:4;3540:1;3533:15", "linkReferences": {}}, "methodIdentifiers": {"blacklist(address)": "f9f92be4", "getBlacklistedAddresses()": "3bbab179", "initialize(address,address)": "485cc955", "isBlacklisted(address)": "fe575a87", "owner()": "8da5cb5b", "renounceOwnership()": "715018a6", "rolesOperator()": "4fecab70", "transferOwnership(address)": "f2fde38b", "unblacklist(address)": "75e3661e"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"Blacklister_AlreadyBlacklisted\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Blacklister_NotAllowed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Blacklister_NotBlacklisted\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidInitialization\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotInitializing\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"OwnableInvalidOwner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"OwnableUnauthorizedAccount\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"Blacklisted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"version\",\"type\":\"uint64\"}],\"name\":\"Initialized\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"Unblacklisted\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"blacklist\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getBlacklistedAddresses\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address payable\",\"name\":\"_owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_roles\",\"type\":\"address\"}],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"isBlacklisted\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"rolesOperator\",\"outputs\":[{\"internalType\":\"contract IRoles\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"unblacklist\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"InvalidInitialization()\":[{\"details\":\"The contract is already initialized.\"}],\"NotInitializing()\":[{\"details\":\"The contract is not initializing.\"}],\"OwnableInvalidOwner(address)\":[{\"details\":\"The owner is not a valid owner account. (eg. `address(0)`)\"}],\"OwnableUnauthorizedAccount(address)\":[{\"details\":\"The caller account is not authorized to perform an operation.\"}]},\"events\":{\"Initialized(uint64)\":{\"details\":\"Triggered when the contract has been initialized or reinitialized.\"}},\"kind\":\"dev\",\"methods\":{\"constructor\":{\"custom:oz-upgrades-unsafe-allow\":\"constructor\"},\"owner()\":{\"details\":\"Returns the address of the current owner.\"},\"renounceOwnership()\":{\"details\":\"Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner.\"},\"transferOwnership(address)\":{\"details\":\"Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"blacklist(address)\":{\"notice\":\"Blacklists a user immediately (onlyOwner).\"},\"getBlacklistedAddresses()\":{\"notice\":\"Returns the list of currently blacklisted addresses.\"},\"isBlacklisted(address)\":{\"notice\":\"Returns whether a user is currently blacklisted.\"},\"unblacklist(address)\":{\"notice\":\"Removes a user from the blacklist (onlyOwner).\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/blacklister/Blacklister.sol\":\"Blacklister\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol\":{\"keccak256\":\"0xc163fcf9bb10138631a9ba5564df1fa25db9adff73bd9ee868a8ae1858fe093a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9706d43a0124053d9880f6e31a59f31bc0a6a3dc1acd66ce0a16e1111658c5f6\",\"dweb:/ipfs/QmUFmfowzkRwGtDu36cXV9SPTBHJ3n7dG9xQiK5B28jTf2\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol\":{\"keccak256\":\"0x631188737069917d2f909d29ce62c4d48611d326686ba6683e26b72a23bfac0b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7a61054ae84cd6c4d04c0c4450ba1d6de41e27e0a2c4f1bcdf58f796b401c609\",\"dweb:/ipfs/QmUvtdp7X1mRVyC3CsHrtPbgoqWaXHp3S1ZR24tpAQYJWM\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol\":{\"keccak256\":\"0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9\",\"dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV\"]},\"src/blacklister/Blacklister.sol\":{\"keccak256\":\"0xfa0b52f7467330267cf6ed2d17615b56a75a8a477a5a6c7670ebd0f4636a4e7c\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://d4b9df3910bdba55615acdf7fbef0772b4a7746d9f5f2c01b3ac41b42bd5d6c4\",\"dweb:/ipfs/QmWbo7tK6A57dQiWzX5rgWuQTXo1EGuTvNmG57A93rJHuA\"]},\"src/interfaces/IBlacklister.sol\":{\"keccak256\":\"0x47ed20135507a9d16473027cbee18de5d952846bada761c6248b91c75cd88be1\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://91a886b95dd578156f9b6cec3236e4751aae8a38e5d15e0d6045217c6fbd3d63\",\"dweb:/ipfs/QmRNjz7sGTuMkQNZhwwcg5DNN2eeqTZSAdZmxWxbx66CHU\"]},\"src/interfaces/IRoles.sol\":{\"keccak256\":\"0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c\",\"dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "type": "error", "name": "Blacklister_AlreadyBlacklisted"}, {"inputs": [], "type": "error", "name": "Blacklister_NotAllowed"}, {"inputs": [], "type": "error", "name": "Blacklister_NotBlacklisted"}, {"inputs": [], "type": "error", "name": "InvalidInitialization"}, {"inputs": [], "type": "error", "name": "NotInitializing"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "type": "error", "name": "OwnableInvalidOwner"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "type": "error", "name": "OwnableUnauthorizedAccount"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address", "indexed": true}], "type": "event", "name": "Blacklisted", "anonymous": false}, {"inputs": [{"internalType": "uint64", "name": "version", "type": "uint64", "indexed": false}], "type": "event", "name": "Initialized", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "previousOwner", "type": "address", "indexed": true}, {"internalType": "address", "name": "new<PERSON>wner", "type": "address", "indexed": true}], "type": "event", "name": "OwnershipTransferred", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "user", "type": "address", "indexed": true}], "type": "event", "name": "Unblacklisted", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "blacklist"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getBlacklistedAddresses", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}]}, {"inputs": [{"internalType": "address payable", "name": "_owner", "type": "address"}, {"internalType": "address", "name": "_roles", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "initialize"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isBlacklisted", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "renounceOwnership"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "rolesOperator", "outputs": [{"internalType": "contract IRoles", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "transferOwnership"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "unblacklist"}], "devdoc": {"kind": "dev", "methods": {"constructor": {"custom:oz-upgrades-unsafe-allow": "constructor"}, "owner()": {"details": "Returns the address of the current owner."}, "renounceOwnership()": {"details": "Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner."}, "transferOwnership(address)": {"details": "Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"blacklist(address)": {"notice": "Blacklists a user immediately (onlyOwner)."}, "getBlacklistedAddresses()": {"notice": "Returns the list of currently blacklisted addresses."}, "isBlacklisted(address)": {"notice": "Returns whether a user is currently blacklisted."}, "unblacklist(address)": {"notice": "Removes a user from the blacklist (onlyOwner)."}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/blacklister/Blacklister.sol": "Blacklister"}, "evmVersion": "london", "libraries": {}}, "sources": {"lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol": {"keccak256": "0xc163fcf9bb10138631a9ba5564df1fa25db9adff73bd9ee868a8ae1858fe093a", "urls": ["bzz-raw://9706d43a0124053d9880f6e31a59f31bc0a6a3dc1acd66ce0a16e1111658c5f6", "dweb:/ipfs/QmUFmfowzkRwGtDu36cXV9SPTBHJ3n7dG9xQiK5B28jTf2"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol": {"keccak256": "0x631188737069917d2f909d29ce62c4d48611d326686ba6683e26b72a23bfac0b", "urls": ["bzz-raw://7a61054ae84cd6c4d04c0c4450ba1d6de41e27e0a2c4f1bcdf58f796b401c609", "dweb:/ipfs/QmUvtdp7X1mRVyC3CsHrtPbgoqWaXHp3S1ZR24tpAQYJWM"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol": {"keccak256": "0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397", "urls": ["bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9", "dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV"], "license": "MIT"}, "src/blacklister/Blacklister.sol": {"keccak256": "0xfa0b52f7467330267cf6ed2d17615b56a75a8a477a5a6c7670ebd0f4636a4e7c", "urls": ["bzz-raw://d4b9df3910bdba55615acdf7fbef0772b4a7746d9f5f2c01b3ac41b42bd5d6c4", "dweb:/ipfs/QmWbo7tK6A57dQiWzX5rgWuQTXo1EGuTvNmG57A93rJHuA"], "license": "BSL-1.1"}, "src/interfaces/IBlacklister.sol": {"keccak256": "0x47ed20135507a9d16473027cbee18de5d952846bada761c6248b91c75cd88be1", "urls": ["bzz-raw://91a886b95dd578156f9b6cec3236e4751aae8a38e5d15e0d6045217c6fbd3d63", "dweb:/ipfs/QmRNjz7sGTuMkQNZhwwcg5DNN2eeqTZSAdZmxWxbx66CHU"], "license": "BSL-1.1"}, "src/interfaces/IRoles.sol": {"keccak256": "0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365", "urls": ["bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c", "dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj"], "license": "BSL-1.1"}}, "version": 1}, "id": 130}