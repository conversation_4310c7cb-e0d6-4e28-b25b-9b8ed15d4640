{"abi": [{"type": "constructor", "inputs": [{"name": "_name", "type": "string", "internalType": "string"}, {"name": "_symbol", "type": "string", "internalType": "string"}, {"name": "_decimals", "type": "uint8", "internalType": "uint8"}, {"name": "_owner", "type": "address", "internalType": "address"}, {"name": "_pohVerify", "type": "address", "internalType": "address"}, {"name": "_limit", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "admin", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "allowance", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "spender", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "approve", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "balanceOf", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "burn", "inputs": [{"name": "_amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "burn", "inputs": [{"name": "_from", "type": "address", "internalType": "address"}, {"name": "_amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "decimals", "inputs": [], "outputs": [{"name": "", "type": "uint8", "internalType": "uint8"}], "stateMutability": "view"}, {"type": "function", "name": "deposit", "inputs": [], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "mint", "inputs": [{"name": "_to", "type": "address", "internalType": "address"}, {"name": "_amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "mint", "inputs": [{"name": "_to", "type": "address", "internalType": "address"}, {"name": "_amount", "type": "uint256", "internalType": "uint256"}, {"name": "signature", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "mintLimit", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "minted", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "name", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "onlyVerified", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "pohVerify", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "setMintLimit", "inputs": [{"name": "_limit", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setOnlyVerify", "inputs": [{"name": "status", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "symbol", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "totalSupply", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "transfer", "inputs": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferFrom", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "event", "name": "Approval", "inputs": [{"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "spender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "value", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Transfer", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "value", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "error", "name": "ERC20InsufficientAllowance", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "allowance", "type": "uint256", "internalType": "uint256"}, {"name": "needed", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC20InsufficientBalance", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}, {"name": "balance", "type": "uint256", "internalType": "uint256"}, {"name": "needed", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC20InvalidApprover", "inputs": [{"name": "approver", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC20InvalidReceiver", "inputs": [{"name": "receiver", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC20InvalidSender", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC20InvalidSpender", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC20Mock_AlreadyMinted", "inputs": []}, {"type": "error", "name": "ERC20Mock_NotAuthorized", "inputs": []}, {"type": "error", "name": "ERC20Mock_OnlyVerified", "inputs": []}, {"type": "error", "name": "ERC20Mock_PohFailed", "inputs": []}, {"type": "error", "name": "ERC20Mock_TooMuch", "inputs": []}], "bytecode": {"object": "0x608060405234801561001057600080fd5b5060405161136b38038061136b83398101604081905261002f9161019e565b8585600361003d83826102d2565b50600461004a82826102d2565b5050600580546001600160a01b03808716610100026001600160a81b031990921660ff89161791909117909155600680549185166001600160a01b031990921691909117905550801561009d57806100ba565b6005546100ae9060ff16600a61048f565b6100ba906103e86104a5565b600855506104bc945050505050565b634e487b7160e01b600052604160045260246000fd5b600082601f8301126100f057600080fd5b81516001600160401b03811115610109576101096100c9565b604051601f8201601f19908116603f011681016001600160401b0381118282101715610137576101376100c9565b60405281815283820160200185101561014f57600080fd5b60005b8281101561016e57602081860181015183830182015201610152565b506000918101602001919091529392505050565b80516001600160a01b038116811461019957600080fd5b919050565b60008060008060008060c087890312156101b757600080fd5b86516001600160401b038111156101cd57600080fd5b6101d989828a016100df565b602089015190975090506001600160401b038111156101f757600080fd5b61020389828a016100df565b955050604087015160ff8116811461021a57600080fd5b935061022860608801610182565b925061023660808801610182565b915060a087015190509295509295509295565b600181811c9082168061025d57607f821691505b60208210810361027d57634e487b7160e01b600052602260045260246000fd5b50919050565b601f8211156102cd57806000526020600020601f840160051c810160208510156102aa5750805b601f840160051c820191505b818110156102ca57600081556001016102b6565b50505b505050565b81516001600160401b038111156102eb576102eb6100c9565b6102ff816102f98454610249565b84610283565b6020601f821160018114610333576000831561031b5750848201515b600019600385901b1c1916600184901b1784556102ca565b600084815260208120601f198516915b828110156103635787850151825560209485019460019092019101610343565b50848210156103815786840151600019600387901b60f8161c191681555b50505050600190811b01905550565b634e487b7160e01b600052601160045260246000fd5b6001815b60018411156103e1578085048111156103c5576103c5610390565b60018416156103d357908102905b60019390931c9280026103aa565b935093915050565b6000826103f857506001610489565b8161040557506000610489565b816001811461041b576002811461042557610441565b6001915050610489565b60ff84111561043657610436610390565b50506001821b610489565b5060208310610133831016604e8410600b8410161715610464575081810a610489565b61047160001984846103a6565b806000190482111561048557610485610390565b0290505b92915050565b600061049e60ff8416836103e9565b9392505050565b808202811582820484141761048957610489610390565b610ea0806104cb6000396000f3fe60806040526004361061012a5760003560e01c806370a08231116100ab5780639e6a1d7d1161006f5780639e6a1d7d14610353578063a9059cbb14610373578063d0e30db014610393578063dd62ed3e1461039b578063f7d070de146103e1578063f851a4401461040257600080fd5b806370a08231146102b257806394d008ef146102e857806395d89b4114610308578063996517cf1461031d5780639dc29fac1461033357600080fd5b8063313ce567116100f2578063313ce567146101f6578063330da60a1461021857806340c10f191461023a57806342966c681461025a578063545373c21461027a57600080fd5b806306fdde031461012f578063095ea7b31461015a57806318160ddd1461018a5780631e7269c5146101a957806323b872dd146101d6575b600080fd5b34801561013b57600080fd5b50610144610427565b6040516101519190610bb0565b60405180910390f35b34801561016657600080fd5b5061017a610175366004610be6565b6104b9565b6040519015158152602001610151565b34801561019657600080fd5b506002545b604051908152602001610151565b3480156101b557600080fd5b5061019b6101c4366004610c10565b60076020526000908152604090205481565b3480156101e257600080fd5b5061017a6101f1366004610c2b565b6104d3565b34801561020257600080fd5b5060055460405160ff9091168152602001610151565b34801561022457600080fd5b50610238610233366004610c76565b6104f7565b005b34801561024657600080fd5b50610238610255366004610be6565b610545565b34801561026657600080fd5b50610238610275366004610c93565b6105f1565b34801561028657600080fd5b5060065461029a906001600160a01b031681565b6040516001600160a01b039091168152602001610151565b3480156102be57600080fd5b5061019b6102cd366004610c10565b6001600160a01b031660009081526020819052604090205490565b3480156102f457600080fd5b50610238610303366004610cc2565b61062e565b34801561031457600080fd5b5061014461073a565b34801561032957600080fd5b5061019b60085481565b34801561033f57600080fd5b5061023861034e366004610be6565b610749565b34801561035f57600080fd5b5061023861036e366004610c93565b6107bc565b34801561037f57600080fd5b5061017a61038e366004610be6565b6107f1565b6102386107ff565b3480156103a757600080fd5b5061019b6103b6366004610d95565b6001600160a01b03918216600090815260016020908152604080832093909416825291909152205490565b3480156103ed57600080fd5b5060065461017a90600160a01b900460ff1681565b34801561040e57600080fd5b5060055461029a9061010090046001600160a01b031681565b60606003805461043690610dc8565b80601f016020809104026020016040519081016040528092919081815260200182805461046290610dc8565b80156104af5780601f10610484576101008083540402835291602001916104af565b820191906000526020600020905b81548152906001019060200180831161049257829003601f168201915b5050505050905090565b6000336104c781858561080b565b60019150505b92915050565b6000336104e185828561081d565b6104ec8585856108a0565b506001949350505050565b60055461010090046001600160a01b0316331461052757604051631947fcf760e11b815260040160405180910390fd5b60068054911515600160a01b0260ff60a01b19909216919091179055565b600654600160a01b900460ff1615610570576040516361fa883f60e11b815260040160405180910390fd5b6008546001600160a01b038316600090815260076020526040902054610597908390610e02565b106105b5576040516372ee742560e01b815260040160405180910390fd5b6001600160a01b038216600090815260076020526040812080548392906105dd908490610e02565b909155506105ed905082826108ff565b5050565b3360009081526007602052604090205481111561062157604051631b2a0e2160e01b815260040160405180910390fd5b61062b3382610935565b50565b6008546001600160a01b038416600090815260076020526040902054610655908490610e02565b10610673576040516372ee742560e01b815260040160405180910390fd5b600654604051633d3ac1b560e01b81526000916001600160a01b031690633d3ac1b5906106a69085903390600401610e23565b602060405180830381865afa1580156106c3573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906106e79190610e4d565b9050806107075760405163013f126160e51b815260040160405180910390fd5b6001600160a01b0384166000908152600760205260408120805485929061072f908490610e02565b909155505050505050565b60606004805461043690610dc8565b60055461010090046001600160a01b0316331461077957604051631947fcf760e11b815260040160405180910390fd5b6001600160a01b0382166000908152600760205260409020548111156107b257604051631b2a0e2160e01b815260040160405180910390fd5b6105ed8282610935565b60055461010090046001600160a01b031633146107ec57604051631947fcf760e11b815260040160405180910390fd5b600855565b6000336104c78185856108a0565b61080933346108ff565b565b610818838383600161096b565b505050565b6001600160a01b03838116600090815260016020908152604080832093861683529290522054600019811461089a578181101561088b57604051637dc7a0d960e11b81526001600160a01b038416600482015260248101829052604481018390526064015b60405180910390fd5b61089a8484848403600061096b565b50505050565b6001600160a01b0383166108ca57604051634b637e8f60e11b815260006004820152602401610882565b6001600160a01b0382166108f45760405163ec442f0560e01b815260006004820152602401610882565b610818838383610a40565b6001600160a01b0382166109295760405163ec442f0560e01b815260006004820152602401610882565b6105ed60008383610a40565b6001600160a01b03821661095f57604051634b637e8f60e11b815260006004820152602401610882565b6105ed82600083610a40565b6001600160a01b0384166109955760405163e602df0560e01b815260006004820152602401610882565b6001600160a01b0383166109bf57604051634a1406b160e11b815260006004820152602401610882565b6001600160a01b038085166000908152600160209081526040808320938716835292905220829055801561089a57826001600160a01b0316846001600160a01b03167f8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b92584604051610a3291815260200190565b60405180910390a350505050565b6001600160a01b038316610a6b578060026000828254610a609190610e02565b90915550610add9050565b6001600160a01b03831660009081526020819052604090205481811015610abe5760405163391434e360e21b81526001600160a01b03851660048201526024810182905260448101839052606401610882565b6001600160a01b03841660009081526020819052604090209082900390555b6001600160a01b038216610af957600280548290039055610b18565b6001600160a01b03821660009081526020819052604090208054820190555b816001600160a01b0316836001600160a01b03167fddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef83604051610b5d91815260200190565b60405180910390a3505050565b6000815180845260005b81811015610b9057602081850181015186830182015201610b74565b506000602082860101526020601f19601f83011685010191505092915050565b602081526000610bc36020830184610b6a565b9392505050565b80356001600160a01b0381168114610be157600080fd5b919050565b60008060408385031215610bf957600080fd5b610c0283610bca565b946020939093013593505050565b600060208284031215610c2257600080fd5b610bc382610bca565b600080600060608486031215610c4057600080fd5b610c4984610bca565b9250610c5760208501610bca565b929592945050506040919091013590565b801515811461062b57600080fd5b600060208284031215610c8857600080fd5b8135610bc381610c68565b600060208284031215610ca557600080fd5b5035919050565b634e487b7160e01b600052604160045260246000fd5b600080600060608486031215610cd757600080fd5b610ce084610bca565b925060208401359150604084013567ffffffffffffffff811115610d0357600080fd5b8401601f81018613610d1457600080fd5b803567ffffffffffffffff811115610d2e57610d2e610cac565b604051601f8201601f19908116603f0116810167ffffffffffffffff81118282101715610d5d57610d5d610cac565b604052818152828201602001881015610d7557600080fd5b816020840160208301376000602083830101528093505050509250925092565b60008060408385031215610da857600080fd5b610db183610bca565b9150610dbf60208401610bca565b90509250929050565b600181811c90821680610ddc57607f821691505b602082108103610dfc57634e487b7160e01b600052602260045260246000fd5b50919050565b808201808211156104cd57634e487b7160e01b600052601160045260246000fd5b604081526000610e366040830185610b6a565b905060018060a01b03831660208301529392505050565b600060208284031215610e5f57600080fd5b8151610bc381610c6856fea26469706673582212200b2bdde2e2278844ef5c1f183158f67cd4ea0489e11a0724d68c5299562aa09264736f6c634300081c0033", "sourceMap": "208:2615:204:-:0;;;605:351;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;790:5;797:7;1962:5:39;:13;790:5:204;1962::39;:13;:::i;:::-;-1:-1:-1;1985:7:39;:17;1995:7;1985;:17;:::i;:::-;-1:-1:-1;;816:2:204::1;:14:::0;;-1:-1:-1;;;;;840:14:204;;::::1;816;840;-1:-1:-1::0;;;;;;840:14:204;;;816::::1;::::0;::::1;840::::0;;;;::::1;::::0;;;864:9:::1;:22:::0;;;;::::1;-1:-1:-1::0;;;;;;864:22:204;;::::1;::::0;;;::::1;::::0;;-1:-1:-1;909:11:204;;:40:::1;;943:6;909:40;;;937:2;::::0;931:8:::1;::::0;937:2:::1;;931;:8;:::i;:::-;923:17;::::0;:4:::1;:17;:::i;:::-;897:9;:52:::0;-1:-1:-1;208:2615:204;;-1:-1:-1;;;;;208:2615:204;14:127:242;75:10;70:3;66:20;63:1;56:31;106:4;103:1;96:15;130:4;127:1;120:15;146:834;200:5;253:3;246:4;238:6;234:17;230:27;220:55;;271:1;268;261:12;220:55;298:13;;-1:-1:-1;;;;;323:30:242;;320:56;;;356:18;;:::i;:::-;405:2;399:9;497:2;459:17;;-1:-1:-1;;455:31:242;;;488:2;451:40;447:54;435:67;;-1:-1:-1;;;;;517:34:242;;553:22;;;514:62;511:88;;;579:18;;:::i;:::-;615:2;608:22;639;;;680:19;;;701:4;676:30;673:39;-1:-1:-1;670:59:242;;;725:1;722;715:12;670:59;747:1;757:143;771:6;768:1;765:13;757:143;;;883:4;867:14;;;863:25;;857:32;834:14;;;830:25;;823:67;786:12;757:143;;;-1:-1:-1;948:1:242;920:19;;;941:4;916:30;909:41;;;;924:6;146:834;-1:-1:-1;;;146:834:242:o;985:177::-;1064:13;;-1:-1:-1;;;;;1106:31:242;;1096:42;;1086:70;;1152:1;1149;1142:12;1086:70;985:177;;;:::o;1167:941::-;1300:6;1308;1316;1324;1332;1340;1393:3;1381:9;1372:7;1368:23;1364:33;1361:53;;;1410:1;1407;1400:12;1361:53;1437:16;;-1:-1:-1;;;;;1465:30:242;;1462:50;;;1508:1;1505;1498:12;1462:50;1531:61;1584:7;1575:6;1564:9;1560:22;1531:61;:::i;:::-;1638:2;1623:18;;1617:25;1521:71;;-1:-1:-1;1617:25:242;-1:-1:-1;;;;;;1654:32:242;;1651:52;;;1699:1;1696;1689:12;1651:52;1722:63;1777:7;1766:8;1755:9;1751:24;1722:63;:::i;:::-;1712:73;;;1828:2;1817:9;1813:18;1807:25;1872:4;1865:5;1861:16;1854:5;1851:27;1841:55;;1892:1;1889;1882:12;1841:55;1915:5;-1:-1:-1;1939:49:242;1984:2;1969:18;;1939:49;:::i;:::-;1929:59;;2007:50;2052:3;2041:9;2037:19;2007:50;:::i;:::-;1997:60;;2097:3;2086:9;2082:19;2076:26;2066:36;;1167:941;;;;;;;;:::o;2113:380::-;2192:1;2188:12;;;;2235;;;2256:61;;2310:4;2302:6;2298:17;2288:27;;2256:61;2363:2;2355:6;2352:14;2332:18;2329:38;2326:161;;2409:10;2404:3;2400:20;2397:1;2390:31;2444:4;2441:1;2434:15;2472:4;2469:1;2462:15;2326:161;;2113:380;;;:::o;2624:518::-;2726:2;2721:3;2718:11;2715:421;;;2762:5;2759:1;2752:16;2806:4;2803:1;2793:18;2876:2;2864:10;2860:19;2857:1;2853:27;2847:4;2843:38;2912:4;2900:10;2897:20;2894:47;;;-1:-1:-1;2935:4:242;2894:47;2990:2;2985:3;2981:12;2978:1;2974:20;2968:4;2964:31;2954:41;;3045:81;3063:2;3056:5;3053:13;3045:81;;;3122:1;3108:16;;3089:1;3078:13;3045:81;;;3049:3;;2715:421;2624:518;;;:::o;3318:1299::-;3438:10;;-1:-1:-1;;;;;3460:30:242;;3457:56;;;3493:18;;:::i;:::-;3522:97;3612:6;3572:38;3604:4;3598:11;3572:38;:::i;:::-;3566:4;3522:97;:::i;:::-;3668:4;3699:2;3688:14;;3716:1;3711:649;;;;4404:1;4421:6;4418:89;;;-1:-1:-1;4473:19:242;;;4467:26;4418:89;-1:-1:-1;;3275:1:242;3271:11;;;3267:24;3263:29;3253:40;3299:1;3295:11;;;3250:57;4520:81;;3681:930;;3711:649;2571:1;2564:14;;;2608:4;2595:18;;-1:-1:-1;;3747:20:242;;;3865:222;3879:7;3876:1;3873:14;3865:222;;;3961:19;;;3955:26;3940:42;;4068:4;4053:20;;;;4021:1;4009:14;;;;3895:12;3865:222;;;3869:3;4115:6;4106:7;4103:19;4100:201;;;4176:19;;;4170:26;-1:-1:-1;;4259:1:242;4255:14;;;4271:3;4251:24;4247:37;4243:42;4228:58;4213:74;;4100:201;-1:-1:-1;;;;4347:1:242;4331:14;;;4327:22;4314:36;;-1:-1:-1;3318:1299:242:o;4622:127::-;4683:10;4678:3;4674:20;4671:1;4664:31;4714:4;4711:1;4704:15;4738:4;4735:1;4728:15;4754:375;4842:1;4860:5;4874:249;4895:1;4885:8;4882:15;4874:249;;;4945:4;4940:3;4936:14;4930:4;4927:24;4924:50;;;4954:18;;:::i;:::-;5004:1;4994:8;4990:16;4987:49;;;5018:16;;;;4987:49;5101:1;5097:16;;;;;5057:15;;4874:249;;;4754:375;;;;;;:::o;5134:902::-;5183:5;5213:8;5203:80;;-1:-1:-1;5254:1:242;5268:5;;5203:80;5302:4;5292:76;;-1:-1:-1;5339:1:242;5353:5;;5292:76;5384:4;5402:1;5397:59;;;;5470:1;5465:174;;;;5377:262;;5397:59;5427:1;5418:10;;5441:5;;;5465:174;5502:3;5492:8;5489:17;5486:43;;;5509:18;;:::i;:::-;-1:-1:-1;;5565:1:242;5551:16;;5624:5;;5377:262;;5723:2;5713:8;5710:16;5704:3;5698:4;5695:13;5691:36;5685:2;5675:8;5672:16;5667:2;5661:4;5658:12;5654:35;5651:77;5648:203;;;-1:-1:-1;5760:19:242;;;5836:5;;5648:203;5883:42;-1:-1:-1;;5908:8:242;5902:4;5883:42;:::i;:::-;5961:6;5957:1;5953:6;5949:19;5940:7;5937:32;5934:58;;;5972:18;;:::i;:::-;6010:20;;-1:-1:-1;5134:902:242;;;;;:::o;6041:140::-;6099:5;6128:47;6169:4;6159:8;6155:19;6149:4;6128:47;:::i;:::-;6119:56;6041:140;-1:-1:-1;;;6041:140:242:o;6186:168::-;6259:9;;;6290;;6307:15;;;6301:22;;6287:37;6277:71;;6328:18;;:::i;6186:168::-;208:2615:204;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "208:2615:204:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2074:89:39;;;;;;;;;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;4293:186;;;;;;;;;;-1:-1:-1;4293:186:39;;;;;:::i;:::-;;:::i;:::-;;;1292:14:242;;1285:22;1267:41;;1255:2;1240:18;4293:186:39;1127:187:242;3144:97:39;;;;;;;;;;-1:-1:-1;3222:12:39;;3144:97;;;1465:25:242;;;1453:2;1438:18;3144:97:39;1319:177:242;351:41:204;;;;;;;;;;-1:-1:-1;351:41:204;;;;;:::i;:::-;;;;;;;;;;;;;;5039:244:39;;;;;;;;;;-1:-1:-1;5039:244:39;;;;;:::i;:::-;;:::i;1310:83:204:-;;;;;;;;;;-1:-1:-1;1384:2:204;;1310:83;;1384:2;;;;2213:36:242;;2201:2;2186:18;1310:83:204;2071:184:242;985:148:204;;;;;;;;;;-1:-1:-1;985:148:204;;;;;:::i;:::-;;:::i;:::-;;1976:258;;;;;;;;;;-1:-1:-1;1976:258:204;;;;;:::i;:::-;;:::i;2302:193::-;;;;;;;;;;-1:-1:-1;2302:193:204;;;;;:::i;:::-;;:::i;291:24::-;;;;;;;;;;-1:-1:-1;291:24:204;;;;-1:-1:-1;;;;;291:24:204;;;;;;-1:-1:-1;;;;;3024:32:242;;;3006:51;;2994:2;2979:18;291:24:204;2860:203:242;3299:116:39;;;;;;;;;;-1:-1:-1;3299:116:39;;;;;:::i;:::-;-1:-1:-1;;;;;3390:18:39;3364:7;3390:18;;;;;;;;;;;;3299:116;1474:324:204;;;;;;;;;;-1:-1:-1;1474:324:204;;;;;:::i;:::-;;:::i;2276:93:39:-;;;;;;;;;;;;;:::i;399:24:204:-;;;;;;;;;;;;;;;;2563:258;;;;;;;;;;-1:-1:-1;2563:258:204;;;;;:::i;:::-;;:::i;1139:147::-;;;;;;;;;;-1:-1:-1;1139:147:204;;;;;:::i;:::-;;:::i;3610:178:39:-;;;;;;;;;;-1:-1:-1;3610:178:39;;;;;:::i;:::-;;:::i;1804:81:204:-;;;:::i;3846:140:39:-;;;;;;;;;;-1:-1:-1;3846:140:39;;;;;:::i;:::-;-1:-1:-1;;;;;3952:18:39;;;3926:7;3952:18;;;:11;:18;;;;;;;;:27;;;;;;;;;;;;;3846:140;321:24:204;;;;;;;;;;-1:-1:-1;321:24:204;;;;-1:-1:-1;;;321:24:204;;;;;;265:20;;;;;;;;;;-1:-1:-1;265:20:204;;;;;;;-1:-1:-1;;;;;265:20:204;;;2074:89:39;2119:13;2151:5;2144:12;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2074:89;:::o;4293:186::-;4366:4;735:10:47;4420:31:39;735:10:47;4436:7:39;4445:5;4420:8;:31::i;:::-;4468:4;4461:11;;;4293:186;;;;;:::o;5039:244::-;5126:4;735:10:47;5182:37:39;5198:4;735:10:47;5213:5:39;5182:15;:37::i;:::-;5229:26;5239:4;5245:2;5249:5;5229:9;:26::i;:::-;-1:-1:-1;5272:4:39;;5039:244;-1:-1:-1;;;;5039:244:39:o;985:148:204:-;1062:5;;;;;-1:-1:-1;;;;;1062:5:204;1048:10;:19;1040:55;;;;-1:-1:-1;;;1040:55:204;;;;;;;;;;;;1105:12;:21;;;;;-1:-1:-1;;;1105:21:204;-1:-1:-1;;;;1105:21:204;;;;;;;;;985:148::o;1976:258::-;2048:12;;-1:-1:-1;;;2048:12:204;;;;2047:13;2039:48;;;;-1:-1:-1;;;2039:48:204;;;;;;;;;;;;2129:9;;-1:-1:-1;;;;;2105:11:204;;;;;;:6;:11;;;;;;:21;;2119:7;;2105:21;:::i;:::-;:33;2097:69;;;;-1:-1:-1;;;2097:69:204;;;;;;;;;;;;-1:-1:-1;;;;;2176:11:204;;;;;;:6;:11;;;;;:22;;2191:7;;2176:11;:22;;2191:7;;2176:22;:::i;:::-;;;;-1:-1:-1;2208:19:204;;-1:-1:-1;2214:3:204;2219:7;2208:5;:19::i;:::-;1976:258;;:::o;2302:193::-;2367:10;2360:18;;;;:6;:18;;;;;;:29;-1:-1:-1;2360:29:204;2352:59;;;;-1:-1:-1;;;2352:59:204;;;;;;;;;;;;2462:26;2468:10;2480:7;2462:5;:26::i;:::-;2302:193;:::o;1474:324::-;1593:9;;-1:-1:-1;;;;;1569:11:204;;;;;;:6;:11;;;;;;:21;;1583:7;;1569:21;:::i;:::-;:33;1561:69;;;;-1:-1:-1;;;1561:69:204;;;;;;;;;;;;1669:9;;1656:53;;-1:-1:-1;;;1656:53:204;;1640:13;;-1:-1:-1;;;;;1669:9:204;;1656:30;;:53;;1687:9;;1698:10;;1656:53;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1640:69;;1727:8;1719:40;;;;-1:-1:-1;;;1719:40:204;;;;;;;;;;;;-1:-1:-1;;;;;1769:11:204;;;;;;:6;:11;;;;;:22;;1784:7;;1769:11;:22;;1784:7;;1769:22;:::i;:::-;;;;-1:-1:-1;;;;;;1474:324:204:o;2276:93:39:-;2323:13;2355:7;2348:14;;;;;:::i;2563:258:204:-;2650:5;;;;;-1:-1:-1;;;;;2650:5:204;2636:10;:19;2628:55;;;;-1:-1:-1;;;2628:55:204;;;;;;;;;;;;-1:-1:-1;;;;;2701:13:204;;;;;;:6;:13;;;;;;:24;-1:-1:-1;2701:24:204;2693:54;;;;-1:-1:-1;;;2693:54:204;;;;;;;;;;;;2793:21;2799:5;2806:7;2793:5;:21::i;1139:147::-;1218:5;;;;;-1:-1:-1;;;;;1218:5:204;1204:10;:19;1196:55;;;;-1:-1:-1;;;1196:55:204;;;;;;;;;;;;1261:9;:18;1139:147::o;3610:178:39:-;3679:4;735:10:47;3733:27:39;735:10:47;3750:2:39;3754:5;3733:9;:27::i;1804:81:204:-;1850:28;1856:10;1868:9;1850:5;:28::i;:::-;1804:81::o;8989:128:39:-;9073:37;9082:5;9089:7;9098:5;9105:4;9073:8;:37::i;:::-;8989:128;;;:::o;10663:477::-;-1:-1:-1;;;;;3952:18:39;;;10762:24;3952:18;;;:11;:18;;;;;;;;:27;;;;;;;;;;-1:-1:-1;;10828:37:39;;10824:310;;10904:5;10885:16;:24;10881:130;;;10936:60;;-1:-1:-1;;;10936:60:39;;-1:-1:-1;;;;;6004:32:242;;10936:60:39;;;5986:51:242;6053:18;;;6046:34;;;6096:18;;;6089:34;;;5959:18;;10936:60:39;;;;;;;;10881:130;11052:57;11061:5;11068:7;11096:5;11077:16;:24;11103:5;11052:8;:57::i;:::-;10752:388;10663:477;;;:::o;5656:300::-;-1:-1:-1;;;;;5739:18:39;;5735:86;;5780:30;;-1:-1:-1;;;5780:30:39;;5807:1;5780:30;;;3006:51:242;2979:18;;5780:30:39;2860:203:242;5735:86:39;-1:-1:-1;;;;;5834:16:39;;5830:86;;5873:32;;-1:-1:-1;;;5873:32:39;;5902:1;5873:32;;;3006:51:242;2979:18;;5873:32:39;2860:203:242;5830:86:39;5925:24;5933:4;5939:2;5943:5;5925:7;:24::i;7721:208::-;-1:-1:-1;;;;;7791:21:39;;7787:91;;7835:32;;-1:-1:-1;;;7835:32:39;;7864:1;7835:32;;;3006:51:242;2979:18;;7835:32:39;2860:203:242;7787:91:39;7887:35;7903:1;7907:7;7916:5;7887:7;:35::i;8247:206::-;-1:-1:-1;;;;;8317:21:39;;8313:89;;8361:30;;-1:-1:-1;;;8361:30:39;;8388:1;8361:30;;;3006:51:242;2979:18;;8361:30:39;2860:203:242;8313:89:39;8411:35;8419:7;8436:1;8440:5;8411:7;:35::i;9949:432::-;-1:-1:-1;;;;;10061:19:39;;10057:89;;10103:32;;-1:-1:-1;;;10103:32:39;;10132:1;10103:32;;;3006:51:242;2979:18;;10103:32:39;2860:203:242;10057:89:39;-1:-1:-1;;;;;10159:21:39;;10155:90;;10203:31;;-1:-1:-1;;;10203:31:39;;10231:1;10203:31;;;3006:51:242;2979:18;;10203:31:39;2860:203:242;10155:90:39;-1:-1:-1;;;;;10254:18:39;;;;;;;:11;:18;;;;;;;;:27;;;;;;;;;:35;;;10299:76;;;;10349:7;-1:-1:-1;;;;;10333:31:39;10342:5;-1:-1:-1;;;;;10333:31:39;;10358:5;10333:31;;;;1465:25:242;;1453:2;1438:18;;1319:177;10333:31:39;;;;;;;;9949:432;;;;:::o;6271:1107::-;-1:-1:-1;;;;;6360:18:39;;6356:540;;6512:5;6496:12;;:21;;;;;;;:::i;:::-;;;;-1:-1:-1;6356:540:39;;-1:-1:-1;6356:540:39;;-1:-1:-1;;;;;6570:15:39;;6548:19;6570:15;;;;;;;;;;;6603:19;;;6599:115;;;6649:50;;-1:-1:-1;;;6649:50:39;;-1:-1:-1;;;;;6004:32:242;;6649:50:39;;;5986:51:242;6053:18;;;6046:34;;;6096:18;;;6089:34;;;5959:18;;6649:50:39;5784:345:242;6599:115:39;-1:-1:-1;;;;;6834:15:39;;:9;:15;;;;;;;;;;6852:19;;;;6834:37;;6356:540;-1:-1:-1;;;;;6910:16:39;;6906:425;;7073:12;:21;;;;;;;6906:425;;;-1:-1:-1;;;;;7284:13:39;;:9;:13;;;;;;;;;;:22;;;;;;6906:425;7361:2;-1:-1:-1;;;;;7346:25:39;7355:4;-1:-1:-1;;;;;7346:25:39;;7365:5;7346:25;;;;1465::242;;1453:2;1438:18;;1319:177;7346:25:39;;;;;;;;6271:1107;;;:::o;14:400:242:-;56:3;94:5;88:12;121:6;116:3;109:19;146:1;156:139;170:6;167:1;164:13;156:139;;;278:4;263:13;;;259:24;;253:31;233:11;;;229:22;;222:63;185:12;156:139;;;160:3;340:1;333:4;324:6;319:3;315:16;311:27;304:38;403:4;396:2;392:7;387:2;379:6;375:15;371:29;366:3;362:39;358:50;351:57;;;14:400;;;;:::o;419:220::-;568:2;557:9;550:21;531:4;588:45;629:2;618:9;614:18;606:6;588:45;:::i;:::-;580:53;419:220;-1:-1:-1;;;419:220:242:o;644:173::-;712:20;;-1:-1:-1;;;;;761:31:242;;751:42;;741:70;;807:1;804;797:12;741:70;644:173;;;:::o;822:300::-;890:6;898;951:2;939:9;930:7;926:23;922:32;919:52;;;967:1;964;957:12;919:52;990:29;1009:9;990:29;:::i;:::-;980:39;1088:2;1073:18;;;;1060:32;;-1:-1:-1;;;822:300:242:o;1501:186::-;1560:6;1613:2;1601:9;1592:7;1588:23;1584:32;1581:52;;;1629:1;1626;1619:12;1581:52;1652:29;1671:9;1652:29;:::i;1692:374::-;1769:6;1777;1785;1838:2;1826:9;1817:7;1813:23;1809:32;1806:52;;;1854:1;1851;1844:12;1806:52;1877:29;1896:9;1877:29;:::i;:::-;1867:39;;1925:38;1959:2;1948:9;1944:18;1925:38;:::i;:::-;1692:374;;1915:48;;-1:-1:-1;;;2032:2:242;2017:18;;;;2004:32;;1692:374::o;2260:118::-;2346:5;2339:13;2332:21;2325:5;2322:32;2312:60;;2368:1;2365;2358:12;2383:241;2439:6;2492:2;2480:9;2471:7;2467:23;2463:32;2460:52;;;2508:1;2505;2498:12;2460:52;2547:9;2534:23;2566:28;2588:5;2566:28;:::i;2629:226::-;2688:6;2741:2;2729:9;2720:7;2716:23;2712:32;2709:52;;;2757:1;2754;2747:12;2709:52;-1:-1:-1;2802:23:242;;2629:226;-1:-1:-1;2629:226:242:o;3068:127::-;3129:10;3124:3;3120:20;3117:1;3110:31;3160:4;3157:1;3150:15;3184:4;3181:1;3174:15;3200:1132;3286:6;3294;3302;3355:2;3343:9;3334:7;3330:23;3326:32;3323:52;;;3371:1;3368;3361:12;3323:52;3394:29;3413:9;3394:29;:::i;:::-;3384:39;-1:-1:-1;3492:2:242;3477:18;;3464:32;;-1:-1:-1;3571:2:242;3556:18;;3543:32;3598:18;3587:30;;3584:50;;;3630:1;3627;3620:12;3584:50;3653:22;;3706:4;3698:13;;3694:27;-1:-1:-1;3684:55:242;;3735:1;3732;3725:12;3684:55;3775:2;3762:16;3801:18;3793:6;3790:30;3787:56;;;3823:18;;:::i;:::-;3872:2;3866:9;3964:2;3926:17;;-1:-1:-1;;3922:31:242;;;3955:2;3918:40;3914:54;3902:67;;3999:18;3984:34;;4020:22;;;3981:62;3978:88;;;4046:18;;:::i;:::-;4082:2;4075:22;4106;;;4147:15;;;4164:2;4143:24;4140:37;-1:-1:-1;4137:57:242;;;4190:1;4187;4180:12;4137:57;4246:6;4241:2;4237;4233:11;4228:2;4220:6;4216:15;4203:50;4299:1;4294:2;4285:6;4277;4273:19;4269:28;4262:39;4320:6;4310:16;;;;;3200:1132;;;;;:::o;4337:260::-;4405:6;4413;4466:2;4454:9;4445:7;4441:23;4437:32;4434:52;;;4482:1;4479;4472:12;4434:52;4505:29;4524:9;4505:29;:::i;:::-;4495:39;;4553:38;4587:2;4576:9;4572:18;4553:38;:::i;:::-;4543:48;;4337:260;;;;;:::o;4602:380::-;4681:1;4677:12;;;;4724;;;4745:61;;4799:4;4791:6;4787:17;4777:27;;4745:61;4852:2;4844:6;4841:14;4821:18;4818:38;4815:161;;4898:10;4893:3;4889:20;4886:1;4879:31;4933:4;4930:1;4923:15;4961:4;4958:1;4951:15;4815:161;;4602:380;;;:::o;4987:222::-;5052:9;;;5073:10;;;5070:133;;;5125:10;5120:3;5116:20;5113:1;5106:31;5160:4;5157:1;5150:15;5188:4;5185:1;5178:15;5214:315;5389:2;5378:9;5371:21;5352:4;5409:45;5450:2;5439:9;5435:18;5427:6;5409:45;:::i;:::-;5401:53;;5519:1;5515;5510:3;5506:11;5502:19;5494:6;5490:32;5485:2;5474:9;5470:18;5463:60;5214:315;;;;;:::o;5534:245::-;5601:6;5654:2;5642:9;5633:7;5629:23;5625:32;5622:52;;;5670:1;5667;5660:12;5622:52;5702:9;5696:16;5721:28;5743:5;5721:28;:::i", "linkReferences": {}}, "methodIdentifiers": {"admin()": "f851a440", "allowance(address,address)": "dd62ed3e", "approve(address,uint256)": "095ea7b3", "balanceOf(address)": "70a08231", "burn(address,uint256)": "9dc29fac", "burn(uint256)": "42966c68", "decimals()": "313ce567", "deposit()": "d0e30db0", "mint(address,uint256)": "40c10f19", "mint(address,uint256,bytes)": "94d008ef", "mintLimit()": "996517cf", "minted(address)": "1e7269c5", "name()": "06fdde03", "onlyVerified()": "f7d070de", "pohVerify()": "545373c2", "setMintLimit(uint256)": "9e6a1d7d", "setOnlyVerify(bool)": "330da60a", "symbol()": "95d89b41", "totalSupply()": "18160ddd", "transfer(address,uint256)": "a9059cbb", "transferFrom(address,address,uint256)": "23b872dd"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"string\",\"name\":\"_name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"_symbol\",\"type\":\"string\"},{\"internalType\":\"uint8\",\"name\":\"_decimals\",\"type\":\"uint8\"},{\"internalType\":\"address\",\"name\":\"_owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_pohVerify\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_limit\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"allowance\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"needed\",\"type\":\"uint256\"}],\"name\":\"ERC20InsufficientAllowance\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"balance\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"needed\",\"type\":\"uint256\"}],\"name\":\"ERC20InsufficientBalance\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"approver\",\"type\":\"address\"}],\"name\":\"ERC20InvalidApprover\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"}],\"name\":\"ERC20InvalidReceiver\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"ERC20InvalidSender\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"}],\"name\":\"ERC20InvalidSpender\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ERC20Mock_AlreadyMinted\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ERC20Mock_NotAuthorized\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ERC20Mock_OnlyVerified\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ERC20Mock_PohFailed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ERC20Mock_TooMuch\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Approval\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"admin\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"}],\"name\":\"allowance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"approve\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_amount\",\"type\":\"uint256\"}],\"name\":\"burn\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_from\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_amount\",\"type\":\"uint256\"}],\"name\":\"burn\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"decimals\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"deposit\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_amount\",\"type\":\"uint256\"}],\"name\":\"mint\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_amount\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"signature\",\"type\":\"bytes\"}],\"name\":\"mint\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"mintLimit\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"minted\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"name\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"onlyVerified\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"pohVerify\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_limit\",\"type\":\"uint256\"}],\"name\":\"setMintLimit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bool\",\"name\":\"status\",\"type\":\"bool\"}],\"name\":\"setOnlyVerify\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"symbol\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalSupply\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"transfer\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"transferFrom\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"ERC20InsufficientAllowance(address,uint256,uint256)\":[{\"details\":\"Indicates a failure with the `spender`\\u2019s `allowance`. Used in transfers.\",\"params\":{\"allowance\":\"Amount of tokens a `spender` is allowed to operate with.\",\"needed\":\"Minimum amount required to perform a transfer.\",\"spender\":\"Address that may be allowed to operate on tokens without being their owner.\"}}],\"ERC20InsufficientBalance(address,uint256,uint256)\":[{\"details\":\"Indicates an error related to the current `balance` of a `sender`. Used in transfers.\",\"params\":{\"balance\":\"Current balance for the interacting account.\",\"needed\":\"Minimum amount required to perform a transfer.\",\"sender\":\"Address whose tokens are being transferred.\"}}],\"ERC20InvalidApprover(address)\":[{\"details\":\"Indicates a failure with the `approver` of a token to be approved. Used in approvals.\",\"params\":{\"approver\":\"Address initiating an approval operation.\"}}],\"ERC20InvalidReceiver(address)\":[{\"details\":\"Indicates a failure with the token `receiver`. Used in transfers.\",\"params\":{\"receiver\":\"Address to which tokens are being transferred.\"}}],\"ERC20InvalidSender(address)\":[{\"details\":\"Indicates a failure with the token `sender`. Used in transfers.\",\"params\":{\"sender\":\"Address whose tokens are being transferred.\"}}],\"ERC20InvalidSpender(address)\":[{\"details\":\"Indicates a failure with the `spender` to be approved. Used in approvals.\",\"params\":{\"spender\":\"Address that may be allowed to operate on tokens without being their owner.\"}}]},\"events\":{\"Approval(address,address,uint256)\":{\"details\":\"Emitted when the allowance of a `spender` for an `owner` is set by a call to {approve}. `value` is the new allowance.\"},\"Transfer(address,address,uint256)\":{\"details\":\"Emitted when `value` tokens are moved from one account (`from`) to another (`to`). Note that `value` may be zero.\"}},\"kind\":\"dev\",\"methods\":{\"allowance(address,address)\":{\"details\":\"See {IERC20-allowance}.\"},\"approve(address,uint256)\":{\"details\":\"See {IERC20-approve}. NOTE: If `value` is the maximum `uint256`, the allowance is not updated on `transferFrom`. This is semantically equivalent to an infinite approval. Requirements: - `spender` cannot be the zero address.\"},\"balanceOf(address)\":{\"details\":\"See {IERC20-balanceOf}.\"},\"burn(address,uint256)\":{\"details\":\"publicburn does not reset `minted`\"},\"burn(uint256)\":{\"details\":\"publicburn does not reset `minted`\"},\"decimals()\":{\"details\":\"view\"},\"mint(address,uint256)\":{\"details\":\"publicmint up to `mintLimit` when `onlyVerified == false`\"},\"mint(address,uint256,bytes)\":{\"details\":\"mint up to `mintLimit` using a proof of humanity verification\"},\"name()\":{\"details\":\"Returns the name of the token.\"},\"setOnlyVerify(bool)\":{\"details\":\"onlyAdmin\"},\"symbol()\":{\"details\":\"Returns the symbol of the token, usually a shorter version of the name.\"},\"totalSupply()\":{\"details\":\"See {IERC20-totalSupply}.\"},\"transfer(address,uint256)\":{\"details\":\"See {IERC20-transfer}. Requirements: - `to` cannot be the zero address. - the caller must have a balance of at least `value`.\"},\"transferFrom(address,address,uint256)\":{\"details\":\"See {IERC20-transferFrom}. Emits an {Approval} event indicating the updated allowance. This is not required by the EIP. See the note at the beginning of {ERC20}. NOTE: Does not update the allowance if the current allowance is the maximum `uint256`. Requirements: - `from` and `to` cannot be the zero address. - `from` must have a balance of at least `value`. - the caller must have allowance for ``from``'s tokens of at least `value`.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"test/mocks/ERC20Mock.sol\":\"ERC20Mock\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol\":{\"keccak256\":\"0x60c65f701957fdd6faea1acb0bb45825791d473693ed9ecb34726fdfaa849dd7\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ea290300e0efc4d901244949dc4d877fd46e6c5e43dc2b26620e8efab3ab803f\",\"dweb:/ipfs/QmcLLJppxKeJWqHxE2CUkcfhuRTgHSn8J4kijcLa5MYhSt\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol\":{\"keccak256\":\"0xc3e1fa9d1987f8d349dfb4d6fe93bf2ca014b52ba335cfac30bfe71e357e6f80\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c5703ccdeb7b1d685e375ed719117e9edf2ab4bc544f24f23b0d50ec82257229\",\"dweb:/ipfs/QmTdwkbQq7owpCiyuzE7eh5LrD2ddrBCZ5WHVsWPi1RrTS\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xc6a8ff0ea489379b61faa647490411b80102578440ab9d84e9a957cc12164e70\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0ea104e577e63faea3b69c415637e99e755dcbf64c5833d7140c35a714d6d90c\",\"dweb:/ipfs/Qmau6x4Ns9XdyynRCNNp3RhLqijJjFm7z5fyZazfYFGYdq\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xaa761817f6cd7892fcf158b3c776b34551cde36f48ff9703d53898bc45a94ea2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0ad7c8d4d08938c8dfc43d75a148863fb324b80cf53e0a36f7e5a4ac29008850\",\"dweb:/ipfs/QmcrhfPgVNf5mkdhQvy1pMv51TFokD3Y4Wa5WZhFqVh8UV\"]},\"lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"src/interfaces/external/poh/IPohVerifier.sol\":{\"keccak256\":\"0xce93e2d734074add2a6d36867d59fd24e2d44ca48c524bd8dddabd4163038205\",\"license\":\"AGPL-3.0\",\"urls\":[\"bzz-raw://9e94dc14721e8873d15d7032afa3d2b5fea76e9c4029698aa489f2ea2edcedf6\",\"dweb:/ipfs/QmR55sQx36Ub7wUjDeemUSNotdmYCkK8AmUEYbRezF5Xhy\"]},\"test/mocks/ERC20Mock.sol\":{\"keccak256\":\"0x86e2d4e622d3172aff95ad8436b1af1213c7be008fbbe1e74f29577ea0afd92f\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://2e2a534d2bf62fd7be511ac74e372b1d1e4cc08033fba742950785315553e3fb\",\"dweb:/ipfs/Qme24GSF4BFzuHETCfPzUafV1bsmVHVvbqQPNxDoU8AafR\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "string", "name": "_name", "type": "string"}, {"internalType": "string", "name": "_symbol", "type": "string"}, {"internalType": "uint8", "name": "_decimals", "type": "uint8"}, {"internalType": "address", "name": "_owner", "type": "address"}, {"internalType": "address", "name": "_pohVerify", "type": "address"}, {"internalType": "uint256", "name": "_limit", "type": "uint256"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "type": "error", "name": "ERC20InsufficientAllowance"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "type": "error", "name": "ERC20InsufficientBalance"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "type": "error", "name": "ERC20InvalidApprover"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "type": "error", "name": "ERC20InvalidReceiver"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "type": "error", "name": "ERC20InvalidSender"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "type": "error", "name": "ERC20InvalidSpender"}, {"inputs": [], "type": "error", "name": "ERC20Mock_AlreadyMinted"}, {"inputs": [], "type": "error", "name": "ERC20Mock_NotAuthorized"}, {"inputs": [], "type": "error", "name": "ERC20Mock_OnlyVerified"}, {"inputs": [], "type": "error", "name": "ERC20Mock_PohFailed"}, {"inputs": [], "type": "error", "name": "ERC20Mock_TooMuch"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address", "indexed": true}, {"internalType": "address", "name": "spender", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "value", "type": "uint256", "indexed": false}], "type": "event", "name": "Approval", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "from", "type": "address", "indexed": true}, {"internalType": "address", "name": "to", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "value", "type": "uint256", "indexed": false}], "type": "event", "name": "Transfer", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "admin", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "stateMutability": "view", "type": "function", "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "_amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "burn"}, {"inputs": [{"internalType": "address", "name": "_from", "type": "address"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "burn"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}]}, {"inputs": [], "stateMutability": "payable", "type": "function", "name": "deposit"}, {"inputs": [{"internalType": "address", "name": "_to", "type": "address"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "mint"}, {"inputs": [{"internalType": "address", "name": "_to", "type": "address"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "mint"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "mintLimit", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "minted", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "onlyVerified", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "pohVerify", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "uint256", "name": "_limit", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "setMintLimit"}, {"inputs": [{"internalType": "bool", "name": "status", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "setOnlyVerify"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}], "devdoc": {"kind": "dev", "methods": {"allowance(address,address)": {"details": "See {IERC20-allowance}."}, "approve(address,uint256)": {"details": "See {IERC20-approve}. NOTE: If `value` is the maximum `uint256`, the allowance is not updated on `transferFrom`. This is semantically equivalent to an infinite approval. Requirements: - `spender` cannot be the zero address."}, "balanceOf(address)": {"details": "See {IERC20-balanceOf}."}, "burn(address,uint256)": {"details": "publicburn does not reset `minted`"}, "burn(uint256)": {"details": "publicburn does not reset `minted`"}, "decimals()": {"details": "view"}, "mint(address,uint256)": {"details": "publicmint up to `mintLimit` when `onlyVerified == false`"}, "mint(address,uint256,bytes)": {"details": "mint up to `mintLimit` using a proof of humanity verification"}, "name()": {"details": "Returns the name of the token."}, "setOnlyVerify(bool)": {"details": "only<PERSON><PERSON><PERSON>"}, "symbol()": {"details": "Returns the symbol of the token, usually a shorter version of the name."}, "totalSupply()": {"details": "See {IERC20-totalSupply}."}, "transfer(address,uint256)": {"details": "See {IERC20-transfer}. Requirements: - `to` cannot be the zero address. - the caller must have a balance of at least `value`."}, "transferFrom(address,address,uint256)": {"details": "See {IERC20-transferFrom}. Emits an {Approval} event indicating the updated allowance. This is not required by the EIP. See the note at the beginning of {ERC20}. NOTE: Does not update the allowance if the current allowance is the maximum `uint256`. Requirements: - `from` and `to` cannot be the zero address. - `from` must have a balance of at least `value`. - the caller must have allowance for ``from``'s tokens of at least `value`."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"test/mocks/ERC20Mock.sol": "ERC20Mock"}, "evmVersion": "london", "libraries": {}}, "sources": {"lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol": {"keccak256": "0x60c65f701957fdd6faea1acb0bb45825791d473693ed9ecb34726fdfaa849dd7", "urls": ["bzz-raw://ea290300e0efc4d901244949dc4d877fd46e6c5e43dc2b26620e8efab3ab803f", "dweb:/ipfs/QmcLLJppxKeJWqHxE2CUkcfhuRTgHSn8J4kijcLa5MYhSt"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol": {"keccak256": "0xc3e1fa9d1987f8d349dfb4d6fe93bf2ca014b52ba335cfac30bfe71e357e6f80", "urls": ["bzz-raw://c5703ccdeb7b1d685e375ed719117e9edf2ab4bc544f24f23b0d50ec82257229", "dweb:/ipfs/QmTdwkbQq7owpCiyuzE7eh5LrD2ddrBCZ5WHVsWPi1RrTS"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0xc6a8ff0ea489379b61faa647490411b80102578440ab9d84e9a957cc12164e70", "urls": ["bzz-raw://0ea104e577e63faea3b69c415637e99e755dcbf64c5833d7140c35a714d6d90c", "dweb:/ipfs/Qmau6x4Ns9XdyynRCNNp3RhLqijJjFm7z5fyZazfYFGYdq"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0xaa761817f6cd7892fcf158b3c776b34551cde36f48ff9703d53898bc45a94ea2", "urls": ["bzz-raw://0ad7c8d4d08938c8dfc43d75a148863fb324b80cf53e0a36f7e5a4ac29008850", "dweb:/ipfs/QmcrhfPgVNf5mkdhQvy1pMv51TFokD3Y4Wa5WZhFqVh8UV"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "src/interfaces/external/poh/IPohVerifier.sol": {"keccak256": "0xce93e2d734074add2a6d36867d59fd24e2d44ca48c524bd8dddabd4163038205", "urls": ["bzz-raw://9e94dc14721e8873d15d7032afa3d2b5fea76e9c4029698aa489f2ea2edcedf6", "dweb:/ipfs/QmR55sQx36Ub7wUjDeemUSNotdmYCkK8AmUEYbRezF5Xhy"], "license": "AGPL-3.0"}, "test/mocks/ERC20Mock.sol": {"keccak256": "0x86e2d4e622d3172aff95ad8436b1af1213c7be008fbbe1e74f29577ea0afd92f", "urls": ["bzz-raw://2e2a534d2bf62fd7be511ac74e372b1d1e4cc08033fba742950785315553e3fb", "dweb:/ipfs/Qme24GSF4BFzuHETCfPzUafV1bsmVHVvbqQPNxDoU8AafR"], "license": "BSL-1.1"}}, "version": 1}, "id": 204}