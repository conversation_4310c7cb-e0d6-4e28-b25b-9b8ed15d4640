{"abi": [{"type": "function", "name": "getBorrowerPosition", "inputs": [{"name": "borrower", "type": "address", "internalType": "address"}, {"name": "market", "type": "address", "internalType": "address"}], "outputs": [{"name": "shouldLiquidate", "type": "bool", "internalType": "bool"}, {"name": "repayAmount", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}], "bytecode": {"object": "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", "sourceMap": "961:1442:196:-:0;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "961:1442:196:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;994:1407;;;;;;:::i;:::-;;:::i;:::-;;;;736:14:242;;729:22;711:41;;783:2;768:18;;761:34;;;;684:18;994:1407:196;;;;;;;;1104:20;1126:19;1179:5;1161:23;;1208:1;1194:15;;1273:18;1312:6;-1:-1:-1;;;;;1304:24:196;;:26;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1345:72;;-1:-1:-1;;;1345:72:196;;1273:58;;-1:-1:-1;;;;;;1345:17:196;;;;;:72;;1363:6;;1371:45;;1345:72;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1341:140;;;1433:37;;;1341:140;1833:44;;-1:-1:-1;;;1833:44:196;;-1:-1:-1;;;;;1958:32:242;;;1833:44:196;;;1940:51:242;1792:6:196;;1759:22;;1833:34;;;;;1913:18:242;;1833:44:196;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1809:68;;1891:13;1908:1;1891:18;1887:86;;1925:37;;;;;1887:86;2034:68;;-1:-1:-1;;;2034:68:196;;-1:-1:-1;;;;;2502:32:242;;;2034:68:196;;;2484:51:242;2013:17:196;2551:18:242;;;2544:60;;;2620:18;;;2613:34;;;2663:18;;;2656:34;;;2013:17:196;2034:40;;;;;;2456:19:242;;2034:68:196;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2010:92;;;2116:9;2129:1;2116:14;2112:82;;2146:37;;;;;;2112:82;2234:27;2264:8;-1:-1:-1;;;;;2264:28:196;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2234:60;-1:-1:-1;2358:4:196;2319:35;2234:60;2319:13;:35;:::i;:::-;2318:44;;;;:::i;:::-;2304:58;;2390:4;2372:22;;1151:1250;;;;;994:1407;;;;;;:::o;14:131:242:-;-1:-1:-1;;;;;89:31:242;;79:42;;69:70;;135:1;132;125:12;69:70;14:131;:::o;150:388::-;218:6;226;279:2;267:9;258:7;254:23;250:32;247:52;;;295:1;292;285:12;247:52;334:9;321:23;353:31;378:5;353:31;:::i;:::-;403:5;-1:-1:-1;460:2:242;445:18;;432:32;473:33;432:32;473:33;:::i;:::-;525:7;515:17;;;150:388;;;;;:::o;806:251::-;876:6;929:2;917:9;908:7;904:23;900:32;897:52;;;945:1;942;935:12;897:52;977:9;971:16;996:31;1021:5;996:31;:::i;:::-;1046:5;806:251;-1:-1:-1;;;806:251:242:o;1062:445::-;-1:-1:-1;;;;;1271:32:242;;1253:51;;1241:2;1226:18;;1334:2;1323:14;;1313:145;;1380:10;1375:3;1371:20;1368:1;1361:31;1415:4;1412:1;1405:15;1443:4;1440:1;1433:15;1313:145;1494:6;1489:2;1478:9;1474:18;1467:34;1062:445;;;;;:::o;1512:277::-;1579:6;1632:2;1620:9;1611:7;1607:23;1603:32;1600:52;;;1648:1;1645;1638:12;1600:52;1680:9;1674:16;1733:5;1726:13;1719:21;1712:5;1709:32;1699:60;;1755:1;1752;1745:12;2002:230;2072:6;2125:2;2113:9;2104:7;2100:23;2096:32;2093:52;;;2141:1;2138;2131:12;2093:52;-1:-1:-1;2186:16:242;;2002:230;-1:-1:-1;2002:230:242:o;2701:343::-;2780:6;2788;2841:2;2829:9;2820:7;2816:23;2812:32;2809:52;;;2857:1;2854;2847:12;2809:52;-1:-1:-1;;2902:16:242;;3008:2;2993:18;;;2987:25;2902:16;;2987:25;;-1:-1:-1;2701:343:242:o;3049:265::-;3122:9;;;3153;;3170:15;;;3164:22;;3150:37;3140:168;;3230:10;3225:3;3221:20;3218:1;3211:31;3265:4;3262:1;3255:15;3293:4;3290:1;3283:15;3140:168;3049:265;;;;:::o;3319:217::-;3359:1;3385;3375:132;;3429:10;3424:3;3420:20;3417:1;3410:31;3464:4;3461:1;3454:15;3492:4;3489:1;3482:15;3375:132;-1:-1:-1;3521:9:242;;3319:217::o", "linkReferences": {}}, "methodIdentifiers": {"getBorrowerPosition(address,address)": "887ecdee"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"market\",\"type\":\"address\"}],\"name\":\"getBorrowerPosition\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"shouldLiquidate\",\"type\":\"bool\"},{\"internalType\":\"uint256\",\"name\":\"repayAmount\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/utils/LiquidationHelper.sol\":\"LiquidationHelper\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"src/interfaces/IBlacklister.sol\":{\"keccak256\":\"0x47ed20135507a9d16473027cbee18de5d952846bada761c6248b91c75cd88be1\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://91a886b95dd578156f9b6cec3236e4751aae8a38e5d15e0d6045217c6fbd3d63\",\"dweb:/ipfs/QmRNjz7sGTuMkQNZhwwcg5DNN2eeqTZSAdZmxWxbx66CHU\"]},\"src/interfaces/IOperator.sol\":{\"keccak256\":\"0x0ae3d63eb07518da5a4fd6de7b013b6f55c87defb31900548378cffc70a51f72\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://1d9710c4ac43cb0563449aa519501b4ed18a331c90d0bc7eb79cdc56407bad8a\",\"dweb:/ipfs/Qmafe9Wup2GF9pD83CLWUrRait93vwvReqU1C7EHQhY7WV\"]},\"src/interfaces/IRoles.sol\":{\"keccak256\":\"0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c\",\"dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj\"]},\"src/interfaces/ImToken.sol\":{\"keccak256\":\"0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36\",\"dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL\"]},\"src/utils/LiquidationHelper.sol\":{\"keccak256\":\"0x0917dd13ed6660eb07e8e0aed4296bcfe54d22b78723cf6bee59dff0c3288ada\",\"license\":\"AGPL-3.0\",\"urls\":[\"bzz-raw://7d3616a8b70c58080b6a0057cbe4824748596cf9d25d65fb07d9bdae648d282b\",\"dweb:/ipfs/QmTCB5sk8w5f2DEsSbQeQbE3oKy8zFKFnpCuG6c2T7f5HP\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "borrower", "type": "address"}, {"internalType": "address", "name": "market", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getBorrowerPosition", "outputs": [{"internalType": "bool", "name": "shouldLiquidate", "type": "bool"}, {"internalType": "uint256", "name": "repayAmount", "type": "uint256"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/utils/LiquidationHelper.sol": "LiquidationHelper"}, "evmVersion": "london", "libraries": {}}, "sources": {"src/interfaces/IBlacklister.sol": {"keccak256": "0x47ed20135507a9d16473027cbee18de5d952846bada761c6248b91c75cd88be1", "urls": ["bzz-raw://91a886b95dd578156f9b6cec3236e4751aae8a38e5d15e0d6045217c6fbd3d63", "dweb:/ipfs/QmRNjz7sGTuMkQNZhwwcg5DNN2eeqTZSAdZmxWxbx66CHU"], "license": "BSL-1.1"}, "src/interfaces/IOperator.sol": {"keccak256": "0x0ae3d63eb07518da5a4fd6de7b013b6f55c87defb31900548378cffc70a51f72", "urls": ["bzz-raw://1d9710c4ac43cb0563449aa519501b4ed18a331c90d0bc7eb79cdc56407bad8a", "dweb:/ipfs/Qmafe9Wup2GF9pD83CLWUrRait93vwvReqU1C7EHQhY7WV"], "license": "BSL-1.1"}, "src/interfaces/IRoles.sol": {"keccak256": "0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365", "urls": ["bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c", "dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj"], "license": "BSL-1.1"}, "src/interfaces/ImToken.sol": {"keccak256": "0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f", "urls": ["bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36", "dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL"], "license": "BSL-1.1"}, "src/utils/LiquidationHelper.sol": {"keccak256": "0x0917dd13ed6660eb07e8e0aed4296bcfe54d22b78723cf6bee59dff0c3288ada", "urls": ["bzz-raw://7d3616a8b70c58080b6a0057cbe4824748596cf9d25d65fb07d9bdae648d282b", "dweb:/ipfs/QmTCB5sk8w5f2DEsSbQeQbE3oKy8zFKFnpCuG6c2T7f5HP"], "license": "AGPL-3.0"}}, "version": 1}, "id": 196}