{"abi": [{"type": "constructor", "inputs": [{"name": "_owner", "type": "address", "internalType": "address"}, {"name": "_imageId", "type": "bytes32", "internalType": "bytes32"}, {"name": "_verifier", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "imageId", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setImageId", "inputs": [{"name": "_imageId", "type": "bytes32", "internalType": "bytes32"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setVerifier", "inputs": [{"name": "_risc0Verifier", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "verifier", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IRiscZeroVerifier"}], "stateMutability": "view"}, {"type": "function", "name": "verifyInput", "inputs": [{"name": "journalEntry", "type": "bytes", "internalType": "bytes"}, {"name": "seal", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "view"}, {"type": "event", "name": "ImageSet", "inputs": [{"name": "_imageId", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "VerifierSet", "inputs": [{"name": "oldVerifier", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newVerifier", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "OwnableInvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ZkVerifier_ImageNotValid", "inputs": []}, {"type": "error", "name": "ZkVerifier_InputNotValid", "inputs": []}, {"type": "error", "name": "ZkVerifier_VerifierNotSet", "inputs": []}], "bytecode": {"object": "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", "sourceMap": "1157:2248:198:-:0;;;1552:304;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1625:6;-1:-1:-1;;;;;1273:26:27;;1269:95;;1322:31;;-1:-1:-1;;;1322:31:27;;1350:1;1322:31;;;701:51:242;674:18;;1322:31:27;;;;;;;1269:95;1373:32;1392:12;1373:18;:32::i;:::-;-1:-1:-1;;;;;;1651:23:198;::::1;1643:60;;;;-1:-1:-1::0;;;1643:60:198::1;;;;;;;;;;;;1721:8:::0;1713:59:::1;;;;-1:-1:-1::0;;;1713:59:198::1;;;;;;;;;;;;1782:8;:39:::0;;-1:-1:-1;;;;;;1782:39:198::1;-1:-1:-1::0;;;;;1782:39:198;;;::::1;::::0;;;::::1;::::0;;1831:7:::1;:18:::0;-1:-1:-1;1157:2248:198;;2912:187:27;2985:16;3004:6;;-1:-1:-1;;;;;3020:17:27;;;-1:-1:-1;;;;;;3020:17:27;;;;;;3052:40;;3004:6;;;;;;;3052:40;;2985:16;3052:40;2975:124;2912:187;:::o;14:177:242:-;93:13;;-1:-1:-1;;;;;135:31:242;;125:42;;115:70;;181:1;178;171:12;115:70;14:177;;;:::o;196:354::-;284:6;292;300;353:2;341:9;332:7;328:23;324:32;321:52;;;369:1;366;359:12;321:52;392:40;422:9;392:40;:::i;:::-;382:50;;472:2;461:9;457:18;451:25;441:35;;495:49;540:2;529:9;525:18;495:49;:::i;:::-;485:59;;196:354;;;;;:::o;555:203::-;1157:2248:198;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "1157:2248:198:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1247:33;;;;;-1:-1:-1;;;;;1247:33:198;;;;;;-1:-1:-1;;;;;205:32:242;;;187:51;;175:2;160:18;1247:33:198;;;;;;;;2867:208;;;;;;:::i;:::-;;:::i;:::-;;2083:261;;;;;;:::i;:::-;;:::i;2293:101:27:-;;;:::i;1638:85::-;1684:7;1710:6;-1:-1:-1;;;;;1710:6:27;1638:85;;2496:194:198;;;;;;:::i;:::-;;:::i;1287:22::-;;;;;;;;;2148:25:242;;;2136:2;2121:18;1287:22:198;2002:177:242;2543:215:27;;;;;;:::i;:::-;;:::i;2867:208:198:-;2988:17;:15;:17::i;:::-;3040:28;3049:12;;3063:4;;3040:8;:28::i;:::-;2867:208;;;;:::o;2083:261::-;1531:13:27;:11;:13::i;:::-;-1:-1:-1;;;;;2165:28:198;::::1;2157:65;;;;-1:-1:-1::0;;;2157:65:198::1;;;;;;;;;;;;2257:8;::::0;2237:46:::1;::::0;-1:-1:-1;;;;;2237:46:198;;::::1;::::0;2257:8:::1;::::0;2237:46:::1;::::0;2257:8:::1;::::0;2237:46:::1;2293:8;:44:::0;;-1:-1:-1;;;;;;2293:44:198::1;-1:-1:-1::0;;;;;2293:44:198;;;::::1;::::0;;;::::1;::::0;;2083:261::o;2293:101:27:-;1531:13;:11;:13::i;:::-;2357:30:::1;2384:1;2357:18;:30::i;:::-;2293:101::o:0;2496:194:198:-;1531:13:27;:11;:13::i;:::-;2571:8:198;2563:59:::1;;;;-1:-1:-1::0;;;2563:59:198::1;;;;;;;;;;;;2637:18;::::0;2148:25:242;;;2637:18:198::1;::::0;2136:2:242;2121:18;2637::198::1;;;;;;;2665:7;:18:::0;2496:194::o;2543:215:27:-;1531:13;:11;:13::i;:::-;-1:-1:-1;;;;;2627:22:27;::::1;2623:91;;2672:31;::::0;-1:-1:-1;;;2672:31:27;;2700:1:::1;2672:31;::::0;::::1;187:51:242::0;160:18;;2672:31:27::1;;;;;;;;2623:91;2723:28;2742:8;2723:18;:28::i;:::-;2543:215:::0;:::o;3121:126:198:-;3187:8;;-1:-1:-1;;;;;3187:8:198;3171:69;;;;-1:-1:-1;;;3171:69:198;;;;;;;;;;;3253:150;3344:8;;;;;;;;;-1:-1:-1;;;;;3344:8:198;-1:-1:-1;;;;;3344:15:198;;3360:4;;3366:7;;3375:20;3382:12;;3375:20;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;3344:52;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3253:150;;;;:::o;1796:162:27:-;1684:7;1710:6;-1:-1:-1;;;;;1710:6:27;735:10:47;1855:23:27;1851:101;;1901:40;;-1:-1:-1;;;1901:40:27;;735:10:47;1901:40:27;;;187:51:242;160:18;;1901:40:27;14:230:242;2912:187:27;2985:16;3004:6;;-1:-1:-1;;;;;3020:17:27;;;-1:-1:-1;;;;;;3020:17:27;;;;;;3052:40;;3004:6;;;;;;;3052:40;;2985:16;3052:40;2975:124;2912:187;:::o;249:347:242:-;300:8;310:6;364:3;357:4;349:6;345:17;341:27;331:55;;382:1;379;372:12;331:55;-1:-1:-1;405:20:242;;448:18;437:30;;434:50;;;480:1;477;470:12;434:50;517:4;509:6;505:17;493:29;;569:3;562:4;553:6;545;541:19;537:30;534:39;531:59;;;586:1;583;576:12;531:59;249:347;;;;;:::o;601:712::-;691:6;699;707;715;768:2;756:9;747:7;743:23;739:32;736:52;;;784:1;781;774:12;736:52;824:9;811:23;857:18;849:6;846:30;843:50;;;889:1;886;879:12;843:50;928:58;978:7;969:6;958:9;954:22;928:58;:::i;:::-;1005:8;;-1:-1:-1;902:84:242;-1:-1:-1;;1093:2:242;1078:18;;1065:32;1122:18;1109:32;;1106:52;;;1154:1;1151;1144:12;1106:52;1193:60;1245:7;1234:8;1223:9;1219:24;1193:60;:::i;:::-;601:712;;;;-1:-1:-1;1272:8:242;-1:-1:-1;;;;601:712:242:o;1318:286::-;1377:6;1430:2;1418:9;1409:7;1405:23;1401:32;1398:52;;;1446:1;1443;1436:12;1398:52;1472:23;;-1:-1:-1;;;;;1524:31:242;;1514:42;;1504:70;;1570:1;1567;1560:12;1504:70;1593:5;1318:286;-1:-1:-1;;;1318:286:242:o;1817:180::-;1876:6;1929:2;1917:9;1908:7;1904:23;1900:32;1897:52;;;1945:1;1942;1935:12;1897:52;-1:-1:-1;1968:23:242;;1817:180;-1:-1:-1;1817:180:242:o;2184:271::-;2367:6;2359;2354:3;2341:33;2323:3;2393:16;;2418:13;;;2393:16;2184:271;-1:-1:-1;2184:271:242:o;2460:184::-;2530:6;2583:2;2571:9;2562:7;2558:23;2554:32;2551:52;;;2599:1;2596;2589:12;2551:52;-1:-1:-1;2622:16:242;;2460:184;-1:-1:-1;2460:184:242:o;2649:535::-;2862:2;2851:9;2844:21;2901:6;2896:2;2885:9;2881:18;2874:34;2959:6;2951;2945:3;2934:9;2930:19;2917:49;3016:1;3010:3;3001:6;2990:9;2986:22;2982:32;2975:43;2825:4;3086:3;3079:2;3075:7;3070:2;3062:6;3058:15;3054:29;3043:9;3039:45;3035:55;3027:63;;3128:6;3121:4;3110:9;3106:20;3099:36;3171:6;3166:2;3155:9;3151:18;3144:34;2649:535;;;;;;;:::o", "linkReferences": {}}, "methodIdentifiers": {"imageId()": "ef3f7dd5", "owner()": "8da5cb5b", "renounceOwnership()": "715018a6", "setImageId(bytes32)": "901129c2", "setVerifier(address)": "5437988d", "transferOwnership(address)": "f2fde38b", "verifier()": "2b7ac3f3", "verifyInput(bytes,bytes)": "385db561"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_owner\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"_imageId\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"_verifier\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"OwnableInvalidOwner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"OwnableUnauthorizedAccount\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ZkVerifier_ImageNotValid\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ZkVerifier_InputNotValid\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ZkVerifier_VerifierNotSet\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"_imageId\",\"type\":\"bytes32\"}],\"name\":\"ImageSet\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"oldVerifier\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newVerifier\",\"type\":\"address\"}],\"name\":\"VerifierSet\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"imageId\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_imageId\",\"type\":\"bytes32\"}],\"name\":\"setImageId\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_risc0Verifier\",\"type\":\"address\"}],\"name\":\"setVerifier\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"verifier\",\"outputs\":[{\"internalType\":\"contract IRiscZeroVerifier\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"journalEntry\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"seal\",\"type\":\"bytes\"}],\"name\":\"verifyInput\",\"outputs\":[],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"OwnableInvalidOwner(address)\":[{\"details\":\"The owner is not a valid owner account. (eg. `address(0)`)\"}],\"OwnableUnauthorizedAccount(address)\":[{\"details\":\"The caller account is not authorized to perform an operation.\"}]},\"kind\":\"dev\",\"methods\":{\"owner()\":{\"details\":\"Returns the address of the current owner.\"},\"renounceOwnership()\":{\"details\":\"Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner.\"},\"setImageId(bytes32)\":{\"details\":\"Admin check is needed on the external method\",\"params\":{\"_imageId\":\"the new image id\"}},\"setVerifier(address)\":{\"details\":\"Admin check is needed on the external method\",\"params\":{\"_risc0Verifier\":\"the new IRiscZeroVerifier address\"}},\"transferOwnership(address)\":{\"details\":\"Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner.\"},\"verifyInput(bytes,bytes)\":{\"params\":{\"journalEntry\":\"the risc0 journal entry\",\"seal\":\"the risc0 seal\"}}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"setImageId(bytes32)\":{\"notice\":\"Sets the image id\"},\"setVerifier(address)\":{\"notice\":\"Sets the _risc0Verifier address\"},\"verifyInput(bytes,bytes)\":{\"notice\":\"Verifies an input\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/verifier/ZkVerifier.sol\":\"ZkVerifier\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"lib/openzeppelin-contracts/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"lib/risc0-ethereum/contracts/src/IRiscZeroVerifier.sol\":{\"keccak256\":\"0xae73e91e4d2829b0a88176584d876382b4b7540ff07c62cb947cdf9ea43d5cf3\",\"license\":\"Apache-2.0\",\"urls\":[\"bzz-raw://f2dd7da39a6f9b100fc1dcde565d799462cdc7d27722d2265317c3a01619c818\",\"dweb:/ipfs/Qmd3k3kcvrtfkLqVza4jHodbFH8wvq5vmfPtwekBKtPjmz\"]},\"lib/risc0-ethereum/contracts/src/Util.sol\":{\"keccak256\":\"0x230a9a58bb2b0c68d4921ecc3672783f941890a2fa3a238dbe5188cfb7658b82\",\"license\":\"Apache-2.0\",\"urls\":[\"bzz-raw://586cb5296c564761609ca8aaae5b1fbbdb19ab1e174f7197f602572d9e82b70c\",\"dweb:/ipfs/QmWxDmucSfg3Wgdk1rVcheDHwkziTsXWvKpKSCnxtpQBqg\"]},\"lib/risc0-ethereum/contracts/src/steel/Steel.sol\":{\"keccak256\":\"0x20811a302f2bc3e36fb612026319c83cf79c16aa56b822d40bcbe8173d1e9544\",\"license\":\"Apache-2.0\",\"urls\":[\"bzz-raw://ed9f7a15c3be0bdc79217feae9e773d280fcfb44aa450d37c6c891f0b505210f\",\"dweb:/ipfs/QmXHtzvLT2AHHHojJ42epUSBF4HYTaVohSmyZft6KZoVqE\"]},\"src/verifier/ZkVerifier.sol\":{\"keccak256\":\"0x1abaa285ad4ee09461aae5df4aecff9bd38b2b3da19b5a920ab1b7d2208e04ec\",\"license\":\"AGPL-3.0\",\"urls\":[\"bzz-raw://7db45391afa7244f70303c5ace73b7937ee30261fd2015e4a7bfa11ec85480cc\",\"dweb:/ipfs/QmTdhCwcbTCRCMF1ifCJoifqwxhKpKBEE8zq9ph6hYhogG\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "_owner", "type": "address"}, {"internalType": "bytes32", "name": "_imageId", "type": "bytes32"}, {"internalType": "address", "name": "_verifier", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "type": "error", "name": "OwnableInvalidOwner"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "type": "error", "name": "OwnableUnauthorizedAccount"}, {"inputs": [], "type": "error", "name": "ZkVerifier_ImageNotValid"}, {"inputs": [], "type": "error", "name": "ZkVerifier_InputNotValid"}, {"inputs": [], "type": "error", "name": "ZkVerifier_VerifierNotSet"}, {"inputs": [{"internalType": "bytes32", "name": "_imageId", "type": "bytes32", "indexed": false}], "type": "event", "name": "ImageSet", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "previousOwner", "type": "address", "indexed": true}, {"internalType": "address", "name": "new<PERSON>wner", "type": "address", "indexed": true}], "type": "event", "name": "OwnershipTransferred", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "oldVerifier", "type": "address", "indexed": true}, {"internalType": "address", "name": "newVerifier", "type": "address", "indexed": true}], "type": "event", "name": "VerifierSet", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "imageId", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "renounceOwnership"}, {"inputs": [{"internalType": "bytes32", "name": "_imageId", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function", "name": "setImageId"}, {"inputs": [{"internalType": "address", "name": "_risc0Verifier", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setVerifier"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "transferOwnership"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "verifier", "outputs": [{"internalType": "contract IRiscZeroVerifier", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "bytes", "name": "journalEntry", "type": "bytes"}, {"internalType": "bytes", "name": "seal", "type": "bytes"}], "stateMutability": "view", "type": "function", "name": "verifyInput"}], "devdoc": {"kind": "dev", "methods": {"owner()": {"details": "Returns the address of the current owner."}, "renounceOwnership()": {"details": "Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner."}, "setImageId(bytes32)": {"details": "Admin check is needed on the external method", "params": {"_imageId": "the new image id"}}, "setVerifier(address)": {"details": "Admin check is needed on the external method", "params": {"_risc0Verifier": "the new IRiscZeroVerifier address"}}, "transferOwnership(address)": {"details": "Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner."}, "verifyInput(bytes,bytes)": {"params": {"journalEntry": "the risc0 journal entry", "seal": "the risc0 seal"}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"setImageId(bytes32)": {"notice": "Sets the image id"}, "setVerifier(address)": {"notice": "Sets the _risc0Verifier address"}, "verifyInput(bytes,bytes)": {"notice": "Verifies an input"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/verifier/ZkVerifier.sol": "ZkVerifier"}, "evmVersion": "london", "libraries": {}}, "sources": {"lib/openzeppelin-contracts/contracts/access/Ownable.sol": {"keccak256": "0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb", "urls": ["bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6", "dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "lib/risc0-ethereum/contracts/src/IRiscZeroVerifier.sol": {"keccak256": "0xae73e91e4d2829b0a88176584d876382b4b7540ff07c62cb947cdf9ea43d5cf3", "urls": ["bzz-raw://f2dd7da39a6f9b100fc1dcde565d799462cdc7d27722d2265317c3a01619c818", "dweb:/ipfs/Qmd3k3kcvrtfkLqVza4jHodbFH8wvq5vmfPtwekBKtPjmz"], "license": "Apache-2.0"}, "lib/risc0-ethereum/contracts/src/Util.sol": {"keccak256": "0x230a9a58bb2b0c68d4921ecc3672783f941890a2fa3a238dbe5188cfb7658b82", "urls": ["bzz-raw://586cb5296c564761609ca8aaae5b1fbbdb19ab1e174f7197f602572d9e82b70c", "dweb:/ipfs/QmWxDmucSfg3Wgdk1rVcheDHwkziTsXWvKpKSCnxtpQBqg"], "license": "Apache-2.0"}, "lib/risc0-ethereum/contracts/src/steel/Steel.sol": {"keccak256": "0x20811a302f2bc3e36fb612026319c83cf79c16aa56b822d40bcbe8173d1e9544", "urls": ["bzz-raw://ed9f7a15c3be0bdc79217feae9e773d280fcfb44aa450d37c6c891f0b505210f", "dweb:/ipfs/QmXHtzvLT2AHHHojJ42epUSBF4HYTaVohSmyZft6KZoVqE"], "license": "Apache-2.0"}, "src/verifier/ZkVerifier.sol": {"keccak256": "0x1abaa285ad4ee09461aae5df4aecff9bd38b2b3da19b5a920ab1b7d2208e04ec", "urls": ["bzz-raw://7db45391afa7244f70303c5ace73b7937ee30261fd2015e4a7bfa11ec85480cc", "dweb:/ipfs/QmTdhCwcbTCRCMF1ifCJoifqwxhKpKBEE8zq9ph6hYhogG"], "license": "AGPL-3.0"}}, "version": 1}, "id": 198}