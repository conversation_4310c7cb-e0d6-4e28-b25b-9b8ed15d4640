{"abi": [{"type": "function", "name": "allowInitializePath", "inputs": [{"name": "_origin", "type": "tuple", "internalType": "struct Origin", "components": [{"name": "srcEid", "type": "uint32", "internalType": "uint32"}, {"name": "sender", "type": "bytes32", "internalType": "bytes32"}, {"name": "nonce", "type": "uint64", "internalType": "uint64"}]}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "lzReceive", "inputs": [{"name": "_origin", "type": "tuple", "internalType": "struct Origin", "components": [{"name": "srcEid", "type": "uint32", "internalType": "uint32"}, {"name": "sender", "type": "bytes32", "internalType": "bytes32"}, {"name": "nonce", "type": "uint64", "internalType": "uint64"}]}, {"name": "_guid", "type": "bytes32", "internalType": "bytes32"}, {"name": "_message", "type": "bytes", "internalType": "bytes"}, {"name": "_executor", "type": "address", "internalType": "address"}, {"name": "_extraData", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "nextNonce", "inputs": [{"name": "_eid", "type": "uint32", "internalType": "uint32"}, {"name": "_sender", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "uint64", "internalType": "uint64"}], "stateMutability": "view"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"allowInitializePath((uint32,bytes32,uint64))": "ff7bd03d", "lzReceive((uint32,bytes32,uint64),bytes32,bytes,address,bytes)": "13137d65", "nextNonce(uint32,bytes32)": "7d25a05e"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"components\":[{\"internalType\":\"uint32\",\"name\":\"srcEid\",\"type\":\"uint32\"},{\"internalType\":\"bytes32\",\"name\":\"sender\",\"type\":\"bytes32\"},{\"internalType\":\"uint64\",\"name\":\"nonce\",\"type\":\"uint64\"}],\"internalType\":\"struct Origin\",\"name\":\"_origin\",\"type\":\"tuple\"}],\"name\":\"allowInitializePath\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"uint32\",\"name\":\"srcEid\",\"type\":\"uint32\"},{\"internalType\":\"bytes32\",\"name\":\"sender\",\"type\":\"bytes32\"},{\"internalType\":\"uint64\",\"name\":\"nonce\",\"type\":\"uint64\"}],\"internalType\":\"struct Origin\",\"name\":\"_origin\",\"type\":\"tuple\"},{\"internalType\":\"bytes32\",\"name\":\"_guid\",\"type\":\"bytes32\"},{\"internalType\":\"bytes\",\"name\":\"_message\",\"type\":\"bytes\"},{\"internalType\":\"address\",\"name\":\"_executor\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"_extraData\",\"type\":\"bytes\"}],\"name\":\"lzReceive\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"_eid\",\"type\":\"uint32\"},{\"internalType\":\"bytes32\",\"name\":\"_sender\",\"type\":\"bytes32\"}],\"name\":\"nextNonce\",\"outputs\":[{\"internalType\":\"uint64\",\"name\":\"\",\"type\":\"uint64\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/external/layerzero/v2/ILayerZeroReceiverV2.sol\":\"ILayerZeroReceiverV2\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"src/interfaces/external/layerzero/v2/ILayerZeroEndpointV2.sol\":{\"keccak256\":\"0xd1b1d757b60ee2e7ac39a04cc0bc3a57dab68b7f0a3a890750590bd51ff377e7\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://54b9c248e75816d26de764aa8bd0333b6c0b5a1c02d6e278a0e6388fd2e09c07\",\"dweb:/ipfs/Qmf3iXi3NfUKgti3hsJ8SrWWBCyNHYCfnzU4xpxNYAuWhE\"]},\"src/interfaces/external/layerzero/v2/ILayerZeroReceiverV2.sol\":{\"keccak256\":\"0x6f9d3b728a6e19993e739a8af525ae77bacea4002778cf131286ffa32f490bc9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6837a9139cc185d78c52eaa43f5f2df35e0b77ca754d3dbbcc3efd98ef8803b4\",\"dweb:/ipfs/QmcfRBbq9PxBkhQEv9Y1X8dFAKNL5hZW5R2n4DDGaHHnDL\"]},\"src/interfaces/external/layerzero/v2/IMessageLibManager.sol\":{\"keccak256\":\"0x94929bdb8d035a15c94d51c16a18903d89fc9291cb6dda23043f6c9864e664f5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c9c509b859cf878757304666a37ff894c3aa414629a19e7ed35ea09139eac3d2\",\"dweb:/ipfs/Qmb8wJfG18Kv24QCVsRQADnMbLPhQ31mVXDk8e2dF3ozJu\"]},\"src/interfaces/external/layerzero/v2/IMessagingChannel.sol\":{\"keccak256\":\"0x77d1b0bd52cb0e3ae72849cd2d916dcde67986a32470c48f18a7c571a91a5d40\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://00b3db0d90d80c40ea84286966dfd4164bd34a984ad73d8280e7b4c2574b15e3\",\"dweb:/ipfs/QmT4tT4wRcq67v7wyh46aukFXPueM3tfmBwoKvRduVxgFh\"]},\"src/interfaces/external/layerzero/v2/IMessagingComposer.sol\":{\"keccak256\":\"0x72eedb733a35770e561727caf76893ef5ca758f35c9ceaada8a4ff3493648b7b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d94ecc1513037856d645960f5a3524089a406585959ac73bfb7c789a31d06d97\",\"dweb:/ipfs/QmewsjB5Fnkx4fu5HcMyd3LhRrNA65zLRG4pjcwQX4eVpC\"]},\"src/interfaces/external/layerzero/v2/IMessagingContext.sol\":{\"keccak256\":\"0xff0c546c2813dae3e440882f46b377375f7461b0714efd80bd3f0c6e5cb8da4e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5173fc9143bea314b159ca5a9adb5626659ef763bc598e27de5fa46efe3291a6\",\"dweb:/ipfs/QmSLFeMFPmVeGxT4sxRPW28ictjAS22M8rLeYRu9TXkA6D\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "struct Origin", "name": "_origin", "type": "tuple", "components": [{"internalType": "uint32", "name": "srcEid", "type": "uint32"}, {"internalType": "bytes32", "name": "sender", "type": "bytes32"}, {"internalType": "uint64", "name": "nonce", "type": "uint64"}]}], "stateMutability": "view", "type": "function", "name": "allowInitializePath", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "struct Origin", "name": "_origin", "type": "tuple", "components": [{"internalType": "uint32", "name": "srcEid", "type": "uint32"}, {"internalType": "bytes32", "name": "sender", "type": "bytes32"}, {"internalType": "uint64", "name": "nonce", "type": "uint64"}]}, {"internalType": "bytes32", "name": "_guid", "type": "bytes32"}, {"internalType": "bytes", "name": "_message", "type": "bytes"}, {"internalType": "address", "name": "_executor", "type": "address"}, {"internalType": "bytes", "name": "_extraData", "type": "bytes"}], "stateMutability": "payable", "type": "function", "name": "lzReceive"}, {"inputs": [{"internalType": "uint32", "name": "_eid", "type": "uint32"}, {"internalType": "bytes32", "name": "_sender", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "nextNonce", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/interfaces/external/layerzero/v2/ILayerZeroReceiverV2.sol": "ILayerZeroReceiverV2"}, "evmVersion": "london", "libraries": {}}, "sources": {"src/interfaces/external/layerzero/v2/ILayerZeroEndpointV2.sol": {"keccak256": "0xd1b1d757b60ee2e7ac39a04cc0bc3a57dab68b7f0a3a890750590bd51ff377e7", "urls": ["bzz-raw://54b9c248e75816d26de764aa8bd0333b6c0b5a1c02d6e278a0e6388fd2e09c07", "dweb:/ipfs/Qmf3iXi3NfUKgti3hsJ8SrWWBCyNHYCfnzU4xpxNYAuWhE"], "license": "MIT"}, "src/interfaces/external/layerzero/v2/ILayerZeroReceiverV2.sol": {"keccak256": "0x6f9d3b728a6e19993e739a8af525ae77bacea4002778cf131286ffa32f490bc9", "urls": ["bzz-raw://6837a9139cc185d78c52eaa43f5f2df35e0b77ca754d3dbbcc3efd98ef8803b4", "dweb:/ipfs/QmcfRBbq9PxBkhQEv9Y1X8dFAKNL5hZW5R2n4DDGaHHnDL"], "license": "MIT"}, "src/interfaces/external/layerzero/v2/IMessageLibManager.sol": {"keccak256": "0x94929bdb8d035a15c94d51c16a18903d89fc9291cb6dda23043f6c9864e664f5", "urls": ["bzz-raw://c9c509b859cf878757304666a37ff894c3aa414629a19e7ed35ea09139eac3d2", "dweb:/ipfs/Qmb8wJfG18Kv24QCVsRQADnMbLPhQ31mVXDk8e2dF3ozJu"], "license": "MIT"}, "src/interfaces/external/layerzero/v2/IMessagingChannel.sol": {"keccak256": "0x77d1b0bd52cb0e3ae72849cd2d916dcde67986a32470c48f18a7c571a91a5d40", "urls": ["bzz-raw://00b3db0d90d80c40ea84286966dfd4164bd34a984ad73d8280e7b4c2574b15e3", "dweb:/ipfs/QmT4tT4wRcq67v7wyh46aukFXPueM3tfmBwoKvRduVxgFh"], "license": "MIT"}, "src/interfaces/external/layerzero/v2/IMessagingComposer.sol": {"keccak256": "0x72eedb733a35770e561727caf76893ef5ca758f35c9ceaada8a4ff3493648b7b", "urls": ["bzz-raw://d94ecc1513037856d645960f5a3524089a406585959ac73bfb7c789a31d06d97", "dweb:/ipfs/QmewsjB5Fnkx4fu5HcMyd3LhRrNA65zLRG4pjcwQX4eVpC"], "license": "MIT"}, "src/interfaces/external/layerzero/v2/IMessagingContext.sol": {"keccak256": "0xff0c546c2813dae3e440882f46b377375f7461b0714efd80bd3f0c6e5cb8da4e", "urls": ["bzz-raw://5173fc9143bea314b159ca5a9adb5626659ef763bc598e27de5fa46efe3291a6", "dweb:/ipfs/QmSLFeMFPmVeGxT4sxRPW28ictjAS22M8rLeYRu9TXkA6D"], "license": "MIT"}}, "version": 1}, "id": 159}