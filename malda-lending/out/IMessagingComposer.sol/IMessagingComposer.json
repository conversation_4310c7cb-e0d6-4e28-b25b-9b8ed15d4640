{"abi": [{"type": "function", "name": "composeQueue", "inputs": [{"name": "_from", "type": "address", "internalType": "address"}, {"name": "_to", "type": "address", "internalType": "address"}, {"name": "_guid", "type": "bytes32", "internalType": "bytes32"}, {"name": "_index", "type": "uint16", "internalType": "uint16"}], "outputs": [{"name": "messageHash", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "lzCompose", "inputs": [{"name": "_from", "type": "address", "internalType": "address"}, {"name": "_to", "type": "address", "internalType": "address"}, {"name": "_guid", "type": "bytes32", "internalType": "bytes32"}, {"name": "_index", "type": "uint16", "internalType": "uint16"}, {"name": "_message", "type": "bytes", "internalType": "bytes"}, {"name": "_extraData", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "sendCompose", "inputs": [{"name": "_to", "type": "address", "internalType": "address"}, {"name": "_guid", "type": "bytes32", "internalType": "bytes32"}, {"name": "_index", "type": "uint16", "internalType": "uint16"}, {"name": "_message", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "ComposeDelivered", "inputs": [{"name": "from", "type": "address", "indexed": false, "internalType": "address"}, {"name": "to", "type": "address", "indexed": false, "internalType": "address"}, {"name": "guid", "type": "bytes32", "indexed": false, "internalType": "bytes32"}, {"name": "index", "type": "uint16", "indexed": false, "internalType": "uint16"}], "anonymous": false}, {"type": "event", "name": "ComposeSent", "inputs": [{"name": "from", "type": "address", "indexed": false, "internalType": "address"}, {"name": "to", "type": "address", "indexed": false, "internalType": "address"}, {"name": "guid", "type": "bytes32", "indexed": false, "internalType": "bytes32"}, {"name": "index", "type": "uint16", "indexed": false, "internalType": "uint16"}, {"name": "message", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "LzComposeAlert", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "executor", "type": "address", "indexed": true, "internalType": "address"}, {"name": "guid", "type": "bytes32", "indexed": false, "internalType": "bytes32"}, {"name": "index", "type": "uint16", "indexed": false, "internalType": "uint16"}, {"name": "gas", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "value", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "message", "type": "bytes", "indexed": false, "internalType": "bytes"}, {"name": "extraData", "type": "bytes", "indexed": false, "internalType": "bytes"}, {"name": "reason", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"composeQueue(address,address,bytes32,uint16)": "35d330b0", "lzCompose(address,address,bytes32,uint16,bytes,bytes)": "91d20fa1", "sendCompose(address,bytes32,uint16,bytes)": "7cb59012"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"guid\",\"type\":\"bytes32\"},{\"indexed\":false,\"internalType\":\"uint16\",\"name\":\"index\",\"type\":\"uint16\"}],\"name\":\"ComposeDelivered\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"guid\",\"type\":\"bytes32\"},{\"indexed\":false,\"internalType\":\"uint16\",\"name\":\"index\",\"type\":\"uint16\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"message\",\"type\":\"bytes\"}],\"name\":\"ComposeSent\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"executor\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"guid\",\"type\":\"bytes32\"},{\"indexed\":false,\"internalType\":\"uint16\",\"name\":\"index\",\"type\":\"uint16\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"gas\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"message\",\"type\":\"bytes\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"extraData\",\"type\":\"bytes\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"reason\",\"type\":\"bytes\"}],\"name\":\"LzComposeAlert\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_to\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"_guid\",\"type\":\"bytes32\"},{\"internalType\":\"uint16\",\"name\":\"_index\",\"type\":\"uint16\"}],\"name\":\"composeQueue\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"messageHash\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_to\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"_guid\",\"type\":\"bytes32\"},{\"internalType\":\"uint16\",\"name\":\"_index\",\"type\":\"uint16\"},{\"internalType\":\"bytes\",\"name\":\"_message\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"_extraData\",\"type\":\"bytes\"}],\"name\":\"lzCompose\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_to\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"_guid\",\"type\":\"bytes32\"},{\"internalType\":\"uint16\",\"name\":\"_index\",\"type\":\"uint16\"},{\"internalType\":\"bytes\",\"name\":\"_message\",\"type\":\"bytes\"}],\"name\":\"sendCompose\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/external/layerzero/v2/IMessagingComposer.sol\":\"IMessagingComposer\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"src/interfaces/external/layerzero/v2/IMessagingComposer.sol\":{\"keccak256\":\"0x72eedb733a35770e561727caf76893ef5ca758f35c9ceaada8a4ff3493648b7b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d94ecc1513037856d645960f5a3524089a406585959ac73bfb7c789a31d06d97\",\"dweb:/ipfs/QmewsjB5Fnkx4fu5HcMyd3LhRrNA65zLRG4pjcwQX4eVpC\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "from", "type": "address", "indexed": false}, {"internalType": "address", "name": "to", "type": "address", "indexed": false}, {"internalType": "bytes32", "name": "guid", "type": "bytes32", "indexed": false}, {"internalType": "uint16", "name": "index", "type": "uint16", "indexed": false}], "type": "event", "name": "ComposeDelivered", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "from", "type": "address", "indexed": false}, {"internalType": "address", "name": "to", "type": "address", "indexed": false}, {"internalType": "bytes32", "name": "guid", "type": "bytes32", "indexed": false}, {"internalType": "uint16", "name": "index", "type": "uint16", "indexed": false}, {"internalType": "bytes", "name": "message", "type": "bytes", "indexed": false}], "type": "event", "name": "ComposeSent", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "from", "type": "address", "indexed": true}, {"internalType": "address", "name": "to", "type": "address", "indexed": true}, {"internalType": "address", "name": "executor", "type": "address", "indexed": true}, {"internalType": "bytes32", "name": "guid", "type": "bytes32", "indexed": false}, {"internalType": "uint16", "name": "index", "type": "uint16", "indexed": false}, {"internalType": "uint256", "name": "gas", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "value", "type": "uint256", "indexed": false}, {"internalType": "bytes", "name": "message", "type": "bytes", "indexed": false}, {"internalType": "bytes", "name": "extraData", "type": "bytes", "indexed": false}, {"internalType": "bytes", "name": "reason", "type": "bytes", "indexed": false}], "type": "event", "name": "LzComposeAlert", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "_from", "type": "address"}, {"internalType": "address", "name": "_to", "type": "address"}, {"internalType": "bytes32", "name": "_guid", "type": "bytes32"}, {"internalType": "uint16", "name": "_index", "type": "uint16"}], "stateMutability": "view", "type": "function", "name": "composeQueue", "outputs": [{"internalType": "bytes32", "name": "messageHash", "type": "bytes32"}]}, {"inputs": [{"internalType": "address", "name": "_from", "type": "address"}, {"internalType": "address", "name": "_to", "type": "address"}, {"internalType": "bytes32", "name": "_guid", "type": "bytes32"}, {"internalType": "uint16", "name": "_index", "type": "uint16"}, {"internalType": "bytes", "name": "_message", "type": "bytes"}, {"internalType": "bytes", "name": "_extraData", "type": "bytes"}], "stateMutability": "payable", "type": "function", "name": "lzCompose"}, {"inputs": [{"internalType": "address", "name": "_to", "type": "address"}, {"internalType": "bytes32", "name": "_guid", "type": "bytes32"}, {"internalType": "uint16", "name": "_index", "type": "uint16"}, {"internalType": "bytes", "name": "_message", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "sendCompose"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/interfaces/external/layerzero/v2/IMessagingComposer.sol": "IMessagingComposer"}, "evmVersion": "london", "libraries": {}}, "sources": {"src/interfaces/external/layerzero/v2/IMessagingComposer.sol": {"keccak256": "0x72eedb733a35770e561727caf76893ef5ca758f35c9ceaada8a4ff3493648b7b", "urls": ["bzz-raw://d94ecc1513037856d645960f5a3524089a406585959ac73bfb7c789a31d06d97", "dweb:/ipfs/QmewsjB5Fnkx4fu5HcMyd3LhRrNA65zLRG4pjcwQX4eVpC"], "license": "MIT"}}, "version": 1}, "id": 163}