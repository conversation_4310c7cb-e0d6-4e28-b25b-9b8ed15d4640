{"abi": [{"type": "function", "name": "isBridgeWhitelisted", "inputs": [{"name": "bridge", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isDestinationWhitelisted", "inputs": [{"name": "dstId", "type": "uint32", "internalType": "uint32"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "nonce", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "sendMsg", "inputs": [{"name": "bridge", "type": "address", "internalType": "address"}, {"name": "_market", "type": "address", "internalType": "address"}, {"name": "_amount", "type": "uint256", "internalType": "uint256"}, {"name": "msg", "type": "tuple", "internalType": "struct IRebalancer.Msg", "components": [{"name": "dst<PERSON>hainId", "type": "uint32", "internalType": "uint32"}, {"name": "token", "type": "address", "internalType": "address"}, {"name": "message", "type": "bytes", "internalType": "bytes"}, {"name": "bridgeData", "type": "bytes", "internalType": "bytes"}]}], "outputs": [], "stateMutability": "payable"}, {"type": "event", "name": "AllowedListUpdated", "inputs": [{"name": "list", "type": "address[]", "indexed": false, "internalType": "address[]"}, {"name": "status", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "BridgeWhitelistedStatusUpdated", "inputs": [{"name": "bridge", "type": "address", "indexed": true, "internalType": "address"}, {"name": "status", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "DestinationWhitelistedStatusUpdated", "inputs": [{"name": "dst<PERSON>hainId", "type": "uint32", "indexed": true, "internalType": "uint32"}, {"name": "status", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "EthSaved", "inputs": [{"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "MaxTransferSizeUpdated", "inputs": [{"name": "dst<PERSON>hainId", "type": "uint32", "indexed": true, "internalType": "uint32"}, {"name": "token", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newLimit", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "MinTransferSizeUpdated", "inputs": [{"name": "dst<PERSON>hainId", "type": "uint32", "indexed": true, "internalType": "uint32"}, {"name": "token", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newLimit", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "MsgSent", "inputs": [{"name": "bridge", "type": "address", "indexed": true, "internalType": "address"}, {"name": "dst<PERSON>hainId", "type": "uint32", "indexed": true, "internalType": "uint32"}, {"name": "token", "type": "address", "indexed": true, "internalType": "address"}, {"name": "message", "type": "bytes", "indexed": false, "internalType": "bytes"}, {"name": "bridgeData", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "error", "name": "Rebalancer_AddressNotValid", "inputs": []}, {"type": "error", "name": "Rebalancer_BridgeNotWhitelisted", "inputs": []}, {"type": "error", "name": "Rebalancer_DestinationNotWhitelisted", "inputs": []}, {"type": "error", "name": "Rebalancer_MarketNotValid", "inputs": []}, {"type": "error", "name": "Rebalancer_NotAuthorized", "inputs": []}, {"type": "error", "name": "Rebalancer_RequestNotValid", "inputs": []}, {"type": "error", "name": "Rebalancer_TransferSizeExcedeed", "inputs": []}, {"type": "error", "name": "Rebalancer_TransferSizeMinNotMet", "inputs": []}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"isBridgeWhitelisted(address)": "b70baeb6", "isDestinationWhitelisted(uint32)": "01a874a2", "nonce()": "affed0e0", "sendMsg(address,address,uint256,(uint32,address,bytes,bytes))": "16d2c2e8"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"Rebalancer_AddressNotValid\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Rebalancer_BridgeNotWhitelisted\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Rebalancer_DestinationNotWhitelisted\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Rebalancer_MarketNotValid\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Rebalancer_NotAuthorized\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Rebalancer_RequestNotValid\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Rebalancer_TransferSizeExcedeed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Rebalancer_TransferSizeMinNotMet\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"list\",\"type\":\"address[]\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"status\",\"type\":\"bool\"}],\"name\":\"AllowedListUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"bridge\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"status\",\"type\":\"bool\"}],\"name\":\"BridgeWhitelistedStatusUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint32\",\"name\":\"dstChainId\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"status\",\"type\":\"bool\"}],\"name\":\"DestinationWhitelistedStatusUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"EthSaved\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint32\",\"name\":\"dstChainId\",\"type\":\"uint32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"newLimit\",\"type\":\"uint256\"}],\"name\":\"MaxTransferSizeUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint32\",\"name\":\"dstChainId\",\"type\":\"uint32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"newLimit\",\"type\":\"uint256\"}],\"name\":\"MinTransferSizeUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"bridge\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint32\",\"name\":\"dstChainId\",\"type\":\"uint32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"message\",\"type\":\"bytes\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"bridgeData\",\"type\":\"bytes\"}],\"name\":\"MsgSent\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"bridge\",\"type\":\"address\"}],\"name\":\"isBridgeWhitelisted\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"dstId\",\"type\":\"uint32\"}],\"name\":\"isDestinationWhitelisted\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"nonce\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"bridge\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_market\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_amount\",\"type\":\"uint256\"},{\"components\":[{\"internalType\":\"uint32\",\"name\":\"dstChainId\",\"type\":\"uint32\"},{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"message\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"bridgeData\",\"type\":\"bytes\"}],\"internalType\":\"struct IRebalancer.Msg\",\"name\":\"msg\",\"type\":\"tuple\"}],\"name\":\"sendMsg\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{\"sendMsg(address,address,uint256,(uint32,address,bytes,bytes))\":{\"params\":{\"_amount\":\"the amount to rebalance\",\"_market\":\"the market to rebalance from address\",\"bridge\":\"the whitelisted bridge address\",\"msg\":\"the message data\"}}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"isBridgeWhitelisted(address)\":{\"notice\":\"returns if a bridge implementation is whitelisted\"},\"isDestinationWhitelisted(uint32)\":{\"notice\":\"returns if a destination is whitelisted\"},\"nonce()\":{\"notice\":\"returns current nonce\"},\"sendMsg(address,address,uint256,(uint32,address,bytes,bytes))\":{\"notice\":\"sends a bridge message\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/IRebalancer.sol\":\"IRebalancer\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"src/interfaces/IRebalancer.sol\":{\"keccak256\":\"0x3dd4db0fa764498e57ba061af2e501eebc9486cccd6483a5d9ee3ad88ac62281\",\"license\":\"AGPL-3.0\",\"urls\":[\"bzz-raw://07489b56d8bd43e84c486ba05e66a0b01fab55970396ec5bad10f5cad10fd27e\",\"dweb:/ipfs/QmWuCXdpWyA4Zj2BFp8uiF9WcpbyDpUR9ysBmAFzVmbuYz\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "Rebalancer_AddressNotValid"}, {"inputs": [], "type": "error", "name": "Rebalancer_BridgeNotWhitelisted"}, {"inputs": [], "type": "error", "name": "Rebalancer_DestinationNotWhitelisted"}, {"inputs": [], "type": "error", "name": "Rebalancer_MarketNotValid"}, {"inputs": [], "type": "error", "name": "Rebalancer_NotAuthorized"}, {"inputs": [], "type": "error", "name": "Rebalancer_RequestNotValid"}, {"inputs": [], "type": "error", "name": "Rebalancer_TransferSizeExcedeed"}, {"inputs": [], "type": "error", "name": "Rebalancer_TransferSizeMinNotMet"}, {"inputs": [{"internalType": "address[]", "name": "list", "type": "address[]", "indexed": false}, {"internalType": "bool", "name": "status", "type": "bool", "indexed": false}], "type": "event", "name": "AllowedListUpdated", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "bridge", "type": "address", "indexed": true}, {"internalType": "bool", "name": "status", "type": "bool", "indexed": false}], "type": "event", "name": "BridgeWhitelistedStatusUpdated", "anonymous": false}, {"inputs": [{"internalType": "uint32", "name": "dst<PERSON>hainId", "type": "uint32", "indexed": true}, {"internalType": "bool", "name": "status", "type": "bool", "indexed": false}], "type": "event", "name": "DestinationWhitelistedStatusUpdated", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}], "type": "event", "name": "EthSaved", "anonymous": false}, {"inputs": [{"internalType": "uint32", "name": "dst<PERSON>hainId", "type": "uint32", "indexed": true}, {"internalType": "address", "name": "token", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "newLimit", "type": "uint256", "indexed": false}], "type": "event", "name": "MaxTransferSizeUpdated", "anonymous": false}, {"inputs": [{"internalType": "uint32", "name": "dst<PERSON>hainId", "type": "uint32", "indexed": true}, {"internalType": "address", "name": "token", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "newLimit", "type": "uint256", "indexed": false}], "type": "event", "name": "MinTransferSizeUpdated", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "bridge", "type": "address", "indexed": true}, {"internalType": "uint32", "name": "dst<PERSON>hainId", "type": "uint32", "indexed": true}, {"internalType": "address", "name": "token", "type": "address", "indexed": true}, {"internalType": "bytes", "name": "message", "type": "bytes", "indexed": false}, {"internalType": "bytes", "name": "bridgeData", "type": "bytes", "indexed": false}], "type": "event", "name": "MsgSent", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "bridge", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isBridgeWhitelisted", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "uint32", "name": "dstId", "type": "uint32"}], "stateMutability": "view", "type": "function", "name": "isDestinationWhitelisted", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "nonce", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "bridge", "type": "address"}, {"internalType": "address", "name": "_market", "type": "address"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}, {"internalType": "struct IRebalancer.Msg", "name": "msg", "type": "tuple", "components": [{"internalType": "uint32", "name": "dst<PERSON>hainId", "type": "uint32"}, {"internalType": "address", "name": "token", "type": "address"}, {"internalType": "bytes", "name": "message", "type": "bytes"}, {"internalType": "bytes", "name": "bridgeData", "type": "bytes"}]}], "stateMutability": "payable", "type": "function", "name": "sendMsg"}], "devdoc": {"kind": "dev", "methods": {"sendMsg(address,address,uint256,(uint32,address,bytes,bytes))": {"params": {"_amount": "the amount to rebalance", "_market": "the market to rebalance from address", "bridge": "the whitelisted bridge address", "msg": "the message data"}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"isBridgeWhitelisted(address)": {"notice": "returns if a bridge implementation is whitelisted"}, "isDestinationWhitelisted(uint32)": {"notice": "returns if a destination is whitelisted"}, "nonce()": {"notice": "returns current nonce"}, "sendMsg(address,address,uint256,(uint32,address,bytes,bytes))": {"notice": "sends a bridge message"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/interfaces/IRebalancer.sol": "IRebalancer"}, "evmVersion": "london", "libraries": {}}, "sources": {"src/interfaces/IRebalancer.sol": {"keccak256": "0x3dd4db0fa764498e57ba061af2e501eebc9486cccd6483a5d9ee3ad88ac62281", "urls": ["bzz-raw://07489b56d8bd43e84c486ba05e66a0b01fab55970396ec5bad10f5cad10fd27e", "dweb:/ipfs/QmWuCXdpWyA4Zj2BFp8uiF9WcpbyDpUR9ysBmAFzVmbuYz"], "license": "AGPL-3.0"}}, "version": 1}, "id": 141}