{"abi": [{"type": "function", "name": "IS_SCRIPT", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "run", "inputs": [{"name": "deployer", "type": "address", "internalType": "contract Deployer"}, {"name": "data", "type": "tuple", "internalType": "struct DeployJumpRateModelV4.InterestData", "components": [{"name": "kink", "type": "uint256", "internalType": "uint256"}, {"name": "name", "type": "string", "internalType": "string"}, {"name": "blocksPerYear", "type": "uint256", "internalType": "uint256"}, {"name": "baseRatePerYear", "type": "uint256", "internalType": "uint256"}, {"name": "multiplierPerYear", "type": "uint256", "internalType": "uint256"}, {"name": "jumpMultiplierPerYear", "type": "uint256", "internalType": "uint256"}]}, {"name": "owner", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}], "bytecode": {"object": "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", "sourceMap": "651:1733:100:-:0;;;3126:44:3;;;-1:-1:-1;;849:28:1;;;;;651:1733:100;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "651:1733:100:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;912:1239;;;;;;:::i;:::-;;:::i;:::-;;;-1:-1:-1;;;;;3132:32:242;;;3114:51;;3102:2;3087:18;912:1239:100;;;;;;;;849:28:1;;;;;;;;;;;;;;;3341:14:242;;3334:22;3316:41;;3304:2;3289:18;849:28:1;3176:187:242;912:1239:100;1034:25;;-1:-1:-1;;;1034:25:100;;3570:2:242;1034:25:100;;;3552:21:242;3609:2;3589:18;;;3582:30;-1:-1:-1;;;3628:18:242;;;3621:41;1001:7:100;;;;336:42:0;;1034:10:100;;3679:18:242;;1034:25:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1020:39;;1070:12;1085:56;1107:4;:9;;;1093:47;;;;;;;;:::i;:::-;;;;;;;;;;;;;1085:7;:56::i;:::-;1070:71;;1152:58;;;;;;;;;;;;;;;;;;1200:4;:9;;;1152:11;:58::i;:::-;1239:25;;-1:-1:-1;;;1239:25:100;;;;;4764::242;;;1221:15:100;;-1:-1:-1;;;;;1239:19:100;;;;;4737:18:242;;1239:25:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1221:43;;1326:7;-1:-1:-1;;;;;1326:19:100;;1349:1;1326:24;1322:798;;1366:22;;-1:-1:-1;;;1366:22:100;;;;;4764:25:242;;;336:42:0;;1366:17:100;;4737:18:242;;1366:22:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1412:8;-1:-1:-1;;;;;1412:15:100;;1445:4;1505:34;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;1597:4;:18;;;1641:4;:20;;;1687:4;:22;;;1735:4;:26;;;1787:4;:9;;;1822:5;1853:4;:9;;;1561:323;;;;;;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;1561:323:100;;;;;;;;;;1467:435;;;1561:323;1467:435;;:::i;:::-;;;;;;;;;;;;;1412:504;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1402:514;;336:42:0;-1:-1:-1;;;;;1930:16:100;;:18;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1962:55;;;;;;;;;;;;;;;;;;2009:7;1962:11;:55::i;:::-;1322:798;;;2048:61;;;;;;;;;;;;;;;;;;2101:7;2048:11;:61::i;:::-;2137:7;912:1239;-1:-1:-1;;;;;;912:1239:100:o;2157:225::-;2301:27;;-1:-1:-1;;;2301:27:100;;7199:2:242;2301:27:100;;;7181:21:242;7238:2;7218:18;;;7211:30;-1:-1:-1;;;7257:18:242;;;7250:41;2217:7:100;;2283:10;;336:42:0;;2301:12:100;;7308:18:242;;2301:27:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;2301:27:100;;;;;;;;;;;;:::i;:::-;2351:4;2337:26;;;;;;;;:::i;:::-;;;;-1:-1:-1;;2337:26:100;;;;;;;;;;2266:99;;;;2337:26;2266:99;;:::i;:::-;;;;;;;;;;;;;2243:132;;;;;;2236:139;;2157:225;;;:::o;7439:150:16:-;7512:70;7574:2;7578;7528:53;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;7528:53:16;;;;;;;;;;;;;;-1:-1:-1;;;;;7528:53:16;-1:-1:-1;;;7528:53:16;;;7512:15;:70::i;:::-;7439:150;;:::o;7740:145::-;7807:71;7870:2;7874;7823:54;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;7823:54:16;;;;;;;;;;;;;;-1:-1:-1;;;;;7823:54:16;-1:-1:-1;;;7823:54:16;;;851:129;922:51;965:7;934:29;922:51::i;:::-;851:129;:::o;180:463::-;265:22;131:42;265:40;;594:1;571;541:7;535:14;510:2;501:7;497:16;461:14;434:5;402:211;381:246;367:270;180:463;:::o;-1:-1:-1:-;;;;;;;;:::o;14:141:242:-;-1:-1:-1;;;;;99:31:242;;89:42;;79:70;;145:1;142;135:12;160:127;221:10;216:3;212:20;209:1;202:31;252:4;249:1;242:15;276:4;273:1;266:15;292:253;364:2;358:9;406:4;394:17;;441:18;426:34;;462:22;;;423:62;420:88;;;488:18;;:::i;:::-;524:2;517:22;292:253;:::o;550:275::-;621:2;615:9;686:2;667:13;;-1:-1:-1;;663:27:242;651:40;;721:18;706:34;;742:22;;;703:62;700:88;;;768:18;;:::i;:::-;804:2;797:22;550:275;;-1:-1:-1;550:275:242:o;830:187::-;879:4;912:18;904:6;901:30;898:56;;;934:18;;:::i;:::-;-1:-1:-1;1000:2:242;979:15;-1:-1:-1;;975:29:242;1006:4;971:40;;830:187::o;1022:144::-;1090:20;;1119:41;1090:20;1119:41;:::i;:::-;1022:144;;;:::o;1171:1792::-;1297:6;1305;1313;1366:2;1354:9;1345:7;1341:23;1337:32;1334:52;;;1382:1;1379;1372:12;1334:52;1421:9;1408:23;1440:41;1475:5;1440:41;:::i;:::-;1500:5;-1:-1:-1;1556:2:242;1541:18;;1528:32;1583:18;1572:30;;1569:50;;;1615:1;1612;1605:12;1569:50;1638:22;;1694:4;1676:16;;;1672:27;1669:47;;;1712:1;1709;1702:12;1669:47;1740:22;;:::i;:::-;1807:16;;1832:24;;1902:2;1894:11;;1881:25;1931:18;1918:32;;1915:52;;;1963:1;1960;1953:12;1915:52;1986:17;;2034:4;2026:13;;2022:27;-1:-1:-1;2012:55:242;;2063:1;2060;2053:12;2012:55;2103:2;2090:16;2128:53;2144:36;2173:6;2144:36;:::i;:::-;2128:53;:::i;:::-;2204:6;2197:5;2190:21;2252:7;2247:2;2238:6;2234:2;2230:15;2226:24;2223:37;2220:57;;;2273:1;2270;2263:12;2220:57;2328:6;2323:2;2319;2315:11;2310:2;2303:5;2299:14;2286:49;2380:1;2375:2;2355:18;;;2351:27;;2344:38;2398:16;;;2391:31;-1:-1:-1;2488:2:242;2480:11;;;2467:25;2508:16;;;2501:33;2600:2;2592:11;;;2579:25;2620:16;;;2613:33;2712:3;2704:12;;;2691:26;2733:17;;;2726:34;2826:3;2818:12;;;2805:26;2847:17;;;2840:34;;;;2398:16;-1:-1:-1;2919:38:242;;2938:18;;2919:38;:::i;:::-;2909:48;;1171:1792;;;;;:::o;3708:184::-;3778:6;3831:2;3819:9;3810:7;3806:23;3802:32;3799:52;;;3847:1;3844;3837:12;3799:52;-1:-1:-1;3870:16:242;;3708:184;-1:-1:-1;3708:184:242:o;3897:250::-;3982:1;3992:113;4006:6;4003:1;4000:13;3992:113;;;4082:11;;;4076:18;4063:11;;;4056:39;4028:2;4021:10;3992:113;;;-1:-1:-1;;4139:1:242;4121:16;;4114:27;3897:250::o;4152:461::-;4374:3;4412:6;4406:13;4428:66;4487:6;4482:3;4475:4;4467:6;4463:17;4428:66;:::i;:::-;-1:-1:-1;;;4516:16:242;;4541:36;;;-1:-1:-1;4604:2:242;4593:14;;4152:461;-1:-1:-1;4152:461:242:o;4800:261::-;4870:6;4923:2;4911:9;4902:7;4898:23;4894:32;4891:52;;;4939:1;4936;4929:12;4891:52;4971:9;4965:16;4990:41;5025:5;4990:41;:::i;:::-;5050:5;4800:261;-1:-1:-1;;;4800:261:242:o;5248:271::-;5290:3;5328:5;5322:12;5355:6;5350:3;5343:19;5371:76;5440:6;5433:4;5428:3;5424:14;5417:4;5410:5;5406:16;5371:76;:::i;:::-;5501:2;5480:15;-1:-1:-1;;5476:29:242;5467:39;;;;5508:4;5463:50;;5248:271;-1:-1:-1;;5248:271:242:o;5524:677::-;5841:6;5830:9;5823:25;5884:6;5879:2;5868:9;5864:18;5857:34;5927:6;5922:2;5911:9;5907:18;5900:34;5970:6;5965:2;5954:9;5950:18;5943:34;6014:6;6008:3;5997:9;5993:19;5986:35;6087:1;6083;6078:3;6074:11;6070:19;6062:6;6058:32;6052:3;6041:9;6037:19;6030:61;6128:3;6122;6111:9;6107:19;6100:32;5804:4;6149:46;6190:3;6179:9;6175:19;6167:6;6149:46;:::i;:::-;6141:54;5524:677;-1:-1:-1;;;;;;;;;5524:677:242:o;6206:492::-;6381:3;6419:6;6413:13;6435:66;6494:6;6489:3;6482:4;6474:6;6470:17;6435:66;:::i;:::-;6564:13;;6523:16;;;;6586:70;6564:13;6523:16;6633:4;6621:17;;6586:70;:::i;:::-;6672:20;;6206:492;-1:-1:-1;;;;6206:492:242:o;6703:289::-;6878:6;6867:9;6860:25;6921:2;6916;6905:9;6901:18;6894:30;6841:4;6941:45;6982:2;6971:9;6967:18;6959:6;6941:45;:::i;:::-;6933:53;6703:289;-1:-1:-1;;;;6703:289:242:o;7337:669::-;7417:6;7470:2;7458:9;7449:7;7445:23;7441:32;7438:52;;;7486:1;7483;7476:12;7438:52;7519:9;7513:16;7552:18;7544:6;7541:30;7538:50;;;7584:1;7581;7574:12;7538:50;7607:22;;7660:4;7652:13;;7648:27;-1:-1:-1;7638:55:242;;7689:1;7686;7679:12;7638:55;7722:2;7716:9;7747:53;7763:36;7792:6;7763:36;:::i;7747:53::-;7823:6;7816:5;7809:21;7871:7;7866:2;7857:6;7853:2;7849:15;7845:24;7842:37;7839:57;;;7892:1;7889;7882:12;7839:57;7905:71;7969:6;7964:2;7957:5;7953:14;7948:2;7944;7940:11;7905:71;:::i;:::-;7995:5;7337:669;-1:-1:-1;;;;;7337:669:242:o;8011:443::-;8232:3;8270:6;8264:13;8286:66;8345:6;8340:3;8333:4;8325:6;8321:17;8286:66;:::i;:::-;-1:-1:-1;;;8374:16:242;;8399:20;;;-1:-1:-1;8446:1:242;8435:13;;8011:443;-1:-1:-1;8011:443:242:o;8459:613::-;8717:26;8713:31;8704:6;8700:2;8696:15;8692:53;8687:3;8680:66;8662:3;8775:6;8769:13;8791:75;8859:6;8854:2;8849:3;8845:12;8838:4;8830:6;8826:17;8791:75;:::i;:::-;8926:13;;8885:16;;;;8948:76;8926:13;9010:2;9002:11;;8995:4;8983:17;;8948:76;:::i;:::-;9044:17;9063:2;9040:26;;8459:613;-1:-1:-1;;;;;8459:613:242:o;9077:383::-;9274:2;9263:9;9256:21;9237:4;9300:45;9341:2;9330:9;9326:18;9318:6;9300:45;:::i;:::-;9393:9;9385:6;9381:22;9376:2;9365:9;9361:18;9354:50;9421:33;9447:6;9439;9421:33;:::i;9465:317::-;9642:2;9631:9;9624:21;9605:4;9662:45;9703:2;9692:9;9688:18;9680:6;9662:45;:::i;:::-;9654:53;;9772:1;9768;9763:3;9759:11;9755:19;9747:6;9743:32;9738:2;9727:9;9723:18;9716:60;9465:317;;;;;:::o", "linkReferences": {}}, "methodIdentifiers": {"IS_SCRIPT()": "f8ccbf47", "run(address,(uint256,string,uint256,uint256,uint256,uint256),address)": "a301879f"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"IS_SCRIPT\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"contract Deployer\",\"name\":\"deployer\",\"type\":\"address\"},{\"components\":[{\"internalType\":\"uint256\",\"name\":\"kink\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"blocksPerYear\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"baseRatePerYear\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"multiplierPerYear\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"jumpMultiplierPerYear\",\"type\":\"uint256\"}],\"internalType\":\"struct DeployJumpRateModelV4.InterestData\",\"name\":\"data\",\"type\":\"tuple\"},{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"run\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"notice\":\"forge script script/deployment/interest/DeployJumpRateModelV4.s.sol:DeployJumpRateModelV4  \\\\     --slow \\\\     --verify \\\\     --verifier-url <url> \\\\     --rpc-url <url> \\\\     --etherscan-api-key <key> \\\\     --sig \\\"run((uint256,string,uint256,uint256,uint256,uint256))\\\" \\\"(750000000000000000,'ExampleName',2102400,20000000000000000,100000000000000000,500000000000000000)\\\" \\\\     --broadcast\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"script/deployment/interest/DeployJumpRateModelV4.s.sol\":\"DeployJumpRateModelV4\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"lib/forge-std/src/Base.sol\":{\"keccak256\":\"0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d\",\"dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z\"]},\"lib/forge-std/src/Script.sol\":{\"keccak256\":\"0xc942e27c7baae499beb01afbbae99f24d42af9a6e4aae675bc6901b704aa8e9b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0456008adf68947247f358b62863af4a8e349549d2260f2ff9569ff0e3cf5c98\",\"dweb:/ipfs/QmdviSUj2i7o3TPN5vd2xocqGMFVqjUzaiJTZRYyPxyHPx\"]},\"lib/forge-std/src/StdChains.sol\":{\"keccak256\":\"0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351\",\"dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm\"]},\"lib/forge-std/src/StdCheats.sol\":{\"keccak256\":\"0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41\",\"dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK\"]},\"lib/forge-std/src/StdConstants.sol\":{\"keccak256\":\"0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc\",\"dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r\"]},\"lib/forge-std/src/StdJson.sol\":{\"keccak256\":\"0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974\",\"dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3\"]},\"lib/forge-std/src/StdMath.sol\":{\"keccak256\":\"0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92\",\"dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC\"]},\"lib/forge-std/src/StdStorage.sol\":{\"keccak256\":\"0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57\",\"dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ\"]},\"lib/forge-std/src/StdStyle.sol\":{\"keccak256\":\"0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8\",\"dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK\"]},\"lib/forge-std/src/StdUtils.sol\":{\"keccak256\":\"0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138\",\"dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776\"]},\"lib/forge-std/src/Vm.sol\":{\"keccak256\":\"0xaae88142a348064677cf81169ea0ca706be3e99e2a0f13a01878ff8d52cf6b25\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://da4fb0ed1b31381c7e60ee386acc72796e8a8325c83739ec9a3678e2f841073c\",\"dweb:/ipfs/Qmb4MU6FpAi8Wt5kUQ3wa7ooHnfMVbdd9zuvnqbTehLmxJ\"]},\"lib/forge-std/src/console.sol\":{\"keccak256\":\"0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57\",\"dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP\"]},\"lib/forge-std/src/console2.sol\":{\"keccak256\":\"0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d\",\"dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ\"]},\"lib/forge-std/src/interfaces/IMulticall3.sol\":{\"keccak256\":\"0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0\",\"dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2\"]},\"lib/forge-std/src/safeconsole.sol\":{\"keccak256\":\"0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab\",\"dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3\"]},\"lib/openzeppelin-contracts/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"script/deployment/interest/DeployJumpRateModelV4.s.sol\":{\"keccak256\":\"0x93643154d2180e3ff59737b85ce9cceb7eb95b39f05d13a6cc48b3e8cca066d0\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://769abf8614dcc1dbfa7a3beeb956313f44a7e27999a71fd25e680dd230ce6e6c\",\"dweb:/ipfs/QmQJhytXsFqKGvuPFHDEyA64rDLAGsNPg2w7hoUGnx5Hx5\"]},\"src/interest/JumpRateModelV4.sol\":{\"keccak256\":\"0xa93081b79a997a666c96497334d721b37abfd0f193c1031e9f7f2e1b0823c37b\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://b640c34eedc95215fc74b43637256f52acb95052a946a952402b18516a67f7d5\",\"dweb:/ipfs/QmeSemgHAutRfCAttB1mTQ2AYXQpRj1Eq7haGS8QgnTHUa\"]},\"src/interfaces/IInterestRateModel.sol\":{\"keccak256\":\"0xdc8386be37a06d93fb0967be32e465f3b1f941ec9ce2f90984a5c70488cddb1e\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://de2bf96a7d0945f58b00e9fc867ac00fc4a09480e9e27c1a800604295320b769\",\"dweb:/ipfs/Qmf5sAgA58GjVxeipJNhP4euxwsZ9FS9CgsWTJ319UmCUE\"]},\"src/libraries/Bytes32AddressLib.sol\":{\"keccak256\":\"0xd2c90ee900d4cfe4b48254363cfbada176cd8b130ddee62ede7f9c8ec2a6b8bd\",\"license\":\"AGPL-3.0-only\",\"urls\":[\"bzz-raw://e864869c9c87f0cb20b898c0435f9585db5e1f52ba0d235af76c8e6a8b4fbffd\",\"dweb:/ipfs/QmRCLJpFCGsra4VFGDNf2hEuEJP7sMs6PQxZz6CV3sGcxa\"]},\"src/libraries/CREATE3.sol\":{\"keccak256\":\"0x71104c800a3f8588df1011b4c03d6c206e4738b6b9f0f2b2c5910754e9e6c975\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://4b1a6e66b89698a8d8d236da1eaa2be8728cb3cd872393b3b4ff00dc79fba6f2\",\"dweb:/ipfs/QmW78LNN4W5h19G9qQsdMd8ffc2kVBmCwT3Dqs28R6ZyPC\"]},\"src/utils/Deployer.sol\":{\"keccak256\":\"0xfb7f1e6a2c171b5d0e3f864383774bee1d97e29c510b0623b357c808c5f36489\",\"license\":\"AGPL-3.0\",\"urls\":[\"bzz-raw://cf252f86bb47a8c766d39602cf345c24d408e536b49053541b942878de57a64d\",\"dweb:/ipfs/QmNsfFKPtXaEG9yX9A7JV9MGQTZ2C8E7nxzZtjpvEzQyQc\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "view", "type": "function", "name": "IS_SCRIPT", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "contract Deployer", "name": "deployer", "type": "address"}, {"internalType": "struct DeployJumpRateModelV4.InterestData", "name": "data", "type": "tuple", "components": [{"internalType": "uint256", "name": "kink", "type": "uint256"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "uint256", "name": "blocksPerYear", "type": "uint256"}, {"internalType": "uint256", "name": "baseRatePerYear", "type": "uint256"}, {"internalType": "uint256", "name": "multiplierPerYear", "type": "uint256"}, {"internalType": "uint256", "name": "jumpMultiplierPerYear", "type": "uint256"}]}, {"internalType": "address", "name": "owner", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "run", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"script/deployment/interest/DeployJumpRateModelV4.s.sol": "DeployJumpRateModelV4"}, "evmVersion": "london", "libraries": {}}, "sources": {"lib/forge-std/src/Base.sol": {"keccak256": "0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd", "urls": ["bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d", "dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z"], "license": "MIT"}, "lib/forge-std/src/Script.sol": {"keccak256": "0xc942e27c7baae499beb01afbbae99f24d42af9a6e4aae675bc6901b704aa8e9b", "urls": ["bzz-raw://0456008adf68947247f358b62863af4a8e349549d2260f2ff9569ff0e3cf5c98", "dweb:/ipfs/QmdviSUj2i7o3TPN5vd2xocqGMFVqjUzaiJTZRYyPxyHPx"], "license": "MIT"}, "lib/forge-std/src/StdChains.sol": {"keccak256": "0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e", "urls": ["bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351", "dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm"], "license": "MIT"}, "lib/forge-std/src/StdCheats.sol": {"keccak256": "0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746", "urls": ["bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41", "dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK"], "license": "MIT"}, "lib/forge-std/src/StdConstants.sol": {"keccak256": "0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534", "urls": ["bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc", "dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r"], "license": "MIT"}, "lib/forge-std/src/StdJson.sol": {"keccak256": "0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500", "urls": ["bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974", "dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3"], "license": "MIT"}, "lib/forge-std/src/StdMath.sol": {"keccak256": "0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2", "urls": ["bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92", "dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC"], "license": "MIT"}, "lib/forge-std/src/StdStorage.sol": {"keccak256": "0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc", "urls": ["bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57", "dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ"], "license": "MIT"}, "lib/forge-std/src/StdStyle.sol": {"keccak256": "0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d", "urls": ["bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8", "dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK"], "license": "MIT"}, "lib/forge-std/src/StdUtils.sol": {"keccak256": "0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737", "urls": ["bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138", "dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776"], "license": "MIT"}, "lib/forge-std/src/Vm.sol": {"keccak256": "0xaae88142a348064677cf81169ea0ca706be3e99e2a0f13a01878ff8d52cf6b25", "urls": ["bzz-raw://da4fb0ed1b31381c7e60ee386acc72796e8a8325c83739ec9a3678e2f841073c", "dweb:/ipfs/Qmb4MU6FpAi8Wt5kUQ3wa7ooHnfMVbdd9zuvnqbTehLmxJ"], "license": "MIT OR Apache-2.0"}, "lib/forge-std/src/console.sol": {"keccak256": "0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5", "urls": ["bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57", "dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP"], "license": "MIT"}, "lib/forge-std/src/console2.sol": {"keccak256": "0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f", "urls": ["bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d", "dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ"], "license": "MIT"}, "lib/forge-std/src/interfaces/IMulticall3.sol": {"keccak256": "0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a", "urls": ["bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0", "dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2"], "license": "MIT"}, "lib/forge-std/src/safeconsole.sol": {"keccak256": "0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11", "urls": ["bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab", "dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/access/Ownable.sol": {"keccak256": "0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb", "urls": ["bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6", "dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "script/deployment/interest/DeployJumpRateModelV4.s.sol": {"keccak256": "0x93643154d2180e3ff59737b85ce9cceb7eb95b39f05d13a6cc48b3e8cca066d0", "urls": ["bzz-raw://769abf8614dcc1dbfa7a3beeb956313f44a7e27999a71fd25e680dd230ce6e6c", "dweb:/ipfs/QmQJhytXsFqKGvuPFHDEyA64rDLAGsNPg2w7hoUGnx5Hx5"], "license": "BSL-1.1"}, "src/interest/JumpRateModelV4.sol": {"keccak256": "0xa93081b79a997a666c96497334d721b37abfd0f193c1031e9f7f2e1b0823c37b", "urls": ["bzz-raw://b640c34eedc95215fc74b43637256f52acb95052a946a952402b18516a67f7d5", "dweb:/ipfs/QmeSemgHAutRfCAttB1mTQ2AYXQpRj1Eq7haGS8QgnTHUa"], "license": "BSL-1.1"}, "src/interfaces/IInterestRateModel.sol": {"keccak256": "0xdc8386be37a06d93fb0967be32e465f3b1f941ec9ce2f90984a5c70488cddb1e", "urls": ["bzz-raw://de2bf96a7d0945f58b00e9fc867ac00fc4a09480e9e27c1a800604295320b769", "dweb:/ipfs/Qmf5sAgA58GjVxeipJNhP4euxwsZ9FS9CgsWTJ319UmCUE"], "license": "BSL-1.1"}, "src/libraries/Bytes32AddressLib.sol": {"keccak256": "0xd2c90ee900d4cfe4b48254363cfbada176cd8b130ddee62ede7f9c8ec2a6b8bd", "urls": ["bzz-raw://e864869c9c87f0cb20b898c0435f9585db5e1f52ba0d235af76c8e6a8b4fbffd", "dweb:/ipfs/QmRCLJpFCGsra4VFGDNf2hEuEJP7sMs6PQxZz6CV3sGcxa"], "license": "AGPL-3.0-only"}, "src/libraries/CREATE3.sol": {"keccak256": "0x71104c800a3f8588df1011b4c03d6c206e4738b6b9f0f2b2c5910754e9e6c975", "urls": ["bzz-raw://4b1a6e66b89698a8d8d236da1eaa2be8728cb3cd872393b3b4ff00dc79fba6f2", "dweb:/ipfs/QmW78LNN4W5h19G9qQsdMd8ffc2kVBmCwT3Dqs28R6ZyPC"], "license": "BSL-1.1"}, "src/utils/Deployer.sol": {"keccak256": "0xfb7f1e6a2c171b5d0e3f864383774bee1d97e29c510b0623b357c808c5f36489", "urls": ["bzz-raw://cf252f86bb47a8c766d39602cf345c24d408e536b49053541b942878de57a64d", "dweb:/ipfs/QmNsfFKPtXaEG9yX9A7JV9MGQTZ2C8E7nxzZtjpvEzQyQc"], "license": "AGPL-3.0"}}, "version": 1}, "id": 100}