{"abi": [{"type": "function", "name": "newIntent", "inputs": [{"name": "_destinations", "type": "uint32[]", "internalType": "uint32[]"}, {"name": "_receiver", "type": "bytes32", "internalType": "bytes32"}, {"name": "_inputAsset", "type": "address", "internalType": "address"}, {"name": "_outputAsset", "type": "bytes32", "internalType": "bytes32"}, {"name": "_amount", "type": "uint256", "internalType": "uint256"}, {"name": "_maxFee", "type": "uint24", "internalType": "uint24"}, {"name": "_ttl", "type": "uint48", "internalType": "uint48"}, {"name": "_data", "type": "bytes", "internalType": "bytes"}, {"name": "_feeParams", "type": "tuple", "internalType": "struct IFeeAdapter.FeeParams", "components": [{"name": "fee", "type": "uint256", "internalType": "uint256"}, {"name": "deadline", "type": "uint256", "internalType": "uint256"}, {"name": "sig", "type": "bytes", "internalType": "bytes"}]}], "outputs": [{"name": "_intentId", "type": "bytes32", "internalType": "bytes32"}, {"name": "_intent", "type": "tuple", "internalType": "struct IFeeAdapter.Intent", "components": [{"name": "initiator", "type": "bytes32", "internalType": "bytes32"}, {"name": "receiver", "type": "bytes32", "internalType": "bytes32"}, {"name": "inputAsset", "type": "bytes32", "internalType": "bytes32"}, {"name": "outputAsset", "type": "bytes32", "internalType": "bytes32"}, {"name": "maxFee", "type": "uint24", "internalType": "uint24"}, {"name": "origin", "type": "uint32", "internalType": "uint32"}, {"name": "nonce", "type": "uint64", "internalType": "uint64"}, {"name": "timestamp", "type": "uint48", "internalType": "uint48"}, {"name": "ttl", "type": "uint48", "internalType": "uint48"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "destinations", "type": "uint32[]", "internalType": "uint32[]"}, {"name": "data", "type": "bytes", "internalType": "bytes"}]}], "stateMutability": "payable"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"newIntent(uint32[],bytes32,address,bytes32,uint256,uint24,uint48,bytes,(uint256,uint256,bytes))": "3bd1c754"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"uint32[]\",\"name\":\"_destinations\",\"type\":\"uint32[]\"},{\"internalType\":\"bytes32\",\"name\":\"_receiver\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"_inputAsset\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"_outputAsset\",\"type\":\"bytes32\"},{\"internalType\":\"uint256\",\"name\":\"_amount\",\"type\":\"uint256\"},{\"internalType\":\"uint24\",\"name\":\"_maxFee\",\"type\":\"uint24\"},{\"internalType\":\"uint48\",\"name\":\"_ttl\",\"type\":\"uint48\"},{\"internalType\":\"bytes\",\"name\":\"_data\",\"type\":\"bytes\"},{\"components\":[{\"internalType\":\"uint256\",\"name\":\"fee\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"deadline\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"sig\",\"type\":\"bytes\"}],\"internalType\":\"struct IFeeAdapter.FeeParams\",\"name\":\"_feeParams\",\"type\":\"tuple\"}],\"name\":\"newIntent\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"_intentId\",\"type\":\"bytes32\"},{\"components\":[{\"internalType\":\"bytes32\",\"name\":\"initiator\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"receiver\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"inputAsset\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"outputAsset\",\"type\":\"bytes32\"},{\"internalType\":\"uint24\",\"name\":\"maxFee\",\"type\":\"uint24\"},{\"internalType\":\"uint32\",\"name\":\"origin\",\"type\":\"uint32\"},{\"internalType\":\"uint64\",\"name\":\"nonce\",\"type\":\"uint64\"},{\"internalType\":\"uint48\",\"name\":\"timestamp\",\"type\":\"uint48\"},{\"internalType\":\"uint48\",\"name\":\"ttl\",\"type\":\"uint48\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"internalType\":\"uint32[]\",\"name\":\"destinations\",\"type\":\"uint32[]\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"internalType\":\"struct IFeeAdapter.Intent\",\"name\":\"_intent\",\"type\":\"tuple\"}],\"stateMutability\":\"payable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/external/everclear/IFeeAdapter.sol\":\"IFeeAdapter\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"src/interfaces/external/everclear/IFeeAdapter.sol\":{\"keccak256\":\"0x5e65e2852b9e9f52fbd8ad0a9c2cefa28e9d167f19f4b39f3880e85e70048942\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://bda4edad9cf58deddcbafadb3c26b9c0a57ab91a1f7222530ad500d03092e87b\",\"dweb:/ipfs/QmRoZCvjneC35i6YqLGX9kKXbp1wmQo9QBqnPdQgVCMYVe\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "uint32[]", "name": "_destinations", "type": "uint32[]"}, {"internalType": "bytes32", "name": "_receiver", "type": "bytes32"}, {"internalType": "address", "name": "_inputAsset", "type": "address"}, {"internalType": "bytes32", "name": "_outputAsset", "type": "bytes32"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}, {"internalType": "uint24", "name": "_maxFee", "type": "uint24"}, {"internalType": "uint48", "name": "_ttl", "type": "uint48"}, {"internalType": "bytes", "name": "_data", "type": "bytes"}, {"internalType": "struct IFeeAdapter.FeeParams", "name": "_feeParams", "type": "tuple", "components": [{"internalType": "uint256", "name": "fee", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "bytes", "name": "sig", "type": "bytes"}]}], "stateMutability": "payable", "type": "function", "name": "newIntent", "outputs": [{"internalType": "bytes32", "name": "_intentId", "type": "bytes32"}, {"internalType": "struct IFeeAdapter.Intent", "name": "_intent", "type": "tuple", "components": [{"internalType": "bytes32", "name": "initiator", "type": "bytes32"}, {"internalType": "bytes32", "name": "receiver", "type": "bytes32"}, {"internalType": "bytes32", "name": "inputAsset", "type": "bytes32"}, {"internalType": "bytes32", "name": "outputAsset", "type": "bytes32"}, {"internalType": "uint24", "name": "maxFee", "type": "uint24"}, {"internalType": "uint32", "name": "origin", "type": "uint32"}, {"internalType": "uint64", "name": "nonce", "type": "uint64"}, {"internalType": "uint48", "name": "timestamp", "type": "uint48"}, {"internalType": "uint48", "name": "ttl", "type": "uint48"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint32[]", "name": "destinations", "type": "uint32[]"}, {"internalType": "bytes", "name": "data", "type": "bytes"}]}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/interfaces/external/everclear/IFeeAdapter.sol": "IFeeAdapter"}, "evmVersion": "london", "libraries": {}}, "sources": {"src/interfaces/external/everclear/IFeeAdapter.sol": {"keccak256": "0x5e65e2852b9e9f52fbd8ad0a9c2cefa28e9d167f19f4b39f3880e85e70048942", "urls": ["bzz-raw://bda4edad9cf58deddcbafadb3c26b9c0a57ab91a1f7222530ad500d03092e87b", "dweb:/ipfs/QmRoZCvjneC35i6YqLGX9kKXbp1wmQo9QBqnPdQgVCMYVe"], "license": "BSL-1.1"}}, "version": 1}, "id": 153}