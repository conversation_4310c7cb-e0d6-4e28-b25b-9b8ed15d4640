{"abi": [{"type": "function", "name": "burn", "inputs": [{"name": "_oapp", "type": "address", "internalType": "address"}, {"name": "_srcEid", "type": "uint32", "internalType": "uint32"}, {"name": "_sender", "type": "bytes32", "internalType": "bytes32"}, {"name": "_nonce", "type": "uint64", "internalType": "uint64"}, {"name": "_payloadHash", "type": "bytes32", "internalType": "bytes32"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "eid", "inputs": [], "outputs": [{"name": "", "type": "uint32", "internalType": "uint32"}], "stateMutability": "view"}, {"type": "function", "name": "inboundNonce", "inputs": [{"name": "_receiver", "type": "address", "internalType": "address"}, {"name": "_srcEid", "type": "uint32", "internalType": "uint32"}, {"name": "_sender", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "uint64", "internalType": "uint64"}], "stateMutability": "view"}, {"type": "function", "name": "inboundPayloadHash", "inputs": [{"name": "_receiver", "type": "address", "internalType": "address"}, {"name": "_srcEid", "type": "uint32", "internalType": "uint32"}, {"name": "_sender", "type": "bytes32", "internalType": "bytes32"}, {"name": "_nonce", "type": "uint64", "internalType": "uint64"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "lazyInboundNonce", "inputs": [{"name": "_receiver", "type": "address", "internalType": "address"}, {"name": "_srcEid", "type": "uint32", "internalType": "uint32"}, {"name": "_sender", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "uint64", "internalType": "uint64"}], "stateMutability": "view"}, {"type": "function", "name": "nextGuid", "inputs": [{"name": "_sender", "type": "address", "internalType": "address"}, {"name": "_dstEid", "type": "uint32", "internalType": "uint32"}, {"name": "_receiver", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "nilify", "inputs": [{"name": "_oapp", "type": "address", "internalType": "address"}, {"name": "_srcEid", "type": "uint32", "internalType": "uint32"}, {"name": "_sender", "type": "bytes32", "internalType": "bytes32"}, {"name": "_nonce", "type": "uint64", "internalType": "uint64"}, {"name": "_payloadHash", "type": "bytes32", "internalType": "bytes32"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "outbound<PERSON><PERSON><PERSON>", "inputs": [{"name": "_sender", "type": "address", "internalType": "address"}, {"name": "_dstEid", "type": "uint32", "internalType": "uint32"}, {"name": "_receiver", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "uint64", "internalType": "uint64"}], "stateMutability": "view"}, {"type": "function", "name": "skip", "inputs": [{"name": "_oapp", "type": "address", "internalType": "address"}, {"name": "_srcEid", "type": "uint32", "internalType": "uint32"}, {"name": "_sender", "type": "bytes32", "internalType": "bytes32"}, {"name": "_nonce", "type": "uint64", "internalType": "uint64"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "InboundNonceSkipped", "inputs": [{"name": "srcEid", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "sender", "type": "bytes32", "indexed": false, "internalType": "bytes32"}, {"name": "receiver", "type": "address", "indexed": false, "internalType": "address"}, {"name": "nonce", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "PacketBurnt", "inputs": [{"name": "srcEid", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "sender", "type": "bytes32", "indexed": false, "internalType": "bytes32"}, {"name": "receiver", "type": "address", "indexed": false, "internalType": "address"}, {"name": "nonce", "type": "uint64", "indexed": false, "internalType": "uint64"}, {"name": "payloadHash", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "PacketNilified", "inputs": [{"name": "srcEid", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "sender", "type": "bytes32", "indexed": false, "internalType": "bytes32"}, {"name": "receiver", "type": "address", "indexed": false, "internalType": "address"}, {"name": "nonce", "type": "uint64", "indexed": false, "internalType": "uint64"}, {"name": "payloadHash", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"burn(address,uint32,bytes32,uint64,bytes32)": "40f80683", "eid()": "416ecebf", "inboundNonce(address,uint32,bytes32)": "a0dd43fc", "inboundPayloadHash(address,uint32,bytes32,uint64)": "c9fc7bcd", "lazyInboundNonce(address,uint32,bytes32)": "5b17bb70", "nextGuid(address,uint32,bytes32)": "aafe5e07", "nilify(address,uint32,bytes32,uint64,bytes32)": "2e80fbf3", "outboundNonce(address,uint32,bytes32)": "9c6d7340", "skip(address,uint32,bytes32,uint64)": "d70b8902"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"srcEid\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"sender\",\"type\":\"bytes32\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"nonce\",\"type\":\"uint64\"}],\"name\":\"InboundNonceSkipped\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"srcEid\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"sender\",\"type\":\"bytes32\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"nonce\",\"type\":\"uint64\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"payloadHash\",\"type\":\"bytes32\"}],\"name\":\"PacketBurnt\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"srcEid\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"sender\",\"type\":\"bytes32\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"nonce\",\"type\":\"uint64\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"payloadHash\",\"type\":\"bytes32\"}],\"name\":\"PacketNilified\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_oapp\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"_srcEid\",\"type\":\"uint32\"},{\"internalType\":\"bytes32\",\"name\":\"_sender\",\"type\":\"bytes32\"},{\"internalType\":\"uint64\",\"name\":\"_nonce\",\"type\":\"uint64\"},{\"internalType\":\"bytes32\",\"name\":\"_payloadHash\",\"type\":\"bytes32\"}],\"name\":\"burn\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"eid\",\"outputs\":[{\"internalType\":\"uint32\",\"name\":\"\",\"type\":\"uint32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_receiver\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"_srcEid\",\"type\":\"uint32\"},{\"internalType\":\"bytes32\",\"name\":\"_sender\",\"type\":\"bytes32\"}],\"name\":\"inboundNonce\",\"outputs\":[{\"internalType\":\"uint64\",\"name\":\"\",\"type\":\"uint64\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_receiver\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"_srcEid\",\"type\":\"uint32\"},{\"internalType\":\"bytes32\",\"name\":\"_sender\",\"type\":\"bytes32\"},{\"internalType\":\"uint64\",\"name\":\"_nonce\",\"type\":\"uint64\"}],\"name\":\"inboundPayloadHash\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_receiver\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"_srcEid\",\"type\":\"uint32\"},{\"internalType\":\"bytes32\",\"name\":\"_sender\",\"type\":\"bytes32\"}],\"name\":\"lazyInboundNonce\",\"outputs\":[{\"internalType\":\"uint64\",\"name\":\"\",\"type\":\"uint64\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_sender\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"_dstEid\",\"type\":\"uint32\"},{\"internalType\":\"bytes32\",\"name\":\"_receiver\",\"type\":\"bytes32\"}],\"name\":\"nextGuid\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_oapp\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"_srcEid\",\"type\":\"uint32\"},{\"internalType\":\"bytes32\",\"name\":\"_sender\",\"type\":\"bytes32\"},{\"internalType\":\"uint64\",\"name\":\"_nonce\",\"type\":\"uint64\"},{\"internalType\":\"bytes32\",\"name\":\"_payloadHash\",\"type\":\"bytes32\"}],\"name\":\"nilify\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_sender\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"_dstEid\",\"type\":\"uint32\"},{\"internalType\":\"bytes32\",\"name\":\"_receiver\",\"type\":\"bytes32\"}],\"name\":\"outboundNonce\",\"outputs\":[{\"internalType\":\"uint64\",\"name\":\"\",\"type\":\"uint64\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_oapp\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"_srcEid\",\"type\":\"uint32\"},{\"internalType\":\"bytes32\",\"name\":\"_sender\",\"type\":\"bytes32\"},{\"internalType\":\"uint64\",\"name\":\"_nonce\",\"type\":\"uint64\"}],\"name\":\"skip\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/external/layerzero/v2/IMessagingChannel.sol\":\"IMessagingChannel\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"src/interfaces/external/layerzero/v2/IMessagingChannel.sol\":{\"keccak256\":\"0x77d1b0bd52cb0e3ae72849cd2d916dcde67986a32470c48f18a7c571a91a5d40\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://00b3db0d90d80c40ea84286966dfd4164bd34a984ad73d8280e7b4c2574b15e3\",\"dweb:/ipfs/QmT4tT4wRcq67v7wyh46aukFXPueM3tfmBwoKvRduVxgFh\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "uint32", "name": "srcEid", "type": "uint32", "indexed": false}, {"internalType": "bytes32", "name": "sender", "type": "bytes32", "indexed": false}, {"internalType": "address", "name": "receiver", "type": "address", "indexed": false}, {"internalType": "uint64", "name": "nonce", "type": "uint64", "indexed": false}], "type": "event", "name": "InboundNonceSkipped", "anonymous": false}, {"inputs": [{"internalType": "uint32", "name": "srcEid", "type": "uint32", "indexed": false}, {"internalType": "bytes32", "name": "sender", "type": "bytes32", "indexed": false}, {"internalType": "address", "name": "receiver", "type": "address", "indexed": false}, {"internalType": "uint64", "name": "nonce", "type": "uint64", "indexed": false}, {"internalType": "bytes32", "name": "payloadHash", "type": "bytes32", "indexed": false}], "type": "event", "name": "PacketBurnt", "anonymous": false}, {"inputs": [{"internalType": "uint32", "name": "srcEid", "type": "uint32", "indexed": false}, {"internalType": "bytes32", "name": "sender", "type": "bytes32", "indexed": false}, {"internalType": "address", "name": "receiver", "type": "address", "indexed": false}, {"internalType": "uint64", "name": "nonce", "type": "uint64", "indexed": false}, {"internalType": "bytes32", "name": "payloadHash", "type": "bytes32", "indexed": false}], "type": "event", "name": "PacketNilified", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "_oapp", "type": "address"}, {"internalType": "uint32", "name": "_srcEid", "type": "uint32"}, {"internalType": "bytes32", "name": "_sender", "type": "bytes32"}, {"internalType": "uint64", "name": "_nonce", "type": "uint64"}, {"internalType": "bytes32", "name": "_payloadHash", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function", "name": "burn"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "eid", "outputs": [{"internalType": "uint32", "name": "", "type": "uint32"}]}, {"inputs": [{"internalType": "address", "name": "_receiver", "type": "address"}, {"internalType": "uint32", "name": "_srcEid", "type": "uint32"}, {"internalType": "bytes32", "name": "_sender", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "inboundNonce", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}]}, {"inputs": [{"internalType": "address", "name": "_receiver", "type": "address"}, {"internalType": "uint32", "name": "_srcEid", "type": "uint32"}, {"internalType": "bytes32", "name": "_sender", "type": "bytes32"}, {"internalType": "uint64", "name": "_nonce", "type": "uint64"}], "stateMutability": "view", "type": "function", "name": "inboundPayloadHash", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "address", "name": "_receiver", "type": "address"}, {"internalType": "uint32", "name": "_srcEid", "type": "uint32"}, {"internalType": "bytes32", "name": "_sender", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "lazyInboundNonce", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}]}, {"inputs": [{"internalType": "address", "name": "_sender", "type": "address"}, {"internalType": "uint32", "name": "_dstEid", "type": "uint32"}, {"internalType": "bytes32", "name": "_receiver", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "nextGuid", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "address", "name": "_oapp", "type": "address"}, {"internalType": "uint32", "name": "_srcEid", "type": "uint32"}, {"internalType": "bytes32", "name": "_sender", "type": "bytes32"}, {"internalType": "uint64", "name": "_nonce", "type": "uint64"}, {"internalType": "bytes32", "name": "_payloadHash", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function", "name": "nilify"}, {"inputs": [{"internalType": "address", "name": "_sender", "type": "address"}, {"internalType": "uint32", "name": "_dstEid", "type": "uint32"}, {"internalType": "bytes32", "name": "_receiver", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "outbound<PERSON><PERSON><PERSON>", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}]}, {"inputs": [{"internalType": "address", "name": "_oapp", "type": "address"}, {"internalType": "uint32", "name": "_srcEid", "type": "uint32"}, {"internalType": "bytes32", "name": "_sender", "type": "bytes32"}, {"internalType": "uint64", "name": "_nonce", "type": "uint64"}], "stateMutability": "nonpayable", "type": "function", "name": "skip"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/interfaces/external/layerzero/v2/IMessagingChannel.sol": "IMessagingChannel"}, "evmVersion": "london", "libraries": {}}, "sources": {"src/interfaces/external/layerzero/v2/IMessagingChannel.sol": {"keccak256": "0x77d1b0bd52cb0e3ae72849cd2d916dcde67986a32470c48f18a7c571a91a5d40", "urls": ["bzz-raw://00b3db0d90d80c40ea84286966dfd4164bd34a984ad73d8280e7b4c2574b15e3", "dweb:/ipfs/QmT4tT4wRcq67v7wyh46aukFXPueM3tfmBwoKvRduVxgFh"], "license": "MIT"}}, "version": 1}, "id": 162}