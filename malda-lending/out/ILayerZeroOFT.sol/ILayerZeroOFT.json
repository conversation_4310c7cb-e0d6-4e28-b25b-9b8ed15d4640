{"abi": [{"type": "function", "name": "approvalRequired", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "oftVersion", "inputs": [], "outputs": [{"name": "interfaceId", "type": "bytes4", "internalType": "bytes4"}, {"name": "version", "type": "uint64", "internalType": "uint64"}], "stateMutability": "view"}, {"type": "function", "name": "quoteOFT", "inputs": [{"name": "_sendParam", "type": "tuple", "internalType": "struct SendP<PERSON><PERSON>", "components": [{"name": "dstEid", "type": "uint32", "internalType": "uint32"}, {"name": "to", "type": "bytes32", "internalType": "bytes32"}, {"name": "amountLD", "type": "uint256", "internalType": "uint256"}, {"name": "minAmountLD", "type": "uint256", "internalType": "uint256"}, {"name": "extraOptions", "type": "bytes", "internalType": "bytes"}, {"name": "composeMsg", "type": "bytes", "internalType": "bytes"}, {"name": "oftCmd", "type": "bytes", "internalType": "bytes"}]}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct OFTLimit", "components": [{"name": "minAmountLD", "type": "uint256", "internalType": "uint256"}, {"name": "maxAmountLD", "type": "uint256", "internalType": "uint256"}]}, {"name": "oftFeeDetails", "type": "tuple[]", "internalType": "struct OFTFeeDetail[]", "components": [{"name": "feeAmountLD", "type": "int256", "internalType": "int256"}, {"name": "description", "type": "string", "internalType": "string"}]}, {"name": "", "type": "tuple", "internalType": "struct OFTReceipt", "components": [{"name": "amountSentLD", "type": "uint256", "internalType": "uint256"}, {"name": "amountReceivedLD", "type": "uint256", "internalType": "uint256"}]}], "stateMutability": "view"}, {"type": "function", "name": "quoteSend", "inputs": [{"name": "_sendParam", "type": "tuple", "internalType": "struct SendP<PERSON><PERSON>", "components": [{"name": "dstEid", "type": "uint32", "internalType": "uint32"}, {"name": "to", "type": "bytes32", "internalType": "bytes32"}, {"name": "amountLD", "type": "uint256", "internalType": "uint256"}, {"name": "minAmountLD", "type": "uint256", "internalType": "uint256"}, {"name": "extraOptions", "type": "bytes", "internalType": "bytes"}, {"name": "composeMsg", "type": "bytes", "internalType": "bytes"}, {"name": "oftCmd", "type": "bytes", "internalType": "bytes"}]}, {"name": "_payInLzToken", "type": "bool", "internalType": "bool"}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct MessagingFee", "components": [{"name": "nativeFee", "type": "uint256", "internalType": "uint256"}, {"name": "lzTokenFee", "type": "uint256", "internalType": "uint256"}]}], "stateMutability": "view"}, {"type": "function", "name": "send", "inputs": [{"name": "_sendParam", "type": "tuple", "internalType": "struct SendP<PERSON><PERSON>", "components": [{"name": "dstEid", "type": "uint32", "internalType": "uint32"}, {"name": "to", "type": "bytes32", "internalType": "bytes32"}, {"name": "amountLD", "type": "uint256", "internalType": "uint256"}, {"name": "minAmountLD", "type": "uint256", "internalType": "uint256"}, {"name": "extraOptions", "type": "bytes", "internalType": "bytes"}, {"name": "composeMsg", "type": "bytes", "internalType": "bytes"}, {"name": "oftCmd", "type": "bytes", "internalType": "bytes"}]}, {"name": "_fee", "type": "tuple", "internalType": "struct MessagingFee", "components": [{"name": "nativeFee", "type": "uint256", "internalType": "uint256"}, {"name": "lzTokenFee", "type": "uint256", "internalType": "uint256"}]}, {"name": "_refundAddress", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct MessagingReceipt", "components": [{"name": "guid", "type": "bytes32", "internalType": "bytes32"}, {"name": "nonce", "type": "uint64", "internalType": "uint64"}, {"name": "fee", "type": "tuple", "internalType": "struct MessagingFee", "components": [{"name": "nativeFee", "type": "uint256", "internalType": "uint256"}, {"name": "lzTokenFee", "type": "uint256", "internalType": "uint256"}]}]}, {"name": "", "type": "tuple", "internalType": "struct OFTReceipt", "components": [{"name": "amountSentLD", "type": "uint256", "internalType": "uint256"}, {"name": "amountReceivedLD", "type": "uint256", "internalType": "uint256"}]}], "stateMutability": "payable"}, {"type": "function", "name": "sharedDecimals", "inputs": [], "outputs": [{"name": "", "type": "uint8", "internalType": "uint8"}], "stateMutability": "view"}, {"type": "function", "name": "token", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "event", "name": "OFTReceived", "inputs": [{"name": "guid", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "srcEid", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "to<PERSON><PERSON><PERSON>", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amountReceivedLD", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "OFTSent", "inputs": [{"name": "guid", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "dstEid", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "fromAddress", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amountSentLD", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "amountReceivedLD", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "error", "name": "InvalidLocalDecimals", "inputs": []}, {"type": "error", "name": "SlippageExceeded", "inputs": [{"name": "amountLD", "type": "uint256", "internalType": "uint256"}, {"name": "minAmountLD", "type": "uint256", "internalType": "uint256"}]}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"approvalRequired()": "9f68b964", "oftVersion()": "156a0d0f", "quoteOFT((uint32,bytes32,uint256,uint256,bytes,bytes,bytes))": "0d35b415", "quoteSend((uint32,bytes32,uint256,uint256,bytes,bytes,bytes),bool)": "3b6f743b", "send((uint32,bytes32,uint256,uint256,bytes,bytes,bytes),(uint256,uint256),address)": "c7c7f5b3", "sharedDecimals()": "857749b0", "token()": "fc0c546a"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"InvalidLocalDecimals\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"amountLD\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"minAmountLD\",\"type\":\"uint256\"}],\"name\":\"SlippageExceeded\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"guid\",\"type\":\"bytes32\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"srcEid\",\"type\":\"uint32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"toAddress\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amountReceivedLD\",\"type\":\"uint256\"}],\"name\":\"OFTReceived\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"guid\",\"type\":\"bytes32\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"dstEid\",\"type\":\"uint32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"fromAddress\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amountSentLD\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amountReceivedLD\",\"type\":\"uint256\"}],\"name\":\"OFTSent\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"approvalRequired\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"oftVersion\",\"outputs\":[{\"internalType\":\"bytes4\",\"name\":\"interfaceId\",\"type\":\"bytes4\"},{\"internalType\":\"uint64\",\"name\":\"version\",\"type\":\"uint64\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"uint32\",\"name\":\"dstEid\",\"type\":\"uint32\"},{\"internalType\":\"bytes32\",\"name\":\"to\",\"type\":\"bytes32\"},{\"internalType\":\"uint256\",\"name\":\"amountLD\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"minAmountLD\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"extraOptions\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"composeMsg\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"oftCmd\",\"type\":\"bytes\"}],\"internalType\":\"struct SendParam\",\"name\":\"_sendParam\",\"type\":\"tuple\"}],\"name\":\"quoteOFT\",\"outputs\":[{\"components\":[{\"internalType\":\"uint256\",\"name\":\"minAmountLD\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"maxAmountLD\",\"type\":\"uint256\"}],\"internalType\":\"struct OFTLimit\",\"name\":\"\",\"type\":\"tuple\"},{\"components\":[{\"internalType\":\"int256\",\"name\":\"feeAmountLD\",\"type\":\"int256\"},{\"internalType\":\"string\",\"name\":\"description\",\"type\":\"string\"}],\"internalType\":\"struct OFTFeeDetail[]\",\"name\":\"oftFeeDetails\",\"type\":\"tuple[]\"},{\"components\":[{\"internalType\":\"uint256\",\"name\":\"amountSentLD\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"amountReceivedLD\",\"type\":\"uint256\"}],\"internalType\":\"struct OFTReceipt\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"uint32\",\"name\":\"dstEid\",\"type\":\"uint32\"},{\"internalType\":\"bytes32\",\"name\":\"to\",\"type\":\"bytes32\"},{\"internalType\":\"uint256\",\"name\":\"amountLD\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"minAmountLD\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"extraOptions\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"composeMsg\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"oftCmd\",\"type\":\"bytes\"}],\"internalType\":\"struct SendParam\",\"name\":\"_sendParam\",\"type\":\"tuple\"},{\"internalType\":\"bool\",\"name\":\"_payInLzToken\",\"type\":\"bool\"}],\"name\":\"quoteSend\",\"outputs\":[{\"components\":[{\"internalType\":\"uint256\",\"name\":\"nativeFee\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"lzTokenFee\",\"type\":\"uint256\"}],\"internalType\":\"struct MessagingFee\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"uint32\",\"name\":\"dstEid\",\"type\":\"uint32\"},{\"internalType\":\"bytes32\",\"name\":\"to\",\"type\":\"bytes32\"},{\"internalType\":\"uint256\",\"name\":\"amountLD\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"minAmountLD\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"extraOptions\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"composeMsg\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"oftCmd\",\"type\":\"bytes\"}],\"internalType\":\"struct SendParam\",\"name\":\"_sendParam\",\"type\":\"tuple\"},{\"components\":[{\"internalType\":\"uint256\",\"name\":\"nativeFee\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"lzTokenFee\",\"type\":\"uint256\"}],\"internalType\":\"struct MessagingFee\",\"name\":\"_fee\",\"type\":\"tuple\"},{\"internalType\":\"address\",\"name\":\"_refundAddress\",\"type\":\"address\"}],\"name\":\"send\",\"outputs\":[{\"components\":[{\"internalType\":\"bytes32\",\"name\":\"guid\",\"type\":\"bytes32\"},{\"internalType\":\"uint64\",\"name\":\"nonce\",\"type\":\"uint64\"},{\"components\":[{\"internalType\":\"uint256\",\"name\":\"nativeFee\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"lzTokenFee\",\"type\":\"uint256\"}],\"internalType\":\"struct MessagingFee\",\"name\":\"fee\",\"type\":\"tuple\"}],\"internalType\":\"struct MessagingReceipt\",\"name\":\"\",\"type\":\"tuple\"},{\"components\":[{\"internalType\":\"uint256\",\"name\":\"amountSentLD\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"amountReceivedLD\",\"type\":\"uint256\"}],\"internalType\":\"struct OFTReceipt\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"sharedDecimals\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"token\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"Interface for the OftChain (OFT) token.Does not inherit ERC20 to accommodate usage by OFTAdapter as well.This specific interface ID is '0x02e49c2c'.\",\"kind\":\"dev\",\"methods\":{\"approvalRequired()\":{\"details\":\"Allows things like wallet implementers to determine integration requirements, without understanding the underlying token implementation.\",\"returns\":{\"_0\":\"requiresApproval Needs approval of the underlying token implementation.\"}},\"oftVersion()\":{\"details\":\"interfaceId: This specific interface ID is '0x02e49c2c'.version: Indicates a cross-chain compatible msg encoding with other OFTs.If a new feature is added to the OFT cross-chain msg encoding, the version will be incremented. ie. localOFT version(x,1) CAN send messages to remoteOFT version(x,1)\",\"returns\":{\"interfaceId\":\"The interface ID.\",\"version\":\"The version.\"}},\"quoteOFT((uint32,bytes32,uint256,uint256,bytes,bytes,bytes))\":{\"params\":{\"_sendParam\":\"The parameters for the send operation.\"},\"returns\":{\"_0\":\"limit The OFT limit information.\",\"_2\":\"receipt The OFT receipt information.\",\"oftFeeDetails\":\"The details of OFT fees.\"}},\"quoteSend((uint32,bytes32,uint256,uint256,bytes,bytes,bytes),bool)\":{\"details\":\"MessagingFee: LayerZero msg fee  - nativeFee: The native fee.  - lzTokenFee: The lzToken fee.\",\"params\":{\"_payInLzToken\":\"Flag indicating whether the caller is paying in the LZ token.\",\"_sendParam\":\"The parameters for the send() operation.\"},\"returns\":{\"_0\":\"fee The calculated LayerZero messaging fee from the send() operation.\"}},\"send((uint32,bytes32,uint256,uint256,bytes,bytes,bytes),(uint256,uint256),address)\":{\"details\":\"MessagingReceipt: LayerZero msg receipt  - guid: The unique identifier for the sent message.  - nonce: The nonce of the sent message.  - fee: The LayerZero fee incurred for the message.\",\"params\":{\"_fee\":\"The fee information supplied by the caller.      - nativeFee: The native fee.      - lzTokenFee: The lzToken fee.\",\"_refundAddress\":\"The address to receive any excess funds from fees etc. on the src.\",\"_sendParam\":\"The parameters for the send operation.\"},\"returns\":{\"_0\":\"receipt The LayerZero messaging receipt from the send() operation.\",\"_1\":\"oftReceipt The OFT receipt information.\"}},\"sharedDecimals()\":{\"returns\":{\"_0\":\"sharedDecimals The shared decimals of the OFT.\"}},\"token()\":{\"returns\":{\"_0\":\"token The address of the ERC20 token implementation.\"}}},\"title\":\"IOFT\",\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"approvalRequired()\":{\"notice\":\"Indicates whether the OFT contract requires approval of the 'token()' to send.\"},\"oftVersion()\":{\"notice\":\"Retrieves interfaceID and the version of the OFT.\"},\"quoteOFT((uint32,bytes32,uint256,uint256,bytes,bytes,bytes))\":{\"notice\":\"Provides a quote for OFT-related operations.\"},\"quoteSend((uint32,bytes32,uint256,uint256,bytes,bytes,bytes),bool)\":{\"notice\":\"Provides a quote for the send() operation.\"},\"send((uint32,bytes32,uint256,uint256,bytes,bytes,bytes),(uint256,uint256),address)\":{\"notice\":\"Executes the send() operation.\"},\"sharedDecimals()\":{\"notice\":\"Retrieves the shared decimals of the OFT.\"},\"token()\":{\"notice\":\"Retrieves the address of the token associated with the OFT.\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/external/layerzero/v2/ILayerZeroOFT.sol\":\"ILayerZeroOFT\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"src/interfaces/external/layerzero/v2/ILayerZeroEndpointV2.sol\":{\"keccak256\":\"0xd1b1d757b60ee2e7ac39a04cc0bc3a57dab68b7f0a3a890750590bd51ff377e7\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://54b9c248e75816d26de764aa8bd0333b6c0b5a1c02d6e278a0e6388fd2e09c07\",\"dweb:/ipfs/Qmf3iXi3NfUKgti3hsJ8SrWWBCyNHYCfnzU4xpxNYAuWhE\"]},\"src/interfaces/external/layerzero/v2/ILayerZeroOFT.sol\":{\"keccak256\":\"0x488d0db767e67ce5393a36ccdd9bbafe098a9cd89ae6addf512615db21873d88\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b07c93368a01a68750b840218f220bc426bf5a9bddee6f153b7c6ed149686332\",\"dweb:/ipfs/QmRT9urUGrrq4WhwcGuYB3wni9CUcNoi6apY3TH12gEFx2\"]},\"src/interfaces/external/layerzero/v2/IMessageLibManager.sol\":{\"keccak256\":\"0x94929bdb8d035a15c94d51c16a18903d89fc9291cb6dda23043f6c9864e664f5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c9c509b859cf878757304666a37ff894c3aa414629a19e7ed35ea09139eac3d2\",\"dweb:/ipfs/Qmb8wJfG18Kv24QCVsRQADnMbLPhQ31mVXDk8e2dF3ozJu\"]},\"src/interfaces/external/layerzero/v2/IMessagingChannel.sol\":{\"keccak256\":\"0x77d1b0bd52cb0e3ae72849cd2d916dcde67986a32470c48f18a7c571a91a5d40\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://00b3db0d90d80c40ea84286966dfd4164bd34a984ad73d8280e7b4c2574b15e3\",\"dweb:/ipfs/QmT4tT4wRcq67v7wyh46aukFXPueM3tfmBwoKvRduVxgFh\"]},\"src/interfaces/external/layerzero/v2/IMessagingComposer.sol\":{\"keccak256\":\"0x72eedb733a35770e561727caf76893ef5ca758f35c9ceaada8a4ff3493648b7b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d94ecc1513037856d645960f5a3524089a406585959ac73bfb7c789a31d06d97\",\"dweb:/ipfs/QmewsjB5Fnkx4fu5HcMyd3LhRrNA65zLRG4pjcwQX4eVpC\"]},\"src/interfaces/external/layerzero/v2/IMessagingContext.sol\":{\"keccak256\":\"0xff0c546c2813dae3e440882f46b377375f7461b0714efd80bd3f0c6e5cb8da4e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5173fc9143bea314b159ca5a9adb5626659ef763bc598e27de5fa46efe3291a6\",\"dweb:/ipfs/QmSLFeMFPmVeGxT4sxRPW28ictjAS22M8rLeYRu9TXkA6D\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "InvalidLocalDecimals"}, {"inputs": [{"internalType": "uint256", "name": "amountLD", "type": "uint256"}, {"internalType": "uint256", "name": "minAmountLD", "type": "uint256"}], "type": "error", "name": "SlippageExceeded"}, {"inputs": [{"internalType": "bytes32", "name": "guid", "type": "bytes32", "indexed": true}, {"internalType": "uint32", "name": "srcEid", "type": "uint32", "indexed": false}, {"internalType": "address", "name": "to<PERSON><PERSON><PERSON>", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "amountReceivedLD", "type": "uint256", "indexed": false}], "type": "event", "name": "OFTReceived", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "guid", "type": "bytes32", "indexed": true}, {"internalType": "uint32", "name": "dstEid", "type": "uint32", "indexed": false}, {"internalType": "address", "name": "fromAddress", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "amountSentLD", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "amountReceivedLD", "type": "uint256", "indexed": false}], "type": "event", "name": "OFTSent", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "approvalRequired", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "oftVersion", "outputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}, {"internalType": "uint64", "name": "version", "type": "uint64"}]}, {"inputs": [{"internalType": "struct SendP<PERSON><PERSON>", "name": "_sendParam", "type": "tuple", "components": [{"internalType": "uint32", "name": "dstEid", "type": "uint32"}, {"internalType": "bytes32", "name": "to", "type": "bytes32"}, {"internalType": "uint256", "name": "amountLD", "type": "uint256"}, {"internalType": "uint256", "name": "minAmountLD", "type": "uint256"}, {"internalType": "bytes", "name": "extraOptions", "type": "bytes"}, {"internalType": "bytes", "name": "composeMsg", "type": "bytes"}, {"internalType": "bytes", "name": "oftCmd", "type": "bytes"}]}], "stateMutability": "view", "type": "function", "name": "quoteOFT", "outputs": [{"internalType": "struct OFTLimit", "name": "", "type": "tuple", "components": [{"internalType": "uint256", "name": "minAmountLD", "type": "uint256"}, {"internalType": "uint256", "name": "maxAmountLD", "type": "uint256"}]}, {"internalType": "struct OFTFeeDetail[]", "name": "oftFeeDetails", "type": "tuple[]", "components": [{"internalType": "int256", "name": "feeAmountLD", "type": "int256"}, {"internalType": "string", "name": "description", "type": "string"}]}, {"internalType": "struct OFTReceipt", "name": "", "type": "tuple", "components": [{"internalType": "uint256", "name": "amountSentLD", "type": "uint256"}, {"internalType": "uint256", "name": "amountReceivedLD", "type": "uint256"}]}]}, {"inputs": [{"internalType": "struct SendP<PERSON><PERSON>", "name": "_sendParam", "type": "tuple", "components": [{"internalType": "uint32", "name": "dstEid", "type": "uint32"}, {"internalType": "bytes32", "name": "to", "type": "bytes32"}, {"internalType": "uint256", "name": "amountLD", "type": "uint256"}, {"internalType": "uint256", "name": "minAmountLD", "type": "uint256"}, {"internalType": "bytes", "name": "extraOptions", "type": "bytes"}, {"internalType": "bytes", "name": "composeMsg", "type": "bytes"}, {"internalType": "bytes", "name": "oftCmd", "type": "bytes"}]}, {"internalType": "bool", "name": "_payInLzToken", "type": "bool"}], "stateMutability": "view", "type": "function", "name": "quoteSend", "outputs": [{"internalType": "struct MessagingFee", "name": "", "type": "tuple", "components": [{"internalType": "uint256", "name": "nativeFee", "type": "uint256"}, {"internalType": "uint256", "name": "lzTokenFee", "type": "uint256"}]}]}, {"inputs": [{"internalType": "struct SendP<PERSON><PERSON>", "name": "_sendParam", "type": "tuple", "components": [{"internalType": "uint32", "name": "dstEid", "type": "uint32"}, {"internalType": "bytes32", "name": "to", "type": "bytes32"}, {"internalType": "uint256", "name": "amountLD", "type": "uint256"}, {"internalType": "uint256", "name": "minAmountLD", "type": "uint256"}, {"internalType": "bytes", "name": "extraOptions", "type": "bytes"}, {"internalType": "bytes", "name": "composeMsg", "type": "bytes"}, {"internalType": "bytes", "name": "oftCmd", "type": "bytes"}]}, {"internalType": "struct MessagingFee", "name": "_fee", "type": "tuple", "components": [{"internalType": "uint256", "name": "nativeFee", "type": "uint256"}, {"internalType": "uint256", "name": "lzTokenFee", "type": "uint256"}]}, {"internalType": "address", "name": "_refundAddress", "type": "address"}], "stateMutability": "payable", "type": "function", "name": "send", "outputs": [{"internalType": "struct MessagingReceipt", "name": "", "type": "tuple", "components": [{"internalType": "bytes32", "name": "guid", "type": "bytes32"}, {"internalType": "uint64", "name": "nonce", "type": "uint64"}, {"internalType": "struct MessagingFee", "name": "fee", "type": "tuple", "components": [{"internalType": "uint256", "name": "nativeFee", "type": "uint256"}, {"internalType": "uint256", "name": "lzTokenFee", "type": "uint256"}]}]}, {"internalType": "struct OFTReceipt", "name": "", "type": "tuple", "components": [{"internalType": "uint256", "name": "amountSentLD", "type": "uint256"}, {"internalType": "uint256", "name": "amountReceivedLD", "type": "uint256"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "sharedDecimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "token", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}], "devdoc": {"kind": "dev", "methods": {"approvalRequired()": {"details": "Allows things like wallet implementers to determine integration requirements, without understanding the underlying token implementation.", "returns": {"_0": "requiresApproval Needs approval of the underlying token implementation."}}, "oftVersion()": {"details": "interfaceId: This specific interface ID is '0x02e49c2c'.version: Indicates a cross-chain compatible msg encoding with other OFTs.If a new feature is added to the OFT cross-chain msg encoding, the version will be incremented. ie. localOFT version(x,1) CAN send messages to remoteOFT version(x,1)", "returns": {"interfaceId": "The interface ID.", "version": "The version."}}, "quoteOFT((uint32,bytes32,uint256,uint256,bytes,bytes,bytes))": {"params": {"_sendParam": "The parameters for the send operation."}, "returns": {"_0": "limit The OFT limit information.", "_2": "receipt The OFT receipt information.", "oftFeeDetails": "The details of OFT fees."}}, "quoteSend((uint32,bytes32,uint256,uint256,bytes,bytes,bytes),bool)": {"details": "MessagingFee: LayerZero msg fee  - nativeFee: The native fee.  - lzTokenFee: The lzToken fee.", "params": {"_payInLzToken": "Flag indicating whether the caller is paying in the LZ token.", "_sendParam": "The parameters for the send() operation."}, "returns": {"_0": "fee The calculated LayerZero messaging fee from the send() operation."}}, "send((uint32,bytes32,uint256,uint256,bytes,bytes,bytes),(uint256,uint256),address)": {"details": "MessagingReceipt: LayerZero msg receipt  - guid: The unique identifier for the sent message.  - nonce: The nonce of the sent message.  - fee: The LayerZero fee incurred for the message.", "params": {"_fee": "The fee information supplied by the caller.      - nativeFee: The native fee.      - lzTokenFee: The lzToken fee.", "_refundAddress": "The address to receive any excess funds from fees etc. on the src.", "_sendParam": "The parameters for the send operation."}, "returns": {"_0": "receipt The LayerZero messaging receipt from the send() operation.", "_1": "oftReceipt The OFT receipt information."}}, "sharedDecimals()": {"returns": {"_0": "sharedDecimals The shared decimals of the OFT."}}, "token()": {"returns": {"_0": "token The address of the ERC20 token implementation."}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"approvalRequired()": {"notice": "Indicates whether the OFT contract requires approval of the 'token()' to send."}, "oftVersion()": {"notice": "Retrieves interfaceID and the version of the OFT."}, "quoteOFT((uint32,bytes32,uint256,uint256,bytes,bytes,bytes))": {"notice": "Provides a quote for OFT-related operations."}, "quoteSend((uint32,bytes32,uint256,uint256,bytes,bytes,bytes),bool)": {"notice": "Provides a quote for the send() operation."}, "send((uint32,bytes32,uint256,uint256,bytes,bytes,bytes),(uint256,uint256),address)": {"notice": "Executes the send() operation."}, "sharedDecimals()": {"notice": "Retrieves the shared decimals of the OFT."}, "token()": {"notice": "Retrieves the address of the token associated with the OFT."}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/interfaces/external/layerzero/v2/ILayerZeroOFT.sol": "ILayerZeroOFT"}, "evmVersion": "london", "libraries": {}}, "sources": {"src/interfaces/external/layerzero/v2/ILayerZeroEndpointV2.sol": {"keccak256": "0xd1b1d757b60ee2e7ac39a04cc0bc3a57dab68b7f0a3a890750590bd51ff377e7", "urls": ["bzz-raw://54b9c248e75816d26de764aa8bd0333b6c0b5a1c02d6e278a0e6388fd2e09c07", "dweb:/ipfs/Qmf3iXi3NfUKgti3hsJ8SrWWBCyNHYCfnzU4xpxNYAuWhE"], "license": "MIT"}, "src/interfaces/external/layerzero/v2/ILayerZeroOFT.sol": {"keccak256": "0x488d0db767e67ce5393a36ccdd9bbafe098a9cd89ae6addf512615db21873d88", "urls": ["bzz-raw://b07c93368a01a68750b840218f220bc426bf5a9bddee6f153b7c6ed149686332", "dweb:/ipfs/QmRT9urUGrrq4WhwcGuYB3wni9CUcNoi6apY3TH12gEFx2"], "license": "MIT"}, "src/interfaces/external/layerzero/v2/IMessageLibManager.sol": {"keccak256": "0x94929bdb8d035a15c94d51c16a18903d89fc9291cb6dda23043f6c9864e664f5", "urls": ["bzz-raw://c9c509b859cf878757304666a37ff894c3aa414629a19e7ed35ea09139eac3d2", "dweb:/ipfs/Qmb8wJfG18Kv24QCVsRQADnMbLPhQ31mVXDk8e2dF3ozJu"], "license": "MIT"}, "src/interfaces/external/layerzero/v2/IMessagingChannel.sol": {"keccak256": "0x77d1b0bd52cb0e3ae72849cd2d916dcde67986a32470c48f18a7c571a91a5d40", "urls": ["bzz-raw://00b3db0d90d80c40ea84286966dfd4164bd34a984ad73d8280e7b4c2574b15e3", "dweb:/ipfs/QmT4tT4wRcq67v7wyh46aukFXPueM3tfmBwoKvRduVxgFh"], "license": "MIT"}, "src/interfaces/external/layerzero/v2/IMessagingComposer.sol": {"keccak256": "0x72eedb733a35770e561727caf76893ef5ca758f35c9ceaada8a4ff3493648b7b", "urls": ["bzz-raw://d94ecc1513037856d645960f5a3524089a406585959ac73bfb7c789a31d06d97", "dweb:/ipfs/QmewsjB5Fnkx4fu5HcMyd3LhRrNA65zLRG4pjcwQX4eVpC"], "license": "MIT"}, "src/interfaces/external/layerzero/v2/IMessagingContext.sol": {"keccak256": "0xff0c546c2813dae3e440882f46b377375f7461b0714efd80bd3f0c6e5cb8da4e", "urls": ["bzz-raw://5173fc9143bea314b159ca5a9adb5626659ef763bc598e27de5fa46efe3291a6", "dweb:/ipfs/QmSLFeMFPmVeGxT4sxRPW28ictjAS22M8rLeYRu9TXkA6D"], "license": "MIT"}}, "version": 1}, "id": 158}