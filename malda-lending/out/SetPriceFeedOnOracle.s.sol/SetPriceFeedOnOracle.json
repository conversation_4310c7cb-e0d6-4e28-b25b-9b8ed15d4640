{"abi": [{"type": "function", "name": "IS_SCRIPT", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "run", "inputs": [{"name": "symbol", "type": "string", "internalType": "string"}, {"name": "priceFeed", "type": "address", "internalType": "address"}, {"name": "toSymbol", "type": "string", "internalType": "string"}, {"name": "underlyingDecimals", "type": "uint8", "internalType": "uint8"}], "outputs": [], "stateMutability": "nonpayable"}], "bytecode": {"object": "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", "sourceMap": "561:854:68:-:0;;;3126:44:3;;;-1:-1:-1;;849:28:1;;;;;561:854:68;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "561:854:68:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;607:806;;;;;;:::i;:::-;;:::i;:::-;;849:28:1;;;;;;;;;;;;;;;2022:14:242;;2015:22;1997:41;;1985:2;1970:18;849:28:1;;;;;;;607:806:68;742:25;;-1:-1:-1;;;742:25:68;;2251:2:242;742:25:68;;;2233:21:242;2290:2;2270:18;;;2263:30;-1:-1:-1;;;2309:18:242;;;2302:41;728:11:68;;336:42:0;;742:10:68;;2360:18:242;;742:25:68;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;794:23;;-1:-1:-1;;;794:23:68;;2780:2:242;794:23:68;;;2762:21:242;2819:1;2799:18;;;2792:29;-1:-1:-1;;;2837:18:242;;;2830:36;728:39:68;;-1:-1:-1;777:14:68;;336:42:0;;794:13:68;;2883:18:242;;794:23:68;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;872:159;;;;;;;;-1:-1:-1;;;;;872:159:68;;;;;;;;;;;;;;;;;1042:22;;-1:-1:-1;;;1042:22:68;;;;;3314:25:242;;;777:40:68;;-1:-1:-1;872:159:68;336:42:0;;1042:17:68;;3287:18:242;;1042:22:68;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;1074:52:68;;-1:-1:-1;;;1074:52:68;;-1:-1:-1;;;;;1074:36:68;;;-1:-1:-1;1074:36:68;;-1:-1:-1;1074:52:68;;1111:6;;1119;;1074:52;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;336:42:0;-1:-1:-1;;;;;1136:16:68;;:18;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1165:66;;;;;;;;;;;;;;;;;;1216:6;1224;1165:11;:66::i;:::-;1241:43;;;;;;;;;;;;;;-1:-1:-1;;;1241:43:68;;;1274:9;1241:11;:43::i;:::-;1294:41;;;;;;;;;;;;;;-1:-1:-1;;;1294:41:68;;;1326:8;1294:11;:41::i;:::-;1345:61;;;;;;;;;;;;;;;;;;1387:18;1345:61;;:11;:61::i;:::-;718:695;;;607:806;;;;:::o;12983:174:16:-;13068:82;13138:2;13142;13146;13084:65;;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;13084:65:16;;;;;;;;;;;;;;-1:-1:-1;;;;;13084:65:16;-1:-1:-1;;;13084:65:16;;;13068:15;:82::i;:::-;12983:174;;;:::o;7740:145::-;7807:71;7870:2;7874;7823:54;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;7823:54:16;;;;;;;;;;;;;;-1:-1:-1;;;;;7823:54:16;-1:-1:-1;;;7823:54:16;;;7807:15;:71::i;:::-;7740:145;;:::o;7439:150::-;7512:70;7574:2;7578;7528:53;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;7528:53:16;;;;;;;;;;;;;;-1:-1:-1;;;;;7528:53:16;-1:-1:-1;;;7528:53:16;;;7512:15;:70::i;7139:145::-;7206:71;7269:2;7273;7222:54;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;7222:54:16;;;;;;;;;;;;;;-1:-1:-1;;;;;7222:54:16;-1:-1:-1;;;7222:54:16;;;851:129;922:51;965:7;934:29;922:51::i;:::-;851:129;:::o;180:463::-;265:22;131:42;265:40;;594:1;571;541:7;535:14;510:2;501:7;497:16;461:14;434:5;402:211;381:246;367:270;180:463;:::o;14:127:242:-;75:10;70:3;66:20;63:1;56:31;106:4;103:1;96:15;130:4;127:1;120:15;146:726;189:5;242:3;235:4;227:6;223:17;219:27;209:55;;260:1;257;250:12;209:55;300:6;287:20;330:18;322:6;319:30;316:56;;;352:18;;:::i;:::-;401:2;395:9;493:2;455:17;;-1:-1:-1;;451:31:242;;;484:2;447:40;443:54;431:67;;528:18;513:34;;549:22;;;510:62;507:88;;;575:18;;:::i;:::-;611:2;604:22;635;;;676:19;;;697:4;672:30;669:39;-1:-1:-1;666:59:242;;;721:1;718;711:12;666:59;785:6;778:4;770:6;766:17;759:4;751:6;747:17;734:58;840:1;812:19;;;833:4;808:30;801:41;;;;816:6;146:726;-1:-1:-1;;;146:726:242:o;877:131::-;-1:-1:-1;;;;;952:31:242;;942:42;;932:70;;998:1;995;988:12;1013:839;1117:6;1125;1133;1141;1194:3;1182:9;1173:7;1169:23;1165:33;1162:53;;;1211:1;1208;1201:12;1162:53;1251:9;1238:23;1284:18;1276:6;1273:30;1270:50;;;1316:1;1313;1306:12;1270:50;1339;1381:7;1372:6;1361:9;1357:22;1339:50;:::i;:::-;1329:60;;;1439:2;1428:9;1424:18;1411:32;1452:31;1477:5;1452:31;:::i;:::-;1502:5;-1:-1:-1;1560:2:242;1545:18;;1532:32;1589:18;1576:32;;1573:52;;;1621:1;1618;1611:12;1573:52;1644;1688:7;1677:8;1666:9;1662:24;1644:52;:::i;:::-;1634:62;;;1748:2;1737:9;1733:18;1720:32;1796:4;1787:7;1783:18;1774:7;1771:31;1761:59;;1816:1;1813;1806:12;1761:59;1013:839;;;;-1:-1:-1;1013:839:242;;-1:-1:-1;;1013:839:242:o;2389:184::-;2459:6;2512:2;2500:9;2491:7;2487:23;2483:32;2480:52;;;2528:1;2525;2518:12;2480:52;-1:-1:-1;2551:16:242;;2389:184;-1:-1:-1;2389:184:242:o;2912:251::-;2982:6;3035:2;3023:9;3014:7;3010:23;3006:32;3003:52;;;3051:1;3048;3041:12;3003:52;3083:9;3077:16;3102:31;3127:5;3102:31;:::i;:::-;3152:5;2912:251;-1:-1:-1;;;2912:251:242:o;3350:400::-;3392:3;3430:5;3424:12;3457:6;3452:3;3445:19;3482:1;3492:139;3506:6;3503:1;3500:13;3492:139;;;3614:4;3599:13;;;3595:24;;3589:31;3569:11;;;3565:22;;3558:63;3521:12;3492:139;;;3496:3;3676:1;3669:4;3660:6;3655:3;3651:16;3647:27;3640:38;3739:4;3732:2;3728:7;3723:2;3715:6;3711:15;3707:29;3702:3;3698:39;3694:50;3687:57;;;3350:400;;;;:::o;3755:678::-;3992:2;3981:9;3974:21;3955:4;4018:45;4059:2;4048:9;4044:18;4036:6;4018:45;:::i;:::-;4111:9;4103:6;4099:22;4094:2;4083:9;4079:18;4072:50;4182:1;4178;4173:3;4169:11;4165:19;4156:6;4150:13;4146:39;4138:6;4131:55;4233:2;4225:6;4221:15;4215:22;4270:4;4265:2;4257:6;4253:15;4246:29;4298:50;4342:4;4334:6;4330:17;4316:12;4298:50;:::i;:::-;4284:64;;4399:2;4391:6;4387:15;4381:22;4376:2;4368:6;4364:15;4357:47;4421:6;4413:14;;;;3755:678;;;;;:::o;4438:480::-;4663:2;4652:9;4645:21;4626:4;4689:45;4730:2;4719:9;4715:18;4707:6;4689:45;:::i;:::-;4782:9;4774:6;4770:22;4765:2;4754:9;4750:18;4743:50;4810:33;4836:6;4828;4810:33;:::i;:::-;4802:41;;;4908:1;4904;4899:3;4895:11;4891:19;4883:6;4879:32;4874:2;4863:9;4859:18;4852:60;4438:480;;;;;;:::o;4923:317::-;5100:2;5089:9;5082:21;5063:4;5120:45;5161:2;5150:9;5146:18;5138:6;5120:45;:::i;:::-;5112:53;;5230:1;5226;5221:3;5217:11;5213:19;5205:6;5201:32;5196:2;5185:9;5181:18;5174:60;4923:317;;;;;:::o;5245:383::-;5442:2;5431:9;5424:21;5405:4;5468:45;5509:2;5498:9;5494:18;5486:6;5468:45;:::i;:::-;5561:9;5553:6;5549:22;5544:2;5533:9;5529:18;5522:50;5589:33;5615:6;5607;5589:33;:::i;:::-;5581:41;5245:383;-1:-1:-1;;;;;5245:383:242:o;5633:291::-;5810:2;5799:9;5792:21;5773:4;5830:45;5871:2;5860:9;5856:18;5848:6;5830:45;:::i;:::-;5822:53;;5911:6;5906:2;5895:9;5891:18;5884:34;5633:291;;;;;:::o", "linkReferences": {}}, "methodIdentifiers": {"IS_SCRIPT()": "f8ccbf47", "run(string,address,string,uint8)": "33b2aa2b"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"IS_SCRIPT\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"},{\"internalType\":\"address\",\"name\":\"priceFeed\",\"type\":\"address\"},{\"internalType\":\"string\",\"name\":\"toSymbol\",\"type\":\"string\"},{\"internalType\":\"uint8\",\"name\":\"underlyingDecimals\",\"type\":\"uint8\"}],\"name\":\"run\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"notice\":\"forge script SetPriceFeedOnOracle  \\\\     --slow \\\\     --verify \\\\     --verifier-url <url> \\\\     --rpc-url <url> \\\\     --etherscan-api-key <key> \\\\     --sig \\\"run(string,address,string,uint8)\\\" \\\"WETHUSD\\\" \\\"******************************************\\\" \\\"USD\\\" 18 \\\\     --broadcast\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"script/configuration/SetPriceFeedOnOracle.s.sol\":\"SetPriceFeedOnOracle\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"lib/forge-std/src/Base.sol\":{\"keccak256\":\"0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d\",\"dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z\"]},\"lib/forge-std/src/Script.sol\":{\"keccak256\":\"0xc942e27c7baae499beb01afbbae99f24d42af9a6e4aae675bc6901b704aa8e9b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0456008adf68947247f358b62863af4a8e349549d2260f2ff9569ff0e3cf5c98\",\"dweb:/ipfs/QmdviSUj2i7o3TPN5vd2xocqGMFVqjUzaiJTZRYyPxyHPx\"]},\"lib/forge-std/src/StdChains.sol\":{\"keccak256\":\"0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351\",\"dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm\"]},\"lib/forge-std/src/StdCheats.sol\":{\"keccak256\":\"0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41\",\"dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK\"]},\"lib/forge-std/src/StdConstants.sol\":{\"keccak256\":\"0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc\",\"dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r\"]},\"lib/forge-std/src/StdJson.sol\":{\"keccak256\":\"0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974\",\"dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3\"]},\"lib/forge-std/src/StdMath.sol\":{\"keccak256\":\"0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92\",\"dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC\"]},\"lib/forge-std/src/StdStorage.sol\":{\"keccak256\":\"0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57\",\"dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ\"]},\"lib/forge-std/src/StdStyle.sol\":{\"keccak256\":\"0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8\",\"dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK\"]},\"lib/forge-std/src/StdUtils.sol\":{\"keccak256\":\"0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138\",\"dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776\"]},\"lib/forge-std/src/Vm.sol\":{\"keccak256\":\"0xaae88142a348064677cf81169ea0ca706be3e99e2a0f13a01878ff8d52cf6b25\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://da4fb0ed1b31381c7e60ee386acc72796e8a8325c83739ec9a3678e2f841073c\",\"dweb:/ipfs/Qmb4MU6FpAi8Wt5kUQ3wa7ooHnfMVbdd9zuvnqbTehLmxJ\"]},\"lib/forge-std/src/console.sol\":{\"keccak256\":\"0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57\",\"dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP\"]},\"lib/forge-std/src/console2.sol\":{\"keccak256\":\"0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d\",\"dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ\"]},\"lib/forge-std/src/interfaces/IMulticall3.sol\":{\"keccak256\":\"0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0\",\"dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2\"]},\"lib/forge-std/src/safeconsole.sol\":{\"keccak256\":\"0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab\",\"dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3\"]},\"script/configuration/SetPriceFeedOnOracle.s.sol\":{\"keccak256\":\"0xefde9aebeefb8cbcde555f7db7a82fb1ad5606d0b2f6ba235cef4687c627bc64\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://e188bfd7a63498a4e1bc74c849d998be3ad04ca566c709194be4520d1615ed90\",\"dweb:/ipfs/QmYZ6oZit2U5UxfbEsFo4Vw1EWoEhqAaMhDKkZKuUdTBNV\"]},\"src/interfaces/IDefaultAdapter.sol\":{\"keccak256\":\"0xbf7e882eeb81776c7be55110bb171c65d166bafeb71d828c085b139bed5735c8\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://7e139fb3ddd0623189493679e73fd42c4e505531502d55e1e699789fa3c1451a\",\"dweb:/ipfs/Qma3XsUVPffiGXZ7epTqMyNJKuh87xrFhqCTwQXznEccU6\"]},\"src/interfaces/IOracleOperator.sol\":{\"keccak256\":\"0xcc2848c75dd5e1531c2b958746cdd76a3b2b329be98fdca4311219a12a6b409e\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://337bdabf03ac279d95ba96aca60105932c7af5c15fdb4b21ab1c369509310fbc\",\"dweb:/ipfs/QmNYrNYNUwWWSrPwHdarhxt1ShdH1dy8B94M49TZ7XvmK1\"]},\"src/interfaces/IRoles.sol\":{\"keccak256\":\"0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c\",\"dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj\"]},\"src/interfaces/ImToken.sol\":{\"keccak256\":\"0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36\",\"dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL\"]},\"src/oracles/MixedPriceOracleV3.sol\":{\"keccak256\":\"0x2a12b509ba518e116b6c1136575e04a2c2bfdd5402730f6746cfabbbe5e6549e\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://2417f80a6dfc814e73df5362226cd89db15ea2314effd80dd16bcb87595ea755\",\"dweb:/ipfs/QmcRPazSFBvLKUqo3SCYhNkzuDuQZHWcWjwTzxV88E6own\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "view", "type": "function", "name": "IS_SCRIPT", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "address", "name": "priceFeed", "type": "address"}, {"internalType": "string", "name": "toSymbol", "type": "string"}, {"internalType": "uint8", "name": "underlyingDecimals", "type": "uint8"}], "stateMutability": "nonpayable", "type": "function", "name": "run"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"script/configuration/SetPriceFeedOnOracle.s.sol": "SetPriceFeedOnOracle"}, "evmVersion": "london", "libraries": {}}, "sources": {"lib/forge-std/src/Base.sol": {"keccak256": "0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd", "urls": ["bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d", "dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z"], "license": "MIT"}, "lib/forge-std/src/Script.sol": {"keccak256": "0xc942e27c7baae499beb01afbbae99f24d42af9a6e4aae675bc6901b704aa8e9b", "urls": ["bzz-raw://0456008adf68947247f358b62863af4a8e349549d2260f2ff9569ff0e3cf5c98", "dweb:/ipfs/QmdviSUj2i7o3TPN5vd2xocqGMFVqjUzaiJTZRYyPxyHPx"], "license": "MIT"}, "lib/forge-std/src/StdChains.sol": {"keccak256": "0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e", "urls": ["bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351", "dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm"], "license": "MIT"}, "lib/forge-std/src/StdCheats.sol": {"keccak256": "0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746", "urls": ["bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41", "dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK"], "license": "MIT"}, "lib/forge-std/src/StdConstants.sol": {"keccak256": "0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534", "urls": ["bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc", "dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r"], "license": "MIT"}, "lib/forge-std/src/StdJson.sol": {"keccak256": "0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500", "urls": ["bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974", "dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3"], "license": "MIT"}, "lib/forge-std/src/StdMath.sol": {"keccak256": "0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2", "urls": ["bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92", "dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC"], "license": "MIT"}, "lib/forge-std/src/StdStorage.sol": {"keccak256": "0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc", "urls": ["bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57", "dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ"], "license": "MIT"}, "lib/forge-std/src/StdStyle.sol": {"keccak256": "0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d", "urls": ["bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8", "dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK"], "license": "MIT"}, "lib/forge-std/src/StdUtils.sol": {"keccak256": "0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737", "urls": ["bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138", "dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776"], "license": "MIT"}, "lib/forge-std/src/Vm.sol": {"keccak256": "0xaae88142a348064677cf81169ea0ca706be3e99e2a0f13a01878ff8d52cf6b25", "urls": ["bzz-raw://da4fb0ed1b31381c7e60ee386acc72796e8a8325c83739ec9a3678e2f841073c", "dweb:/ipfs/Qmb4MU6FpAi8Wt5kUQ3wa7ooHnfMVbdd9zuvnqbTehLmxJ"], "license": "MIT OR Apache-2.0"}, "lib/forge-std/src/console.sol": {"keccak256": "0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5", "urls": ["bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57", "dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP"], "license": "MIT"}, "lib/forge-std/src/console2.sol": {"keccak256": "0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f", "urls": ["bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d", "dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ"], "license": "MIT"}, "lib/forge-std/src/interfaces/IMulticall3.sol": {"keccak256": "0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a", "urls": ["bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0", "dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2"], "license": "MIT"}, "lib/forge-std/src/safeconsole.sol": {"keccak256": "0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11", "urls": ["bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab", "dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3"], "license": "MIT"}, "script/configuration/SetPriceFeedOnOracle.s.sol": {"keccak256": "0xefde9aebeefb8cbcde555f7db7a82fb1ad5606d0b2f6ba235cef4687c627bc64", "urls": ["bzz-raw://e188bfd7a63498a4e1bc74c849d998be3ad04ca566c709194be4520d1615ed90", "dweb:/ipfs/QmYZ6oZit2U5UxfbEsFo4Vw1EWoEhqAaMhDKkZKuUdTBNV"], "license": "BSL-1.1"}, "src/interfaces/IDefaultAdapter.sol": {"keccak256": "0xbf7e882eeb81776c7be55110bb171c65d166bafeb71d828c085b139bed5735c8", "urls": ["bzz-raw://7e139fb3ddd0623189493679e73fd42c4e505531502d55e1e699789fa3c1451a", "dweb:/ipfs/Qma3XsUVPffiGXZ7epTqMyNJKuh87xrFhqCTwQXznEccU6"], "license": "BSL-1.1"}, "src/interfaces/IOracleOperator.sol": {"keccak256": "0xcc2848c75dd5e1531c2b958746cdd76a3b2b329be98fdca4311219a12a6b409e", "urls": ["bzz-raw://337bdabf03ac279d95ba96aca60105932c7af5c15fdb4b21ab1c369509310fbc", "dweb:/ipfs/QmNYrNYNUwWWSrPwHdarhxt1ShdH1dy8B94M49TZ7XvmK1"], "license": "BSL-1.1"}, "src/interfaces/IRoles.sol": {"keccak256": "0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365", "urls": ["bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c", "dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj"], "license": "BSL-1.1"}, "src/interfaces/ImToken.sol": {"keccak256": "0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f", "urls": ["bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36", "dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL"], "license": "BSL-1.1"}, "src/oracles/MixedPriceOracleV3.sol": {"keccak256": "0x2a12b509ba518e116b6c1136575e04a2c2bfdd5402730f6746cfabbbe5e6549e", "urls": ["bzz-raw://2417f80a6dfc814e73df5362226cd89db15ea2314effd80dd16bcb87595ea755", "dweb:/ipfs/QmcRPazSFBvLKUqo3SCYhNkzuDuQZHWcWjwTzxV88E6own"], "license": "BSL-1.1"}}, "version": 1}, "id": 68}