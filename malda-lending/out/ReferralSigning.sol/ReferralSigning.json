{"abi": [{"type": "function", "name": "claimReferral", "inputs": [{"name": "signature", "type": "bytes", "internalType": "bytes"}, {"name": "referrer", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "isUserReferred", "inputs": [{"name": "user", "type": "address", "internalType": "address"}], "outputs": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "nonces", "inputs": [{"name": "user", "type": "address", "internalType": "address"}], "outputs": [{"name": "nonce", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "referralRegistry", "inputs": [{"name": "<PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "users", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "referralsForUserRegistry", "inputs": [{"name": "user", "type": "address", "internalType": "address"}], "outputs": [{"name": "<PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "referredByRegistry", "inputs": [{"name": "<PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}, {"name": "user", "type": "address", "internalType": "address"}], "outputs": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "totalReferred", "inputs": [{"name": "<PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}], "outputs": [{"name": "total", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "event", "name": "ReferralClaimed", "inputs": [{"name": "referred", "type": "address", "indexed": true, "internalType": "address"}, {"name": "referrer", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "ReferralRejected", "inputs": [{"name": "referred", "type": "address", "indexed": true, "internalType": "address"}, {"name": "referrer", "type": "address", "indexed": true, "internalType": "address"}, {"name": "reason", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "error", "name": "ECDSAInvalidSignature", "inputs": []}, {"type": "error", "name": "ECDSAInvalidSignatureLength", "inputs": [{"name": "length", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ECDSAInvalidSignatureS", "inputs": [{"name": "s", "type": "bytes32", "internalType": "bytes32"}]}, {"type": "error", "name": "ReferralSigning_ContractReferrerNotAllowed", "inputs": []}, {"type": "error", "name": "ReferralSigning_InvalidSignature", "inputs": []}, {"type": "error", "name": "ReferralSigning_SameUser", "inputs": []}, {"type": "error", "name": "ReferralSigning_UserAlreadyReferred", "inputs": []}], "bytecode": {"object": "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", "sourceMap": "1027:2577:192:-:0;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "1027:2577:192:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1389:65;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;529:25:242;;;517:2;502:18;1389:65:192;;;;;;;;1313:70;;;;;;:::i;:::-;;:::i;:::-;;;-1:-1:-1;;;;;988:32:242;;;970:51;;958:2;943:18;1313:70:192;824:203:242;1460:63:192;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;1197:14:242;;1190:22;1172:41;;1160:2;1145:18;1460:63:192;1032:187:242;1529:52:192;;;;;;:::i;:::-;;;;;;;;;;;;;;1232:75;;;;;;:::i;:::-;;;;;;;;;;;;-1:-1:-1;;;;;1232:75:192;;;2348:1254;;;;;;:::i;:::-;;:::i;:::-;;1128:98;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;1313:70;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;1313:70:192;;-1:-1:-1;1313:70:192;;-1:-1:-1;1313:70:192:o;2348:1254::-;2125:10;2110:26;;;;:14;:26;;;;;;;;2106:180;;;2157:60;;2186:10;;;;-1:-1:-1;;;;;;;;;;;2157:60:192;;;2362:2:242;2344:21;;;2401:2;2381:18;;;2374:30;-1:-1:-1;;;2435:2:242;2420:18;;2413:46;2491:2;2476:18;;2160:340;2157:60:192;;;;;;;;2238:37;;-1:-1:-1;;;2238:37:192;;;;;;;;;;;2106:180;-1:-1:-1;;;;;2450:22:192;::::1;:10;:22:::0;2446:172:::1;;2522:8;-1:-1:-1::0;;;;;2493:67:192::1;2510:10;-1:-1:-1::0;;;;;2493:67:192::1;-1:-1:-1::0;;;;;;;;;;;2493:67:192::1;;;;2707:2:242::0;2689:21;;;2746:2;2726:18;;;2719:30;2785:27;2780:2;2765:18;;2758:55;2845:2;2830:18;;2505:349;2493:67:192::1;;;;;;;;2581:26;;-1:-1:-1::0;;;2581:26:192::1;;;;;;;;;;;2446:172;-1:-1:-1::0;;;;;2632:20:192;::::1;;:25:::0;2628:198:::1;;2707:8;-1:-1:-1::0;;;;;2678:72:192::1;2695:10;-1:-1:-1::0;;;;;2678:72:192::1;-1:-1:-1::0;;;;;;;;;;;2678:72:192::1;;;;3061:2:242::0;3043:21;;;3100:2;3080:18;;;3073:30;3139:32;3134:2;3119:18;;3112:60;3204:2;3189:18;;2859:354;2678:72:192::1;;;;;;;;2771:44;;-1:-1:-1::0;;;2771:44:192::1;;;;;;;;;;;2628:198;2885:10;2836:19;2907:18:::0;;;:6:::1;:18;::::0;;;;;;;;2868:58;;-1:-1:-1;;3423:2:242;3419:15;;;3415:53;;2868:58:192;;::::1;3403:66:242::0;;;;3503:15;;;;3499:53;;;3485:12;;;3478:75;3569:12;;;3562:28;2836:19:192;3606:12:242;;2868:58:192::1;;;;;;;;;;;;2858:69;;;;;;2836:91;;2937:28;2968:52;3008:11;1403:34:52::0;1298:14;1390:48;;;1499:4;1492:25;;;;1597:4;1581:21;;;1222:460;2968:52:192::1;2937:83;;3031:14;3048:39;3077:9;;3048:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;::::0;::::1;::::0;;;;-1:-1:-1;3048:20:192;;:39;-1:-1:-1;;3048:28:192::1;:39:::0;-1:-1:-1;3048:39:192:i:1;:::-;3031:56:::0;-1:-1:-1;;;;;;3101:20:192;::::1;3111:10;3101:20;3097:170;;3171:8;-1:-1:-1::0;;;;;3142:59:192::1;3159:10;-1:-1:-1::0;;;;;3142:59:192::1;-1:-1:-1::0;;;;;;;;;;;3142:59:192::1;;;;3831:2:242::0;3813:21;;;3870:2;3850:18;;;3843:30;-1:-1:-1;;;3904:2:242;3889:18;;3882:47;3961:2;3946:18;;3629:341;3142:59:192::1;;;;;;;;3222:34;;-1:-1:-1::0;;;3222:34:192::1;;;;;;;;;;;3097:170;-1:-1:-1::0;;;;;3277:28:192;::::1;:18;:28:::0;;;::::1;::::0;;;;;;;3306:10:::1;3277:40:::0;;;;;;;;;:47;;-1:-1:-1;;3277:47:192::1;3320:4;3277:47:::0;;::::1;::::0;;;3334:36;;;;;;:47;;-1:-1:-1;;;;;;3334:47:192;;::::1;::::0;::::1;::::0;;;3391:26;;;:16:::1;:26:::0;;;;;:43;;;;::::1;::::0;;;;;;;;;::::1;::::0;;;;::::1;::::0;;::::1;::::0;;3444:23;;;:13:::1;:23:::0;;;;;:25;;;::::1;::::0;::::1;:::i;:::-;::::0;;;-1:-1:-1;;3494:10:192::1;3479:26;::::0;;;:14:::1;:26;::::0;;;;;;;:33;;-1:-1:-1;;3479:33:192::1;3508:4;3479:33;::::0;;3522:6:::1;:18:::0;;;;;:20;;;::::1;::::0;::::1;:::i;:::-;::::0;;;-1:-1:-1;;3558:37:192::1;::::0;-1:-1:-1;;;;;3558:37:192;::::1;::::0;3574:10:::1;::::0;3558:37:::1;::::0;;;::::1;2436:1166;;;2348:1254:::0;;;:::o;3702:255:51:-;3780:7;3800:17;3819:18;3839:16;3859:27;3870:4;3876:9;3859:10;:27::i;:::-;3799:87;;;;;;3896:28;3908:5;3915:8;3896:11;:28::i;:::-;-1:-1:-1;3941:9:51;;3702:255;-1:-1:-1;;;;3702:255:51:o;2129:766::-;2210:7;2219:12;2233:7;2256:9;:16;2276:2;2256:22;2252:637;;2592:4;2577:20;;2571:27;2641:4;2626:20;;2620:27;2698:4;2683:20;;2677:27;2294:9;2669:36;2739:25;2750:4;2669:36;2571:27;2620;2739:10;:25::i;:::-;2732:32;;;;;;;;;;;2252:637;-1:-1:-1;;2860:16:51;;2811:1;;-1:-1:-1;2815:35:51;;2252:637;2129:766;;;;;:::o;7196:532::-;7291:20;7282:5;:29;;;;;;;;:::i;:::-;;7278:444;;7196:532;;:::o;7278:444::-;7387:29;7378:5;:38;;;;;;;;:::i;:::-;;7374:348;;7439:23;;-1:-1:-1;;;7439:23:51;;;;;;;;;;;7374:348;7492:35;7483:5;:44;;;;;;;;:::i;:::-;;7479:243;;7550:46;;-1:-1:-1;;;7550:46:51;;;;;529:25:242;;;502:18;;7550:46:51;;;;;;;;7479:243;7626:30;7617:5;:39;;;;;;;;:::i;:::-;;7613:109;;7679:32;;-1:-1:-1;;;7679:32:51;;;;;529:25:242;;;502:18;;7679:32:51;383:177:242;7613:109:51;7196:532;;:::o;5140:1530::-;5266:7;;;6199:66;6186:79;;6182:164;;;-1:-1:-1;6297:1:51;;-1:-1:-1;6301:30:51;;-1:-1:-1;6333:1:51;6281:54;;6182:164;6457:24;;;6440:14;6457:24;;;;;;;;;4753:25:242;;;4826:4;4814:17;;4794:18;;;4787:45;;;;4848:18;;;4841:34;;;4891:18;;;4884:34;;;6457:24:51;;4725:19:242;;6457:24:51;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;6457:24:51;;-1:-1:-1;;6457:24:51;;;-1:-1:-1;;;;;;;6495:20:51;;6491:113;;-1:-1:-1;6547:1:51;;-1:-1:-1;6551:29:51;;-1:-1:-1;6547:1:51;;-1:-1:-1;6531:62:51;;6491:113;6622:6;-1:-1:-1;6630:20:51;;-1:-1:-1;6630:20:51;;-1:-1:-1;5140:1530:51;;;;;;;;;:::o;14:173:242:-;82:20;;-1:-1:-1;;;;;131:31:242;;121:42;;111:70;;177:1;174;167:12;111:70;14:173;;;:::o;192:186::-;251:6;304:2;292:9;283:7;279:23;275:32;272:52;;;320:1;317;310:12;272:52;343:29;362:9;343:29;:::i;:::-;333:39;192:186;-1:-1:-1;;;192:186:242:o;565:254::-;633:6;641;694:2;682:9;673:7;669:23;665:32;662:52;;;710:1;707;700:12;662:52;733:29;752:9;733:29;:::i;:::-;723:39;809:2;794:18;;;;781:32;;-1:-1:-1;;;565:254:242:o;1224:666::-;1303:6;1311;1319;1372:2;1360:9;1351:7;1347:23;1343:32;1340:52;;;1388:1;1385;1378:12;1340:52;1428:9;1415:23;1461:18;1453:6;1450:30;1447:50;;;1493:1;1490;1483:12;1447:50;1516:22;;1569:4;1561:13;;1557:27;-1:-1:-1;1547:55:242;;1598:1;1595;1588:12;1547:55;1638:2;1625:16;1664:18;1656:6;1653:30;1650:50;;;1696:1;1693;1686:12;1650:50;1743:7;1736:4;1727:6;1723:2;1719:15;1715:26;1712:39;1709:59;;;1764:1;1761;1754:12;1709:59;1795:4;1787:13;;;;-1:-1:-1;1819:6:242;-1:-1:-1;1844:40:242;;1863:20;;1844:40;:::i;:::-;1834:50;;1224:666;;;;;:::o;1895:260::-;1963:6;1971;2024:2;2012:9;2003:7;1999:23;1995:32;1992:52;;;2040:1;2037;2030:12;1992:52;2063:29;2082:9;2063:29;:::i;:::-;2053:39;;2111:38;2145:2;2134:9;2130:18;2111:38;:::i;:::-;2101:48;;1895:260;;;;;:::o;3975:232::-;4014:3;4035:17;;;4032:140;;4094:10;4089:3;4085:20;4082:1;4075:31;4129:4;4126:1;4119:15;4157:4;4154:1;4147:15;4032:140;-1:-1:-1;4199:1:242;4188:13;;3975:232::o;4212:127::-;4273:10;4268:3;4264:20;4261:1;4254:31;4304:4;4301:1;4294:15;4328:4;4325:1;4318:15", "linkReferences": {}}, "methodIdentifiers": {"claimReferral(bytes,address)": "c11c1df5", "isUserReferred(address)": "7baf9406", "nonces(address)": "7ecebe00", "referralRegistry(address,uint256)": "26ea2001", "referralsForUserRegistry(address)": "7fdf2339", "referredByRegistry(address,address)": "e6bf9d93", "totalReferred(address)": "1150e223"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"ECDSAInvalidSignature\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"length\",\"type\":\"uint256\"}],\"name\":\"ECDSAInvalidSignatureLength\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"s\",\"type\":\"bytes32\"}],\"name\":\"ECDSAInvalidSignatureS\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ReferralSigning_ContractReferrerNotAllowed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ReferralSigning_InvalidSignature\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ReferralSigning_SameUser\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ReferralSigning_UserAlreadyReferred\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"referred\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"referrer\",\"type\":\"address\"}],\"name\":\"ReferralClaimed\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"referred\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"referrer\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"reason\",\"type\":\"string\"}],\"name\":\"ReferralRejected\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"signature\",\"type\":\"bytes\"},{\"internalType\":\"address\",\"name\":\"referrer\",\"type\":\"address\"}],\"name\":\"claimReferral\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"isUserReferred\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"wasReferred\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"nonces\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"nonce\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"referredBy\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"referralRegistry\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"users\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"referralsForUserRegistry\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"referredBy\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"referredBy\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"referredByRegistry\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"wasReferred\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"referredBy\",\"type\":\"address\"}],\"name\":\"totalReferred\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"total\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"ECDSAInvalidSignature()\":[{\"details\":\"The signature derives the `address(0)`.\"}],\"ECDSAInvalidSignatureLength(uint256)\":[{\"details\":\"The signature has an invalid length.\"}],\"ECDSAInvalidSignatureS(bytes32)\":[{\"details\":\"The signature has an S value that is in the upper half order.\"}]},\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/referral/ReferralSigning.sol\":\"ReferralSigning\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"lib/openzeppelin-contracts/contracts/utils/Strings.sol\":{\"keccak256\":\"0x55f102ea785d8399c0e58d1108e2d289506dde18abc6db1b7f68c1f9f9bc5792\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6e52e0a7765c943ef14e5bcf11e46e6139fa044be564881378349236bf2e3453\",\"dweb:/ipfs/QmZEeeXoFPW47amyP35gfzomF9DixqqTEPwzBakv6cZw6i\"]},\"lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol\":{\"keccak256\":\"0xeed0a08b0b091f528356cbc7245891a4c748682d4f6a18055e8e6ca77d12a6cf\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ba80ba06c8e6be852847e4c5f4492cef801feb6558ae09ed705ff2e04ea8b13c\",\"dweb:/ipfs/QmXRJDv3xHLVQCVXg1ZvR35QS9sij5y9NDWYzMfUfAdTHF\"]},\"lib/openzeppelin-contracts/contracts/utils/cryptography/MessageHashUtils.sol\":{\"keccak256\":\"0xba333517a3add42cd35fe877656fc3dfcc9de53baa4f3aabbd6d12a92e4ea435\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2ceacff44c0fdc81e48e0e0b1db87a2076d3c1fb497341de077bf1da9f6b406c\",\"dweb:/ipfs/QmRUo1muMRAewxrKQ7TkXUtknyRoR57AyEkoPpiuZQ8FzX\"]},\"lib/openzeppelin-contracts/contracts/utils/math/Math.sol\":{\"keccak256\":\"0x005ec64c6313f0555d59e278f9a7a5ab2db5bdc72a027f255a37c327af1ec02d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4ece9f0b9c8daca08c76b6b5405a6446b6f73b3a15fab7ff56e296cbd4a2c875\",\"dweb:/ipfs/QmQyRpyPRL5SQuAgj6SHmbir3foX65FJjbVTTQrA2EFg6L\"]},\"lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol\":{\"keccak256\":\"0x5f7e4076e175393767754387c962926577f1660dd9b810187b9002407656be72\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7d533a1c97cd43a57cd9c465f7ee8dd0e39ae93a8fb8ff8e5303a356b081cdcc\",\"dweb:/ipfs/QmVBEei6aTnvYNZp2CHYVNKyZS4q1KkjANfY39WVXZXVoT\"]},\"src/referral/ReferralSigning.sol\":{\"keccak256\":\"0x6532889563da0b0165936f9968d6b3e4a760e99f0d36232d941609df3858f5d1\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://03cb8704916687f0ca4698a4dd6fe186a1d8c96277aabba386fad4b36bc5f826\",\"dweb:/ipfs/QmdsikPF42FaShk3kSha6NY9b7i1n7S7gHZzcbkswBn7Yo\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "ECDSAInvalidSignature"}, {"inputs": [{"internalType": "uint256", "name": "length", "type": "uint256"}], "type": "error", "name": "ECDSAInvalidSignatureLength"}, {"inputs": [{"internalType": "bytes32", "name": "s", "type": "bytes32"}], "type": "error", "name": "ECDSAInvalidSignatureS"}, {"inputs": [], "type": "error", "name": "ReferralSigning_ContractReferrerNotAllowed"}, {"inputs": [], "type": "error", "name": "ReferralSigning_InvalidSignature"}, {"inputs": [], "type": "error", "name": "ReferralSigning_SameUser"}, {"inputs": [], "type": "error", "name": "ReferralSigning_UserAlreadyReferred"}, {"inputs": [{"internalType": "address", "name": "referred", "type": "address", "indexed": true}, {"internalType": "address", "name": "referrer", "type": "address", "indexed": true}], "type": "event", "name": "ReferralClaimed", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "referred", "type": "address", "indexed": true}, {"internalType": "address", "name": "referrer", "type": "address", "indexed": true}, {"internalType": "string", "name": "reason", "type": "string", "indexed": false}], "type": "event", "name": "ReferralRejected", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "signature", "type": "bytes"}, {"internalType": "address", "name": "referrer", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "claimReferral"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isUserReferred", "outputs": [{"internalType": "bool", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "stateMutability": "view", "type": "function", "name": "nonces", "outputs": [{"internalType": "uint256", "name": "nonce", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "referralRegistry", "outputs": [{"internalType": "address", "name": "users", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "stateMutability": "view", "type": "function", "name": "referralsForUserRegistry", "outputs": [{"internalType": "address", "name": "<PERSON><PERSON><PERSON>", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "address", "name": "user", "type": "address"}], "stateMutability": "view", "type": "function", "name": "referredByRegistry", "outputs": [{"internalType": "bool", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "<PERSON><PERSON><PERSON>", "type": "address"}], "stateMutability": "view", "type": "function", "name": "totalReferred", "outputs": [{"internalType": "uint256", "name": "total", "type": "uint256"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/referral/ReferralSigning.sol": "ReferralSigning"}, "evmVersion": "london", "libraries": {}}, "sources": {"lib/openzeppelin-contracts/contracts/utils/Strings.sol": {"keccak256": "0x55f102ea785d8399c0e58d1108e2d289506dde18abc6db1b7f68c1f9f9bc5792", "urls": ["bzz-raw://6e52e0a7765c943ef14e5bcf11e46e6139fa044be564881378349236bf2e3453", "dweb:/ipfs/QmZEeeXoFPW47amyP35gfzomF9DixqqTEPwzBakv6cZw6i"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol": {"keccak256": "0xeed0a08b0b091f528356cbc7245891a4c748682d4f6a18055e8e6ca77d12a6cf", "urls": ["bzz-raw://ba80ba06c8e6be852847e4c5f4492cef801feb6558ae09ed705ff2e04ea8b13c", "dweb:/ipfs/QmXRJDv3xHLVQCVXg1ZvR35QS9sij5y9NDWYzMfUfAdTHF"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/cryptography/MessageHashUtils.sol": {"keccak256": "0xba333517a3add42cd35fe877656fc3dfcc9de53baa4f3aabbd6d12a92e4ea435", "urls": ["bzz-raw://2ceacff44c0fdc81e48e0e0b1db87a2076d3c1fb497341de077bf1da9f6b406c", "dweb:/ipfs/QmRUo1muMRAewxrKQ7TkXUtknyRoR57AyEkoPpiuZQ8FzX"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/Math.sol": {"keccak256": "0x005ec64c6313f0555d59e278f9a7a5ab2db5bdc72a027f255a37c327af1ec02d", "urls": ["bzz-raw://4ece9f0b9c8daca08c76b6b5405a6446b6f73b3a15fab7ff56e296cbd4a2c875", "dweb:/ipfs/QmQyRpyPRL5SQuAgj6SHmbir3foX65FJjbVTTQrA2EFg6L"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol": {"keccak256": "0x5f7e4076e175393767754387c962926577f1660dd9b810187b9002407656be72", "urls": ["bzz-raw://7d533a1c97cd43a57cd9c465f7ee8dd0e39ae93a8fb8ff8e5303a356b081cdcc", "dweb:/ipfs/QmVBEei6aTnvYNZp2CHYVNKyZS4q1KkjANfY39WVXZXVoT"], "license": "MIT"}, "src/referral/ReferralSigning.sol": {"keccak256": "0x6532889563da0b0165936f9968d6b3e4a760e99f0d36232d941609df3858f5d1", "urls": ["bzz-raw://03cb8704916687f0ca4698a4dd6fe186a1d8c96277aabba386fad4b36bc5f826", "dweb:/ipfs/QmdsikPF42FaShk3kSha6NY9b7i1n7S7gHZzcbkswBn7Yo"], "license": "BSL-1.1"}}, "version": 1}, "id": 192}