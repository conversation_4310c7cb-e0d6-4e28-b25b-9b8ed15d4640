{"abi": [{"type": "function", "name": "acceptAdmin", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "accrualBlockTimestamp", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "accrueInterest", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "addReserves", "inputs": [{"name": "addAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "admin", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address payable"}], "stateMutability": "view"}, {"type": "function", "name": "allowance", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "spender", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "approve", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "balanceOf", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "balanceOfUnderlying", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "borrow", "inputs": [{"name": "borrowAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "borrowBalanceCurrent", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "borrowBalanceStored", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "borrowIndex", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "borrowRateMaxMantissa", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "borrowRatePerBlock", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "decimals", "inputs": [], "outputs": [{"name": "", "type": "uint8", "internalType": "uint8"}], "stateMutability": "view"}, {"type": "function", "name": "exchangeRateCurrent", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "exchangeRateStored", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getAccountSnapshot", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getCash", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "interestRateModel", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "liquidate", "inputs": [{"name": "borrower", "type": "address", "internalType": "address"}, {"name": "repayAmount", "type": "uint256", "internalType": "uint256"}, {"name": "mTokenCollateral", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "mint", "inputs": [{"name": "mintAmount", "type": "uint256", "internalType": "uint256"}, {"name": "receiver", "type": "address", "internalType": "address"}, {"name": "minAmountOut", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "name", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "operator", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "pendingAdmin", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address payable"}], "stateMutability": "view"}, {"type": "function", "name": "redeem", "inputs": [{"name": "redeemTokens", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "redeemUnderlying", "inputs": [{"name": "redeemAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "reduceReserves", "inputs": [{"name": "reduceAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "repay", "inputs": [{"name": "repayAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "repayBehalf", "inputs": [{"name": "borrower", "type": "address", "internalType": "address"}, {"name": "repayAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "reserveFactorMantissa", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "rolesOperator", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IRoles"}], "stateMutability": "view"}, {"type": "function", "name": "seize", "inputs": [{"name": "liquidator", "type": "address", "internalType": "address"}, {"name": "borrower", "type": "address", "internalType": "address"}, {"name": "seizeTokens", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setBorrowRateMaxMantissa", "inputs": [{"name": "maxMantissa", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setInterestRateModel", "inputs": [{"name": "newInterestRateModel", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setOperator", "inputs": [{"name": "_operator", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setPendingAdmin", "inputs": [{"name": "newPendingAdmin", "type": "address", "internalType": "address payable"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setReserveFactor", "inputs": [{"name": "newReserveFactorMantissa", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setRolesOperator", "inputs": [{"name": "_roles", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "supplyRatePerBlock", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "sweepToken", "inputs": [{"name": "token", "type": "address", "internalType": "contract IERC20"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "symbol", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "totalBorrows", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "totalBorrowsCurrent", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "totalReserves", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "totalSupply", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "totalUnderlying", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "transfer", "inputs": [{"name": "dst", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferFrom", "inputs": [{"name": "src", "type": "address", "internalType": "address"}, {"name": "dst", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "underlying", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "event", "name": "AccrueInterest", "inputs": [{"name": "cashPrior", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "interestAccumulated", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "borrowIndex", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "totalBorrows", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Approval", "inputs": [{"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "spender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Borrow", "inputs": [{"name": "borrower", "type": "address", "indexed": true, "internalType": "address"}, {"name": "borrowAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "accountBorrows", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "totalBorrows", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "LiquidateBorrow", "inputs": [{"name": "liquidator", "type": "address", "indexed": true, "internalType": "address"}, {"name": "borrower", "type": "address", "indexed": true, "internalType": "address"}, {"name": "repayAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "mTokenCollateral", "type": "address", "indexed": true, "internalType": "address"}, {"name": "seizeTokens", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Mint", "inputs": [{"name": "minter", "type": "address", "indexed": true, "internalType": "address"}, {"name": "receiver", "type": "address", "indexed": true, "internalType": "address"}, {"name": "mintAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "mintTokens", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "NewBorrowRateMaxMantissa", "inputs": [{"name": "oldVal", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "maxMantissa", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "NewMarketInterestRateModel", "inputs": [{"name": "oldInterestRateModel", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newInterestRateModel", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "NewOperator", "inputs": [{"name": "oldOperator", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newOperator", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "NewReserveFactor", "inputs": [{"name": "oldReserveFactorMantissa", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "newReserveFactorMantissa", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "NewRolesOperator", "inputs": [{"name": "oldRoles", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newRoles", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Redeem", "inputs": [{"name": "redeemer", "type": "address", "indexed": true, "internalType": "address"}, {"name": "redeemAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "redeemTokens", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "RepayBorrow", "inputs": [{"name": "payer", "type": "address", "indexed": true, "internalType": "address"}, {"name": "borrower", "type": "address", "indexed": true, "internalType": "address"}, {"name": "repayAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "accountBorrows", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "totalBorrows", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "ReservesAdded", "inputs": [{"name": "benefactor", "type": "address", "indexed": true, "internalType": "address"}, {"name": "addAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "newTotalReserves", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "ReservesReduced", "inputs": [{"name": "admin", "type": "address", "indexed": true, "internalType": "address"}, {"name": "reduceAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "newTotalReserves", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "SameChainFlowStateUpdated", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "_oldState", "type": "bool", "indexed": false, "internalType": "bool"}, {"name": "_newState", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "Transfer", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "ZkVerifierUpdated", "inputs": [{"name": "oldVerifier", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newVerifier", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "AddressEmptyCode", "inputs": [{"name": "target", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "AddressInsufficientBalance", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "FailedInnerCall", "inputs": []}, {"type": "error", "name": "ReentrancyGuardReentrantCall", "inputs": []}, {"type": "error", "name": "SafeERC20FailedOperation", "inputs": [{"name": "token", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "mErc20_TokenNotValid", "inputs": []}, {"type": "error", "name": "mt_AlreadyInitialized", "inputs": []}, {"type": "error", "name": "mt_BorrowCashNotAvailable", "inputs": []}, {"type": "error", "name": "mt_BorrowRateTooHigh", "inputs": []}, {"type": "error", "name": "mt_CollateralBlockTimestampNotValid", "inputs": []}, {"type": "error", "name": "mt_ExchangeRateNotValid", "inputs": []}, {"type": "error", "name": "mt_InvalidInput", "inputs": []}, {"type": "error", "name": "mt_LiquidateSeizeTooMuch", "inputs": []}, {"type": "error", "name": "mt_MarketMethodNotValid", "inputs": []}, {"type": "error", "name": "mt_MinAmountNotValid", "inputs": []}, {"type": "error", "name": "mt_OnlyAdmin", "inputs": []}, {"type": "error", "name": "mt_OnlyAdminOrRole", "inputs": []}, {"type": "error", "name": "mt_RedeemCashNotAvailable", "inputs": []}, {"type": "error", "name": "mt_RedeemEmpty", "inputs": []}, {"type": "error", "name": "mt_RedeemTransferOutNotPossible", "inputs": []}, {"type": "error", "name": "mt_ReserveCashNotAvailable", "inputs": []}, {"type": "error", "name": "mt_ReserveFactorTooHigh", "inputs": []}, {"type": "error", "name": "mt_SameChainOperationsAreDisabled", "inputs": []}, {"type": "error", "name": "mt_TransferNotValid", "inputs": []}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"acceptAdmin()": "0e18b681", "accrualBlockTimestamp()": "cfa99201", "accrueInterest()": "a6afed95", "addReserves(uint256)": "7821a514", "admin()": "f851a440", "allowance(address,address)": "dd62ed3e", "approve(address,uint256)": "095ea7b3", "balanceOf(address)": "70a08231", "balanceOfUnderlying(address)": "3af9e669", "borrow(uint256)": "c5ebeaec", "borrowBalanceCurrent(address)": "17bfdfbc", "borrowBalanceStored(address)": "95dd9193", "borrowIndex()": "aa5af0fd", "borrowRateMaxMantissa()": "ee27a2f2", "borrowRatePerBlock()": "f8f9da28", "decimals()": "313ce567", "exchangeRateCurrent()": "bd6d894d", "exchangeRateStored()": "182df0f5", "getAccountSnapshot(address)": "c37f68e2", "getCash()": "3b1d21a2", "interestRateModel()": "f3fdb15a", "liquidate(address,uint256,address)": "4914c008", "mint(uint256,address,uint256)": "836a1040", "name()": "06fdde03", "operator()": "570ca735", "pendingAdmin()": "********", "redeem(uint256)": "db006a75", "redeemUnderlying(uint256)": "852a12e3", "reduceReserves(uint256)": "07e27959", "repay(uint256)": "371fd8e6", "repayBehalf(address,uint256)": "5bdcecb7", "reserveFactorMantissa()": "173b9904", "rolesOperator()": "4fecab70", "seize(address,address,uint256)": "b2a02ff1", "setBorrowRateMaxMantissa(uint256)": "e67218cd", "setInterestRateModel(address)": "8bcd4016", "setOperator(address)": "b3ab15fb", "setPendingAdmin(address)": "4dd18bf5", "setReserveFactor(uint256)": "1c446983", "setRolesOperator(address)": "f89416ee", "supplyRatePerBlock()": "ae9d70b0", "sweepToken(address,uint256)": "e90a182f", "symbol()": "95d89b41", "totalBorrows()": "47bd3718", "totalBorrowsCurrent()": "73acee98", "totalReserves()": "8f840ddd", "totalSupply()": "18160ddd", "totalUnderlying()": "c70920bc", "transfer(address,uint256)": "a9059cbb", "transferFrom(address,address,uint256)": "23b872dd", "underlying()": "6f307dc3"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"target\",\"type\":\"address\"}],\"name\":\"AddressEmptyCode\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"AddressInsufficientBalance\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"FailedInnerCall\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ReentrancyGuardReentrantCall\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"SafeERC20FailedOperation\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mErc20_TokenNotValid\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_AlreadyInitialized\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_BorrowCashNotAvailable\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_BorrowRateTooHigh\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_CollateralBlockTimestampNotValid\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_ExchangeRateNotValid\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_InvalidInput\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_LiquidateSeizeTooMuch\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_MarketMethodNotValid\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_MinAmountNotValid\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_OnlyAdmin\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_OnlyAdminOrRole\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_RedeemCashNotAvailable\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_RedeemEmpty\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_RedeemTransferOutNotPossible\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_ReserveCashNotAvailable\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_ReserveFactorTooHigh\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_SameChainOperationsAreDisabled\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mt_TransferNotValid\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"cashPrior\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"interestAccumulated\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"borrowIndex\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"totalBorrows\",\"type\":\"uint256\"}],\"name\":\"AccrueInterest\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"Approval\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"borrowAmount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"accountBorrows\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"totalBorrows\",\"type\":\"uint256\"}],\"name\":\"Borrow\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"liquidator\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"repayAmount\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"mTokenCollateral\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"seizeTokens\",\"type\":\"uint256\"}],\"name\":\"LiquidateBorrow\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"minter\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"mintAmount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"mintTokens\",\"type\":\"uint256\"}],\"name\":\"Mint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"oldVal\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"maxMantissa\",\"type\":\"uint256\"}],\"name\":\"NewBorrowRateMaxMantissa\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"oldInterestRateModel\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newInterestRateModel\",\"type\":\"address\"}],\"name\":\"NewMarketInterestRateModel\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"oldOperator\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOperator\",\"type\":\"address\"}],\"name\":\"NewOperator\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"oldReserveFactorMantissa\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"newReserveFactorMantissa\",\"type\":\"uint256\"}],\"name\":\"NewReserveFactor\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"oldRoles\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newRoles\",\"type\":\"address\"}],\"name\":\"NewRolesOperator\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"redeemer\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"redeemAmount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"redeemTokens\",\"type\":\"uint256\"}],\"name\":\"Redeem\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"payer\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"repayAmount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"accountBorrows\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"totalBorrows\",\"type\":\"uint256\"}],\"name\":\"RepayBorrow\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"benefactor\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"addAmount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"newTotalReserves\",\"type\":\"uint256\"}],\"name\":\"ReservesAdded\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"admin\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"reduceAmount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"newTotalReserves\",\"type\":\"uint256\"}],\"name\":\"ReservesReduced\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"_oldState\",\"type\":\"bool\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"_newState\",\"type\":\"bool\"}],\"name\":\"SameChainFlowStateUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"oldVerifier\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newVerifier\",\"type\":\"address\"}],\"name\":\"ZkVerifierUpdated\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"acceptAdmin\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"accrualBlockTimestamp\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"accrueInterest\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"addAmount\",\"type\":\"uint256\"}],\"name\":\"addReserves\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"admin\",\"outputs\":[{\"internalType\":\"address payable\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"}],\"name\":\"allowance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"approve\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"balanceOfUnderlying\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"borrowAmount\",\"type\":\"uint256\"}],\"name\":\"borrow\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"borrowBalanceCurrent\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"borrowBalanceStored\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"borrowIndex\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"borrowRateMaxMantissa\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"borrowRatePerBlock\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"decimals\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"exchangeRateCurrent\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"exchangeRateStored\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"getAccountSnapshot\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getCash\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"interestRateModel\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"repayAmount\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"mTokenCollateral\",\"type\":\"address\"}],\"name\":\"liquidate\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"mintAmount\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"minAmountOut\",\"type\":\"uint256\"}],\"name\":\"mint\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"name\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"operator\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"pendingAdmin\",\"outputs\":[{\"internalType\":\"address payable\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"redeemTokens\",\"type\":\"uint256\"}],\"name\":\"redeem\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"redeemAmount\",\"type\":\"uint256\"}],\"name\":\"redeemUnderlying\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"reduceAmount\",\"type\":\"uint256\"}],\"name\":\"reduceReserves\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"repayAmount\",\"type\":\"uint256\"}],\"name\":\"repay\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"repayAmount\",\"type\":\"uint256\"}],\"name\":\"repayBehalf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"reserveFactorMantissa\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"rolesOperator\",\"outputs\":[{\"internalType\":\"contract IRoles\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"liquidator\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"seizeTokens\",\"type\":\"uint256\"}],\"name\":\"seize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"maxMantissa\",\"type\":\"uint256\"}],\"name\":\"setBorrowRateMaxMantissa\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newInterestRateModel\",\"type\":\"address\"}],\"name\":\"setInterestRateModel\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_operator\",\"type\":\"address\"}],\"name\":\"setOperator\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address payable\",\"name\":\"newPendingAdmin\",\"type\":\"address\"}],\"name\":\"setPendingAdmin\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"newReserveFactorMantissa\",\"type\":\"uint256\"}],\"name\":\"setReserveFactor\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_roles\",\"type\":\"address\"}],\"name\":\"setRolesOperator\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"supplyRatePerBlock\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"contract IERC20\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"sweepToken\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"symbol\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalBorrows\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalBorrowsCurrent\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalReserves\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalSupply\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalUnderlying\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"dst\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"transfer\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"src\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"dst\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"transferFrom\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"underlying\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"AddressEmptyCode(address)\":[{\"details\":\"There's no code at `target` (it is not a contract).\"}],\"AddressInsufficientBalance(address)\":[{\"details\":\"The ETH balance of the account is not enough to perform the operation.\"}],\"FailedInnerCall()\":[{\"details\":\"A call to an address target failed. The target may have reverted.\"}],\"ReentrancyGuardReentrantCall()\":[{\"details\":\"Unauthorized reentrant call.\"}],\"SafeERC20FailedOperation(address)\":[{\"details\":\"An operation with an ERC20 token failed.\"}]},\"kind\":\"dev\",\"methods\":{\"acceptAdmin()\":{\"details\":\"Admin function for pending admin to accept role and update admin\"},\"addReserves(uint256)\":{\"params\":{\"addAmount\":\"The amount fo underlying token to add as reserves\"}},\"allowance(address,address)\":{\"params\":{\"owner\":\"The address of the token holder\",\"spender\":\"The address authorized to spend the tokens\"},\"returns\":{\"_0\":\"The current remaining number of tokens `spender` can spend\"}},\"approve(address,uint256)\":{\"params\":{\"amount\":\"The number of tokens to approve\",\"spender\":\"The address authorized to spend tokens\"},\"returns\":{\"_0\":\"Whether the approval was successful or not\"}},\"balanceOf(address)\":{\"params\":{\"account\":\"The account to check for\"}},\"balanceOfUnderlying(address)\":{\"params\":{\"owner\":\"The address to query the balance of underlying assets for\"},\"returns\":{\"_0\":\"The balance of underlying assets owned by `owner`\"}},\"borrow(uint256)\":{\"params\":{\"borrowAmount\":\"The amount of the underlying asset to borrow\"}},\"borrowBalanceCurrent(address)\":{\"params\":{\"account\":\"The address to query the borrow balance for\"},\"returns\":{\"_0\":\"The current borrow balance\"}},\"borrowBalanceStored(address)\":{\"params\":{\"account\":\"The address to query the stored borrow balance for\"},\"returns\":{\"_0\":\"The stored borrow balance\"}},\"borrowRatePerBlock()\":{\"returns\":{\"_0\":\"The current borrow rate per block, scaled by 1e18\"}},\"exchangeRateCurrent()\":{\"returns\":{\"_0\":\"The current exchange rate\"}},\"exchangeRateStored()\":{\"returns\":{\"_0\":\"The stored exchange rate\"}},\"getAccountSnapshot(address)\":{\"params\":{\"account\":\"The address to query the account snapshot for\"},\"returns\":{\"_0\":\"(token balance, borrow balance, exchange rate)\"}},\"getCash()\":{\"returns\":{\"_0\":\"The total amount of cash\"}},\"liquidate(address,uint256,address)\":{\"params\":{\"borrower\":\"The borrower of this mToken to be liquidated\",\"mTokenCollateral\":\"The market in which to seize collateral from the borrower\",\"repayAmount\":\"The amount of the underlying borrowed asset to repay\"}},\"mint(uint256,address,uint256)\":{\"details\":\"Accrues interest whether or not the operation succeeds, unless reverted\",\"params\":{\"minAmountOut\":\"The min amounts to be received\",\"mintAmount\":\"The amount of the underlying asset to supply\",\"receiver\":\"The mTokens receiver\"}},\"redeem(uint256)\":{\"details\":\"Accrues interest whether or not the operation succeeds, unless reverted\",\"params\":{\"redeemTokens\":\"The number of mTokens to redeem into underlying\"}},\"redeemUnderlying(uint256)\":{\"details\":\"Accrues interest whether or not the operation succeeds, unless reverted\",\"params\":{\"redeemAmount\":\"The amount of underlying to redeem\"}},\"reduceReserves(uint256)\":{\"params\":{\"reduceAmount\":\"Amount of reduction to reserves\"}},\"repay(uint256)\":{\"params\":{\"repayAmount\":\"The amount to repay, or type(uint256).max for the full outstanding amount\"}},\"repayBehalf(address,uint256)\":{\"params\":{\"borrower\":\"the account with the debt being payed off\",\"repayAmount\":\"The amount to repay, or type(uint256).max for the full outstanding amount\"}},\"seize(address,address,uint256)\":{\"details\":\"Will fail unless called by another mToken during the process of liquidation.  Its absolutely critical to use msg.sender as the borrowed mToken and not a parameter.\",\"params\":{\"borrower\":\"The account having collateral seized\",\"liquidator\":\"The account receiving seized collateral\",\"seizeTokens\":\"The number of mTokens to seize\"}},\"setInterestRateModel(address)\":{\"details\":\"Admin function to accrue interest and update the interest rate model\",\"params\":{\"newInterestRateModel\":\"the new interest rate model to use\"}},\"setOperator(address)\":{\"details\":\"Admin function to set a new operator\"},\"setPendingAdmin(address)\":{\"details\":\"Admin function to begin change of admin. The newPendingAdmin must call `_acceptAdmin` to finalize the transfer.\",\"params\":{\"newPendingAdmin\":\"New pending admin.\"}},\"setReserveFactor(uint256)\":{\"details\":\"Admin function to accrue interest and set a new reserve factor\"},\"setRolesOperator(address)\":{\"details\":\"Admin function to set a new operator\"},\"supplyRatePerBlock()\":{\"returns\":{\"_0\":\"The current supply rate per block, scaled by 1e18\"}},\"sweepToken(address,uint256)\":{\"params\":{\"token\":\"The address of the ERC-20 token to sweep\"}},\"totalBorrowsCurrent()\":{\"returns\":{\"_0\":\"The total amount of borrows\"}},\"transfer(address,uint256)\":{\"params\":{\"amount\":\"The number of tokens to transfer\",\"dst\":\"The address of the recipient\"},\"returns\":{\"_0\":\"Whether the transfer was successful or not\"}},\"transferFrom(address,address,uint256)\":{\"params\":{\"amount\":\"The number of tokens to transfer\",\"dst\":\"The address to which tokens are transferred\",\"src\":\"The address from which tokens are transferred\"},\"returns\":{\"_0\":\"Whether the transfer was successful or not\"}}},\"title\":\"Malda's mErc20 Contract\",\"version\":1},\"userdoc\":{\"events\":{\"AccrueInterest(uint256,uint256,uint256,uint256)\":{\"notice\":\"Event emitted when interest is accrued\"},\"Approval(address,address,uint256)\":{\"notice\":\"EIP20 Approval event\"},\"Borrow(address,uint256,uint256,uint256)\":{\"notice\":\"Event emitted when underlying is borrowed\"},\"LiquidateBorrow(address,address,uint256,address,uint256)\":{\"notice\":\"Event emitted when a borrow is liquidated\"},\"Mint(address,address,uint256,uint256)\":{\"notice\":\"Event emitted when tokens are minted\"},\"NewBorrowRateMaxMantissa(uint256,uint256)\":{\"notice\":\"Event emitted when the borrow max mantissa is updated\"},\"NewMarketInterestRateModel(address,address)\":{\"notice\":\"Event emitted when interestRateModel is changed\"},\"NewOperator(address,address)\":{\"notice\":\"Event emitted when Operator is changed\"},\"NewReserveFactor(uint256,uint256)\":{\"notice\":\"Event emitted when the reserve factor is changed\"},\"NewRolesOperator(address,address)\":{\"notice\":\"Event emitted when rolesOperator is changed\"},\"Redeem(address,uint256,uint256)\":{\"notice\":\"Event emitted when tokens are redeemed\"},\"RepayBorrow(address,address,uint256,uint256,uint256)\":{\"notice\":\"Event emitted when a borrow is repaid\"},\"ReservesAdded(address,uint256,uint256)\":{\"notice\":\"Event emitted when the reserves are added\"},\"ReservesReduced(address,uint256,uint256)\":{\"notice\":\"Event emitted when the reserves are reduced\"},\"SameChainFlowStateUpdated(address,bool,bool)\":{\"notice\":\"Event emitted when same chain flow state is enabled or disabled\"},\"Transfer(address,address,uint256)\":{\"notice\":\"EIP20 Transfer event\"},\"ZkVerifierUpdated(address,address)\":{\"notice\":\"Event emitted when same chain flow state is enabled or disabled\"}},\"kind\":\"user\",\"methods\":{\"acceptAdmin()\":{\"notice\":\"Accepts transfer of admin rights. msg.sender must be pendingAdmin\"},\"accrualBlockTimestamp()\":{\"notice\":\"Block timestamp that interest was last accrued at\"},\"accrueInterest()\":{\"notice\":\"Accrues interest on the contract's outstanding loans\"},\"addReserves(uint256)\":{\"notice\":\"The sender adds to reserves.\"},\"admin()\":{\"notice\":\"Administrator for this contract\"},\"allowance(address,address)\":{\"notice\":\"Returns the current allowance the `spender` has from the `owner`\"},\"approve(address,uint256)\":{\"notice\":\"Approves `spender` to spend `amount` tokens on behalf of the caller\"},\"balanceOf(address)\":{\"notice\":\"Returns the value of tokens owned by `account`.\"},\"balanceOfUnderlying(address)\":{\"notice\":\"Returns the underlying asset balance of the `owner`\"},\"borrow(uint256)\":{\"notice\":\"Sender borrows assets from the protocol to their own address\"},\"borrowBalanceCurrent(address)\":{\"notice\":\"Returns the current borrow balance for `account`, accounting for interest\"},\"borrowBalanceStored(address)\":{\"notice\":\"Returns the stored borrow balance for `account`, without accruing interest\"},\"borrowIndex()\":{\"notice\":\"Accumulator of the total earned interest rate since the opening of the market\"},\"borrowRateMaxMantissa()\":{\"notice\":\"Maximum borrow rate that can ever be applied\"},\"borrowRatePerBlock()\":{\"notice\":\"Returns the current borrow rate per block\"},\"decimals()\":{\"notice\":\"EIP-20 token decimals for this token\"},\"exchangeRateCurrent()\":{\"notice\":\"Returns the current exchange rate, with interest accrued\"},\"exchangeRateStored()\":{\"notice\":\"Returns the stored exchange rate, without accruing interest\"},\"getAccountSnapshot(address)\":{\"notice\":\"Returns the snapshot of account details for the given `account`\"},\"getCash()\":{\"notice\":\"Returns the total amount of available cash in the contract\"},\"interestRateModel()\":{\"notice\":\"Model which tells what the current interest rate should be\"},\"liquidate(address,uint256,address)\":{\"notice\":\"The sender liquidates the borrowers collateral.  The collateral seized is transferred to the liquidator.\"},\"mint(uint256,address,uint256)\":{\"notice\":\"Sender supplies assets into the market and receives mTokens in exchange\"},\"name()\":{\"notice\":\"EIP-20 token name for this token\"},\"operator()\":{\"notice\":\"Contract which oversees inter-mToken operations\"},\"pendingAdmin()\":{\"notice\":\"Pending administrator for this contract\"},\"redeem(uint256)\":{\"notice\":\"Sender redeems mTokens in exchange for the underlying asset\"},\"redeemUnderlying(uint256)\":{\"notice\":\"Sender redeems mTokens in exchange for a specified amount of underlying asset\"},\"reduceReserves(uint256)\":{\"notice\":\"Accrues interest and reduces reserves by transferring to admin\"},\"repay(uint256)\":{\"notice\":\"Sender repays their own borrow\"},\"repayBehalf(address,uint256)\":{\"notice\":\"Sender repays a borrow belonging to borrower\"},\"reserveFactorMantissa()\":{\"notice\":\"Fraction of interest currently set aside for reserves\"},\"rolesOperator()\":{\"notice\":\"Roles manager\"},\"seize(address,address,uint256)\":{\"notice\":\"Transfers collateral tokens (this market) to the liquidator.\"},\"setInterestRateModel(address)\":{\"notice\":\"accrues interest and updates the interest rate model using _setInterestRateModelFresh\"},\"setOperator(address)\":{\"notice\":\"Sets a new Operator for the market\"},\"setPendingAdmin(address)\":{\"notice\":\"Begins transfer of admin rights. The newPendingAdmin must call `_acceptAdmin` to finalize the transfer.\"},\"setReserveFactor(uint256)\":{\"notice\":\"accrues interest and sets a new reserve factor for the protocol using _setReserveFactorFresh\"},\"setRolesOperator(address)\":{\"notice\":\"Sets a new Operator for the market\"},\"supplyRatePerBlock()\":{\"notice\":\"Returns the current supply rate per block\"},\"sweepToken(address,uint256)\":{\"notice\":\"A public function to sweep accidental ERC-20 transfers to this contract. Tokens are sent to admin (timelock)\"},\"symbol()\":{\"notice\":\"EIP-20 token symbol for this token\"},\"totalBorrows()\":{\"notice\":\"Total amount of outstanding borrows of the underlying in this market\"},\"totalBorrowsCurrent()\":{\"notice\":\"Returns the total amount of borrows, accounting for interest\"},\"totalReserves()\":{\"notice\":\"Total amount of reserves of the underlying held in this market\"},\"totalSupply()\":{\"notice\":\"Returns the value of tokens in existence.\"},\"totalUnderlying()\":{\"notice\":\"Returns the amount of underlying tokens\"},\"transfer(address,uint256)\":{\"notice\":\"Transfers `amount` tokens to the `dst` address\"},\"transferFrom(address,address,uint256)\":{\"notice\":\"Transfers `amount` tokens from the `src` address to the `dst` address\"},\"underlying()\":{\"notice\":\"Underlying asset for this mToken\"}},\"notice\":\"mTokens which wrap an EIP-20 underlying\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/mToken/mErc20.sol\":\"mErc20\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xc6a8ff0ea489379b61faa647490411b80102578440ab9d84e9a957cc12164e70\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0ea104e577e63faea3b69c415637e99e755dcbf64c5833d7140c35a714d6d90c\",\"dweb:/ipfs/Qmau6x4Ns9XdyynRCNNp3RhLqijJjFm7z5fyZazfYFGYdq\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol\":{\"keccak256\":\"0x6008dabfe393240d73d7dd7688033f72740d570aa422254d29a7dce8568f3aff\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f5196ec75139918c6c7bb4251b36395e668f1fa6d206beba7e7520e74913940d\",\"dweb:/ipfs/QmSyqjksXxmm2mCG6qRd1yuwLykypkSVBbnBnGqJRcuJMi\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol\":{\"keccak256\":\"0x37bb49513c49c87c4642a891b13b63571bc87013dde806617aa1efb54605f386\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b3036b3a83b7c48f96641f2a9002b9f2dcb6a5958dd670894ada21ae8229b3d0\",\"dweb:/ipfs/QmUNfSBdoVtjhETaUJCYcaC7pTMgbhht926tJ2uXJbiVd3\"]},\"lib/openzeppelin-contracts/contracts/utils/Address.sol\":{\"keccak256\":\"0xaf28a975a78550e45f65e559a3ad6a5ad43b9b8a37366999abd1b7084eb70721\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b7bd24e224f67f65bfadf85dc2929fa965456bb2415478bd0125471b5ce35245\",\"dweb:/ipfs/QmRaydGr8BTHs1kvaZfsNU69pKzUAGFrvABn1KiRSbE51y\"]},\"lib/openzeppelin-contracts/contracts/utils/ReentrancyGuard.sol\":{\"keccak256\":\"0xf980daa263b661ab8ddee7d4fd833c7da7e7995e2c359ff1f17e67e4112f2236\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7448ab095d6940130bcf76ba47a2eab14148c83119523b93dd89f6d84edd6c02\",\"dweb:/ipfs/QmawrZ4voKQjH3oomXT3Kuheb3Mnmo2VvVpxg8Ne5UJUrd\"]},\"src/interfaces/IBlacklister.sol\":{\"keccak256\":\"0x47ed20135507a9d16473027cbee18de5d952846bada761c6248b91c75cd88be1\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://91a886b95dd578156f9b6cec3236e4751aae8a38e5d15e0d6045217c6fbd3d63\",\"dweb:/ipfs/QmRNjz7sGTuMkQNZhwwcg5DNN2eeqTZSAdZmxWxbx66CHU\"]},\"src/interfaces/IInterestRateModel.sol\":{\"keccak256\":\"0xdc8386be37a06d93fb0967be32e465f3b1f941ec9ce2f90984a5c70488cddb1e\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://de2bf96a7d0945f58b00e9fc867ac00fc4a09480e9e27c1a800604295320b769\",\"dweb:/ipfs/Qmf5sAgA58GjVxeipJNhP4euxwsZ9FS9CgsWTJ319UmCUE\"]},\"src/interfaces/IOperator.sol\":{\"keccak256\":\"0x0ae3d63eb07518da5a4fd6de7b013b6f55c87defb31900548378cffc70a51f72\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://1d9710c4ac43cb0563449aa519501b4ed18a331c90d0bc7eb79cdc56407bad8a\",\"dweb:/ipfs/Qmafe9Wup2GF9pD83CLWUrRait93vwvReqU1C7EHQhY7WV\"]},\"src/interfaces/IRoles.sol\":{\"keccak256\":\"0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c\",\"dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj\"]},\"src/interfaces/ImErc20.sol\":{\"keccak256\":\"0x86a1dcaf0d188107ceae7c5eb0911ac63feafbbeda6d0686e4f9950a51d17888\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://d7dd8e0fcfd68501d9f40e37e1c95d8cb653d65626aeeeb5492b73c0e967e6cb\",\"dweb:/ipfs/QmcadG9QuHtAX5qxTKzNr6FMaXSgijr542tFUVTLBkYhAF\"]},\"src/interfaces/ImToken.sol\":{\"keccak256\":\"0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36\",\"dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL\"]},\"src/mToken/mErc20.sol\":{\"keccak256\":\"0x46aa77f6808c1ca56c7d51b4822bc03d26d036151b4eeb324a3dbdc52bc90643\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://4681c86b025cb7d0872c003146b4e6d56b2e86387c832134f4224fc9d559218c\",\"dweb:/ipfs/QmWM9zVEmNWYSYEvGPd8jwSioG8zBC3hQnCEHbup1gCCLe\"]},\"src/mToken/mToken.sol\":{\"keccak256\":\"0xeefa3394ae7a01c38bc97404ca6375a497e0bce2a8ae3f83feb4c5bd681aaf43\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://cd94a416031e33c169d36692a661899dc9697b8c543bdb8c051f70a3e7eced2c\",\"dweb:/ipfs/QmRBoo7LQ5nbwLpBjB9yEEib8RM5i9yQjfeE7FELHXvBBk\"]},\"src/mToken/mTokenConfiguration.sol\":{\"keccak256\":\"0x4ac1b80b93e3b13ce568f859c1fc4dd54360578d6055027819fee3e6d0b2b83f\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://e27a83d36a038105631a1ce53f92d225b9949a2cc6aed6d898c736850c10676a\",\"dweb:/ipfs/QmZhvhCfZsDxvyYJ1x9TNSfnHKrxpbgLq3qr7hJiL3XpZa\"]},\"src/mToken/mTokenStorage.sol\":{\"keccak256\":\"0x42236cd9e37c2eef2d317f05510bbe21cf5174813ee40f9a6fcbaf84d4fed2f7\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://2679166e1ca0e5d21294447566bba7549900a8f9a9b1882c8c2895a9751a1792\",\"dweb:/ipfs/Qmb49FVvre1He18e6ABHjNJ2yzCPdkYeyzSwMXixZ7fUw3\"]},\"src/utils/ExponentialNoError.sol\":{\"keccak256\":\"0xa97178117d8c32059d55dd2224e5514c54b31f896e565c3834cab3d7502b1bb6\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://1cecc0713709f57a5bb7f64d2fa06b9437a42752fa3fed5d3c0b19d82aa56ef4\",\"dweb:/ipfs/QmQqBCZkzmZxomXLr3b1tHUV55n8EPk3wAUsXgojYSpR41\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "target", "type": "address"}], "type": "error", "name": "AddressEmptyCode"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "type": "error", "name": "AddressInsufficientBalance"}, {"inputs": [], "type": "error", "name": "FailedInnerCall"}, {"inputs": [], "type": "error", "name": "ReentrancyGuardReentrantCall"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "type": "error", "name": "SafeERC20FailedOperation"}, {"inputs": [], "type": "error", "name": "mErc20_TokenNotValid"}, {"inputs": [], "type": "error", "name": "mt_AlreadyInitialized"}, {"inputs": [], "type": "error", "name": "mt_BorrowCashNotAvailable"}, {"inputs": [], "type": "error", "name": "mt_BorrowRateTooHigh"}, {"inputs": [], "type": "error", "name": "mt_CollateralBlockTimestampNotValid"}, {"inputs": [], "type": "error", "name": "mt_ExchangeRateNotValid"}, {"inputs": [], "type": "error", "name": "mt_InvalidInput"}, {"inputs": [], "type": "error", "name": "mt_LiquidateSeizeTooMuch"}, {"inputs": [], "type": "error", "name": "mt_MarketMethodNotValid"}, {"inputs": [], "type": "error", "name": "mt_MinAmountNotValid"}, {"inputs": [], "type": "error", "name": "mt_OnlyAdmin"}, {"inputs": [], "type": "error", "name": "mt_OnlyAdminOrRole"}, {"inputs": [], "type": "error", "name": "mt_RedeemCashNotAvailable"}, {"inputs": [], "type": "error", "name": "mt_RedeemEmpty"}, {"inputs": [], "type": "error", "name": "mt_RedeemTransferOutNotPossible"}, {"inputs": [], "type": "error", "name": "mt_ReserveCashNotAvailable"}, {"inputs": [], "type": "error", "name": "mt_ReserveFactorTooHigh"}, {"inputs": [], "type": "error", "name": "mt_SameChainOperationsAreDisabled"}, {"inputs": [], "type": "error", "name": "mt_TransferNotValid"}, {"inputs": [{"internalType": "uint256", "name": "cashPrior", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "interestAccumulated", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "borrowIndex", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "totalBorrows", "type": "uint256", "indexed": false}], "type": "event", "name": "AccrueInterest", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address", "indexed": true}, {"internalType": "address", "name": "spender", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}], "type": "event", "name": "Approval", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "borrower", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "borrowAmount", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "accountBorrows", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "totalBorrows", "type": "uint256", "indexed": false}], "type": "event", "name": "Borrow", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "liquidator", "type": "address", "indexed": true}, {"internalType": "address", "name": "borrower", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "repayAmount", "type": "uint256", "indexed": false}, {"internalType": "address", "name": "mTokenCollateral", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "seizeTokens", "type": "uint256", "indexed": false}], "type": "event", "name": "LiquidateBorrow", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "minter", "type": "address", "indexed": true}, {"internalType": "address", "name": "receiver", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "mintAmount", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "mintTokens", "type": "uint256", "indexed": false}], "type": "event", "name": "Mint", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "oldVal", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "maxMantissa", "type": "uint256", "indexed": false}], "type": "event", "name": "NewBorrowRateMaxMantissa", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "oldInterestRateModel", "type": "address", "indexed": true}, {"internalType": "address", "name": "newInterestRateModel", "type": "address", "indexed": true}], "type": "event", "name": "NewMarketInterestRateModel", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "oldOperator", "type": "address", "indexed": true}, {"internalType": "address", "name": "newOperator", "type": "address", "indexed": true}], "type": "event", "name": "NewOperator", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "oldReserveFactorMantissa", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "newReserveFactorMantissa", "type": "uint256", "indexed": false}], "type": "event", "name": "NewReserveFactor", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "oldRoles", "type": "address", "indexed": true}, {"internalType": "address", "name": "newRoles", "type": "address", "indexed": true}], "type": "event", "name": "NewRolesOperator", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "redeemer", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "redeemAmount", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "redeemTokens", "type": "uint256", "indexed": false}], "type": "event", "name": "Redeem", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "payer", "type": "address", "indexed": true}, {"internalType": "address", "name": "borrower", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "repayAmount", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "accountBorrows", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "totalBorrows", "type": "uint256", "indexed": false}], "type": "event", "name": "RepayBorrow", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "benefactor", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "addAmount", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "newTotalReserves", "type": "uint256", "indexed": false}], "type": "event", "name": "ReservesAdded", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "admin", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "reduceAmount", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "newTotalReserves", "type": "uint256", "indexed": false}], "type": "event", "name": "ReservesReduced", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address", "indexed": true}, {"internalType": "bool", "name": "_oldState", "type": "bool", "indexed": false}, {"internalType": "bool", "name": "_newState", "type": "bool", "indexed": false}], "type": "event", "name": "SameChainFlowStateUpdated", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "from", "type": "address", "indexed": true}, {"internalType": "address", "name": "to", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}], "type": "event", "name": "Transfer", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "oldVerifier", "type": "address", "indexed": true}, {"internalType": "address", "name": "newVerifier", "type": "address", "indexed": true}], "type": "event", "name": "ZkVerifierUpdated", "anonymous": false}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "acceptAdmin"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "accrualBlockTimestamp", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "accrueInterest"}, {"inputs": [{"internalType": "uint256", "name": "addAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "addReserves"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "admin", "outputs": [{"internalType": "address payable", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "stateMutability": "view", "type": "function", "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "stateMutability": "view", "type": "function", "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "balanceOfUnderlying", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "borrowAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "borrow"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "borrowBalanceCurrent", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "borrowBalanceStored", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "borrowIndex", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "borrowRateMaxMantissa", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "borrowRatePerBlock", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "exchangeRateCurrent", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "exchangeRateStored", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getAccountSnapshot", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getCash", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "interestRateModel", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "borrower", "type": "address"}, {"internalType": "uint256", "name": "repayAmount", "type": "uint256"}, {"internalType": "address", "name": "mTokenCollateral", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "liquidate"}, {"inputs": [{"internalType": "uint256", "name": "mintAmount", "type": "uint256"}, {"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "uint256", "name": "minAmountOut", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "mint"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "operator", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "pendingAdmin", "outputs": [{"internalType": "address payable", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "uint256", "name": "redeemTokens", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "redeem"}, {"inputs": [{"internalType": "uint256", "name": "redeemAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "redeemUnderlying"}, {"inputs": [{"internalType": "uint256", "name": "reduceAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "reduceReserves"}, {"inputs": [{"internalType": "uint256", "name": "repayAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "repay", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "borrower", "type": "address"}, {"internalType": "uint256", "name": "repayAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "repayBehalf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "reserveFactorMantissa", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "rolesOperator", "outputs": [{"internalType": "contract IRoles", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "liquidator", "type": "address"}, {"internalType": "address", "name": "borrower", "type": "address"}, {"internalType": "uint256", "name": "seizeTokens", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "seize"}, {"inputs": [{"internalType": "uint256", "name": "maxMantissa", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "setBorrowRateMaxMantissa"}, {"inputs": [{"internalType": "address", "name": "newInterestRateModel", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setInterestRateModel"}, {"inputs": [{"internalType": "address", "name": "_operator", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setOperator"}, {"inputs": [{"internalType": "address payable", "name": "newPendingAdmin", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setPendingAdmin"}, {"inputs": [{"internalType": "uint256", "name": "newReserveFactorMantissa", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "setReserveFactor"}, {"inputs": [{"internalType": "address", "name": "_roles", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setRolesOperator"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "supplyRatePerBlock", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "contract IERC20", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "sweepToken"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "totalBorrows", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "totalBorrowsCurrent", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "totalReserves", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "totalUnderlying", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "dst", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "src", "type": "address"}, {"internalType": "address", "name": "dst", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "underlying", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}], "devdoc": {"kind": "dev", "methods": {"acceptAdmin()": {"details": "Admin function for pending admin to accept role and update admin"}, "addReserves(uint256)": {"params": {"addAmount": "The amount fo underlying token to add as reserves"}}, "allowance(address,address)": {"params": {"owner": "The address of the token holder", "spender": "The address authorized to spend the tokens"}, "returns": {"_0": "The current remaining number of tokens `spender` can spend"}}, "approve(address,uint256)": {"params": {"amount": "The number of tokens to approve", "spender": "The address authorized to spend tokens"}, "returns": {"_0": "Whether the approval was successful or not"}}, "balanceOf(address)": {"params": {"account": "The account to check for"}}, "balanceOfUnderlying(address)": {"params": {"owner": "The address to query the balance of underlying assets for"}, "returns": {"_0": "The balance of underlying assets owned by `owner`"}}, "borrow(uint256)": {"params": {"borrowAmount": "The amount of the underlying asset to borrow"}}, "borrowBalanceCurrent(address)": {"params": {"account": "The address to query the borrow balance for"}, "returns": {"_0": "The current borrow balance"}}, "borrowBalanceStored(address)": {"params": {"account": "The address to query the stored borrow balance for"}, "returns": {"_0": "The stored borrow balance"}}, "borrowRatePerBlock()": {"returns": {"_0": "The current borrow rate per block, scaled by 1e18"}}, "exchangeRateCurrent()": {"returns": {"_0": "The current exchange rate"}}, "exchangeRateStored()": {"returns": {"_0": "The stored exchange rate"}}, "getAccountSnapshot(address)": {"params": {"account": "The address to query the account snapshot for"}, "returns": {"_0": "(token balance, borrow balance, exchange rate)"}}, "getCash()": {"returns": {"_0": "The total amount of cash"}}, "liquidate(address,uint256,address)": {"params": {"borrower": "The borrower of this mToken to be liquidated", "mTokenCollateral": "The market in which to seize collateral from the borrower", "repayAmount": "The amount of the underlying borrowed asset to repay"}}, "mint(uint256,address,uint256)": {"details": "Accrues interest whether or not the operation succeeds, unless reverted", "params": {"minAmountOut": "The min amounts to be received", "mintAmount": "The amount of the underlying asset to supply", "receiver": "The mTokens receiver"}}, "redeem(uint256)": {"details": "Accrues interest whether or not the operation succeeds, unless reverted", "params": {"redeemTokens": "The number of mTokens to redeem into underlying"}}, "redeemUnderlying(uint256)": {"details": "Accrues interest whether or not the operation succeeds, unless reverted", "params": {"redeemAmount": "The amount of underlying to redeem"}}, "reduceReserves(uint256)": {"params": {"reduceAmount": "Amount of reduction to reserves"}}, "repay(uint256)": {"params": {"repayAmount": "The amount to repay, or type(uint256).max for the full outstanding amount"}}, "repayBehalf(address,uint256)": {"params": {"borrower": "the account with the debt being payed off", "repayAmount": "The amount to repay, or type(uint256).max for the full outstanding amount"}}, "seize(address,address,uint256)": {"details": "Will fail unless called by another mToken during the process of liquidation.  Its absolutely critical to use msg.sender as the borrowed mToken and not a parameter.", "params": {"borrower": "The account having collateral seized", "liquidator": "The account receiving seized collateral", "seizeTokens": "The number of mTokens to seize"}}, "setInterestRateModel(address)": {"details": "Admin function to accrue interest and update the interest rate model", "params": {"newInterestRateModel": "the new interest rate model to use"}}, "setOperator(address)": {"details": "Admin function to set a new operator"}, "setPendingAdmin(address)": {"details": "Admin function to begin change of admin. The newPendingAdmin must call `_acceptAdmin` to finalize the transfer.", "params": {"newPendingAdmin": "New pending admin."}}, "setReserveFactor(uint256)": {"details": "Admin function to accrue interest and set a new reserve factor"}, "setRolesOperator(address)": {"details": "Admin function to set a new operator"}, "supplyRatePerBlock()": {"returns": {"_0": "The current supply rate per block, scaled by 1e18"}}, "sweepToken(address,uint256)": {"params": {"token": "The address of the ERC-20 token to sweep"}}, "totalBorrowsCurrent()": {"returns": {"_0": "The total amount of borrows"}}, "transfer(address,uint256)": {"params": {"amount": "The number of tokens to transfer", "dst": "The address of the recipient"}, "returns": {"_0": "Whether the transfer was successful or not"}}, "transferFrom(address,address,uint256)": {"params": {"amount": "The number of tokens to transfer", "dst": "The address to which tokens are transferred", "src": "The address from which tokens are transferred"}, "returns": {"_0": "Whether the transfer was successful or not"}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"acceptAdmin()": {"notice": "Accepts transfer of admin rights. msg.sender must be pendingAdmin"}, "accrualBlockTimestamp()": {"notice": "Block timestamp that interest was last accrued at"}, "accrueInterest()": {"notice": "Accrues interest on the contract's outstanding loans"}, "addReserves(uint256)": {"notice": "The sender adds to reserves."}, "admin()": {"notice": "Administrator for this contract"}, "allowance(address,address)": {"notice": "Returns the current allowance the `spender` has from the `owner`"}, "approve(address,uint256)": {"notice": "Approves `spender` to spend `amount` tokens on behalf of the caller"}, "balanceOf(address)": {"notice": "Returns the value of tokens owned by `account`."}, "balanceOfUnderlying(address)": {"notice": "Returns the underlying asset balance of the `owner`"}, "borrow(uint256)": {"notice": "Sender borrows assets from the protocol to their own address"}, "borrowBalanceCurrent(address)": {"notice": "Returns the current borrow balance for `account`, accounting for interest"}, "borrowBalanceStored(address)": {"notice": "Returns the stored borrow balance for `account`, without accruing interest"}, "borrowIndex()": {"notice": "Accumulator of the total earned interest rate since the opening of the market"}, "borrowRateMaxMantissa()": {"notice": "Maximum borrow rate that can ever be applied"}, "borrowRatePerBlock()": {"notice": "Returns the current borrow rate per block"}, "decimals()": {"notice": "EIP-20 token decimals for this token"}, "exchangeRateCurrent()": {"notice": "Returns the current exchange rate, with interest accrued"}, "exchangeRateStored()": {"notice": "Returns the stored exchange rate, without accruing interest"}, "getAccountSnapshot(address)": {"notice": "Returns the snapshot of account details for the given `account`"}, "getCash()": {"notice": "Returns the total amount of available cash in the contract"}, "interestRateModel()": {"notice": "Model which tells what the current interest rate should be"}, "liquidate(address,uint256,address)": {"notice": "The sender liquidates the borrowers collateral.  The collateral seized is transferred to the liquidator."}, "mint(uint256,address,uint256)": {"notice": "Sender supplies assets into the market and receives mTokens in exchange"}, "name()": {"notice": "EIP-20 token name for this token"}, "operator()": {"notice": "Contract which oversees inter-mToken operations"}, "pendingAdmin()": {"notice": "Pending administrator for this contract"}, "redeem(uint256)": {"notice": "Sender redeems mTokens in exchange for the underlying asset"}, "redeemUnderlying(uint256)": {"notice": "Sender redeems mTokens in exchange for a specified amount of underlying asset"}, "reduceReserves(uint256)": {"notice": "Accrues interest and reduces reserves by transferring to admin"}, "repay(uint256)": {"notice": "Sender repays their own borrow"}, "repayBehalf(address,uint256)": {"notice": "Sender repays a borrow belonging to borrower"}, "reserveFactorMantissa()": {"notice": "Fraction of interest currently set aside for reserves"}, "rolesOperator()": {"notice": "Roles manager"}, "seize(address,address,uint256)": {"notice": "Transfers collateral tokens (this market) to the liquidator."}, "setInterestRateModel(address)": {"notice": "accrues interest and updates the interest rate model using _setInterestRateModelFresh"}, "setOperator(address)": {"notice": "Sets a new Operator for the market"}, "setPendingAdmin(address)": {"notice": "Begins transfer of admin rights. The newPendingAdmin must call `_acceptAdmin` to finalize the transfer."}, "setReserveFactor(uint256)": {"notice": "accrues interest and sets a new reserve factor for the protocol using _setReserveFactorFresh"}, "setRolesOperator(address)": {"notice": "Sets a new Operator for the market"}, "supplyRatePerBlock()": {"notice": "Returns the current supply rate per block"}, "sweepToken(address,uint256)": {"notice": "A public function to sweep accidental ERC-20 transfers to this contract. Tokens are sent to admin (timelock)"}, "symbol()": {"notice": "EIP-20 token symbol for this token"}, "totalBorrows()": {"notice": "Total amount of outstanding borrows of the underlying in this market"}, "totalBorrowsCurrent()": {"notice": "Returns the total amount of borrows, accounting for interest"}, "totalReserves()": {"notice": "Total amount of reserves of the underlying held in this market"}, "totalSupply()": {"notice": "Returns the value of tokens in existence."}, "totalUnderlying()": {"notice": "Returns the amount of underlying tokens"}, "transfer(address,uint256)": {"notice": "Transfers `amount` tokens to the `dst` address"}, "transferFrom(address,address,uint256)": {"notice": "Transfers `amount` tokens from the `src` address to the `dst` address"}, "underlying()": {"notice": "Underlying asset for this mToken"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/mToken/mErc20.sol": "mErc20"}, "evmVersion": "london", "libraries": {}}, "sources": {"lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0xc6a8ff0ea489379b61faa647490411b80102578440ab9d84e9a957cc12164e70", "urls": ["bzz-raw://0ea104e577e63faea3b69c415637e99e755dcbf64c5833d7140c35a714d6d90c", "dweb:/ipfs/Qmau6x4Ns9XdyynRCNNp3RhLqijJjFm7z5fyZazfYFGYdq"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol": {"keccak256": "0x6008dabfe393240d73d7dd7688033f72740d570aa422254d29a7dce8568f3aff", "urls": ["bzz-raw://f5196ec75139918c6c7bb4251b36395e668f1fa6d206beba7e7520e74913940d", "dweb:/ipfs/QmSyqjksXxmm2mCG6qRd1yuwLykypkSVBbnBnGqJRcuJMi"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol": {"keccak256": "0x37bb49513c49c87c4642a891b13b63571bc87013dde806617aa1efb54605f386", "urls": ["bzz-raw://b3036b3a83b7c48f96641f2a9002b9f2dcb6a5958dd670894ada21ae8229b3d0", "dweb:/ipfs/QmUNfSBdoVtjhETaUJCYcaC7pTMgbhht926tJ2uXJbiVd3"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Address.sol": {"keccak256": "0xaf28a975a78550e45f65e559a3ad6a5ad43b9b8a37366999abd1b7084eb70721", "urls": ["bzz-raw://b7bd24e224f67f65bfadf85dc2929fa965456bb2415478bd0125471b5ce35245", "dweb:/ipfs/QmRaydGr8BTHs1kvaZfsNU69pKzUAGFrvABn1KiRSbE51y"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/ReentrancyGuard.sol": {"keccak256": "0xf980daa263b661ab8ddee7d4fd833c7da7e7995e2c359ff1f17e67e4112f2236", "urls": ["bzz-raw://7448ab095d6940130bcf76ba47a2eab14148c83119523b93dd89f6d84edd6c02", "dweb:/ipfs/QmawrZ4voKQjH3oomXT3Kuheb3Mnmo2VvVpxg8Ne5UJUrd"], "license": "MIT"}, "src/interfaces/IBlacklister.sol": {"keccak256": "0x47ed20135507a9d16473027cbee18de5d952846bada761c6248b91c75cd88be1", "urls": ["bzz-raw://91a886b95dd578156f9b6cec3236e4751aae8a38e5d15e0d6045217c6fbd3d63", "dweb:/ipfs/QmRNjz7sGTuMkQNZhwwcg5DNN2eeqTZSAdZmxWxbx66CHU"], "license": "BSL-1.1"}, "src/interfaces/IInterestRateModel.sol": {"keccak256": "0xdc8386be37a06d93fb0967be32e465f3b1f941ec9ce2f90984a5c70488cddb1e", "urls": ["bzz-raw://de2bf96a7d0945f58b00e9fc867ac00fc4a09480e9e27c1a800604295320b769", "dweb:/ipfs/Qmf5sAgA58GjVxeipJNhP4euxwsZ9FS9CgsWTJ319UmCUE"], "license": "BSL-1.1"}, "src/interfaces/IOperator.sol": {"keccak256": "0x0ae3d63eb07518da5a4fd6de7b013b6f55c87defb31900548378cffc70a51f72", "urls": ["bzz-raw://1d9710c4ac43cb0563449aa519501b4ed18a331c90d0bc7eb79cdc56407bad8a", "dweb:/ipfs/Qmafe9Wup2GF9pD83CLWUrRait93vwvReqU1C7EHQhY7WV"], "license": "BSL-1.1"}, "src/interfaces/IRoles.sol": {"keccak256": "0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365", "urls": ["bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c", "dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj"], "license": "BSL-1.1"}, "src/interfaces/ImErc20.sol": {"keccak256": "0x86a1dcaf0d188107ceae7c5eb0911ac63feafbbeda6d0686e4f9950a51d17888", "urls": ["bzz-raw://d7dd8e0fcfd68501d9f40e37e1c95d8cb653d65626aeeeb5492b73c0e967e6cb", "dweb:/ipfs/QmcadG9QuHtAX5qxTKzNr6FMaXSgijr542tFUVTLBkYhAF"], "license": "BSL-1.1"}, "src/interfaces/ImToken.sol": {"keccak256": "0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f", "urls": ["bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36", "dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL"], "license": "BSL-1.1"}, "src/mToken/mErc20.sol": {"keccak256": "0x46aa77f6808c1ca56c7d51b4822bc03d26d036151b4eeb324a3dbdc52bc90643", "urls": ["bzz-raw://4681c86b025cb7d0872c003146b4e6d56b2e86387c832134f4224fc9d559218c", "dweb:/ipfs/QmWM9zVEmNWYSYEvGPd8jwSioG8zBC3hQnCEHbup1gCCLe"], "license": "BSL-1.1"}, "src/mToken/mToken.sol": {"keccak256": "0xeefa3394ae7a01c38bc97404ca6375a497e0bce2a8ae3f83feb4c5bd681aaf43", "urls": ["bzz-raw://cd94a416031e33c169d36692a661899dc9697b8c543bdb8c051f70a3e7eced2c", "dweb:/ipfs/QmRBoo7LQ5nbwLpBjB9yEEib8RM5i9yQjfeE7FELHXvBBk"], "license": "BSL-1.1"}, "src/mToken/mTokenConfiguration.sol": {"keccak256": "0x4ac1b80b93e3b13ce568f859c1fc4dd54360578d6055027819fee3e6d0b2b83f", "urls": ["bzz-raw://e27a83d36a038105631a1ce53f92d225b9949a2cc6aed6d898c736850c10676a", "dweb:/ipfs/QmZhvhCfZsDxvyYJ1x9TNSfnHKrxpbgLq3qr7hJiL3XpZa"], "license": "BSL-1.1"}, "src/mToken/mTokenStorage.sol": {"keccak256": "0x42236cd9e37c2eef2d317f05510bbe21cf5174813ee40f9a6fcbaf84d4fed2f7", "urls": ["bzz-raw://2679166e1ca0e5d21294447566bba7549900a8f9a9b1882c8c2895a9751a1792", "dweb:/ipfs/Qmb49FVvre1He18e6ABHjNJ2yzCPdkYeyzSwMXixZ7fUw3"], "license": "BSL-1.1"}, "src/utils/ExponentialNoError.sol": {"keccak256": "0xa97178117d8c32059d55dd2224e5514c54b31f896e565c3834cab3d7502b1bb6", "urls": ["bzz-raw://1cecc0713709f57a5bb7f64d2fa06b9437a42752fa3fed5d3c0b19d82aa56ef4", "dweb:/ipfs/QmQqBCZkzmZxomXLr3b1tHUV55n8EPk3wAUsXgojYSpR41"], "license": "BSL-1.1"}}, "version": 1}, "id": 175}