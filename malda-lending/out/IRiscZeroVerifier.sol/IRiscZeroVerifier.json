{"abi": [{"type": "function", "name": "verify", "inputs": [{"name": "seal", "type": "bytes", "internalType": "bytes"}, {"name": "imageId", "type": "bytes32", "internalType": "bytes32"}, {"name": "journalDigest", "type": "bytes32", "internalType": "bytes32"}], "outputs": [], "stateMutability": "view"}, {"type": "function", "name": "verifyIntegrity", "inputs": [{"name": "receipt", "type": "tuple", "internalType": "struct Receipt", "components": [{"name": "seal", "type": "bytes", "internalType": "bytes"}, {"name": "claimDigest", "type": "bytes32", "internalType": "bytes32"}]}], "outputs": [], "stateMutability": "view"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"verify(bytes,bytes32,bytes32)": "ab750e75", "verifyIntegrity((bytes,bytes32))": "1599ead5"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"seal\",\"type\":\"bytes\"},{\"internalType\":\"bytes32\",\"name\":\"imageId\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"journalDigest\",\"type\":\"bytes32\"}],\"name\":\"verify\",\"outputs\":[],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"bytes\",\"name\":\"seal\",\"type\":\"bytes\"},{\"internalType\":\"bytes32\",\"name\":\"claimDigest\",\"type\":\"bytes32\"}],\"internalType\":\"struct Receipt\",\"name\":\"receipt\",\"type\":\"tuple\"}],\"name\":\"verifyIntegrity\",\"outputs\":[],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{\"verify(bytes,bytes32,bytes32)\":{\"details\":\"This method additionally ensures that the input hash is all-zeros (i.e. no committed input), the exit code is (Halted, 0), and there are no assumptions (i.e. the receipt is unconditional).\",\"params\":{\"imageId\":\"The identifier for the guest program.\",\"journalDigest\":\"The SHA-256 digest of the journal bytes.\",\"seal\":\"The encoded cryptographic proof (i.e. SNARK).\"}},\"verifyIntegrity((bytes,bytes32))\":{\"params\":{\"receipt\":\"The receipt to be verified.\"}}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"verify(bytes,bytes32,bytes32)\":{\"notice\":\"Verify that the given seal is a valid RISC Zero proof of execution with the     given image ID and journal digest. Reverts on failure.\"},\"verifyIntegrity((bytes,bytes32))\":{\"notice\":\"Verify that the given receipt is a valid RISC Zero receipt, ensuring the `seal` is valid a cryptographic proof of the execution with the given `claim`. Reverts on failure.\"}},\"notice\":\"Verifier interface for RISC Zero receipts of execution.\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"lib/risc0-ethereum/contracts/src/IRiscZeroVerifier.sol\":\"IRiscZeroVerifier\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"lib/risc0-ethereum/contracts/src/IRiscZeroVerifier.sol\":{\"keccak256\":\"0xae73e91e4d2829b0a88176584d876382b4b7540ff07c62cb947cdf9ea43d5cf3\",\"license\":\"Apache-2.0\",\"urls\":[\"bzz-raw://f2dd7da39a6f9b100fc1dcde565d799462cdc7d27722d2265317c3a01619c818\",\"dweb:/ipfs/Qmd3k3kcvrtfkLqVza4jHodbFH8wvq5vmfPtwekBKtPjmz\"]},\"lib/risc0-ethereum/contracts/src/Util.sol\":{\"keccak256\":\"0x230a9a58bb2b0c68d4921ecc3672783f941890a2fa3a238dbe5188cfb7658b82\",\"license\":\"Apache-2.0\",\"urls\":[\"bzz-raw://586cb5296c564761609ca8aaae5b1fbbdb19ab1e174f7197f602572d9e82b70c\",\"dweb:/ipfs/QmWxDmucSfg3Wgdk1rVcheDHwkziTsXWvKpKSCnxtpQBqg\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "bytes", "name": "seal", "type": "bytes"}, {"internalType": "bytes32", "name": "imageId", "type": "bytes32"}, {"internalType": "bytes32", "name": "journalDigest", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "verify"}, {"inputs": [{"internalType": "struct Receipt", "name": "receipt", "type": "tuple", "components": [{"internalType": "bytes", "name": "seal", "type": "bytes"}, {"internalType": "bytes32", "name": "claimDigest", "type": "bytes32"}]}], "stateMutability": "view", "type": "function", "name": "verifyIntegrity"}], "devdoc": {"kind": "dev", "methods": {"verify(bytes,bytes32,bytes32)": {"details": "This method additionally ensures that the input hash is all-zeros (i.e. no committed input), the exit code is (Halted, 0), and there are no assumptions (i.e. the receipt is unconditional).", "params": {"imageId": "The identifier for the guest program.", "journalDigest": "The SHA-256 digest of the journal bytes.", "seal": "The encoded cryptographic proof (i.e. SNARK)."}}, "verifyIntegrity((bytes,bytes32))": {"params": {"receipt": "The receipt to be verified."}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"verify(bytes,bytes32,bytes32)": {"notice": "Verify that the given seal is a valid RISC Zero proof of execution with the     given image ID and journal digest. Reverts on failure."}, "verifyIntegrity((bytes,bytes32))": {"notice": "Verify that the given receipt is a valid RISC Zero receipt, ensuring the `seal` is valid a cryptographic proof of the execution with the given `claim`. Reverts on failure."}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"lib/risc0-ethereum/contracts/src/IRiscZeroVerifier.sol": "IRiscZeroVerifier"}, "evmVersion": "london", "libraries": {}}, "sources": {"lib/risc0-ethereum/contracts/src/IRiscZeroVerifier.sol": {"keccak256": "0xae73e91e4d2829b0a88176584d876382b4b7540ff07c62cb947cdf9ea43d5cf3", "urls": ["bzz-raw://f2dd7da39a6f9b100fc1dcde565d799462cdc7d27722d2265317c3a01619c818", "dweb:/ipfs/Qmd3k3kcvrtfkLqVza4jHodbFH8wvq5vmfPtwekBKtPjmz"], "license": "Apache-2.0"}, "lib/risc0-ethereum/contracts/src/Util.sol": {"keccak256": "0x230a9a58bb2b0c68d4921ecc3672783f941890a2fa3a238dbe5188cfb7658b82", "urls": ["bzz-raw://586cb5296c564761609ca8aaae5b1fbbdb19ab1e174f7197f602572d9e82b70c", "dweb:/ipfs/QmWxDmucSfg3Wgdk1rVcheDHwkziTsXWvKpKSCnxtpQBqg"], "license": "Apache-2.0"}}, "version": 1}, "id": 57}