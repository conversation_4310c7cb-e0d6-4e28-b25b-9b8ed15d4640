{"abi": [{"type": "function", "name": "IS_SCRIPT", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "run", "inputs": [{"name": "rolesContract", "type": "address", "internalType": "address"}, {"name": "receiver", "type": "address", "internalType": "address"}, {"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "status", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}], "bytecode": {"object": "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", "sourceMap": "154:682:72:-:0;;;3126:44:3;;;-1:-1:-1;;849:28:1;;;;;154:682:72;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "154:682:72:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;187:647;;;;;;:::i;:::-;;:::i;:::-;;849:28:1;;;;;;;;;;;;;;;943:14:242;;936:22;918:41;;906:2;891:18;849:28:1;;;;;;;187:647:72;307:25;;-1:-1:-1;;;307:25:72;;1172:2:242;307:25:72;;;1154:21:242;1211:2;1191:18;;;1184:30;-1:-1:-1;;;1230:18:242;;;1223:41;293:11:72;;336:42:0;;307:10:72;;1281:18:242;;307:25:72;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;293:39;;343:44;;;;;;;;;;;;;;-1:-1:-1;;;343:44:72;;;378:8;343:11;:44::i;:::-;402:49;;-1:-1:-1;;;402:49:72;;-1:-1:-1;;;;;1691:32:242;;;402:49:72;;;1673:51:242;1740:18;;;1733:34;;;402:59:72;;;;:33;;;;;;1646:18:242;;402:49:72;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:59;;;398:141;;477:31;;;;;;;;;;;;;;-1:-1:-1;;;477:31:72;;;:11;:31::i;:::-;522:7;;;398:141;549:22;;-1:-1:-1;;;549:22:72;;;;;2174:25:242;;;336:42:0;;549:17:72;;2147:18:242;;549:22:72;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;581:53:72;;-1:-1:-1;;;581:53:72;;-1:-1:-1;;;;;2424:32:242;;;581:53:72;;;2406:51:242;2473:18;;;2466:34;;;2543:14;;2536:22;2516:18;;;2509:50;581:29:72;;;-1:-1:-1;581:29:72;;-1:-1:-1;2379:18:242;;581:53:72;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;336:42:0;-1:-1:-1;;;;;644:16:72;;:18;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;677:6;673:155;;;699:42;;;;;;;;;;;;;;-1:-1:-1;;;699:42:72;;;732:8;699:11;:42::i;:::-;673:155;;;772:45;;;;;;;;;;;;;;-1:-1:-1;;;772:45:72;;;808:8;772:11;:45::i;:::-;283:551;187:647;;;;;:::o;7740:145:16:-;7807:71;7870:2;7874;7823:54;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;7823:54:16;;;;;;;;;;;;;;-1:-1:-1;;;;;7823:54:16;-1:-1:-1;;;7823:54:16;;;7807:15;:71::i;:::-;7740:145;;:::o;6191:121::-;6246:59;6301:2;6262:42;;;;;;;;:::i;:::-;;;;-1:-1:-1;;6262:42:16;;;;;;;;;;;;;;-1:-1:-1;;;;;6262:42:16;-1:-1:-1;;;6262:42:16;;;6246:15;:59::i;:::-;6191:121;:::o;851:129::-;922:51;965:7;265:22;131:42;265:40;;594:1;571;541:7;535:14;510:2;501:7;497:16;461:14;434:5;402:211;381:246;367:270;180:463;:::o;14:173:242:-;82:20;;-1:-1:-1;;;;;131:31:242;;121:42;;111:70;;177:1;174;167:12;111:70;14:173;;;:::o;192:118::-;278:5;271:13;264:21;257:5;254:32;244:60;;300:1;297;290:12;315:458;398:6;406;414;422;475:3;463:9;454:7;450:23;446:33;443:53;;;492:1;489;482:12;443:53;515:29;534:9;515:29;:::i;:::-;505:39;;563:38;597:2;586:9;582:18;563:38;:::i;:::-;553:48;;648:2;637:9;633:18;620:32;610:42;;702:2;691:9;687:18;674:32;715:28;737:5;715:28;:::i;:::-;315:458;;;;-1:-1:-1;315:458:242;;-1:-1:-1;;315:458:242:o;1310:184::-;1380:6;1433:2;1421:9;1412:7;1408:23;1404:32;1401:52;;;1449:1;1446;1439:12;1401:52;-1:-1:-1;1472:16:242;;1310:184;-1:-1:-1;1310:184:242:o;1778:245::-;1845:6;1898:2;1886:9;1877:7;1873:23;1869:32;1866:52;;;1914:1;1911;1904:12;1866:52;1946:9;1940:16;1965:28;1987:5;1965:28;:::i;:::-;2012:5;1778:245;-1:-1:-1;;;1778:245:242:o;2570:400::-;2612:3;2650:5;2644:12;2677:6;2672:3;2665:19;2702:1;2712:139;2726:6;2723:1;2720:13;2712:139;;;2834:4;2819:13;;;2815:24;;2809:31;2789:11;;;2785:22;;2778:63;2741:12;2712:139;;;2716:3;2896:1;2889:4;2880:6;2875:3;2871:16;2867:27;2860:38;2959:4;2952:2;2948:7;2943:2;2935:6;2931:15;2927:29;2922:3;2918:39;2914:50;2907:57;;;2570:400;;;;:::o;2975:317::-;3152:2;3141:9;3134:21;3115:4;3172:45;3213:2;3202:9;3198:18;3190:6;3172:45;:::i;:::-;3164:53;;3282:1;3278;3273:3;3269:11;3265:19;3257:6;3253:32;3248:2;3237:9;3233:18;3226:60;2975:317;;;;;:::o;3297:220::-;3446:2;3435:9;3428:21;3409:4;3466:45;3507:2;3496:9;3492:18;3484:6;3466:45;:::i", "linkReferences": {}}, "methodIdentifiers": {"IS_SCRIPT()": "f8ccbf47", "run(address,address,bytes32,bool)": "038ac0e7"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"IS_SCRIPT\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"rolesContract\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"bool\",\"name\":\"status\",\"type\":\"bool\"}],\"name\":\"run\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"script/configuration/SetRole.s.sol\":\"SetRole\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"lib/forge-std/src/Base.sol\":{\"keccak256\":\"0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d\",\"dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z\"]},\"lib/forge-std/src/Script.sol\":{\"keccak256\":\"0xc942e27c7baae499beb01afbbae99f24d42af9a6e4aae675bc6901b704aa8e9b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0456008adf68947247f358b62863af4a8e349549d2260f2ff9569ff0e3cf5c98\",\"dweb:/ipfs/QmdviSUj2i7o3TPN5vd2xocqGMFVqjUzaiJTZRYyPxyHPx\"]},\"lib/forge-std/src/StdChains.sol\":{\"keccak256\":\"0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351\",\"dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm\"]},\"lib/forge-std/src/StdCheats.sol\":{\"keccak256\":\"0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41\",\"dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK\"]},\"lib/forge-std/src/StdConstants.sol\":{\"keccak256\":\"0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc\",\"dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r\"]},\"lib/forge-std/src/StdJson.sol\":{\"keccak256\":\"0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974\",\"dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3\"]},\"lib/forge-std/src/StdMath.sol\":{\"keccak256\":\"0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92\",\"dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC\"]},\"lib/forge-std/src/StdStorage.sol\":{\"keccak256\":\"0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57\",\"dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ\"]},\"lib/forge-std/src/StdStyle.sol\":{\"keccak256\":\"0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8\",\"dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK\"]},\"lib/forge-std/src/StdUtils.sol\":{\"keccak256\":\"0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138\",\"dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776\"]},\"lib/forge-std/src/Vm.sol\":{\"keccak256\":\"0xaae88142a348064677cf81169ea0ca706be3e99e2a0f13a01878ff8d52cf6b25\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://da4fb0ed1b31381c7e60ee386acc72796e8a8325c83739ec9a3678e2f841073c\",\"dweb:/ipfs/Qmb4MU6FpAi8Wt5kUQ3wa7ooHnfMVbdd9zuvnqbTehLmxJ\"]},\"lib/forge-std/src/console.sol\":{\"keccak256\":\"0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57\",\"dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP\"]},\"lib/forge-std/src/console2.sol\":{\"keccak256\":\"0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d\",\"dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ\"]},\"lib/forge-std/src/interfaces/IMulticall3.sol\":{\"keccak256\":\"0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0\",\"dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2\"]},\"lib/forge-std/src/safeconsole.sol\":{\"keccak256\":\"0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab\",\"dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3\"]},\"lib/openzeppelin-contracts/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"script/configuration/SetRole.s.sol\":{\"keccak256\":\"0xe571c5f31438b2b11399a359dd522eab25840a01d1858bda360d9851c29b37d9\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://05c10e306712290c5cb0f605ff8db2fd72f117473e65b3c6a1d97d18e4349464\",\"dweb:/ipfs/QmZrC8rdmSnCZQBhEFumcUN2zR5vqpRpqYPLjEV6wTzu6Y\"]},\"src/Roles.sol\":{\"keccak256\":\"0xaa5bbf73d5d2ba654b7f0fe960ae9e17282d959fc559d09c2222b78dad1911b0\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://e683df4c25f2f8f725915d68e8ee9e0871c0d93ad592b0ca985b1829e460cabc\",\"dweb:/ipfs/QmTsQgUKXCtzucNF9DSq1wKVfeqSUeoKPMFCkM8hNLxTmV\"]},\"src/interfaces/IRoles.sol\":{\"keccak256\":\"0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c\",\"dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "view", "type": "function", "name": "IS_SCRIPT", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "rolesContract", "type": "address"}, {"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "bool", "name": "status", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "run"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"script/configuration/SetRole.s.sol": "SetRole"}, "evmVersion": "london", "libraries": {}}, "sources": {"lib/forge-std/src/Base.sol": {"keccak256": "0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd", "urls": ["bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d", "dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z"], "license": "MIT"}, "lib/forge-std/src/Script.sol": {"keccak256": "0xc942e27c7baae499beb01afbbae99f24d42af9a6e4aae675bc6901b704aa8e9b", "urls": ["bzz-raw://0456008adf68947247f358b62863af4a8e349549d2260f2ff9569ff0e3cf5c98", "dweb:/ipfs/QmdviSUj2i7o3TPN5vd2xocqGMFVqjUzaiJTZRYyPxyHPx"], "license": "MIT"}, "lib/forge-std/src/StdChains.sol": {"keccak256": "0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e", "urls": ["bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351", "dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm"], "license": "MIT"}, "lib/forge-std/src/StdCheats.sol": {"keccak256": "0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746", "urls": ["bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41", "dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK"], "license": "MIT"}, "lib/forge-std/src/StdConstants.sol": {"keccak256": "0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534", "urls": ["bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc", "dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r"], "license": "MIT"}, "lib/forge-std/src/StdJson.sol": {"keccak256": "0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500", "urls": ["bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974", "dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3"], "license": "MIT"}, "lib/forge-std/src/StdMath.sol": {"keccak256": "0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2", "urls": ["bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92", "dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC"], "license": "MIT"}, "lib/forge-std/src/StdStorage.sol": {"keccak256": "0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc", "urls": ["bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57", "dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ"], "license": "MIT"}, "lib/forge-std/src/StdStyle.sol": {"keccak256": "0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d", "urls": ["bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8", "dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK"], "license": "MIT"}, "lib/forge-std/src/StdUtils.sol": {"keccak256": "0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737", "urls": ["bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138", "dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776"], "license": "MIT"}, "lib/forge-std/src/Vm.sol": {"keccak256": "0xaae88142a348064677cf81169ea0ca706be3e99e2a0f13a01878ff8d52cf6b25", "urls": ["bzz-raw://da4fb0ed1b31381c7e60ee386acc72796e8a8325c83739ec9a3678e2f841073c", "dweb:/ipfs/Qmb4MU6FpAi8Wt5kUQ3wa7ooHnfMVbdd9zuvnqbTehLmxJ"], "license": "MIT OR Apache-2.0"}, "lib/forge-std/src/console.sol": {"keccak256": "0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5", "urls": ["bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57", "dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP"], "license": "MIT"}, "lib/forge-std/src/console2.sol": {"keccak256": "0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f", "urls": ["bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d", "dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ"], "license": "MIT"}, "lib/forge-std/src/interfaces/IMulticall3.sol": {"keccak256": "0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a", "urls": ["bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0", "dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2"], "license": "MIT"}, "lib/forge-std/src/safeconsole.sol": {"keccak256": "0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11", "urls": ["bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab", "dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/access/Ownable.sol": {"keccak256": "0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb", "urls": ["bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6", "dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "script/configuration/SetRole.s.sol": {"keccak256": "0xe571c5f31438b2b11399a359dd522eab25840a01d1858bda360d9851c29b37d9", "urls": ["bzz-raw://05c10e306712290c5cb0f605ff8db2fd72f117473e65b3c6a1d97d18e4349464", "dweb:/ipfs/QmZrC8rdmSnCZQBhEFumcUN2zR5vqpRpqYPLjEV6wTzu6Y"], "license": "BSL-1.1"}, "src/Roles.sol": {"keccak256": "0xaa5bbf73d5d2ba654b7f0fe960ae9e17282d959fc559d09c2222b78dad1911b0", "urls": ["bzz-raw://e683df4c25f2f8f725915d68e8ee9e0871c0d93ad592b0ca985b1829e460cabc", "dweb:/ipfs/QmTsQgUKXCtzucNF9DSq1wKVfeqSUeoKPMFCkM8hNLxTmV"], "license": "BSL-1.1"}, "src/interfaces/IRoles.sol": {"keccak256": "0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365", "urls": ["bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c", "dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj"], "license": "BSL-1.1"}}, "version": 1}, "id": 72}