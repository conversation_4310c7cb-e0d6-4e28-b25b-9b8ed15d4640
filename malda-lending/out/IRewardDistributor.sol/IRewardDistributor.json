{"abi": [{"type": "function", "name": "claim", "inputs": [{"name": "holders", "type": "address[]", "internalType": "address[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "getBlockTimestamp", "inputs": [], "outputs": [{"name": "", "type": "uint32", "internalType": "uint32"}], "stateMutability": "view"}, {"type": "function", "name": "getRewardTokens", "inputs": [], "outputs": [{"name": "", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "isRewardToken", "inputs": [{"name": "_token", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "notifyBorrowIndex", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "notify<PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}, {"name": "borrower", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "notifySupplier", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}, {"name": "supplier", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "notifySupplyIndex", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "operator", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "event", "name": "BorrowIndexNotified", "inputs": [{"name": "rewardToken", "type": "address", "indexed": true, "internalType": "address"}, {"name": "mToken", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "BorrowSpeedUpdated", "inputs": [{"name": "rewardToken", "type": "address", "indexed": true, "internalType": "address"}, {"name": "mToken", "type": "address", "indexed": true, "internalType": "address"}, {"name": "borrowSpeed", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "OperatorSet", "inputs": [{"name": "oldOperator", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newOperator", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "RewardAccrued", "inputs": [{"name": "rewardToken", "type": "address", "indexed": true, "internalType": "address"}, {"name": "user", "type": "address", "indexed": true, "internalType": "address"}, {"name": "deltaAccrued", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "totalAccrued", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "RewardGranted", "inputs": [{"name": "rewardToken", "type": "address", "indexed": true, "internalType": "address"}, {"name": "user", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "SupplyIndexNotified", "inputs": [{"name": "rewardToken", "type": "address", "indexed": true, "internalType": "address"}, {"name": "mToken", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "SupplySpeedUpdated", "inputs": [{"name": "rewardToken", "type": "address", "indexed": true, "internalType": "address"}, {"name": "mToken", "type": "address", "indexed": true, "internalType": "address"}, {"name": "supplySpeed", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "WhitelistedToken", "inputs": [{"name": "token", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"claim(address[])": "318d9e5d", "getBlockTimestamp()": "796b89b9", "getRewardTokens()": "c4f59f9b", "isRewardToken(address)": "b5fd73f8", "notifyBorrowIndex(address)": "5ce65fe9", "notifyBorrower(address,address)": "eb8f2806", "notifySupplier(address,address)": "45a49d3c", "notifySupplyIndex(address)": "e86b2fbe", "operator()": "570ca735"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"rewardToken\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"}],\"name\":\"BorrowIndexNotified\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"rewardToken\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"borrowSpeed\",\"type\":\"uint256\"}],\"name\":\"BorrowSpeedUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"oldOperator\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOperator\",\"type\":\"address\"}],\"name\":\"OperatorSet\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"rewardToken\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"deltaAccrued\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"totalAccrued\",\"type\":\"uint256\"}],\"name\":\"RewardAccrued\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"rewardToken\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"RewardGranted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"rewardToken\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"}],\"name\":\"SupplyIndexNotified\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"rewardToken\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"supplySpeed\",\"type\":\"uint256\"}],\"name\":\"SupplySpeedUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"WhitelistedToken\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"holders\",\"type\":\"address[]\"}],\"name\":\"claim\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getBlockTimestamp\",\"outputs\":[{\"internalType\":\"uint32\",\"name\":\"\",\"type\":\"uint32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getRewardTokens\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_token\",\"type\":\"address\"}],\"name\":\"isRewardToken\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"}],\"name\":\"notifyBorrowIndex\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"}],\"name\":\"notifyBorrower\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"supplier\",\"type\":\"address\"}],\"name\":\"notifySupplier\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"}],\"name\":\"notifySupplyIndex\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"operator\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{\"claim(address[])\":{\"params\":{\"holders\":\"the accounts to claim for\"}},\"isRewardToken(address)\":{\"params\":{\"_token\":\"the token to check for\"}}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"claim(address[])\":{\"notice\":\"Claim tokens for `holders\"},\"getBlockTimestamp()\":{\"notice\":\"Get block timestamp\"},\"getRewardTokens()\":{\"notice\":\"Added reward tokens\"},\"isRewardToken(address)\":{\"notice\":\"Flag to check if reward token added before\"},\"notifyBorrowIndex(address)\":{\"notice\":\"Notifies borrow index\"},\"notifyBorrower(address,address)\":{\"notice\":\"Notifies borrower\"},\"notifySupplier(address,address)\":{\"notice\":\"Notifies supplier\"},\"notifySupplyIndex(address)\":{\"notice\":\"Notifies supply index\"},\"operator()\":{\"notice\":\"The operator that rewards are distributed to\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/IRewardDistributor.sol\":\"IRewardDistributor\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"src/interfaces/IRewardDistributor.sol\":{\"keccak256\":\"0xd39c4db6e2a85e90db82e20685d4b235966d993c3407e1c0fb52add6421b86df\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://e865fe1569ec1ac79cd924a9aa2e2b43754555e510526f6cd030395e9923f01d\",\"dweb:/ipfs/QmXcshgzCxLKBZJYGuQviEamruhmwXWNJ3ZYLxazWBahuc\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "rewardToken", "type": "address", "indexed": true}, {"internalType": "address", "name": "mToken", "type": "address", "indexed": true}], "type": "event", "name": "BorrowIndexNotified", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "rewardToken", "type": "address", "indexed": true}, {"internalType": "address", "name": "mToken", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "borrowSpeed", "type": "uint256", "indexed": false}], "type": "event", "name": "BorrowSpeedUpdated", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "oldOperator", "type": "address", "indexed": true}, {"internalType": "address", "name": "newOperator", "type": "address", "indexed": true}], "type": "event", "name": "OperatorSet", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "rewardToken", "type": "address", "indexed": true}, {"internalType": "address", "name": "user", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "deltaAccrued", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "totalAccrued", "type": "uint256", "indexed": false}], "type": "event", "name": "RewardAccrued", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "rewardToken", "type": "address", "indexed": true}, {"internalType": "address", "name": "user", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}], "type": "event", "name": "RewardGranted", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "rewardToken", "type": "address", "indexed": true}, {"internalType": "address", "name": "mToken", "type": "address", "indexed": true}], "type": "event", "name": "SupplyIndexNotified", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "rewardToken", "type": "address", "indexed": true}, {"internalType": "address", "name": "mToken", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "supplySpeed", "type": "uint256", "indexed": false}], "type": "event", "name": "SupplySpeedUpdated", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "token", "type": "address", "indexed": true}], "type": "event", "name": "WhitelistedToken", "anonymous": false}, {"inputs": [{"internalType": "address[]", "name": "holders", "type": "address[]"}], "stateMutability": "nonpayable", "type": "function", "name": "claim"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getBlockTimestamp", "outputs": [{"internalType": "uint32", "name": "", "type": "uint32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getRewardTokens", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}]}, {"inputs": [{"internalType": "address", "name": "_token", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isRewardToken", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "notifyBorrowIndex"}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}, {"internalType": "address", "name": "borrower", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "notify<PERSON><PERSON><PERSON><PERSON>"}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}, {"internalType": "address", "name": "supplier", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "notifySupplier"}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "notifySupplyIndex"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "operator", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}], "devdoc": {"kind": "dev", "methods": {"claim(address[])": {"params": {"holders": "the accounts to claim for"}}, "isRewardToken(address)": {"params": {"_token": "the token to check for"}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"claim(address[])": {"notice": "Claim tokens for `holders"}, "getBlockTimestamp()": {"notice": "Get block timestamp"}, "getRewardTokens()": {"notice": "Added reward tokens"}, "isRewardToken(address)": {"notice": "Flag to check if reward token added before"}, "notifyBorrowIndex(address)": {"notice": "Notifies borrow index"}, "notifyBorrower(address,address)": {"notice": "Notifies borrower"}, "notifySupplier(address,address)": {"notice": "Notifies supplier"}, "notifySupplyIndex(address)": {"notice": "Notifies supply index"}, "operator()": {"notice": "The operator that rewards are distributed to"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/interfaces/IRewardDistributor.sol": "IRewardDistributor"}, "evmVersion": "london", "libraries": {}}, "sources": {"src/interfaces/IRewardDistributor.sol": {"keccak256": "0xd39c4db6e2a85e90db82e20685d4b235966d993c3407e1c0fb52add6421b86df", "urls": ["bzz-raw://e865fe1569ec1ac79cd924a9aa2e2b43754555e510526f6cd030395e9923f01d", "dweb:/ipfs/QmXcshgzCxLKBZJYGuQviEamruhmwXWNJ3ZYLxazWBahuc"], "license": "BSL-1.1"}}, "version": 1}, "id": 142}