{"abi": [{"type": "constructor", "inputs": [{"name": "_owner", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "gasFees", "inputs": [{"name": "", "type": "uint32", "internalType": "uint32"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setGasFee", "inputs": [{"name": "dst<PERSON>hainId", "type": "uint32", "internalType": "uint32"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "GasFeeUpdated", "inputs": [{"name": "dstChainid", "type": "uint32", "indexed": true, "internalType": "uint32"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "OwnableInvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}], "bytecode": {"object": "0x608060405234801561001057600080fd5b506040516103cd3803806103cd83398101604081905261002f916100be565b806001600160a01b03811661005e57604051631e4fbdf760e01b81526000600482015260240160405180910390fd5b6100678161006e565b50506100ee565b600080546001600160a01b038381166001600160a01b0319831681178455604051919092169283917f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e09190a35050565b6000602082840312156100d057600080fd5b81516001600160a01b03811681146100e757600080fd5b9392505050565b6102d0806100fd6000396000f3fe608060405234801561001057600080fd5b50600436106100575760003560e01c8063715018a61461005c5780638da5cb5b14610066578063c9768c4914610086578063d8ddb33c146100b4578063f2fde38b146100c7575b600080fd5b6100646100da565b005b6000546040516001600160a01b0390911681526020015b60405180910390f35b6100a6610094366004610225565b60016020526000908152604090205481565b60405190815260200161007d565b6100646100c2366004610247565b6100ee565b6100646100d5366004610271565b61014c565b6100e261018f565b6100ec60006101bc565b565b6100f661018f565b63ffffffff821660008181526001602052604090819020839055517fdc5ba3e465676fa955bab271f0215cab617344571019d9fa96dc8850c4a961f1906101409084815260200190565b60405180910390a25050565b61015461018f565b6001600160a01b03811661018357604051631e4fbdf760e01b8152600060048201526024015b60405180910390fd5b61018c816101bc565b50565b6000546001600160a01b031633146100ec5760405163118cdaa760e01b815233600482015260240161017a565b600080546001600160a01b038381166001600160a01b0319831681178455604051919092169283917f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e09190a35050565b803563ffffffff8116811461022057600080fd5b919050565b60006020828403121561023757600080fd5b6102408261020c565b9392505050565b6000806040838503121561025a57600080fd5b6102638361020c565b946020939093013593505050565b60006020828403121561028357600080fd5b81356001600160a01b038116811461024057600080fdfea26469706673582212207c15ff14f1680b7bfb60501e04b5f69431e281b5ec728e22738d0a9849d5093164736f6c634300081c0033", "sourceMap": "699:635:186:-:0;;;938:46;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;974:6;-1:-1:-1;;;;;1273:26:27;;1269:95;;1322:31;;-1:-1:-1;;;1322:31:27;;1350:1;1322:31;;;455:51:242;428:18;;1322:31:27;;;;;;;1269:95;1373:32;1392:12;1373:18;:32::i;:::-;1225:187;938:46:186;699:635;;2912:187:27;2985:16;3004:6;;-1:-1:-1;;;;;3020:17:27;;;-1:-1:-1;;;;;;3020:17:27;;;;;;3052:40;;3004:6;;;;;;;3052:40;;2985:16;3052:40;2975:124;2912:187;:::o;14:290:242:-;84:6;137:2;125:9;116:7;112:23;108:32;105:52;;;153:1;150;143:12;105:52;179:16;;-1:-1:-1;;;;;224:31:242;;214:42;;204:70;;270:1;267;260:12;204:70;293:5;14:290;-1:-1:-1;;;14:290:242:o;309:203::-;699:635:186;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x608060405234801561001057600080fd5b50600436106100575760003560e01c8063715018a61461005c5780638da5cb5b14610066578063c9768c4914610086578063d8ddb33c146100b4578063f2fde38b146100c7575b600080fd5b6100646100da565b005b6000546040516001600160a01b0390911681526020015b60405180910390f35b6100a6610094366004610225565b60016020526000908152604090205481565b60405190815260200161007d565b6100646100c2366004610247565b6100ee565b6100646100d5366004610271565b61014c565b6100e261018f565b6100ec60006101bc565b565b6100f661018f565b63ffffffff821660008181526001602052604090819020839055517fdc5ba3e465676fa955bab271f0215cab617344571019d9fa96dc8850c4a961f1906101409084815260200190565b60405180910390a25050565b61015461018f565b6001600160a01b03811661018357604051631e4fbdf760e01b8152600060048201526024015b60405180910390fd5b61018c816101bc565b50565b6000546001600160a01b031633146100ec5760405163118cdaa760e01b815233600482015260240161017a565b600080546001600160a01b038381166001600160a01b0319831681178455604051919092169283917f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e09190a35050565b803563ffffffff8116811461022057600080fd5b919050565b60006020828403121561023757600080fd5b6102408261020c565b9392505050565b6000806040838503121561025a57600080fd5b6102638361020c565b946020939093013593505050565b60006020828403121561028357600080fd5b81356001600160a01b038116811461024057600080fdfea26469706673582212207c15ff14f1680b7bfb60501e04b5f69431e281b5ec728e22738d0a9849d5093164736f6c634300081c0033", "sourceMap": "699:635:186:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2293:101:27;;;:::i;:::-;;1638:85;1684:7;1710:6;1638:85;;-1:-1:-1;;;;;1710:6:27;;;160:51:242;;148:2;133:18;1638:85:27;;;;;;;;782:41:186;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;725:25:242;;;713:2;698:18;782:41:186;579:177:242;1166:166:186;;;;;;:::i;:::-;;:::i;2543:215:27:-;;;;;;:::i;:::-;;:::i;2293:101::-;1531:13;:11;:13::i;:::-;2357:30:::1;2384:1;2357:18;:30::i;:::-;2293:101::o:0;1166:166:186:-;1531:13:27;:11;:13::i;:::-;1249:19:186::1;::::0;::::1;;::::0;;;:7:::1;:19;::::0;;;;;;:28;;;1292:33;::::1;::::0;::::1;::::0;1271:6;725:25:242;;713:2;698:18;;579:177;1292:33:186::1;;;;;;;;1166:166:::0;;:::o;2543:215:27:-;1531:13;:11;:13::i;:::-;-1:-1:-1;;;;;2627:22:27;::::1;2623:91;;2672:31;::::0;-1:-1:-1;;;2672:31:27;;2700:1:::1;2672:31;::::0;::::1;160:51:242::0;133:18;;2672:31:27::1;;;;;;;;2623:91;2723:28;2742:8;2723:18;:28::i;:::-;2543:215:::0;:::o;1796:162::-;1684:7;1710:6;-1:-1:-1;;;;;1710:6:27;735:10:47;1855:23:27;1851:101;;1901:40;;-1:-1:-1;;;1901:40:27;;735:10:47;1901:40:27;;;160:51:242;133:18;;1901:40:27;14:203:242;2912:187:27;2985:16;3004:6;;-1:-1:-1;;;;;3020:17:27;;;-1:-1:-1;;;;;;3020:17:27;;;;;;3052:40;;3004:6;;;;;;;3052:40;;2985:16;3052:40;2975:124;2912:187;:::o;222:163:242:-;289:20;;349:10;338:22;;328:33;;318:61;;375:1;372;365:12;318:61;222:163;;;:::o;390:184::-;448:6;501:2;489:9;480:7;476:23;472:32;469:52;;;517:1;514;507:12;469:52;540:28;558:9;540:28;:::i;:::-;530:38;390:184;-1:-1:-1;;;390:184:242:o;761:252::-;828:6;836;889:2;877:9;868:7;864:23;860:32;857:52;;;905:1;902;895:12;857:52;928:28;946:9;928:28;:::i;:::-;918:38;1003:2;988:18;;;;975:32;;-1:-1:-1;;;761:252:242:o;1018:286::-;1077:6;1130:2;1118:9;1109:7;1105:23;1101:32;1098:52;;;1146:1;1143;1136:12;1098:52;1172:23;;-1:-1:-1;;;;;1224:31:242;;1214:42;;1204:70;;1270:1;1267;1260:12", "linkReferences": {}}, "methodIdentifiers": {"gasFees(uint32)": "c9768c49", "owner()": "8da5cb5b", "renounceOwnership()": "715018a6", "setGasFee(uint32,uint256)": "d8ddb33c", "transferOwnership(address)": "f2fde38b"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_owner\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"OwnableInvalidOwner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"OwnableUnauthorizedAccount\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint32\",\"name\":\"dstChainid\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"GasFeeUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"\",\"type\":\"uint32\"}],\"name\":\"gasFees\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"dstChainId\",\"type\":\"uint32\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"setGasFee\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"OwnableInvalidOwner(address)\":[{\"details\":\"The owner is not a valid owner account. (eg. `address(0)`)\"}],\"OwnableUnauthorizedAccount(address)\":[{\"details\":\"The caller account is not authorized to perform an operation.\"}]},\"kind\":\"dev\",\"methods\":{\"owner()\":{\"details\":\"Returns the address of the current owner.\"},\"renounceOwnership()\":{\"details\":\"Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner.\"},\"setGasFee(uint32,uint256)\":{\"params\":{\"amount\":\"the gas fee amount\",\"dstChainId\":\"the destination chain id\"}},\"transferOwnership(address)\":{\"details\":\"Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"setGasFee(uint32,uint256)\":{\"notice\":\"Sets the gas fee\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/oracles/gas/DefaultGasHelper.sol\":\"DefaultGasHelper\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"lib/openzeppelin-contracts/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"src/oracles/gas/DefaultGasHelper.sol\":{\"keccak256\":\"0x16aa69f5dd078bd59ef74b1da4aa6baa96ae0046689c448f867082e6d35183b6\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://db3cfca6364c151aec0364c34568b755d836672ae37d93f5e11f821b4fb08809\",\"dweb:/ipfs/QmR4tivwaWzFkWtPazTFaimWUDUquEqhJ9Bgt1snctmoeB\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "_owner", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "type": "error", "name": "OwnableInvalidOwner"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "type": "error", "name": "OwnableUnauthorizedAccount"}, {"inputs": [{"internalType": "uint32", "name": "dstChainid", "type": "uint32", "indexed": true}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}], "type": "event", "name": "GasFeeUpdated", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "previousOwner", "type": "address", "indexed": true}, {"internalType": "address", "name": "new<PERSON>wner", "type": "address", "indexed": true}], "type": "event", "name": "OwnershipTransferred", "anonymous": false}, {"inputs": [{"internalType": "uint32", "name": "", "type": "uint32"}], "stateMutability": "view", "type": "function", "name": "gasFees", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "renounceOwnership"}, {"inputs": [{"internalType": "uint32", "name": "dst<PERSON>hainId", "type": "uint32"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "setGasFee"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "transferOwnership"}], "devdoc": {"kind": "dev", "methods": {"owner()": {"details": "Returns the address of the current owner."}, "renounceOwnership()": {"details": "Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner."}, "setGasFee(uint32,uint256)": {"params": {"amount": "the gas fee amount", "dstChainId": "the destination chain id"}}, "transferOwnership(address)": {"details": "Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"setGasFee(uint32,uint256)": {"notice": "Sets the gas fee"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/oracles/gas/DefaultGasHelper.sol": "DefaultGasHelper"}, "evmVersion": "london", "libraries": {}}, "sources": {"lib/openzeppelin-contracts/contracts/access/Ownable.sol": {"keccak256": "0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb", "urls": ["bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6", "dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "src/oracles/gas/DefaultGasHelper.sol": {"keccak256": "0x16aa69f5dd078bd59ef74b1da4aa6baa96ae0046689c448f867082e6d35183b6", "urls": ["bzz-raw://db3cfca6364c151aec0364c34568b755d836672ae37d93f5e11f821b4fb08809", "dweb:/ipfs/QmR4tivwaWzFkWtPazTFaimWUDUquEqhJ9Bgt1snctmoeB"], "license": "BSL-1.1"}}, "version": 1}, "id": 186}