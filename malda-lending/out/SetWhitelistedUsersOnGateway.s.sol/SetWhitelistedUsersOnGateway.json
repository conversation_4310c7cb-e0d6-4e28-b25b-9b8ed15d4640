{"abi": [{"type": "function", "name": "IS_SCRIPT", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "run", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}], "bytecode": {"object": "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", "sourceMap": "187:2159:76:-:0;;;3126:44:3;;;-1:-1:-1;;849:28:1;;;;;187:2159:76;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "187:2159:76:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;241:2103;;;:::i;:::-;;849:28:1;;;;;;;;;;;;;;;179:14:242;;172:22;154:41;;142:2;127:18;849:28:1;;;;;;;241:2103:76;295:25;;-1:-1:-1;;;295:25:76;;408:2:242;295:25:76;;;390:21:242;447:2;427:18;;;420:30;-1:-1:-1;;;466:18:242;;;459:41;281:11:76;;336:42:0;;295:10:76;;517:18:242;;295:25:76;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;447:17;;;461:2;447:17;;;;;;;;;281:39;;-1:-1:-1;368:42:76;;331:20;;447:17;;;;;;;;;;-1:-1:-1;447:17:76;422:42;;485;474:5;480:1;474:8;;;;;;;;:::i;:::-;;;;;;:53;-1:-1:-1;;;;;474:53:76;;;-1:-1:-1;;;;;474:53:76;;;;;548:42;537:5;543:1;537:8;;;;;;;;:::i;:::-;;;;;;:53;-1:-1:-1;;;;;537:53:76;;;-1:-1:-1;;;;;537:53:76;;;;;611:42;600:5;606:1;600:8;;;;;;;;:::i;:::-;;;;;;:53;-1:-1:-1;;;;;600:53:76;;;-1:-1:-1;;;;;600:53:76;;;;;674:42;663:5;669:1;663:8;;;;;;;;:::i;:::-;;;;;;:53;-1:-1:-1;;;;;663:53:76;;;-1:-1:-1;;;;;663:53:76;;;;;737:42;726:5;732:1;726:8;;;;;;;;:::i;:::-;;;;;;:53;-1:-1:-1;;;;;726:53:76;;;-1:-1:-1;;;;;726:53:76;;;;;800:42;789:5;795:1;789:8;;;;;;;;:::i;:::-;;;;;;:53;-1:-1:-1;;;;;789:53:76;;;-1:-1:-1;;;;;789:53:76;;;;;863:42;852:5;858:1;852:8;;;;;;;;:::i;:::-;;;;;;:53;-1:-1:-1;;;;;852:53:76;;;-1:-1:-1;;;;;852:53:76;;;;;926:42;915:5;921:1;915:8;;;;;;;;:::i;:::-;;;;;;:53;-1:-1:-1;;;;;915:53:76;;;-1:-1:-1;;;;;915:53:76;;;;;989:42;978:5;984:1;978:8;;;;;;;;:::i;:::-;;;;;;:53;-1:-1:-1;;;;;978:53:76;;;-1:-1:-1;;;;;978:53:76;;;;;1052:42;1041:5;1047:1;1041:8;;;;;;;;:::i;:::-;;;;;;:53;-1:-1:-1;;;;;1041:53:76;;;-1:-1:-1;;;;;1041:53:76;;;;;1116:42;1104:5;1110:2;1104:9;;;;;;;;:::i;:::-;;;;;;:54;-1:-1:-1;;;;;1104:54:76;;;-1:-1:-1;;;;;1104:54:76;;;;;1180:42;1168:5;1174:2;1168:9;;;;;;;;:::i;:::-;;;;;;:54;-1:-1:-1;;;;;1168:54:76;;;-1:-1:-1;;;;;1168:54:76;;;;;1244:42;1232:5;1238:2;1232:9;;;;;;;;:::i;:::-;;;;;;:54;-1:-1:-1;;;;;1232:54:76;;;-1:-1:-1;;;;;1232:54:76;;;;;1308:42;1296:5;1302:2;1296:9;;;;;;;;:::i;:::-;;;;;;:54;-1:-1:-1;;;;;1296:54:76;;;-1:-1:-1;;;;;1296:54:76;;;;;1372:42;1360:5;1366:2;1360:9;;;;;;;;:::i;:::-;;;;;;:54;-1:-1:-1;;;;;1360:54:76;;;-1:-1:-1;;;;;1360:54:76;;;;;1436:42;1424:5;1430:2;1424:9;;;;;;;;:::i;:::-;;;;;;:54;-1:-1:-1;;;;;1424:54:76;;;-1:-1:-1;;;;;1424:54:76;;;;;1500:42;1488:5;1494:2;1488:9;;;;;;;;:::i;:::-;;;;;;:54;-1:-1:-1;;;;;1488:54:76;;;-1:-1:-1;;;;;1488:54:76;;;;;1564:42;1552:5;1558:2;1552:9;;;;;;;;:::i;:::-;;;;;;:54;-1:-1:-1;;;;;1552:54:76;;;-1:-1:-1;;;;;1552:54:76;;;;;1628:42;1616:5;1622:2;1616:9;;;;;;;;:::i;:::-;;;;;;:54;-1:-1:-1;;;;;1616:54:76;;;-1:-1:-1;;;;;1616:54:76;;;;;1692:42;1680:5;1686:2;1680:9;;;;;;;;:::i;:::-;;;;;;:54;-1:-1:-1;;;;;1680:54:76;;;-1:-1:-1;;;;;1680:54:76;;;;;1756:42;1744:5;1750:2;1744:9;;;;;;;;:::i;:::-;;;;;;:54;-1:-1:-1;;;;;1744:54:76;;;-1:-1:-1;;;;;1744:54:76;;;;;1820:42;1808:5;1814:2;1808:9;;;;;;;;:::i;:::-;;;;;;:54;-1:-1:-1;;;;;1808:54:76;;;-1:-1:-1;;;;;1808:54:76;;;;;1884:42;1872:5;1878:2;1872:9;;;;;;;;:::i;:::-;;;;;;:54;-1:-1:-1;;;;;1872:54:76;;;-1:-1:-1;;;;;1872:54:76;;;;;1948:42;1936:5;1942:2;1936:9;;;;;;;;:::i;:::-;;;;;;:54;-1:-1:-1;;;;;1936:54:76;;;-1:-1:-1;;;;;1936:54:76;;;;;2012:42;2000:5;2006:2;2000:9;;;;;;;;:::i;:::-;;;;;;:54;-1:-1:-1;;;;;2000:54:76;;;-1:-1:-1;;;;;2000:54:76;;;;;2070:9;2065:273;2085:5;:12;2081:1;:16;2065:273;;;2118:50;;;;;;;;;;;;;;;;;;2159:5;2165:1;2159:8;;;;;;;;:::i;:::-;;;;;;;2118:11;:50::i;:::-;2182:22;;-1:-1:-1;;;2182:22:76;;;;;1145:25:242;;;336:42:0;;2182:17:76;;1118:18:242;;2182:22:76;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2218:6;-1:-1:-1;;;;;2218:25:76;;2244:5;2250:1;2244:8;;;;;;;;:::i;:::-;;;;;;;;;;;2218:41;;-1:-1:-1;;;;;;2218:41:76;;;;;;;-1:-1:-1;;;;;1367:32:242;;;2218:41:76;;;1349:51:242;2254:4:76;1416:18:242;;;1409:50;1322:18;;2218:41:76;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;336:42:0;-1:-1:-1;;;;;2273:16:76;;:18;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2306:21;;;;;;;;;;;;;;-1:-1:-1;;;2306:21:76;;;2325:1;2306:11;:21::i;:::-;2099:3;;2065:273;;;;271:2073;;;241:2103::o;7740:145:16:-;7807:71;7870:2;7874;7823:54;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;7823:54:16;;;;;;;;;;;;;;-1:-1:-1;;;;;7823:54:16;-1:-1:-1;;;7823:54:16;;;7807:15;:71::i;:::-;7740:145;;:::o;7139:::-;7206:71;7269:2;7273;7222:54;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;7222:54:16;;;;;;;;;;;;;;-1:-1:-1;;;;;7222:54:16;-1:-1:-1;;;7222:54:16;;;851:129;922:51;965:7;934:29;922:51::i;:::-;851:129;:::o;180:463::-;265:22;131:42;265:40;;594:1;571;541:7;535:14;510:2;501:7;497:16;461:14;434:5;402:211;381:246;367:270;180:463;:::o;546:184:242:-;616:6;669:2;657:9;648:7;644:23;640:32;637:52;;;685:1;682;675:12;637:52;-1:-1:-1;708:16:242;;546:184;-1:-1:-1;546:184:242:o;867:127::-;928:10;923:3;919:20;916:1;909:31;959:4;956:1;949:15;983:4;980:1;973:15;1470:400;1512:3;1550:5;1544:12;1577:6;1572:3;1565:19;1602:1;1612:139;1626:6;1623:1;1620:13;1612:139;;;1734:4;1719:13;;;1715:24;;1709:31;1689:11;;;1685:22;;1678:63;1641:12;1612:139;;;1616:3;1796:1;1789:4;1780:6;1775:3;1771:16;1767:27;1760:38;1859:4;1852:2;1848:7;1843:2;1835:6;1831:15;1827:29;1822:3;1818:39;1814:50;1807:57;;;1470:400;;;;:::o;1875:317::-;2052:2;2041:9;2034:21;2015:4;2072:45;2113:2;2102:9;2098:18;2090:6;2072:45;:::i;:::-;2064:53;;2182:1;2178;2173:3;2169:11;2165:19;2157:6;2153:32;2148:2;2137:9;2133:18;2126:60;1875:317;;;;;:::o;2197:291::-;2374:2;2363:9;2356:21;2337:4;2394:45;2435:2;2424:9;2420:18;2412:6;2394:45;:::i;:::-;2386:53;;2475:6;2470:2;2459:9;2455:18;2448:34;2197:291;;;;;:::o", "linkReferences": {}}, "methodIdentifiers": {"IS_SCRIPT()": "f8ccbf47", "run()": "c0406226"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"IS_SCRIPT\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"run\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"script/configuration/SetWhitelistedUsersOnGateway.s.sol\":\"SetWhitelistedUsersOnGateway\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"lib/forge-std/src/Base.sol\":{\"keccak256\":\"0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d\",\"dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z\"]},\"lib/forge-std/src/Script.sol\":{\"keccak256\":\"0xc942e27c7baae499beb01afbbae99f24d42af9a6e4aae675bc6901b704aa8e9b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0456008adf68947247f358b62863af4a8e349549d2260f2ff9569ff0e3cf5c98\",\"dweb:/ipfs/QmdviSUj2i7o3TPN5vd2xocqGMFVqjUzaiJTZRYyPxyHPx\"]},\"lib/forge-std/src/StdChains.sol\":{\"keccak256\":\"0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351\",\"dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm\"]},\"lib/forge-std/src/StdCheats.sol\":{\"keccak256\":\"0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41\",\"dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK\"]},\"lib/forge-std/src/StdConstants.sol\":{\"keccak256\":\"0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc\",\"dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r\"]},\"lib/forge-std/src/StdJson.sol\":{\"keccak256\":\"0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974\",\"dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3\"]},\"lib/forge-std/src/StdMath.sol\":{\"keccak256\":\"0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92\",\"dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC\"]},\"lib/forge-std/src/StdStorage.sol\":{\"keccak256\":\"0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57\",\"dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ\"]},\"lib/forge-std/src/StdStyle.sol\":{\"keccak256\":\"0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8\",\"dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK\"]},\"lib/forge-std/src/StdUtils.sol\":{\"keccak256\":\"0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138\",\"dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776\"]},\"lib/forge-std/src/Vm.sol\":{\"keccak256\":\"0xaae88142a348064677cf81169ea0ca706be3e99e2a0f13a01878ff8d52cf6b25\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://da4fb0ed1b31381c7e60ee386acc72796e8a8325c83739ec9a3678e2f841073c\",\"dweb:/ipfs/Qmb4MU6FpAi8Wt5kUQ3wa7ooHnfMVbdd9zuvnqbTehLmxJ\"]},\"lib/forge-std/src/console.sol\":{\"keccak256\":\"0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57\",\"dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP\"]},\"lib/forge-std/src/console2.sol\":{\"keccak256\":\"0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d\",\"dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ\"]},\"lib/forge-std/src/interfaces/IMulticall3.sol\":{\"keccak256\":\"0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0\",\"dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2\"]},\"lib/forge-std/src/safeconsole.sol\":{\"keccak256\":\"0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab\",\"dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol\":{\"keccak256\":\"0xc163fcf9bb10138631a9ba5564df1fa25db9adff73bd9ee868a8ae1858fe093a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9706d43a0124053d9880f6e31a59f31bc0a6a3dc1acd66ce0a16e1111658c5f6\",\"dweb:/ipfs/QmUFmfowzkRwGtDu36cXV9SPTBHJ3n7dG9xQiK5B28jTf2\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol\":{\"keccak256\":\"0x631188737069917d2f909d29ce62c4d48611d326686ba6683e26b72a23bfac0b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7a61054ae84cd6c4d04c0c4450ba1d6de41e27e0a2c4f1bcdf58f796b401c609\",\"dweb:/ipfs/QmUvtdp7X1mRVyC3CsHrtPbgoqWaXHp3S1ZR24tpAQYJWM\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol\":{\"keccak256\":\"0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9\",\"dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV\"]},\"lib/openzeppelin-contracts/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xc6a8ff0ea489379b61faa647490411b80102578440ab9d84e9a957cc12164e70\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0ea104e577e63faea3b69c415637e99e755dcbf64c5833d7140c35a714d6d90c\",\"dweb:/ipfs/Qmau6x4Ns9XdyynRCNNp3RhLqijJjFm7z5fyZazfYFGYdq\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol\":{\"keccak256\":\"0x6008dabfe393240d73d7dd7688033f72740d570aa422254d29a7dce8568f3aff\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f5196ec75139918c6c7bb4251b36395e668f1fa6d206beba7e7520e74913940d\",\"dweb:/ipfs/QmSyqjksXxmm2mCG6qRd1yuwLykypkSVBbnBnGqJRcuJMi\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol\":{\"keccak256\":\"0x37bb49513c49c87c4642a891b13b63571bc87013dde806617aa1efb54605f386\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b3036b3a83b7c48f96641f2a9002b9f2dcb6a5958dd670894ada21ae8229b3d0\",\"dweb:/ipfs/QmUNfSBdoVtjhETaUJCYcaC7pTMgbhht926tJ2uXJbiVd3\"]},\"lib/openzeppelin-contracts/contracts/utils/Address.sol\":{\"keccak256\":\"0xaf28a975a78550e45f65e559a3ad6a5ad43b9b8a37366999abd1b7084eb70721\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b7bd24e224f67f65bfadf85dc2929fa965456bb2415478bd0125471b5ce35245\",\"dweb:/ipfs/QmRaydGr8BTHs1kvaZfsNU69pKzUAGFrvABn1KiRSbE51y\"]},\"lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"lib/risc0-ethereum/contracts/src/IRiscZeroVerifier.sol\":{\"keccak256\":\"0xae73e91e4d2829b0a88176584d876382b4b7540ff07c62cb947cdf9ea43d5cf3\",\"license\":\"Apache-2.0\",\"urls\":[\"bzz-raw://f2dd7da39a6f9b100fc1dcde565d799462cdc7d27722d2265317c3a01619c818\",\"dweb:/ipfs/Qmd3k3kcvrtfkLqVza4jHodbFH8wvq5vmfPtwekBKtPjmz\"]},\"lib/risc0-ethereum/contracts/src/Util.sol\":{\"keccak256\":\"0x230a9a58bb2b0c68d4921ecc3672783f941890a2fa3a238dbe5188cfb7658b82\",\"license\":\"Apache-2.0\",\"urls\":[\"bzz-raw://586cb5296c564761609ca8aaae5b1fbbdb19ab1e174f7197f602572d9e82b70c\",\"dweb:/ipfs/QmWxDmucSfg3Wgdk1rVcheDHwkziTsXWvKpKSCnxtpQBqg\"]},\"lib/risc0-ethereum/contracts/src/steel/Steel.sol\":{\"keccak256\":\"0x20811a302f2bc3e36fb612026319c83cf79c16aa56b822d40bcbe8173d1e9544\",\"license\":\"Apache-2.0\",\"urls\":[\"bzz-raw://ed9f7a15c3be0bdc79217feae9e773d280fcfb44aa450d37c6c891f0b505210f\",\"dweb:/ipfs/QmXHtzvLT2AHHHojJ42epUSBF4HYTaVohSmyZft6KZoVqE\"]},\"script/configuration/SetWhitelistedUsersOnGateway.s.sol\":{\"keccak256\":\"0x47ec2bbc763c87bae299556be83c285a6ae4ea9ebf4d7be988a8b745eab311fc\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://a45b5bc0ab4b8ebf739727f6ab95aa8b5c1910753d763fb058aa3b352f952baf\",\"dweb:/ipfs/QmZZo2SKaozAf7Gg4iCvaf7ecCizg8s39DALGJJE98AHNM\"]},\"src/interfaces/IBlacklister.sol\":{\"keccak256\":\"0x47ed20135507a9d16473027cbee18de5d952846bada761c6248b91c75cd88be1\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://91a886b95dd578156f9b6cec3236e4751aae8a38e5d15e0d6045217c6fbd3d63\",\"dweb:/ipfs/QmRNjz7sGTuMkQNZhwwcg5DNN2eeqTZSAdZmxWxbx66CHU\"]},\"src/interfaces/IRoles.sol\":{\"keccak256\":\"0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c\",\"dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj\"]},\"src/interfaces/ImToken.sol\":{\"keccak256\":\"0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36\",\"dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL\"]},\"src/interfaces/ImTokenGateway.sol\":{\"keccak256\":\"0x7b47f5229cd07de60af49eb473eddc27e429abd846df81001ef05af2c3d47634\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://04d8c52462eeb19a4c28980ac5c81a901fd35982dc47c0ab95a4ee6466b150c1\",\"dweb:/ipfs/QmfTyZrLWMnWLU6zo85RVwjv5W2qXVQPkLWzru5zcBarYm\"]},\"src/libraries/BytesLib.sol\":{\"keccak256\":\"0xfaf080296887274c71c2f2eb3552c4742c96ff24616c61f8ab98399f30b18ef0\",\"license\":\"Unlicense\",\"urls\":[\"bzz-raw://02dcb01b6c9cf4f158792a15e9b40d046332949cd6b2c1e5e54e474901cf579b\",\"dweb:/ipfs/QmWiBhygucYoY8Uu6WQPao2r64Y25Vmmj3gYyZZ6Wy9wnE\"]},\"src/libraries/mTokenProofDecoderLib.sol\":{\"keccak256\":\"0x00216e7389b2d64450d9d13b648f80e459742e1dd91dec543d415df920f8ce71\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://660deae410969bca243a7b8ad2e6ca6d1cb11d3647af0e227355e5b74f949ea0\",\"dweb:/ipfs/QmUmynqsY1kdEoWHNwqHEVBedepdDBaNHotAP7CCQ7PBzN\"]},\"src/mToken/extension/mTokenGateway.sol\":{\"keccak256\":\"0x3032b6ebde1f45e840a58637b38a34fda2464322a9fce0455c51160fe6b7578a\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://09f97ecce05268a85fcedf34de77d6623bd07cd743d28891aeec2716ab254163\",\"dweb:/ipfs/QmT87wtFZLjku1Lg7Fn4VYp52ubpsoYxUnEyccHmX7n1Ex\"]},\"src/verifier/ZkVerifier.sol\":{\"keccak256\":\"0x1abaa285ad4ee09461aae5df4aecff9bd38b2b3da19b5a920ab1b7d2208e04ec\",\"license\":\"AGPL-3.0\",\"urls\":[\"bzz-raw://7db45391afa7244f70303c5ace73b7937ee30261fd2015e4a7bfa11ec85480cc\",\"dweb:/ipfs/QmTdhCwcbTCRCMF1ifCJoifqwxhKpKBEE8zq9ph6hYhogG\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "view", "type": "function", "name": "IS_SCRIPT", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "run"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"script/configuration/SetWhitelistedUsersOnGateway.s.sol": "SetWhitelistedUsersOnGateway"}, "evmVersion": "london", "libraries": {}}, "sources": {"lib/forge-std/src/Base.sol": {"keccak256": "0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd", "urls": ["bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d", "dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z"], "license": "MIT"}, "lib/forge-std/src/Script.sol": {"keccak256": "0xc942e27c7baae499beb01afbbae99f24d42af9a6e4aae675bc6901b704aa8e9b", "urls": ["bzz-raw://0456008adf68947247f358b62863af4a8e349549d2260f2ff9569ff0e3cf5c98", "dweb:/ipfs/QmdviSUj2i7o3TPN5vd2xocqGMFVqjUzaiJTZRYyPxyHPx"], "license": "MIT"}, "lib/forge-std/src/StdChains.sol": {"keccak256": "0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e", "urls": ["bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351", "dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm"], "license": "MIT"}, "lib/forge-std/src/StdCheats.sol": {"keccak256": "0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746", "urls": ["bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41", "dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK"], "license": "MIT"}, "lib/forge-std/src/StdConstants.sol": {"keccak256": "0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534", "urls": ["bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc", "dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r"], "license": "MIT"}, "lib/forge-std/src/StdJson.sol": {"keccak256": "0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500", "urls": ["bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974", "dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3"], "license": "MIT"}, "lib/forge-std/src/StdMath.sol": {"keccak256": "0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2", "urls": ["bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92", "dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC"], "license": "MIT"}, "lib/forge-std/src/StdStorage.sol": {"keccak256": "0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc", "urls": ["bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57", "dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ"], "license": "MIT"}, "lib/forge-std/src/StdStyle.sol": {"keccak256": "0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d", "urls": ["bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8", "dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK"], "license": "MIT"}, "lib/forge-std/src/StdUtils.sol": {"keccak256": "0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737", "urls": ["bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138", "dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776"], "license": "MIT"}, "lib/forge-std/src/Vm.sol": {"keccak256": "0xaae88142a348064677cf81169ea0ca706be3e99e2a0f13a01878ff8d52cf6b25", "urls": ["bzz-raw://da4fb0ed1b31381c7e60ee386acc72796e8a8325c83739ec9a3678e2f841073c", "dweb:/ipfs/Qmb4MU6FpAi8Wt5kUQ3wa7ooHnfMVbdd9zuvnqbTehLmxJ"], "license": "MIT OR Apache-2.0"}, "lib/forge-std/src/console.sol": {"keccak256": "0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5", "urls": ["bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57", "dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP"], "license": "MIT"}, "lib/forge-std/src/console2.sol": {"keccak256": "0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f", "urls": ["bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d", "dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ"], "license": "MIT"}, "lib/forge-std/src/interfaces/IMulticall3.sol": {"keccak256": "0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a", "urls": ["bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0", "dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2"], "license": "MIT"}, "lib/forge-std/src/safeconsole.sol": {"keccak256": "0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11", "urls": ["bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab", "dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol": {"keccak256": "0xc163fcf9bb10138631a9ba5564df1fa25db9adff73bd9ee868a8ae1858fe093a", "urls": ["bzz-raw://9706d43a0124053d9880f6e31a59f31bc0a6a3dc1acd66ce0a16e1111658c5f6", "dweb:/ipfs/QmUFmfowzkRwGtDu36cXV9SPTBHJ3n7dG9xQiK5B28jTf2"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol": {"keccak256": "0x631188737069917d2f909d29ce62c4d48611d326686ba6683e26b72a23bfac0b", "urls": ["bzz-raw://7a61054ae84cd6c4d04c0c4450ba1d6de41e27e0a2c4f1bcdf58f796b401c609", "dweb:/ipfs/QmUvtdp7X1mRVyC3CsHrtPbgoqWaXHp3S1ZR24tpAQYJWM"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol": {"keccak256": "0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397", "urls": ["bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9", "dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/access/Ownable.sol": {"keccak256": "0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb", "urls": ["bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6", "dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0xc6a8ff0ea489379b61faa647490411b80102578440ab9d84e9a957cc12164e70", "urls": ["bzz-raw://0ea104e577e63faea3b69c415637e99e755dcbf64c5833d7140c35a714d6d90c", "dweb:/ipfs/Qmau6x4Ns9XdyynRCNNp3RhLqijJjFm7z5fyZazfYFGYdq"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol": {"keccak256": "0x6008dabfe393240d73d7dd7688033f72740d570aa422254d29a7dce8568f3aff", "urls": ["bzz-raw://f5196ec75139918c6c7bb4251b36395e668f1fa6d206beba7e7520e74913940d", "dweb:/ipfs/QmSyqjksXxmm2mCG6qRd1yuwLykypkSVBbnBnGqJRcuJMi"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol": {"keccak256": "0x37bb49513c49c87c4642a891b13b63571bc87013dde806617aa1efb54605f386", "urls": ["bzz-raw://b3036b3a83b7c48f96641f2a9002b9f2dcb6a5958dd670894ada21ae8229b3d0", "dweb:/ipfs/QmUNfSBdoVtjhETaUJCYcaC7pTMgbhht926tJ2uXJbiVd3"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Address.sol": {"keccak256": "0xaf28a975a78550e45f65e559a3ad6a5ad43b9b8a37366999abd1b7084eb70721", "urls": ["bzz-raw://b7bd24e224f67f65bfadf85dc2929fa965456bb2415478bd0125471b5ce35245", "dweb:/ipfs/QmRaydGr8BTHs1kvaZfsNU69pKzUAGFrvABn1KiRSbE51y"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "lib/risc0-ethereum/contracts/src/IRiscZeroVerifier.sol": {"keccak256": "0xae73e91e4d2829b0a88176584d876382b4b7540ff07c62cb947cdf9ea43d5cf3", "urls": ["bzz-raw://f2dd7da39a6f9b100fc1dcde565d799462cdc7d27722d2265317c3a01619c818", "dweb:/ipfs/Qmd3k3kcvrtfkLqVza4jHodbFH8wvq5vmfPtwekBKtPjmz"], "license": "Apache-2.0"}, "lib/risc0-ethereum/contracts/src/Util.sol": {"keccak256": "0x230a9a58bb2b0c68d4921ecc3672783f941890a2fa3a238dbe5188cfb7658b82", "urls": ["bzz-raw://586cb5296c564761609ca8aaae5b1fbbdb19ab1e174f7197f602572d9e82b70c", "dweb:/ipfs/QmWxDmucSfg3Wgdk1rVcheDHwkziTsXWvKpKSCnxtpQBqg"], "license": "Apache-2.0"}, "lib/risc0-ethereum/contracts/src/steel/Steel.sol": {"keccak256": "0x20811a302f2bc3e36fb612026319c83cf79c16aa56b822d40bcbe8173d1e9544", "urls": ["bzz-raw://ed9f7a15c3be0bdc79217feae9e773d280fcfb44aa450d37c6c891f0b505210f", "dweb:/ipfs/QmXHtzvLT2AHHHojJ42epUSBF4HYTaVohSmyZft6KZoVqE"], "license": "Apache-2.0"}, "script/configuration/SetWhitelistedUsersOnGateway.s.sol": {"keccak256": "0x47ec2bbc763c87bae299556be83c285a6ae4ea9ebf4d7be988a8b745eab311fc", "urls": ["bzz-raw://a45b5bc0ab4b8ebf739727f6ab95aa8b5c1910753d763fb058aa3b352f952baf", "dweb:/ipfs/QmZZo2SKaozAf7Gg4iCvaf7ecCizg8s39DALGJJE98AHNM"], "license": "BSL-1.1"}, "src/interfaces/IBlacklister.sol": {"keccak256": "0x47ed20135507a9d16473027cbee18de5d952846bada761c6248b91c75cd88be1", "urls": ["bzz-raw://91a886b95dd578156f9b6cec3236e4751aae8a38e5d15e0d6045217c6fbd3d63", "dweb:/ipfs/QmRNjz7sGTuMkQNZhwwcg5DNN2eeqTZSAdZmxWxbx66CHU"], "license": "BSL-1.1"}, "src/interfaces/IRoles.sol": {"keccak256": "0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365", "urls": ["bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c", "dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj"], "license": "BSL-1.1"}, "src/interfaces/ImToken.sol": {"keccak256": "0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f", "urls": ["bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36", "dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL"], "license": "BSL-1.1"}, "src/interfaces/ImTokenGateway.sol": {"keccak256": "0x7b47f5229cd07de60af49eb473eddc27e429abd846df81001ef05af2c3d47634", "urls": ["bzz-raw://04d8c52462eeb19a4c28980ac5c81a901fd35982dc47c0ab95a4ee6466b150c1", "dweb:/ipfs/QmfTyZrLWMnWLU6zo85RVwjv5W2qXVQPkLWzru5zcBarYm"], "license": "BSL-1.1"}, "src/libraries/BytesLib.sol": {"keccak256": "0xfaf080296887274c71c2f2eb3552c4742c96ff24616c61f8ab98399f30b18ef0", "urls": ["bzz-raw://02dcb01b6c9cf4f158792a15e9b40d046332949cd6b2c1e5e54e474901cf579b", "dweb:/ipfs/QmWiBhygucYoY8Uu6WQPao2r64Y25Vmmj3gYyZZ6Wy9wnE"], "license": "Unlicense"}, "src/libraries/mTokenProofDecoderLib.sol": {"keccak256": "0x00216e7389b2d64450d9d13b648f80e459742e1dd91dec543d415df920f8ce71", "urls": ["bzz-raw://660deae410969bca243a7b8ad2e6ca6d1cb11d3647af0e227355e5b74f949ea0", "dweb:/ipfs/QmUmynqsY1kdEoWHNwqHEVBedepdDBaNHotAP7CCQ7PBzN"], "license": "BSL-1.1"}, "src/mToken/extension/mTokenGateway.sol": {"keccak256": "0x3032b6ebde1f45e840a58637b38a34fda2464322a9fce0455c51160fe6b7578a", "urls": ["bzz-raw://09f97ecce05268a85fcedf34de77d6623bd07cd743d28891aeec2716ab254163", "dweb:/ipfs/QmT87wtFZLjku1Lg7Fn4VYp52ubpsoYxUnEyccHmX7n1Ex"], "license": "BSL-1.1"}, "src/verifier/ZkVerifier.sol": {"keccak256": "0x1abaa285ad4ee09461aae5df4aecff9bd38b2b3da19b5a920ab1b7d2208e04ec", "urls": ["bzz-raw://7db45391afa7244f70303c5ace73b7937ee30261fd2015e4a7bfa11ec85480cc", "dweb:/ipfs/QmTdhCwcbTCRCMF1ifCJoifqwxhKpKBEE8zq9ph6hYhogG"], "license": "AGPL-3.0"}}, "version": 1}, "id": 76}