{"abi": [{"type": "function", "name": "estimateFees", "inputs": [{"name": "dst<PERSON>hainId_", "type": "uint16", "internalType": "uint16"}, {"name": "userApplication_", "type": "address", "internalType": "address"}, {"name": "payload_", "type": "bytes", "internalType": "bytes"}, {"name": "_payInZRO", "type": "bool", "internalType": "bool"}, {"name": "_adapterParam", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "nativeFee", "type": "uint256", "internalType": "uint256"}, {"name": "zroFee", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "forceResumeReceive", "inputs": [{"name": "_srcChainId", "type": "uint16", "internalType": "uint16"}, {"name": "_srcAddress", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "get<PERSON>hainId", "inputs": [], "outputs": [{"name": "", "type": "uint16", "internalType": "uint16"}], "stateMutability": "view"}, {"type": "function", "name": "getConfig", "inputs": [{"name": "version_", "type": "uint16", "internalType": "uint16"}, {"name": "chainId_", "type": "uint16", "internalType": "uint16"}, {"name": "userApplication_", "type": "address", "internalType": "address"}, {"name": "configType_", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "getInboundNonce", "inputs": [{"name": "srcChainId_", "type": "uint16", "internalType": "uint16"}, {"name": "srcAddress_", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "uint64", "internalType": "uint64"}], "stateMutability": "view"}, {"type": "function", "name": "getOutboundNonce", "inputs": [{"name": "dst<PERSON>hainId_", "type": "uint16", "internalType": "uint16"}, {"name": "srcAddress_", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint64", "internalType": "uint64"}], "stateMutability": "view"}, {"type": "function", "name": "getReceiveLibraryAddress", "inputs": [{"name": "userApplication_", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "getReceiveVersion", "inputs": [{"name": "userApplication_", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint16", "internalType": "uint16"}], "stateMutability": "view"}, {"type": "function", "name": "getSendLibraryAddress", "inputs": [{"name": "userApplication_", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "getSendVersion", "inputs": [{"name": "userApplication_", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint16", "internalType": "uint16"}], "stateMutability": "view"}, {"type": "function", "name": "hasStoredPayload", "inputs": [{"name": "srcChainId_", "type": "uint16", "internalType": "uint16"}, {"name": "srcAddress_", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isReceivingPayload", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isSendingPayload", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "receivePayload", "inputs": [{"name": "srcChainId_", "type": "uint16", "internalType": "uint16"}, {"name": "srcAddress_", "type": "bytes", "internalType": "bytes"}, {"name": "dstAddress_", "type": "address", "internalType": "address"}, {"name": "nonce_", "type": "uint64", "internalType": "uint64"}, {"name": "gasLimit_", "type": "uint256", "internalType": "uint256"}, {"name": "payload_", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "retryPayload", "inputs": [{"name": "srcChainId_", "type": "uint16", "internalType": "uint16"}, {"name": "srcAddress_", "type": "bytes", "internalType": "bytes"}, {"name": "payload_", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "send", "inputs": [{"name": "dst<PERSON>hainId_", "type": "uint16", "internalType": "uint16"}, {"name": "destination_", "type": "bytes", "internalType": "bytes"}, {"name": "payload_", "type": "bytes", "internalType": "bytes"}, {"name": "refundAddress_", "type": "address", "internalType": "address payable"}, {"name": "zroPaymentAddress_", "type": "address", "internalType": "address"}, {"name": "adapterParams_", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "setConfig", "inputs": [{"name": "_version", "type": "uint16", "internalType": "uint16"}, {"name": "_chainId", "type": "uint16", "internalType": "uint16"}, {"name": "_configType", "type": "uint256", "internalType": "uint256"}, {"name": "_config", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setReceiveVersion", "inputs": [{"name": "_version", "type": "uint16", "internalType": "uint16"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setSendVersion", "inputs": [{"name": "_version", "type": "uint16", "internalType": "uint16"}], "outputs": [], "stateMutability": "nonpayable"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"estimateFees(uint16,address,bytes,bool,bytes)": "40a7bb10", "forceResumeReceive(uint16,bytes)": "42d65a8d", "getChainId()": "3408e470", "getConfig(uint16,uint16,address,uint256)": "f5ecbdbc", "getInboundNonce(uint16,bytes)": "fdc07c70", "getOutboundNonce(uint16,address)": "7a145748", "getReceiveLibraryAddress(address)": "71ba2fd6", "getReceiveVersion(address)": "da1a7c9a", "getSendLibraryAddress(address)": "9c729da1", "getSendVersion(address)": "096568f6", "hasStoredPayload(uint16,bytes)": "0eaf6ea6", "isReceivingPayload()": "ca066b35", "isSendingPayload()": "e97a448a", "receivePayload(uint16,bytes,address,uint64,uint256,bytes)": "c2fa4813", "retryPayload(uint16,bytes,bytes)": "aaff5f16", "send(uint16,bytes,bytes,address,address,bytes)": "c5803100", "setConfig(uint16,uint16,uint256,bytes)": "cbed8b9c", "setReceiveVersion(uint16)": "10ddb137", "setSendVersion(uint16)": "07e0db17"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"uint16\",\"name\":\"dstChainId_\",\"type\":\"uint16\"},{\"internalType\":\"address\",\"name\":\"userApplication_\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"payload_\",\"type\":\"bytes\"},{\"internalType\":\"bool\",\"name\":\"_payInZRO\",\"type\":\"bool\"},{\"internalType\":\"bytes\",\"name\":\"_adapterParam\",\"type\":\"bytes\"}],\"name\":\"estimateFees\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"nativeFee\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"zroFee\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint16\",\"name\":\"_srcChainId\",\"type\":\"uint16\"},{\"internalType\":\"bytes\",\"name\":\"_srcAddress\",\"type\":\"bytes\"}],\"name\":\"forceResumeReceive\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getChainId\",\"outputs\":[{\"internalType\":\"uint16\",\"name\":\"\",\"type\":\"uint16\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint16\",\"name\":\"version_\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"chainId_\",\"type\":\"uint16\"},{\"internalType\":\"address\",\"name\":\"userApplication_\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"configType_\",\"type\":\"uint256\"}],\"name\":\"getConfig\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint16\",\"name\":\"srcChainId_\",\"type\":\"uint16\"},{\"internalType\":\"bytes\",\"name\":\"srcAddress_\",\"type\":\"bytes\"}],\"name\":\"getInboundNonce\",\"outputs\":[{\"internalType\":\"uint64\",\"name\":\"\",\"type\":\"uint64\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint16\",\"name\":\"dstChainId_\",\"type\":\"uint16\"},{\"internalType\":\"address\",\"name\":\"srcAddress_\",\"type\":\"address\"}],\"name\":\"getOutboundNonce\",\"outputs\":[{\"internalType\":\"uint64\",\"name\":\"\",\"type\":\"uint64\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"userApplication_\",\"type\":\"address\"}],\"name\":\"getReceiveLibraryAddress\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"userApplication_\",\"type\":\"address\"}],\"name\":\"getReceiveVersion\",\"outputs\":[{\"internalType\":\"uint16\",\"name\":\"\",\"type\":\"uint16\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"userApplication_\",\"type\":\"address\"}],\"name\":\"getSendLibraryAddress\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"userApplication_\",\"type\":\"address\"}],\"name\":\"getSendVersion\",\"outputs\":[{\"internalType\":\"uint16\",\"name\":\"\",\"type\":\"uint16\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint16\",\"name\":\"srcChainId_\",\"type\":\"uint16\"},{\"internalType\":\"bytes\",\"name\":\"srcAddress_\",\"type\":\"bytes\"}],\"name\":\"hasStoredPayload\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"isReceivingPayload\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"isSendingPayload\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint16\",\"name\":\"srcChainId_\",\"type\":\"uint16\"},{\"internalType\":\"bytes\",\"name\":\"srcAddress_\",\"type\":\"bytes\"},{\"internalType\":\"address\",\"name\":\"dstAddress_\",\"type\":\"address\"},{\"internalType\":\"uint64\",\"name\":\"nonce_\",\"type\":\"uint64\"},{\"internalType\":\"uint256\",\"name\":\"gasLimit_\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"payload_\",\"type\":\"bytes\"}],\"name\":\"receivePayload\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint16\",\"name\":\"srcChainId_\",\"type\":\"uint16\"},{\"internalType\":\"bytes\",\"name\":\"srcAddress_\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"payload_\",\"type\":\"bytes\"}],\"name\":\"retryPayload\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint16\",\"name\":\"dstChainId_\",\"type\":\"uint16\"},{\"internalType\":\"bytes\",\"name\":\"destination_\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"payload_\",\"type\":\"bytes\"},{\"internalType\":\"address payable\",\"name\":\"refundAddress_\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"zroPaymentAddress_\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"adapterParams_\",\"type\":\"bytes\"}],\"name\":\"send\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint16\",\"name\":\"_version\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"_chainId\",\"type\":\"uint16\"},{\"internalType\":\"uint256\",\"name\":\"_configType\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"_config\",\"type\":\"bytes\"}],\"name\":\"setConfig\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint16\",\"name\":\"_version\",\"type\":\"uint16\"}],\"name\":\"setReceiveVersion\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint16\",\"name\":\"_version\",\"type\":\"uint16\"}],\"name\":\"setSendVersion\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"is imported from (https://github.com/LayerZero-Labs/LayerZero/blob/main/contracts/interfaces/ILayerZeroEndpoint.sol)\",\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/external/layerzero/ILayerZeroEndpoint.sol\":\"ILayerZeroEndpoint\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"src/interfaces/external/layerzero/ILayerZeroEndpoint.sol\":{\"keccak256\":\"0x9185b54d24606690d61f40c1ff612e2353774d63f7c9075023f4991ced4085ba\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://cee96885281ce5c9a7adeded47f5b14be5e3da88c4b2c7775974adcd45612af5\",\"dweb:/ipfs/QmR823apgG1pvdjjsa8GZSwMHm1XWkZtK7ExQKXbgASXt3\"]},\"src/interfaces/external/layerzero/ILayerZeroUserApplicationConfig.sol\":{\"keccak256\":\"0x3e3d9949a418520798f7c4eb179d6b6ec6584a75b680e7eb19d8e880287043ee\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://dff2ef4cac1e5f6517c68c0a048f1910ca0dde45b03522539bd04ac8b92b514e\",\"dweb:/ipfs/QmZ2S26Nse3FMy8sqEr4kjM5DfeVpaZyjyKtNd9Xtq64Bd\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "uint16", "name": "dst<PERSON>hainId_", "type": "uint16"}, {"internalType": "address", "name": "userApplication_", "type": "address"}, {"internalType": "bytes", "name": "payload_", "type": "bytes"}, {"internalType": "bool", "name": "_payInZRO", "type": "bool"}, {"internalType": "bytes", "name": "_adapterParam", "type": "bytes"}], "stateMutability": "view", "type": "function", "name": "estimateFees", "outputs": [{"internalType": "uint256", "name": "nativeFee", "type": "uint256"}, {"internalType": "uint256", "name": "zroFee", "type": "uint256"}]}, {"inputs": [{"internalType": "uint16", "name": "_srcChainId", "type": "uint16"}, {"internalType": "bytes", "name": "_srcAddress", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "forceResumeReceive"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "get<PERSON>hainId", "outputs": [{"internalType": "uint16", "name": "", "type": "uint16"}]}, {"inputs": [{"internalType": "uint16", "name": "version_", "type": "uint16"}, {"internalType": "uint16", "name": "chainId_", "type": "uint16"}, {"internalType": "address", "name": "userApplication_", "type": "address"}, {"internalType": "uint256", "name": "configType_", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getConfig", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [{"internalType": "uint16", "name": "srcChainId_", "type": "uint16"}, {"internalType": "bytes", "name": "srcAddress_", "type": "bytes"}], "stateMutability": "view", "type": "function", "name": "getInboundNonce", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}]}, {"inputs": [{"internalType": "uint16", "name": "dst<PERSON>hainId_", "type": "uint16"}, {"internalType": "address", "name": "srcAddress_", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getOutboundNonce", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}]}, {"inputs": [{"internalType": "address", "name": "userApplication_", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getReceiveLibraryAddress", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "userApplication_", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getReceiveVersion", "outputs": [{"internalType": "uint16", "name": "", "type": "uint16"}]}, {"inputs": [{"internalType": "address", "name": "userApplication_", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getSendLibraryAddress", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "userApplication_", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getSendVersion", "outputs": [{"internalType": "uint16", "name": "", "type": "uint16"}]}, {"inputs": [{"internalType": "uint16", "name": "srcChainId_", "type": "uint16"}, {"internalType": "bytes", "name": "srcAddress_", "type": "bytes"}], "stateMutability": "view", "type": "function", "name": "hasStoredPayload", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "isReceivingPayload", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "isSendingPayload", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "uint16", "name": "srcChainId_", "type": "uint16"}, {"internalType": "bytes", "name": "srcAddress_", "type": "bytes"}, {"internalType": "address", "name": "dstAddress_", "type": "address"}, {"internalType": "uint64", "name": "nonce_", "type": "uint64"}, {"internalType": "uint256", "name": "gasLimit_", "type": "uint256"}, {"internalType": "bytes", "name": "payload_", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "receivePayload"}, {"inputs": [{"internalType": "uint16", "name": "srcChainId_", "type": "uint16"}, {"internalType": "bytes", "name": "srcAddress_", "type": "bytes"}, {"internalType": "bytes", "name": "payload_", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "retryPayload"}, {"inputs": [{"internalType": "uint16", "name": "dst<PERSON>hainId_", "type": "uint16"}, {"internalType": "bytes", "name": "destination_", "type": "bytes"}, {"internalType": "bytes", "name": "payload_", "type": "bytes"}, {"internalType": "address payable", "name": "refundAddress_", "type": "address"}, {"internalType": "address", "name": "zroPaymentAddress_", "type": "address"}, {"internalType": "bytes", "name": "adapterParams_", "type": "bytes"}], "stateMutability": "payable", "type": "function", "name": "send"}, {"inputs": [{"internalType": "uint16", "name": "_version", "type": "uint16"}, {"internalType": "uint16", "name": "_chainId", "type": "uint16"}, {"internalType": "uint256", "name": "_configType", "type": "uint256"}, {"internalType": "bytes", "name": "_config", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "setConfig"}, {"inputs": [{"internalType": "uint16", "name": "_version", "type": "uint16"}], "stateMutability": "nonpayable", "type": "function", "name": "setReceiveVersion"}, {"inputs": [{"internalType": "uint16", "name": "_version", "type": "uint16"}], "stateMutability": "nonpayable", "type": "function", "name": "setSendVersion"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/interfaces/external/layerzero/ILayerZeroEndpoint.sol": "ILayerZeroEndpoint"}, "evmVersion": "london", "libraries": {}}, "sources": {"src/interfaces/external/layerzero/ILayerZeroEndpoint.sol": {"keccak256": "0x9185b54d24606690d61f40c1ff612e2353774d63f7c9075023f4991ced4085ba", "urls": ["bzz-raw://cee96885281ce5c9a7adeded47f5b14be5e3da88c4b2c7775974adcd45612af5", "dweb:/ipfs/QmR823apgG1pvdjjsa8GZSwMHm1XWkZtK7ExQKXbgASXt3"], "license": "BUSL-1.1"}, "src/interfaces/external/layerzero/ILayerZeroUserApplicationConfig.sol": {"keccak256": "0x3e3d9949a418520798f7c4eb179d6b6ec6584a75b680e7eb19d8e880287043ee", "urls": ["bzz-raw://dff2ef4cac1e5f6517c68c0a048f1910ca0dde45b03522539bd04ac8b92b514e", "dweb:/ipfs/QmZ2S26Nse3FMy8sqEr4kjM5DfeVpaZyjyKtNd9Xtq64Bd"], "license": "BUSL-1.1"}}, "version": 1}, "id": 154}