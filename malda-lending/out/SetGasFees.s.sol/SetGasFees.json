{"abi": [{"type": "function", "name": "IS_SCRIPT", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "run", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}], "bytecode": {"object": "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", "sourceMap": "188:765:64:-:0;;;3126:44:3;;;-1:-1:-1;;849:28:1;;;;;188:765:64;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "188:765:64:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;224:727;;;:::i;:::-;;849:28:1;;;;;;;;;;;;;;;179:14:242;;172:22;154:41;;142:2;127:18;849:28:1;;;;;;;224:727:64;278:25;;-1:-1:-1;;;278:25:64;;408:2:242;278:25:64;;;390:21:242;447:2;427:18;;;420:30;-1:-1:-1;;;466:18:242;;;459:41;264:11:64;;336:42:0;;278:10:64;;517:18:242;;278:25:64;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;404:15;;;417:1;404:15;;;;;;;;;264:39;;-1:-1:-1;314:17:64;;;;404:15;;;;;;;;;;-1:-1:-1;404:15:64;379:40;;441:4;429:6;436:1;429:9;;;;;;;;:::i;:::-;;;;;;:16;;;;;;;;;;;467:5;455:6;462:1;455:9;;;;;;;;:::i;:::-;;;;;;:17;;;;;;;;;;;494:1;482:6;489:1;482:9;;;;;;;;:::i;:::-;:13;;;;;:9;;;;;;;;;;:13;533:16;;;547:1;533:16;;;;;;;;;506:24;;533:16;;547:1;533:16;;;;;;;;;-1:-1:-1;533:16:64;506:43;;572:14;559:7;567:1;559:10;;;;;;;;:::i;:::-;;;;;;:27;;;;;609:15;596:7;604:1;596:10;;;;;;;;:::i;:::-;;;;;;:28;;;;;647:16;634:7;642:1;634:10;;;;;;;;:::i;:::-;;;;;;:29;;;;;674:39;;;;;;;;;;;;;;;;;;:11;:39::i;:::-;723:22;;-1:-1:-1;;;723:22:64;;;;;1145:25:242;;;336:42:0;;723:17:64;;1118:18:242;;723:22:64;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;760:9;755:125;775:6;:13;771:1;:17;755:125;;;826:9;-1:-1:-1;;;;;809:37:64;;847:6;854:1;847:9;;;;;;;;:::i;:::-;;;;;;;858:7;866:1;858:10;;;;;;;;:::i;:::-;;;;;;;809:60;;;;;;;;;;;;;;;1383:10:242;1371:23;;;;1353:42;;1426:2;1411:18;;1404:34;1341:2;1326:18;;1181:263;809:60:64;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;790:3:64;;;;;-1:-1:-1;755:125:64;;-1:-1:-1;755:125:64;;;336:42:0;-1:-1:-1;;;;;889:16:64;;:18;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;917:27;;;;;;;;;;;;;;-1:-1:-1;;;917:27:64;;;:11;:27::i;:::-;254:697;;;;224:727::o;6191:121:16:-;6246:59;6301:2;6262:42;;;;;;;;:::i;:::-;;;;-1:-1:-1;;6262:42:16;;;;;;;;;;;;;;-1:-1:-1;;;;;6262:42:16;-1:-1:-1;;;6262:42:16;;;6246:15;:59::i;:::-;6191:121;:::o;851:129::-;922:51;965:7;265:22;131:42;265:40;;594:1;571;541:7;535:14;510:2;501:7;497:16;461:14;434:5;402:211;381:246;367:270;180:463;:::o;546:184:242:-;616:6;669:2;657:9;648:7;644:23;640:32;637:52;;;685:1;682;675:12;637:52;-1:-1:-1;708:16:242;;546:184;-1:-1:-1;546:184:242:o;867:127::-;928:10;923:3;919:20;916:1;909:31;959:4;956:1;949:15;983:4;980:1;973:15;1449:527;1598:2;1587:9;1580:21;1561:4;1630:6;1624:13;1673:6;1668:2;1657:9;1653:18;1646:34;1698:1;1708:140;1722:6;1719:1;1716:13;1708:140;;;1833:2;1817:14;;;1813:23;;1807:30;1802:2;1783:17;;;1779:26;1772:66;1737:10;1708:140;;;1712:3;1897:1;1892:2;1883:6;1872:9;1868:22;1864:31;1857:42;1967:2;1960;1956:7;1951:2;1943:6;1939:15;1935:29;1924:9;1920:45;1916:54;1908:62;;;1449:527;;;;:::o", "linkReferences": {}}, "methodIdentifiers": {"IS_SCRIPT()": "f8ccbf47", "run()": "c0406226"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"IS_SCRIPT\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"run\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"script/configuration/SetGasFees.s.sol\":\"SetGasFees\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"lib/forge-std/src/Base.sol\":{\"keccak256\":\"0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d\",\"dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z\"]},\"lib/forge-std/src/Script.sol\":{\"keccak256\":\"0xc942e27c7baae499beb01afbbae99f24d42af9a6e4aae675bc6901b704aa8e9b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0456008adf68947247f358b62863af4a8e349549d2260f2ff9569ff0e3cf5c98\",\"dweb:/ipfs/QmdviSUj2i7o3TPN5vd2xocqGMFVqjUzaiJTZRYyPxyHPx\"]},\"lib/forge-std/src/StdChains.sol\":{\"keccak256\":\"0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351\",\"dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm\"]},\"lib/forge-std/src/StdCheats.sol\":{\"keccak256\":\"0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41\",\"dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK\"]},\"lib/forge-std/src/StdConstants.sol\":{\"keccak256\":\"0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc\",\"dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r\"]},\"lib/forge-std/src/StdJson.sol\":{\"keccak256\":\"0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974\",\"dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3\"]},\"lib/forge-std/src/StdMath.sol\":{\"keccak256\":\"0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92\",\"dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC\"]},\"lib/forge-std/src/StdStorage.sol\":{\"keccak256\":\"0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57\",\"dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ\"]},\"lib/forge-std/src/StdStyle.sol\":{\"keccak256\":\"0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8\",\"dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK\"]},\"lib/forge-std/src/StdUtils.sol\":{\"keccak256\":\"0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138\",\"dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776\"]},\"lib/forge-std/src/Vm.sol\":{\"keccak256\":\"0xaae88142a348064677cf81169ea0ca706be3e99e2a0f13a01878ff8d52cf6b25\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://da4fb0ed1b31381c7e60ee386acc72796e8a8325c83739ec9a3678e2f841073c\",\"dweb:/ipfs/Qmb4MU6FpAi8Wt5kUQ3wa7ooHnfMVbdd9zuvnqbTehLmxJ\"]},\"lib/forge-std/src/console.sol\":{\"keccak256\":\"0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57\",\"dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP\"]},\"lib/forge-std/src/console2.sol\":{\"keccak256\":\"0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d\",\"dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ\"]},\"lib/forge-std/src/interfaces/IMulticall3.sol\":{\"keccak256\":\"0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0\",\"dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2\"]},\"lib/forge-std/src/safeconsole.sol\":{\"keccak256\":\"0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab\",\"dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3\"]},\"lib/openzeppelin-contracts/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"script/configuration/SetGasFees.s.sol\":{\"keccak256\":\"0x051e6740eee2ebfd98593e027f4b7821746fbbd01d61ba1778d47a65c28bd53d\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://48639cd17a7251b3f6bacd8cc7f72831ecda0cfea05879c29ff74082bb9366b3\",\"dweb:/ipfs/QmUFizyShEduuEjpDM1Wwb55MbCL7MrQrCcHTEcmtrjT8u\"]},\"src/oracles/gas/DefaultGasHelper.sol\":{\"keccak256\":\"0x16aa69f5dd078bd59ef74b1da4aa6baa96ae0046689c448f867082e6d35183b6\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://db3cfca6364c151aec0364c34568b755d836672ae37d93f5e11f821b4fb08809\",\"dweb:/ipfs/QmR4tivwaWzFkWtPazTFaimWUDUquEqhJ9Bgt1snctmoeB\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "view", "type": "function", "name": "IS_SCRIPT", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "run"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"script/configuration/SetGasFees.s.sol": "SetGasFees"}, "evmVersion": "london", "libraries": {}}, "sources": {"lib/forge-std/src/Base.sol": {"keccak256": "0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd", "urls": ["bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d", "dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z"], "license": "MIT"}, "lib/forge-std/src/Script.sol": {"keccak256": "0xc942e27c7baae499beb01afbbae99f24d42af9a6e4aae675bc6901b704aa8e9b", "urls": ["bzz-raw://0456008adf68947247f358b62863af4a8e349549d2260f2ff9569ff0e3cf5c98", "dweb:/ipfs/QmdviSUj2i7o3TPN5vd2xocqGMFVqjUzaiJTZRYyPxyHPx"], "license": "MIT"}, "lib/forge-std/src/StdChains.sol": {"keccak256": "0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e", "urls": ["bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351", "dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm"], "license": "MIT"}, "lib/forge-std/src/StdCheats.sol": {"keccak256": "0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746", "urls": ["bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41", "dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK"], "license": "MIT"}, "lib/forge-std/src/StdConstants.sol": {"keccak256": "0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534", "urls": ["bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc", "dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r"], "license": "MIT"}, "lib/forge-std/src/StdJson.sol": {"keccak256": "0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500", "urls": ["bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974", "dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3"], "license": "MIT"}, "lib/forge-std/src/StdMath.sol": {"keccak256": "0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2", "urls": ["bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92", "dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC"], "license": "MIT"}, "lib/forge-std/src/StdStorage.sol": {"keccak256": "0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc", "urls": ["bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57", "dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ"], "license": "MIT"}, "lib/forge-std/src/StdStyle.sol": {"keccak256": "0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d", "urls": ["bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8", "dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK"], "license": "MIT"}, "lib/forge-std/src/StdUtils.sol": {"keccak256": "0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737", "urls": ["bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138", "dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776"], "license": "MIT"}, "lib/forge-std/src/Vm.sol": {"keccak256": "0xaae88142a348064677cf81169ea0ca706be3e99e2a0f13a01878ff8d52cf6b25", "urls": ["bzz-raw://da4fb0ed1b31381c7e60ee386acc72796e8a8325c83739ec9a3678e2f841073c", "dweb:/ipfs/Qmb4MU6FpAi8Wt5kUQ3wa7ooHnfMVbdd9zuvnqbTehLmxJ"], "license": "MIT OR Apache-2.0"}, "lib/forge-std/src/console.sol": {"keccak256": "0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5", "urls": ["bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57", "dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP"], "license": "MIT"}, "lib/forge-std/src/console2.sol": {"keccak256": "0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f", "urls": ["bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d", "dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ"], "license": "MIT"}, "lib/forge-std/src/interfaces/IMulticall3.sol": {"keccak256": "0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a", "urls": ["bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0", "dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2"], "license": "MIT"}, "lib/forge-std/src/safeconsole.sol": {"keccak256": "0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11", "urls": ["bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab", "dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/access/Ownable.sol": {"keccak256": "0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb", "urls": ["bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6", "dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "script/configuration/SetGasFees.s.sol": {"keccak256": "0x051e6740eee2ebfd98593e027f4b7821746fbbd01d61ba1778d47a65c28bd53d", "urls": ["bzz-raw://48639cd17a7251b3f6bacd8cc7f72831ecda0cfea05879c29ff74082bb9366b3", "dweb:/ipfs/QmUFizyShEduuEjpDM1Wwb55MbCL7MrQrCcHTEcmtrjT8u"], "license": "BSL-1.1"}, "src/oracles/gas/DefaultGasHelper.sol": {"keccak256": "0x16aa69f5dd078bd59ef74b1da4aa6baa96ae0046689c448f867082e6d35183b6", "urls": ["bzz-raw://db3cfca6364c151aec0364c34568b755d836672ae37d93f5e11f821b4fb08809", "dweb:/ipfs/QmR4tivwaWzFkWtPazTFaimWUDUquEqhJ9Bgt1snctmoeB"], "license": "BSL-1.1"}}, "version": 1}, "id": 64}