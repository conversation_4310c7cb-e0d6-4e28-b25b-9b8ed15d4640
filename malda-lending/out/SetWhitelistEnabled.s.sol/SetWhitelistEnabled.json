{"abi": [{"type": "function", "name": "IS_SCRIPT", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "run", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}], "bytecode": {"object": "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", "sourceMap": "249:860:75:-:0;;;3126:44:3;;;-1:-1:-1;;849:28:1;;;;;249:860:75;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x608060405234801561001057600080fd5b50600436106100365760003560e01c8063c04062261461003b578063f8ccbf4714610045575b600080fd5b61004361006c565b005b600c546100589062010000900460ff1681565b604051901515815260200160405180910390f35b60405163c1978d1f60e01b815260206004820152600b60248201526a505249564154455f4b455960a81b6044820152600090737109709ecfa91a80626ff3989d68f67f5b1dd12d9063c1978d1f90606401602060405180830381865afa1580156100da573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906100fe9190610488565b6040805160078082526101008201909252919250600091906020820160e08036833701905050905073269c36a173d881720544fb303e681370158ff1fd8160008151811061014e5761014e6104a1565b60200260200101906001600160a01b031690816001600160a01b03168152505073c7bc6bd45eb84d594f51ced3c5497e6812c7732f81600181518110610196576101966104a1565b60200260200101906001600160a01b031690816001600160a01b03168152505073df0635c1ecfdf08146150691a97e2ff6a8aa1a90816002815181106101de576101de6104a1565b60200260200101906001600160a01b031690816001600160a01b031681525050732b588f7f4832561e46924f3ea54c24456972491581600381518110610226576102266104a1565b60200260200101906001600160a01b031690816001600160a01b031681525050731d8e8cefeb085f3211ab6a443ad9051b54d1cd1a8160048151811061026e5761026e6104a1565b60200260200101906001600160a01b031690816001600160a01b031681525050738bad0c523516262a439197736fff982f5e0987cc816005815181106102b6576102b66104a1565b60200260200101906001600160a01b031690816001600160a01b031681525050734df3dd62db219c47f6a7cb1be02c511afceadf5e816006815181106102fe576102fe6104a1565b60200260200101906001600160a01b031690816001600160a01b03168152505060005b815181101561048357600082828151811061033e5761033e6104a1565b60200260200101519050737109709ecfa91a80626ff3989d68f67f5b1dd12d6001600160a01b031663ce817d47856040518263ffffffff1660e01b815260040161038a91815260200190565b600060405180830381600087803b1580156103a457600080fd5b505af11580156103b8573d6000803e3d6000fd5b50505050806001600160a01b031663cdfb2b4e6040518163ffffffff1660e01b8152600401600060405180830381600087803b1580156103f757600080fd5b505af115801561040b573d6000803e3d6000fd5b50505050737109709ecfa91a80626ff3989d68f67f5b1dd12d6001600160a01b03166376eadd366040518163ffffffff1660e01b8152600401600060405180830381600087803b15801561045e57600080fd5b505af1158015610472573d6000803e3d6000fd5b505060019093019250610321915050565b505050565b60006020828403121561049a57600080fd5b5051919050565b634e487b7160e01b600052603260045260246000fdfea2646970667358221220dcedf34e8e2525980beb2cf30e9046d18bd802502462af2f1404759065a8686064736f6c634300081c0033", "sourceMap": "249:860:75:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;294:813;;;:::i;:::-;;849:28:1;;;;;;;;;;;;;;;179:14:242;;172:22;154:41;;142:2;127:18;849:28:1;;;;;;;294:813:75;348:25;;-1:-1:-1;;;348:25:75;;408:2:242;348:25:75;;;390:21:242;447:2;427:18;;;420:30;-1:-1:-1;;;466:18:242;;;459:41;334:11:75;;336:42:0;;348:10:75;;517:18:242;;348:25:75;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;411:16;;;425:1;411:16;;;;;;;;;334:39;;-1:-1:-1;384:24:75;;411:16;;;;;;;;;;;-1:-1:-1;411:16:75;384:43;;450:42;437:7;445:1;437:10;;;;;;;;:::i;:::-;;;;;;:55;-1:-1:-1;;;;;437:55:75;;;-1:-1:-1;;;;;437:55:75;;;;;515:42;502:7;510:1;502:10;;;;;;;;:::i;:::-;;;;;;:55;-1:-1:-1;;;;;502:55:75;;;-1:-1:-1;;;;;502:55:75;;;;;580:42;567:7;575:1;567:10;;;;;;;;:::i;:::-;;;;;;:55;-1:-1:-1;;;;;567:55:75;;;-1:-1:-1;;;;;567:55:75;;;;;645:42;632:7;640:1;632:10;;;;;;;;:::i;:::-;;;;;;:55;-1:-1:-1;;;;;632:55:75;;;-1:-1:-1;;;;;632:55:75;;;;;710:42;697:7;705:1;697:10;;;;;;;;:::i;:::-;;;;;;:55;-1:-1:-1;;;;;697:55:75;;;-1:-1:-1;;;;;697:55:75;;;;;775:42;762:7;770:1;762:10;;;;;;;;:::i;:::-;;;;;;:55;-1:-1:-1;;;;;762:55:75;;;-1:-1:-1;;;;;762:55:75;;;;;840:42;827:7;835:1;827:10;;;;;;;;:::i;:::-;;;;;;:55;-1:-1:-1;;;;;827:55:75;;;-1:-1:-1;;;;;827:55:75;;;;;898:9;893:208;913:7;:14;909:1;:18;893:208;;;948:14;965:7;973:1;965:10;;;;;;;;:::i;:::-;;;;;;;948:27;;336:42:0;-1:-1:-1;;;;;989:17:75;;1007:3;989:22;;;;;;;;;;;;;1145:25:242;;1133:2;1118:18;;999:177;989:22:75;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1033:6;-1:-1:-1;;;;;1025:31:75;;:33;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;336:42:0;-1:-1:-1;;;;;1072:16:75;;:18;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;929:3:75;;;;;-1:-1:-1;893:208:75;;-1:-1:-1;;893:208:75;;;324:783;;294:813::o;546:184:242:-;616:6;669:2;657:9;648:7;644:23;640:32;637:52;;;685:1;682;675:12;637:52;-1:-1:-1;708:16:242;;546:184;-1:-1:-1;546:184:242:o;867:127::-;928:10;923:3;919:20;916:1;909:31;959:4;956:1;949:15;983:4;980:1;973:15", "linkReferences": {}}, "methodIdentifiers": {"IS_SCRIPT()": "f8ccbf47", "run()": "c0406226"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"IS_SCRIPT\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"run\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"script/configuration/SetWhitelistEnabled.s.sol\":\"SetWhitelistEnabled\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"lib/forge-std/src/Base.sol\":{\"keccak256\":\"0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d\",\"dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z\"]},\"lib/forge-std/src/Script.sol\":{\"keccak256\":\"0xc942e27c7baae499beb01afbbae99f24d42af9a6e4aae675bc6901b704aa8e9b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0456008adf68947247f358b62863af4a8e349549d2260f2ff9569ff0e3cf5c98\",\"dweb:/ipfs/QmdviSUj2i7o3TPN5vd2xocqGMFVqjUzaiJTZRYyPxyHPx\"]},\"lib/forge-std/src/StdChains.sol\":{\"keccak256\":\"0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351\",\"dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm\"]},\"lib/forge-std/src/StdCheats.sol\":{\"keccak256\":\"0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41\",\"dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK\"]},\"lib/forge-std/src/StdConstants.sol\":{\"keccak256\":\"0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc\",\"dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r\"]},\"lib/forge-std/src/StdJson.sol\":{\"keccak256\":\"0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974\",\"dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3\"]},\"lib/forge-std/src/StdMath.sol\":{\"keccak256\":\"0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92\",\"dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC\"]},\"lib/forge-std/src/StdStorage.sol\":{\"keccak256\":\"0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57\",\"dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ\"]},\"lib/forge-std/src/StdStyle.sol\":{\"keccak256\":\"0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8\",\"dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK\"]},\"lib/forge-std/src/StdUtils.sol\":{\"keccak256\":\"0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138\",\"dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776\"]},\"lib/forge-std/src/Vm.sol\":{\"keccak256\":\"0xaae88142a348064677cf81169ea0ca706be3e99e2a0f13a01878ff8d52cf6b25\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://da4fb0ed1b31381c7e60ee386acc72796e8a8325c83739ec9a3678e2f841073c\",\"dweb:/ipfs/Qmb4MU6FpAi8Wt5kUQ3wa7ooHnfMVbdd9zuvnqbTehLmxJ\"]},\"lib/forge-std/src/console.sol\":{\"keccak256\":\"0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57\",\"dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP\"]},\"lib/forge-std/src/console2.sol\":{\"keccak256\":\"0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d\",\"dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ\"]},\"lib/forge-std/src/interfaces/IMulticall3.sol\":{\"keccak256\":\"0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0\",\"dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2\"]},\"lib/forge-std/src/safeconsole.sol\":{\"keccak256\":\"0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab\",\"dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol\":{\"keccak256\":\"0xc163fcf9bb10138631a9ba5564df1fa25db9adff73bd9ee868a8ae1858fe093a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9706d43a0124053d9880f6e31a59f31bc0a6a3dc1acd66ce0a16e1111658c5f6\",\"dweb:/ipfs/QmUFmfowzkRwGtDu36cXV9SPTBHJ3n7dG9xQiK5B28jTf2\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol\":{\"keccak256\":\"0x631188737069917d2f909d29ce62c4d48611d326686ba6683e26b72a23bfac0b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7a61054ae84cd6c4d04c0c4450ba1d6de41e27e0a2c4f1bcdf58f796b401c609\",\"dweb:/ipfs/QmUvtdp7X1mRVyC3CsHrtPbgoqWaXHp3S1ZR24tpAQYJWM\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol\":{\"keccak256\":\"0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9\",\"dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV\"]},\"lib/openzeppelin-contracts/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xc6a8ff0ea489379b61faa647490411b80102578440ab9d84e9a957cc12164e70\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0ea104e577e63faea3b69c415637e99e755dcbf64c5833d7140c35a714d6d90c\",\"dweb:/ipfs/Qmau6x4Ns9XdyynRCNNp3RhLqijJjFm7z5fyZazfYFGYdq\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol\":{\"keccak256\":\"0x6008dabfe393240d73d7dd7688033f72740d570aa422254d29a7dce8568f3aff\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f5196ec75139918c6c7bb4251b36395e668f1fa6d206beba7e7520e74913940d\",\"dweb:/ipfs/QmSyqjksXxmm2mCG6qRd1yuwLykypkSVBbnBnGqJRcuJMi\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol\":{\"keccak256\":\"0x37bb49513c49c87c4642a891b13b63571bc87013dde806617aa1efb54605f386\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b3036b3a83b7c48f96641f2a9002b9f2dcb6a5958dd670894ada21ae8229b3d0\",\"dweb:/ipfs/QmUNfSBdoVtjhETaUJCYcaC7pTMgbhht926tJ2uXJbiVd3\"]},\"lib/openzeppelin-contracts/contracts/utils/Address.sol\":{\"keccak256\":\"0xaf28a975a78550e45f65e559a3ad6a5ad43b9b8a37366999abd1b7084eb70721\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b7bd24e224f67f65bfadf85dc2929fa965456bb2415478bd0125471b5ce35245\",\"dweb:/ipfs/QmRaydGr8BTHs1kvaZfsNU69pKzUAGFrvABn1KiRSbE51y\"]},\"lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"lib/openzeppelin-contracts/contracts/utils/ReentrancyGuard.sol\":{\"keccak256\":\"0xf980daa263b661ab8ddee7d4fd833c7da7e7995e2c359ff1f17e67e4112f2236\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7448ab095d6940130bcf76ba47a2eab14148c83119523b93dd89f6d84edd6c02\",\"dweb:/ipfs/QmawrZ4voKQjH3oomXT3Kuheb3Mnmo2VvVpxg8Ne5UJUrd\"]},\"lib/risc0-ethereum/contracts/src/IRiscZeroVerifier.sol\":{\"keccak256\":\"0xae73e91e4d2829b0a88176584d876382b4b7540ff07c62cb947cdf9ea43d5cf3\",\"license\":\"Apache-2.0\",\"urls\":[\"bzz-raw://f2dd7da39a6f9b100fc1dcde565d799462cdc7d27722d2265317c3a01619c818\",\"dweb:/ipfs/Qmd3k3kcvrtfkLqVza4jHodbFH8wvq5vmfPtwekBKtPjmz\"]},\"lib/risc0-ethereum/contracts/src/Util.sol\":{\"keccak256\":\"0x230a9a58bb2b0c68d4921ecc3672783f941890a2fa3a238dbe5188cfb7658b82\",\"license\":\"Apache-2.0\",\"urls\":[\"bzz-raw://586cb5296c564761609ca8aaae5b1fbbdb19ab1e174f7197f602572d9e82b70c\",\"dweb:/ipfs/QmWxDmucSfg3Wgdk1rVcheDHwkziTsXWvKpKSCnxtpQBqg\"]},\"lib/risc0-ethereum/contracts/src/steel/Steel.sol\":{\"keccak256\":\"0x20811a302f2bc3e36fb612026319c83cf79c16aa56b822d40bcbe8173d1e9544\",\"license\":\"Apache-2.0\",\"urls\":[\"bzz-raw://ed9f7a15c3be0bdc79217feae9e773d280fcfb44aa450d37c6c891f0b505210f\",\"dweb:/ipfs/QmXHtzvLT2AHHHojJ42epUSBF4HYTaVohSmyZft6KZoVqE\"]},\"script/configuration/SetWhitelistEnabled.s.sol\":{\"keccak256\":\"0x21004049c15d08a750afa2df3592eb8e8428e7f1c49672208b5eff4c1833c1f2\",\"license\":\"UNLICENSED\",\"urls\":[\"bzz-raw://79b5dfa036619602222d32ba2ebd0950847fb4f5bf75f4d44166f5fb24f917f3\",\"dweb:/ipfs/QmYzthNNgpXKSCzFnUM4NarqdHt6134qDipGmrRNQWw5B7\"]},\"src/Operator/Operator.sol\":{\"keccak256\":\"0x1486ed6af6f40f0e045403ac9e35bc045ae7031d9ecac7d1f0358f78ff83b469\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://e136048d46ae9b4384f561e16dfd4638e68db987007d2059aaf19011dcfc8f65\",\"dweb:/ipfs/QmcyrVa45W2qYtd9UuJz9C89M3xKzKzACDQqVMAk4hvPeq\"]},\"src/Operator/OperatorStorage.sol\":{\"keccak256\":\"0x3d9e7f2bc64ab9b5de39d4a58f0a72ac0f86c3b95a6318dd03a7b8acde166783\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://dc0d5e026e00f5f5f92cded7704bb602b06f387370ddd7ee0d75111099b01f3a\",\"dweb:/ipfs/QmQfZzADQpBuvTHJpbHz6P4fGhaGWvQgkU8qMkKULCLdjQ\"]},\"src/interfaces/IBlacklister.sol\":{\"keccak256\":\"0x47ed20135507a9d16473027cbee18de5d952846bada761c6248b91c75cd88be1\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://91a886b95dd578156f9b6cec3236e4751aae8a38e5d15e0d6045217c6fbd3d63\",\"dweb:/ipfs/QmRNjz7sGTuMkQNZhwwcg5DNN2eeqTZSAdZmxWxbx66CHU\"]},\"src/interfaces/IGasFeesHelper.sol\":{\"keccak256\":\"0xb8d36f15aea38582370a4660ff72bba857a7f0050107b52e20f0171ba027ba2b\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://2db8bf066e2a10e0c49f839a577ed2bc3a5d07632ab646878fdba56a322fb7c0\",\"dweb:/ipfs/QmQWHYawhBzmKb7WM5TxyyZJBw6MerMUBu2UjoHj3uPK6d\"]},\"src/interfaces/IInterestRateModel.sol\":{\"keccak256\":\"0xdc8386be37a06d93fb0967be32e465f3b1f941ec9ce2f90984a5c70488cddb1e\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://de2bf96a7d0945f58b00e9fc867ac00fc4a09480e9e27c1a800604295320b769\",\"dweb:/ipfs/Qmf5sAgA58GjVxeipJNhP4euxwsZ9FS9CgsWTJ319UmCUE\"]},\"src/interfaces/IOperator.sol\":{\"keccak256\":\"0x0ae3d63eb07518da5a4fd6de7b013b6f55c87defb31900548378cffc70a51f72\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://1d9710c4ac43cb0563449aa519501b4ed18a331c90d0bc7eb79cdc56407bad8a\",\"dweb:/ipfs/Qmafe9Wup2GF9pD83CLWUrRait93vwvReqU1C7EHQhY7WV\"]},\"src/interfaces/IOracleOperator.sol\":{\"keccak256\":\"0xcc2848c75dd5e1531c2b958746cdd76a3b2b329be98fdca4311219a12a6b409e\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://337bdabf03ac279d95ba96aca60105932c7af5c15fdb4b21ab1c369509310fbc\",\"dweb:/ipfs/QmNYrNYNUwWWSrPwHdarhxt1ShdH1dy8B94M49TZ7XvmK1\"]},\"src/interfaces/IRewardDistributor.sol\":{\"keccak256\":\"0xd39c4db6e2a85e90db82e20685d4b235966d993c3407e1c0fb52add6421b86df\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://e865fe1569ec1ac79cd924a9aa2e2b43754555e510526f6cd030395e9923f01d\",\"dweb:/ipfs/QmXcshgzCxLKBZJYGuQviEamruhmwXWNJ3ZYLxazWBahuc\"]},\"src/interfaces/IRoles.sol\":{\"keccak256\":\"0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c\",\"dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj\"]},\"src/interfaces/ImErc20.sol\":{\"keccak256\":\"0x86a1dcaf0d188107ceae7c5eb0911ac63feafbbeda6d0686e4f9950a51d17888\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://d7dd8e0fcfd68501d9f40e37e1c95d8cb653d65626aeeeb5492b73c0e967e6cb\",\"dweb:/ipfs/QmcadG9QuHtAX5qxTKzNr6FMaXSgijr542tFUVTLBkYhAF\"]},\"src/interfaces/ImErc20Host.sol\":{\"keccak256\":\"0x90f1ba59e63b0bd8d11deb1154bb885906daf858e81ff3eca579db73281a1577\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://4202334b0825195dc2d50cf29420f4bcebdf1d91ebcadc56155d92243f133c11\",\"dweb:/ipfs/QmVrcpP8YcTHNUCGJQCeBMUZU9VMpvsvUfVwN14pVzkD5o\"]},\"src/interfaces/ImToken.sol\":{\"keccak256\":\"0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36\",\"dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL\"]},\"src/libraries/BytesLib.sol\":{\"keccak256\":\"0xfaf080296887274c71c2f2eb3552c4742c96ff24616c61f8ab98399f30b18ef0\",\"license\":\"Unlicense\",\"urls\":[\"bzz-raw://02dcb01b6c9cf4f158792a15e9b40d046332949cd6b2c1e5e54e474901cf579b\",\"dweb:/ipfs/QmWiBhygucYoY8Uu6WQPao2r64Y25Vmmj3gYyZZ6Wy9wnE\"]},\"src/libraries/CommonLib.sol\":{\"keccak256\":\"0x623d4c8f1f699570493de557fe93b355a662ca6caab81f4c84b02777fd7d31d8\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://866dc2b2215ae0dae498833b8d647d0c8b011d9086eeadc8fd6acfac2191c232\",\"dweb:/ipfs/QmRwNuVDFD4wjo4CjgMaJJKQ8yZU3PgS6L3k6NkCJnwyNZ\"]},\"src/libraries/mTokenProofDecoderLib.sol\":{\"keccak256\":\"0x00216e7389b2d64450d9d13b648f80e459742e1dd91dec543d415df920f8ce71\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://660deae410969bca243a7b8ad2e6ca6d1cb11d3647af0e227355e5b74f949ea0\",\"dweb:/ipfs/QmUmynqsY1kdEoWHNwqHEVBedepdDBaNHotAP7CCQ7PBzN\"]},\"src/mToken/host/mErc20Host.sol\":{\"keccak256\":\"0x65a79ff94abf90ab7cb2720702872324049bc70f668987fdc9affa0cb5a83e3e\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://7e31aeb1b939cff561cb07e924df3e43c62a93654f742d5d4f3e45d98f8ec586\",\"dweb:/ipfs/QmXPzSvPLYf2WVrfkuxuuSyt2AZw7b8efiz1KrtgxF4oJG\"]},\"src/mToken/mErc20.sol\":{\"keccak256\":\"0x46aa77f6808c1ca56c7d51b4822bc03d26d036151b4eeb324a3dbdc52bc90643\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://4681c86b025cb7d0872c003146b4e6d56b2e86387c832134f4224fc9d559218c\",\"dweb:/ipfs/QmWM9zVEmNWYSYEvGPd8jwSioG8zBC3hQnCEHbup1gCCLe\"]},\"src/mToken/mErc20Upgradable.sol\":{\"keccak256\":\"0x52fb217ef3aff11698af9e57f237fa449ca536a072c5fa3b3fcb5206ff3d7a99\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://4077d30ef391bb5d4c2a3bab863e8e5db105c4f05d1a80c382c34ea83ebed431\",\"dweb:/ipfs/QmXC7ofa9Nvyqgw2H9TLQqkCoD4ThT6SAx8CZ2GP2rRJft\"]},\"src/mToken/mToken.sol\":{\"keccak256\":\"0xeefa3394ae7a01c38bc97404ca6375a497e0bce2a8ae3f83feb4c5bd681aaf43\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://cd94a416031e33c169d36692a661899dc9697b8c543bdb8c051f70a3e7eced2c\",\"dweb:/ipfs/QmRBoo7LQ5nbwLpBjB9yEEib8RM5i9yQjfeE7FELHXvBBk\"]},\"src/mToken/mTokenConfiguration.sol\":{\"keccak256\":\"0x4ac1b80b93e3b13ce568f859c1fc4dd54360578d6055027819fee3e6d0b2b83f\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://e27a83d36a038105631a1ce53f92d225b9949a2cc6aed6d898c736850c10676a\",\"dweb:/ipfs/QmZhvhCfZsDxvyYJ1x9TNSfnHKrxpbgLq3qr7hJiL3XpZa\"]},\"src/mToken/mTokenStorage.sol\":{\"keccak256\":\"0x42236cd9e37c2eef2d317f05510bbe21cf5174813ee40f9a6fcbaf84d4fed2f7\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://2679166e1ca0e5d21294447566bba7549900a8f9a9b1882c8c2895a9751a1792\",\"dweb:/ipfs/Qmb49FVvre1He18e6ABHjNJ2yzCPdkYeyzSwMXixZ7fUw3\"]},\"src/migration/IMigrator.sol\":{\"keccak256\":\"0xbb7b40994ce7362a28dc4997d11105d3f5a904f26432f735ff68622d779341a8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://091dee95ab811def8446c4a5768b3caafe5504c5d16955fc0adb76ee6140198b\",\"dweb:/ipfs/QmNqVbBQ7QuN1NgtFiZVSPtwq5UnBAKes9TxC9fgBueNvy\"]},\"src/migration/Migrator.sol\":{\"keccak256\":\"0xa34d08708ab2976389b2f8f5255869440d47e2f7f9849eafd6e7221defc64720\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://52331e338f89658fad9ac954e9c9ce274418056645b42d144ba215c96e9a402e\",\"dweb:/ipfs/Qmc4VzufbwwAuJbqkWWs2V11hodnkiTK4Q5NZFAEctEhNQ\"]},\"src/utils/ExponentialNoError.sol\":{\"keccak256\":\"0xa97178117d8c32059d55dd2224e5514c54b31f896e565c3834cab3d7502b1bb6\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://1cecc0713709f57a5bb7f64d2fa06b9437a42752fa3fed5d3c0b19d82aa56ef4\",\"dweb:/ipfs/QmQqBCZkzmZxomXLr3b1tHUV55n8EPk3wAUsXgojYSpR41\"]},\"src/verifier/ZkVerifier.sol\":{\"keccak256\":\"0x1abaa285ad4ee09461aae5df4aecff9bd38b2b3da19b5a920ab1b7d2208e04ec\",\"license\":\"AGPL-3.0\",\"urls\":[\"bzz-raw://7db45391afa7244f70303c5ace73b7937ee30261fd2015e4a7bfa11ec85480cc\",\"dweb:/ipfs/QmTdhCwcbTCRCMF1ifCJoifqwxhKpKBEE8zq9ph6hYhogG\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "view", "type": "function", "name": "IS_SCRIPT", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "run"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"script/configuration/SetWhitelistEnabled.s.sol": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "evmVersion": "london", "libraries": {}}, "sources": {"lib/forge-std/src/Base.sol": {"keccak256": "0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd", "urls": ["bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d", "dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z"], "license": "MIT"}, "lib/forge-std/src/Script.sol": {"keccak256": "0xc942e27c7baae499beb01afbbae99f24d42af9a6e4aae675bc6901b704aa8e9b", "urls": ["bzz-raw://0456008adf68947247f358b62863af4a8e349549d2260f2ff9569ff0e3cf5c98", "dweb:/ipfs/QmdviSUj2i7o3TPN5vd2xocqGMFVqjUzaiJTZRYyPxyHPx"], "license": "MIT"}, "lib/forge-std/src/StdChains.sol": {"keccak256": "0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e", "urls": ["bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351", "dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm"], "license": "MIT"}, "lib/forge-std/src/StdCheats.sol": {"keccak256": "0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746", "urls": ["bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41", "dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK"], "license": "MIT"}, "lib/forge-std/src/StdConstants.sol": {"keccak256": "0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534", "urls": ["bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc", "dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r"], "license": "MIT"}, "lib/forge-std/src/StdJson.sol": {"keccak256": "0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500", "urls": ["bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974", "dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3"], "license": "MIT"}, "lib/forge-std/src/StdMath.sol": {"keccak256": "0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2", "urls": ["bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92", "dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC"], "license": "MIT"}, "lib/forge-std/src/StdStorage.sol": {"keccak256": "0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc", "urls": ["bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57", "dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ"], "license": "MIT"}, "lib/forge-std/src/StdStyle.sol": {"keccak256": "0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d", "urls": ["bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8", "dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK"], "license": "MIT"}, "lib/forge-std/src/StdUtils.sol": {"keccak256": "0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737", "urls": ["bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138", "dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776"], "license": "MIT"}, "lib/forge-std/src/Vm.sol": {"keccak256": "0xaae88142a348064677cf81169ea0ca706be3e99e2a0f13a01878ff8d52cf6b25", "urls": ["bzz-raw://da4fb0ed1b31381c7e60ee386acc72796e8a8325c83739ec9a3678e2f841073c", "dweb:/ipfs/Qmb4MU6FpAi8Wt5kUQ3wa7ooHnfMVbdd9zuvnqbTehLmxJ"], "license": "MIT OR Apache-2.0"}, "lib/forge-std/src/console.sol": {"keccak256": "0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5", "urls": ["bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57", "dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP"], "license": "MIT"}, "lib/forge-std/src/console2.sol": {"keccak256": "0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f", "urls": ["bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d", "dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ"], "license": "MIT"}, "lib/forge-std/src/interfaces/IMulticall3.sol": {"keccak256": "0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a", "urls": ["bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0", "dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2"], "license": "MIT"}, "lib/forge-std/src/safeconsole.sol": {"keccak256": "0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11", "urls": ["bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab", "dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol": {"keccak256": "0xc163fcf9bb10138631a9ba5564df1fa25db9adff73bd9ee868a8ae1858fe093a", "urls": ["bzz-raw://9706d43a0124053d9880f6e31a59f31bc0a6a3dc1acd66ce0a16e1111658c5f6", "dweb:/ipfs/QmUFmfowzkRwGtDu36cXV9SPTBHJ3n7dG9xQiK5B28jTf2"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol": {"keccak256": "0x631188737069917d2f909d29ce62c4d48611d326686ba6683e26b72a23bfac0b", "urls": ["bzz-raw://7a61054ae84cd6c4d04c0c4450ba1d6de41e27e0a2c4f1bcdf58f796b401c609", "dweb:/ipfs/QmUvtdp7X1mRVyC3CsHrtPbgoqWaXHp3S1ZR24tpAQYJWM"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol": {"keccak256": "0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397", "urls": ["bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9", "dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/access/Ownable.sol": {"keccak256": "0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb", "urls": ["bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6", "dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0xc6a8ff0ea489379b61faa647490411b80102578440ab9d84e9a957cc12164e70", "urls": ["bzz-raw://0ea104e577e63faea3b69c415637e99e755dcbf64c5833d7140c35a714d6d90c", "dweb:/ipfs/Qmau6x4Ns9XdyynRCNNp3RhLqijJjFm7z5fyZazfYFGYdq"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol": {"keccak256": "0x6008dabfe393240d73d7dd7688033f72740d570aa422254d29a7dce8568f3aff", "urls": ["bzz-raw://f5196ec75139918c6c7bb4251b36395e668f1fa6d206beba7e7520e74913940d", "dweb:/ipfs/QmSyqjksXxmm2mCG6qRd1yuwLykypkSVBbnBnGqJRcuJMi"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol": {"keccak256": "0x37bb49513c49c87c4642a891b13b63571bc87013dde806617aa1efb54605f386", "urls": ["bzz-raw://b3036b3a83b7c48f96641f2a9002b9f2dcb6a5958dd670894ada21ae8229b3d0", "dweb:/ipfs/QmUNfSBdoVtjhETaUJCYcaC7pTMgbhht926tJ2uXJbiVd3"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Address.sol": {"keccak256": "0xaf28a975a78550e45f65e559a3ad6a5ad43b9b8a37366999abd1b7084eb70721", "urls": ["bzz-raw://b7bd24e224f67f65bfadf85dc2929fa965456bb2415478bd0125471b5ce35245", "dweb:/ipfs/QmRaydGr8BTHs1kvaZfsNU69pKzUAGFrvABn1KiRSbE51y"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/ReentrancyGuard.sol": {"keccak256": "0xf980daa263b661ab8ddee7d4fd833c7da7e7995e2c359ff1f17e67e4112f2236", "urls": ["bzz-raw://7448ab095d6940130bcf76ba47a2eab14148c83119523b93dd89f6d84edd6c02", "dweb:/ipfs/QmawrZ4voKQjH3oomXT3Kuheb3Mnmo2VvVpxg8Ne5UJUrd"], "license": "MIT"}, "lib/risc0-ethereum/contracts/src/IRiscZeroVerifier.sol": {"keccak256": "0xae73e91e4d2829b0a88176584d876382b4b7540ff07c62cb947cdf9ea43d5cf3", "urls": ["bzz-raw://f2dd7da39a6f9b100fc1dcde565d799462cdc7d27722d2265317c3a01619c818", "dweb:/ipfs/Qmd3k3kcvrtfkLqVza4jHodbFH8wvq5vmfPtwekBKtPjmz"], "license": "Apache-2.0"}, "lib/risc0-ethereum/contracts/src/Util.sol": {"keccak256": "0x230a9a58bb2b0c68d4921ecc3672783f941890a2fa3a238dbe5188cfb7658b82", "urls": ["bzz-raw://586cb5296c564761609ca8aaae5b1fbbdb19ab1e174f7197f602572d9e82b70c", "dweb:/ipfs/QmWxDmucSfg3Wgdk1rVcheDHwkziTsXWvKpKSCnxtpQBqg"], "license": "Apache-2.0"}, "lib/risc0-ethereum/contracts/src/steel/Steel.sol": {"keccak256": "0x20811a302f2bc3e36fb612026319c83cf79c16aa56b822d40bcbe8173d1e9544", "urls": ["bzz-raw://ed9f7a15c3be0bdc79217feae9e773d280fcfb44aa450d37c6c891f0b505210f", "dweb:/ipfs/QmXHtzvLT2AHHHojJ42epUSBF4HYTaVohSmyZft6KZoVqE"], "license": "Apache-2.0"}, "script/configuration/SetWhitelistEnabled.s.sol": {"keccak256": "0x21004049c15d08a750afa2df3592eb8e8428e7f1c49672208b5eff4c1833c1f2", "urls": ["bzz-raw://79b5dfa036619602222d32ba2ebd0950847fb4f5bf75f4d44166f5fb24f917f3", "dweb:/ipfs/QmYzthNNgpXKSCzFnUM4NarqdHt6134qDipGmrRNQWw5B7"], "license": "UNLICENSED"}, "src/Operator/Operator.sol": {"keccak256": "0x1486ed6af6f40f0e045403ac9e35bc045ae7031d9ecac7d1f0358f78ff83b469", "urls": ["bzz-raw://e136048d46ae9b4384f561e16dfd4638e68db987007d2059aaf19011dcfc8f65", "dweb:/ipfs/QmcyrVa45W2qYtd9UuJz9C89M3xKzKzACDQqVMAk4hvPeq"], "license": "BSL-1.1"}, "src/Operator/OperatorStorage.sol": {"keccak256": "0x3d9e7f2bc64ab9b5de39d4a58f0a72ac0f86c3b95a6318dd03a7b8acde166783", "urls": ["bzz-raw://dc0d5e026e00f5f5f92cded7704bb602b06f387370ddd7ee0d75111099b01f3a", "dweb:/ipfs/QmQfZzADQpBuvTHJpbHz6P4fGhaGWvQgkU8qMkKULCLdjQ"], "license": "BSL-1.1"}, "src/interfaces/IBlacklister.sol": {"keccak256": "0x47ed20135507a9d16473027cbee18de5d952846bada761c6248b91c75cd88be1", "urls": ["bzz-raw://91a886b95dd578156f9b6cec3236e4751aae8a38e5d15e0d6045217c6fbd3d63", "dweb:/ipfs/QmRNjz7sGTuMkQNZhwwcg5DNN2eeqTZSAdZmxWxbx66CHU"], "license": "BSL-1.1"}, "src/interfaces/IGasFeesHelper.sol": {"keccak256": "0xb8d36f15aea38582370a4660ff72bba857a7f0050107b52e20f0171ba027ba2b", "urls": ["bzz-raw://2db8bf066e2a10e0c49f839a577ed2bc3a5d07632ab646878fdba56a322fb7c0", "dweb:/ipfs/QmQWHYawhBzmKb7WM5TxyyZJBw6MerMUBu2UjoHj3uPK6d"], "license": "BSL-1.1"}, "src/interfaces/IInterestRateModel.sol": {"keccak256": "0xdc8386be37a06d93fb0967be32e465f3b1f941ec9ce2f90984a5c70488cddb1e", "urls": ["bzz-raw://de2bf96a7d0945f58b00e9fc867ac00fc4a09480e9e27c1a800604295320b769", "dweb:/ipfs/Qmf5sAgA58GjVxeipJNhP4euxwsZ9FS9CgsWTJ319UmCUE"], "license": "BSL-1.1"}, "src/interfaces/IOperator.sol": {"keccak256": "0x0ae3d63eb07518da5a4fd6de7b013b6f55c87defb31900548378cffc70a51f72", "urls": ["bzz-raw://1d9710c4ac43cb0563449aa519501b4ed18a331c90d0bc7eb79cdc56407bad8a", "dweb:/ipfs/Qmafe9Wup2GF9pD83CLWUrRait93vwvReqU1C7EHQhY7WV"], "license": "BSL-1.1"}, "src/interfaces/IOracleOperator.sol": {"keccak256": "0xcc2848c75dd5e1531c2b958746cdd76a3b2b329be98fdca4311219a12a6b409e", "urls": ["bzz-raw://337bdabf03ac279d95ba96aca60105932c7af5c15fdb4b21ab1c369509310fbc", "dweb:/ipfs/QmNYrNYNUwWWSrPwHdarhxt1ShdH1dy8B94M49TZ7XvmK1"], "license": "BSL-1.1"}, "src/interfaces/IRewardDistributor.sol": {"keccak256": "0xd39c4db6e2a85e90db82e20685d4b235966d993c3407e1c0fb52add6421b86df", "urls": ["bzz-raw://e865fe1569ec1ac79cd924a9aa2e2b43754555e510526f6cd030395e9923f01d", "dweb:/ipfs/QmXcshgzCxLKBZJYGuQviEamruhmwXWNJ3ZYLxazWBahuc"], "license": "BSL-1.1"}, "src/interfaces/IRoles.sol": {"keccak256": "0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365", "urls": ["bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c", "dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj"], "license": "BSL-1.1"}, "src/interfaces/ImErc20.sol": {"keccak256": "0x86a1dcaf0d188107ceae7c5eb0911ac63feafbbeda6d0686e4f9950a51d17888", "urls": ["bzz-raw://d7dd8e0fcfd68501d9f40e37e1c95d8cb653d65626aeeeb5492b73c0e967e6cb", "dweb:/ipfs/QmcadG9QuHtAX5qxTKzNr6FMaXSgijr542tFUVTLBkYhAF"], "license": "BSL-1.1"}, "src/interfaces/ImErc20Host.sol": {"keccak256": "0x90f1ba59e63b0bd8d11deb1154bb885906daf858e81ff3eca579db73281a1577", "urls": ["bzz-raw://4202334b0825195dc2d50cf29420f4bcebdf1d91ebcadc56155d92243f133c11", "dweb:/ipfs/QmVrcpP8YcTHNUCGJQCeBMUZU9VMpvsvUfVwN14pVzkD5o"], "license": "BSL-1.1"}, "src/interfaces/ImToken.sol": {"keccak256": "0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f", "urls": ["bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36", "dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL"], "license": "BSL-1.1"}, "src/libraries/BytesLib.sol": {"keccak256": "0xfaf080296887274c71c2f2eb3552c4742c96ff24616c61f8ab98399f30b18ef0", "urls": ["bzz-raw://02dcb01b6c9cf4f158792a15e9b40d046332949cd6b2c1e5e54e474901cf579b", "dweb:/ipfs/QmWiBhygucYoY8Uu6WQPao2r64Y25Vmmj3gYyZZ6Wy9wnE"], "license": "Unlicense"}, "src/libraries/CommonLib.sol": {"keccak256": "0x623d4c8f1f699570493de557fe93b355a662ca6caab81f4c84b02777fd7d31d8", "urls": ["bzz-raw://866dc2b2215ae0dae498833b8d647d0c8b011d9086eeadc8fd6acfac2191c232", "dweb:/ipfs/QmRwNuVDFD4wjo4CjgMaJJKQ8yZU3PgS6L3k6NkCJnwyNZ"], "license": "BSL-1.1"}, "src/libraries/mTokenProofDecoderLib.sol": {"keccak256": "0x00216e7389b2d64450d9d13b648f80e459742e1dd91dec543d415df920f8ce71", "urls": ["bzz-raw://660deae410969bca243a7b8ad2e6ca6d1cb11d3647af0e227355e5b74f949ea0", "dweb:/ipfs/QmUmynqsY1kdEoWHNwqHEVBedepdDBaNHotAP7CCQ7PBzN"], "license": "BSL-1.1"}, "src/mToken/host/mErc20Host.sol": {"keccak256": "0x65a79ff94abf90ab7cb2720702872324049bc70f668987fdc9affa0cb5a83e3e", "urls": ["bzz-raw://7e31aeb1b939cff561cb07e924df3e43c62a93654f742d5d4f3e45d98f8ec586", "dweb:/ipfs/QmXPzSvPLYf2WVrfkuxuuSyt2AZw7b8efiz1KrtgxF4oJG"], "license": "BSL-1.1"}, "src/mToken/mErc20.sol": {"keccak256": "0x46aa77f6808c1ca56c7d51b4822bc03d26d036151b4eeb324a3dbdc52bc90643", "urls": ["bzz-raw://4681c86b025cb7d0872c003146b4e6d56b2e86387c832134f4224fc9d559218c", "dweb:/ipfs/QmWM9zVEmNWYSYEvGPd8jwSioG8zBC3hQnCEHbup1gCCLe"], "license": "BSL-1.1"}, "src/mToken/mErc20Upgradable.sol": {"keccak256": "0x52fb217ef3aff11698af9e57f237fa449ca536a072c5fa3b3fcb5206ff3d7a99", "urls": ["bzz-raw://4077d30ef391bb5d4c2a3bab863e8e5db105c4f05d1a80c382c34ea83ebed431", "dweb:/ipfs/QmXC7ofa9Nvyqgw2H9TLQqkCoD4ThT6SAx8CZ2GP2rRJft"], "license": "BSL-1.1"}, "src/mToken/mToken.sol": {"keccak256": "0xeefa3394ae7a01c38bc97404ca6375a497e0bce2a8ae3f83feb4c5bd681aaf43", "urls": ["bzz-raw://cd94a416031e33c169d36692a661899dc9697b8c543bdb8c051f70a3e7eced2c", "dweb:/ipfs/QmRBoo7LQ5nbwLpBjB9yEEib8RM5i9yQjfeE7FELHXvBBk"], "license": "BSL-1.1"}, "src/mToken/mTokenConfiguration.sol": {"keccak256": "0x4ac1b80b93e3b13ce568f859c1fc4dd54360578d6055027819fee3e6d0b2b83f", "urls": ["bzz-raw://e27a83d36a038105631a1ce53f92d225b9949a2cc6aed6d898c736850c10676a", "dweb:/ipfs/QmZhvhCfZsDxvyYJ1x9TNSfnHKrxpbgLq3qr7hJiL3XpZa"], "license": "BSL-1.1"}, "src/mToken/mTokenStorage.sol": {"keccak256": "0x42236cd9e37c2eef2d317f05510bbe21cf5174813ee40f9a6fcbaf84d4fed2f7", "urls": ["bzz-raw://2679166e1ca0e5d21294447566bba7549900a8f9a9b1882c8c2895a9751a1792", "dweb:/ipfs/Qmb49FVvre1He18e6ABHjNJ2yzCPdkYeyzSwMXixZ7fUw3"], "license": "BSL-1.1"}, "src/migration/IMigrator.sol": {"keccak256": "0xbb7b40994ce7362a28dc4997d11105d3f5a904f26432f735ff68622d779341a8", "urls": ["bzz-raw://091dee95ab811def8446c4a5768b3caafe5504c5d16955fc0adb76ee6140198b", "dweb:/ipfs/QmNqVbBQ7QuN1NgtFiZVSPtwq5UnBAKes9TxC9fgBueNvy"], "license": "MIT"}, "src/migration/Migrator.sol": {"keccak256": "0xa34d08708ab2976389b2f8f5255869440d47e2f7f9849eafd6e7221defc64720", "urls": ["bzz-raw://52331e338f89658fad9ac954e9c9ce274418056645b42d144ba215c96e9a402e", "dweb:/ipfs/Qmc4VzufbwwAuJbqkWWs2V11hodnkiTK4Q5NZFAEctEhNQ"], "license": "MIT"}, "src/utils/ExponentialNoError.sol": {"keccak256": "0xa97178117d8c32059d55dd2224e5514c54b31f896e565c3834cab3d7502b1bb6", "urls": ["bzz-raw://1cecc0713709f57a5bb7f64d2fa06b9437a42752fa3fed5d3c0b19d82aa56ef4", "dweb:/ipfs/QmQqBCZkzmZxomXLr3b1tHUV55n8EPk3wAUsXgojYSpR41"], "license": "BSL-1.1"}, "src/verifier/ZkVerifier.sol": {"keccak256": "0x1abaa285ad4ee09461aae5df4aecff9bd38b2b3da19b5a920ab1b7d2208e04ec", "urls": ["bzz-raw://7db45391afa7244f70303c5ace73b7937ee30261fd2015e4a7bfa11ec85480cc", "dweb:/ipfs/QmTdhCwcbTCRCMF1ifCJoifqwxhKpKBEE8zq9ph6hYhogG"], "license": "AGPL-3.0"}}, "version": 1}, "id": 75}