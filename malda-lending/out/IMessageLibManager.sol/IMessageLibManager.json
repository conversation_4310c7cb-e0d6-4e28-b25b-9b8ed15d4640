{"abi": [{"type": "function", "name": "defaultReceiveLibrary", "inputs": [{"name": "_eid", "type": "uint32", "internalType": "uint32"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "defaultReceiveLibraryTimeout", "inputs": [{"name": "_eid", "type": "uint32", "internalType": "uint32"}], "outputs": [{"name": "lib", "type": "address", "internalType": "address"}, {"name": "expiry", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "defaultSendLibrary", "inputs": [{"name": "_eid", "type": "uint32", "internalType": "uint32"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "getConfig", "inputs": [{"name": "_oapp", "type": "address", "internalType": "address"}, {"name": "_lib", "type": "address", "internalType": "address"}, {"name": "_eid", "type": "uint32", "internalType": "uint32"}, {"name": "_configType", "type": "uint32", "internalType": "uint32"}], "outputs": [{"name": "config", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "getReceiveLibrary", "inputs": [{"name": "_receiver", "type": "address", "internalType": "address"}, {"name": "_eid", "type": "uint32", "internalType": "uint32"}], "outputs": [{"name": "lib", "type": "address", "internalType": "address"}, {"name": "isDefault", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "getRegisteredLibraries", "inputs": [], "outputs": [{"name": "", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "getSendLibrary", "inputs": [{"name": "_sender", "type": "address", "internalType": "address"}, {"name": "_eid", "type": "uint32", "internalType": "uint32"}], "outputs": [{"name": "lib", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "isDefaultSendLibrary", "inputs": [{"name": "_sender", "type": "address", "internalType": "address"}, {"name": "_eid", "type": "uint32", "internalType": "uint32"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isRegisteredLibrary", "inputs": [{"name": "_lib", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isSupportedEid", "inputs": [{"name": "_eid", "type": "uint32", "internalType": "uint32"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isValidReceiveLibrary", "inputs": [{"name": "_receiver", "type": "address", "internalType": "address"}, {"name": "_eid", "type": "uint32", "internalType": "uint32"}, {"name": "_lib", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "receiveLibraryTimeout", "inputs": [{"name": "_receiver", "type": "address", "internalType": "address"}, {"name": "_eid", "type": "uint32", "internalType": "uint32"}], "outputs": [{"name": "lib", "type": "address", "internalType": "address"}, {"name": "expiry", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "registerLibrary", "inputs": [{"name": "_lib", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setConfig", "inputs": [{"name": "_oapp", "type": "address", "internalType": "address"}, {"name": "_lib", "type": "address", "internalType": "address"}, {"name": "_params", "type": "tuple[]", "internalType": "struct SetConfigParam[]", "components": [{"name": "eid", "type": "uint32", "internalType": "uint32"}, {"name": "configType", "type": "uint32", "internalType": "uint32"}, {"name": "config", "type": "bytes", "internalType": "bytes"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setDefaultReceiveLibrary", "inputs": [{"name": "_eid", "type": "uint32", "internalType": "uint32"}, {"name": "_newLib", "type": "address", "internalType": "address"}, {"name": "_<PERSON><PERSON><PERSON><PERSON>", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setDefaultReceiveLibraryTimeout", "inputs": [{"name": "_eid", "type": "uint32", "internalType": "uint32"}, {"name": "_lib", "type": "address", "internalType": "address"}, {"name": "_expiry", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setDefaultSendLibrary", "inputs": [{"name": "_eid", "type": "uint32", "internalType": "uint32"}, {"name": "_newLib", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setReceiveLibrary", "inputs": [{"name": "_oapp", "type": "address", "internalType": "address"}, {"name": "_eid", "type": "uint32", "internalType": "uint32"}, {"name": "_newLib", "type": "address", "internalType": "address"}, {"name": "_<PERSON><PERSON><PERSON><PERSON>", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setReceiveLibraryTimeout", "inputs": [{"name": "_oapp", "type": "address", "internalType": "address"}, {"name": "_eid", "type": "uint32", "internalType": "uint32"}, {"name": "_lib", "type": "address", "internalType": "address"}, {"name": "_expiry", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setSendLibrary", "inputs": [{"name": "_oapp", "type": "address", "internalType": "address"}, {"name": "_eid", "type": "uint32", "internalType": "uint32"}, {"name": "_newLib", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "DefaultReceiveLibrarySet", "inputs": [{"name": "eid", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "newLib", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "DefaultReceiveLibraryTimeoutSet", "inputs": [{"name": "eid", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "oldLib", "type": "address", "indexed": false, "internalType": "address"}, {"name": "expiry", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "DefaultSendLibrarySet", "inputs": [{"name": "eid", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "newLib", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "LibraryRegistered", "inputs": [{"name": "newLib", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "ReceiveLibrarySet", "inputs": [{"name": "receiver", "type": "address", "indexed": false, "internalType": "address"}, {"name": "eid", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "newLib", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "ReceiveLibraryTimeoutSet", "inputs": [{"name": "receiver", "type": "address", "indexed": false, "internalType": "address"}, {"name": "eid", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "oldLib", "type": "address", "indexed": false, "internalType": "address"}, {"name": "timeout", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "SendLibrarySet", "inputs": [{"name": "sender", "type": "address", "indexed": false, "internalType": "address"}, {"name": "eid", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "newLib", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"defaultReceiveLibrary(uint32)": "6f50a803", "defaultReceiveLibraryTimeout(uint32)": "6e83f5bb", "defaultSendLibrary(uint32)": "f64be4c7", "getConfig(address,address,uint32,uint32)": "2b3197b9", "getReceiveLibrary(address,uint32)": "402f8468", "getRegisteredLibraries()": "9132e5c3", "getSendLibrary(address,uint32)": "b96a277f", "isDefaultSendLibrary(address,uint32)": "dc93c8a2", "isRegisteredLibrary(address)": "dc706a62", "isSupportedEid(uint32)": "6750cd4c", "isValidReceiveLibrary(address,uint32,address)": "9d7f9775", "receiveLibraryTimeout(address,uint32)": "ef667aa1", "registerLibrary(address)": "e8964e81", "setConfig(address,address,(uint32,uint32,bytes)[])": "6dbd9f90", "setDefaultReceiveLibrary(uint32,address,uint256)": "a718531b", "setDefaultReceiveLibraryTimeout(uint32,address,uint256)": "d4b4ec8f", "setDefaultSendLibrary(uint32,address)": "aafea312", "setReceiveLibrary(address,uint32,address,uint256)": "6a14d715", "setReceiveLibraryTimeout(address,uint32,address,uint256)": "183c834f", "setSendLibrary(address,uint32,address)": "9535ff30"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"eid\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"newLib\",\"type\":\"address\"}],\"name\":\"DefaultReceiveLibrarySet\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"eid\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"oldLib\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"expiry\",\"type\":\"uint256\"}],\"name\":\"DefaultReceiveLibraryTimeoutSet\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"eid\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"newLib\",\"type\":\"address\"}],\"name\":\"DefaultSendLibrarySet\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"newLib\",\"type\":\"address\"}],\"name\":\"LibraryRegistered\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"eid\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"newLib\",\"type\":\"address\"}],\"name\":\"ReceiveLibrarySet\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"eid\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"oldLib\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"timeout\",\"type\":\"uint256\"}],\"name\":\"ReceiveLibraryTimeoutSet\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"eid\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"newLib\",\"type\":\"address\"}],\"name\":\"SendLibrarySet\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"_eid\",\"type\":\"uint32\"}],\"name\":\"defaultReceiveLibrary\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"_eid\",\"type\":\"uint32\"}],\"name\":\"defaultReceiveLibraryTimeout\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"lib\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"expiry\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"_eid\",\"type\":\"uint32\"}],\"name\":\"defaultSendLibrary\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_oapp\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_lib\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"_eid\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"_configType\",\"type\":\"uint32\"}],\"name\":\"getConfig\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"config\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_receiver\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"_eid\",\"type\":\"uint32\"}],\"name\":\"getReceiveLibrary\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"lib\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"isDefault\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getRegisteredLibraries\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_sender\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"_eid\",\"type\":\"uint32\"}],\"name\":\"getSendLibrary\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"lib\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_sender\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"_eid\",\"type\":\"uint32\"}],\"name\":\"isDefaultSendLibrary\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_lib\",\"type\":\"address\"}],\"name\":\"isRegisteredLibrary\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"_eid\",\"type\":\"uint32\"}],\"name\":\"isSupportedEid\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_receiver\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"_eid\",\"type\":\"uint32\"},{\"internalType\":\"address\",\"name\":\"_lib\",\"type\":\"address\"}],\"name\":\"isValidReceiveLibrary\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_receiver\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"_eid\",\"type\":\"uint32\"}],\"name\":\"receiveLibraryTimeout\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"lib\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"expiry\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_lib\",\"type\":\"address\"}],\"name\":\"registerLibrary\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_oapp\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_lib\",\"type\":\"address\"},{\"components\":[{\"internalType\":\"uint32\",\"name\":\"eid\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"configType\",\"type\":\"uint32\"},{\"internalType\":\"bytes\",\"name\":\"config\",\"type\":\"bytes\"}],\"internalType\":\"struct SetConfigParam[]\",\"name\":\"_params\",\"type\":\"tuple[]\"}],\"name\":\"setConfig\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"_eid\",\"type\":\"uint32\"},{\"internalType\":\"address\",\"name\":\"_newLib\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_gracePeriod\",\"type\":\"uint256\"}],\"name\":\"setDefaultReceiveLibrary\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"_eid\",\"type\":\"uint32\"},{\"internalType\":\"address\",\"name\":\"_lib\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_expiry\",\"type\":\"uint256\"}],\"name\":\"setDefaultReceiveLibraryTimeout\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"_eid\",\"type\":\"uint32\"},{\"internalType\":\"address\",\"name\":\"_newLib\",\"type\":\"address\"}],\"name\":\"setDefaultSendLibrary\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_oapp\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"_eid\",\"type\":\"uint32\"},{\"internalType\":\"address\",\"name\":\"_newLib\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_gracePeriod\",\"type\":\"uint256\"}],\"name\":\"setReceiveLibrary\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_oapp\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"_eid\",\"type\":\"uint32\"},{\"internalType\":\"address\",\"name\":\"_lib\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_expiry\",\"type\":\"uint256\"}],\"name\":\"setReceiveLibraryTimeout\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_oapp\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"_eid\",\"type\":\"uint32\"},{\"internalType\":\"address\",\"name\":\"_newLib\",\"type\":\"address\"}],\"name\":\"setSendLibrary\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"setSendLibrary(address,uint32,address)\":{\"notice\":\"------------------- OApp interfaces -------------------\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/external/layerzero/v2/IMessageLibManager.sol\":\"IMessageLibManager\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"src/interfaces/external/layerzero/v2/IMessageLibManager.sol\":{\"keccak256\":\"0x94929bdb8d035a15c94d51c16a18903d89fc9291cb6dda23043f6c9864e664f5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c9c509b859cf878757304666a37ff894c3aa414629a19e7ed35ea09139eac3d2\",\"dweb:/ipfs/Qmb8wJfG18Kv24QCVsRQADnMbLPhQ31mVXDk8e2dF3ozJu\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "uint32", "name": "eid", "type": "uint32", "indexed": false}, {"internalType": "address", "name": "newLib", "type": "address", "indexed": false}], "type": "event", "name": "DefaultReceiveLibrarySet", "anonymous": false}, {"inputs": [{"internalType": "uint32", "name": "eid", "type": "uint32", "indexed": false}, {"internalType": "address", "name": "oldLib", "type": "address", "indexed": false}, {"internalType": "uint256", "name": "expiry", "type": "uint256", "indexed": false}], "type": "event", "name": "DefaultReceiveLibraryTimeoutSet", "anonymous": false}, {"inputs": [{"internalType": "uint32", "name": "eid", "type": "uint32", "indexed": false}, {"internalType": "address", "name": "newLib", "type": "address", "indexed": false}], "type": "event", "name": "DefaultSendLibrarySet", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "newLib", "type": "address", "indexed": false}], "type": "event", "name": "LibraryRegistered", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address", "indexed": false}, {"internalType": "uint32", "name": "eid", "type": "uint32", "indexed": false}, {"internalType": "address", "name": "newLib", "type": "address", "indexed": false}], "type": "event", "name": "ReceiveLibrarySet", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address", "indexed": false}, {"internalType": "uint32", "name": "eid", "type": "uint32", "indexed": false}, {"internalType": "address", "name": "oldLib", "type": "address", "indexed": false}, {"internalType": "uint256", "name": "timeout", "type": "uint256", "indexed": false}], "type": "event", "name": "ReceiveLibraryTimeoutSet", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address", "indexed": false}, {"internalType": "uint32", "name": "eid", "type": "uint32", "indexed": false}, {"internalType": "address", "name": "newLib", "type": "address", "indexed": false}], "type": "event", "name": "SendLibrarySet", "anonymous": false}, {"inputs": [{"internalType": "uint32", "name": "_eid", "type": "uint32"}], "stateMutability": "view", "type": "function", "name": "defaultReceiveLibrary", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "uint32", "name": "_eid", "type": "uint32"}], "stateMutability": "view", "type": "function", "name": "defaultReceiveLibraryTimeout", "outputs": [{"internalType": "address", "name": "lib", "type": "address"}, {"internalType": "uint256", "name": "expiry", "type": "uint256"}]}, {"inputs": [{"internalType": "uint32", "name": "_eid", "type": "uint32"}], "stateMutability": "view", "type": "function", "name": "defaultSendLibrary", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "_oapp", "type": "address"}, {"internalType": "address", "name": "_lib", "type": "address"}, {"internalType": "uint32", "name": "_eid", "type": "uint32"}, {"internalType": "uint32", "name": "_configType", "type": "uint32"}], "stateMutability": "view", "type": "function", "name": "getConfig", "outputs": [{"internalType": "bytes", "name": "config", "type": "bytes"}]}, {"inputs": [{"internalType": "address", "name": "_receiver", "type": "address"}, {"internalType": "uint32", "name": "_eid", "type": "uint32"}], "stateMutability": "view", "type": "function", "name": "getReceiveLibrary", "outputs": [{"internalType": "address", "name": "lib", "type": "address"}, {"internalType": "bool", "name": "isDefault", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getRegisteredLibraries", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}]}, {"inputs": [{"internalType": "address", "name": "_sender", "type": "address"}, {"internalType": "uint32", "name": "_eid", "type": "uint32"}], "stateMutability": "view", "type": "function", "name": "getSendLibrary", "outputs": [{"internalType": "address", "name": "lib", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "_sender", "type": "address"}, {"internalType": "uint32", "name": "_eid", "type": "uint32"}], "stateMutability": "view", "type": "function", "name": "isDefaultSendLibrary", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "_lib", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isRegisteredLibrary", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "uint32", "name": "_eid", "type": "uint32"}], "stateMutability": "view", "type": "function", "name": "isSupportedEid", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "_receiver", "type": "address"}, {"internalType": "uint32", "name": "_eid", "type": "uint32"}, {"internalType": "address", "name": "_lib", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isValidReceiveLibrary", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "_receiver", "type": "address"}, {"internalType": "uint32", "name": "_eid", "type": "uint32"}], "stateMutability": "view", "type": "function", "name": "receiveLibraryTimeout", "outputs": [{"internalType": "address", "name": "lib", "type": "address"}, {"internalType": "uint256", "name": "expiry", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "_lib", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "registerLibrary"}, {"inputs": [{"internalType": "address", "name": "_oapp", "type": "address"}, {"internalType": "address", "name": "_lib", "type": "address"}, {"internalType": "struct SetConfigParam[]", "name": "_params", "type": "tuple[]", "components": [{"internalType": "uint32", "name": "eid", "type": "uint32"}, {"internalType": "uint32", "name": "configType", "type": "uint32"}, {"internalType": "bytes", "name": "config", "type": "bytes"}]}], "stateMutability": "nonpayable", "type": "function", "name": "setConfig"}, {"inputs": [{"internalType": "uint32", "name": "_eid", "type": "uint32"}, {"internalType": "address", "name": "_newLib", "type": "address"}, {"internalType": "uint256", "name": "_<PERSON><PERSON><PERSON><PERSON>", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "setDefaultReceiveLibrary"}, {"inputs": [{"internalType": "uint32", "name": "_eid", "type": "uint32"}, {"internalType": "address", "name": "_lib", "type": "address"}, {"internalType": "uint256", "name": "_expiry", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "setDefaultReceiveLibraryTimeout"}, {"inputs": [{"internalType": "uint32", "name": "_eid", "type": "uint32"}, {"internalType": "address", "name": "_newLib", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setDefaultSendLibrary"}, {"inputs": [{"internalType": "address", "name": "_oapp", "type": "address"}, {"internalType": "uint32", "name": "_eid", "type": "uint32"}, {"internalType": "address", "name": "_newLib", "type": "address"}, {"internalType": "uint256", "name": "_<PERSON><PERSON><PERSON><PERSON>", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "setReceiveLibrary"}, {"inputs": [{"internalType": "address", "name": "_oapp", "type": "address"}, {"internalType": "uint32", "name": "_eid", "type": "uint32"}, {"internalType": "address", "name": "_lib", "type": "address"}, {"internalType": "uint256", "name": "_expiry", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "setReceiveLibraryTimeout"}, {"inputs": [{"internalType": "address", "name": "_oapp", "type": "address"}, {"internalType": "uint32", "name": "_eid", "type": "uint32"}, {"internalType": "address", "name": "_newLib", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setSendLibrary"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {"setSendLibrary(address,uint32,address)": {"notice": "------------------- OApp interfaces -------------------"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/interfaces/external/layerzero/v2/IMessageLibManager.sol": "IMessageLibManager"}, "evmVersion": "london", "libraries": {}}, "sources": {"src/interfaces/external/layerzero/v2/IMessageLibManager.sol": {"keccak256": "0x94929bdb8d035a15c94d51c16a18903d89fc9291cb6dda23043f6c9864e664f5", "urls": ["bzz-raw://c9c509b859cf878757304666a37ff894c3aa414629a19e7ed35ea09139eac3d2", "dweb:/ipfs/Qmb8wJfG18Kv24QCVsRQADnMbLPhQ31mVXDk8e2dF3ozJu"], "license": "MIT"}}, "version": 1}, "id": 161}