{"abi": [{"type": "constructor", "inputs": [{"name": "symbols_", "type": "string[]", "internalType": "string[]"}, {"name": "configs_", "type": "tuple[]", "internalType": "struct IDefaultAdapter.PriceConfig[]", "components": [{"name": "defaultFeed", "type": "address", "internalType": "address"}, {"name": "toSymbol", "type": "string", "internalType": "string"}, {"name": "underlyingDecimals", "type": "uint256", "internalType": "uint256"}]}, {"name": "roles_", "type": "address", "internalType": "address"}, {"name": "stalenessPeriod_", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "STALENESS_PERIOD", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "configs", "inputs": [{"name": "", "type": "string", "internalType": "string"}], "outputs": [{"name": "defaultFeed", "type": "address", "internalType": "address"}, {"name": "toSymbol", "type": "string", "internalType": "string"}, {"name": "underlyingDecimals", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getPrice", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getUnderlyingPrice", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "roles", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IRoles"}], "stateMutability": "view"}, {"type": "function", "name": "setConfig", "inputs": [{"name": "symbol", "type": "string", "internalType": "string"}, {"name": "config", "type": "tuple", "internalType": "struct IDefaultAdapter.PriceConfig", "components": [{"name": "defaultFeed", "type": "address", "internalType": "address"}, {"name": "toSymbol", "type": "string", "internalType": "string"}, {"name": "underlyingDecimals", "type": "uint256", "internalType": "uint256"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setStaleness", "inputs": [{"name": "symbol", "type": "string", "internalType": "string"}, {"name": "val", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "stalenessPerSymbol", "inputs": [{"name": "", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "event", "name": "ConfigSet", "inputs": [{"name": "symbol", "type": "string", "indexed": false, "internalType": "string"}, {"name": "config", "type": "tuple", "indexed": false, "internalType": "struct IDefaultAdapter.PriceConfig", "components": [{"name": "defaultFeed", "type": "address", "internalType": "address"}, {"name": "toSymbol", "type": "string", "internalType": "string"}, {"name": "underlyingDecimals", "type": "uint256", "internalType": "uint256"}]}], "anonymous": false}, {"type": "event", "name": "StalenessUpdated", "inputs": [{"name": "symbol", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "error", "name": "MixedPriceOracle_InvalidConfig", "inputs": []}, {"type": "error", "name": "MixedPriceOracle_InvalidPrice", "inputs": []}, {"type": "error", "name": "MixedPriceOracle_InvalidRound", "inputs": []}, {"type": "error", "name": "MixedPriceOracle_StalePrice", "inputs": []}, {"type": "error", "name": "MixedPriceOracle_Unauthorized", "inputs": []}], "bytecode": {"object": "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", "sourceMap": "738:3932:184:-:0;;;1384:363;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;1559:22:184;;;;1596:9;1591:105;1615:8;:15;1611:1;:19;1591:105;;;1674:8;1683:1;1674:11;;;;;;;;:::i;:::-;;;;;;;1651:7;1659:8;1668:1;1659:11;;;;;;;;:::i;:::-;;;;;;;1651:20;;;;;;:::i;:::-;;;;;;;;;;;;;;;:34;;;;-1:-1:-1;;;;;;1651:34:184;-1:-1:-1;;;;;1651:34:184;;;;;;;;;;-1:-1:-1;1651:34:184;;;;;;;:::i;:::-;-1:-1:-1;1651:34:184;;;;;;;;;;;1632:3;;1591:105;;;-1:-1:-1;1705:35:184;;-1:-1:-1;738:3932:184;;-1:-1:-1;;738:3932:184;14:127:242;75:10;70:3;66:20;63:1;56:31;106:4;103:1;96:15;130:4;127:1;120:15;146:253;218:2;212:9;260:4;248:17;;-1:-1:-1;;;;;280:34:242;;316:22;;;277:62;274:88;;;342:18;;:::i;:::-;378:2;371:22;146:253;:::o;404:275::-;475:2;469:9;540:2;521:13;;-1:-1:-1;;517:27:242;505:40;;-1:-1:-1;;;;;560:34:242;;596:22;;;557:62;554:88;;;622:18;;:::i;:::-;658:2;651:22;404:275;;-1:-1:-1;404:275:242:o;684:182::-;743:4;-1:-1:-1;;;;;765:30:242;;762:56;;;798:18;;:::i;:::-;-1:-1:-1;843:1:242;839:14;855:4;835:25;;684:182::o;871:250::-;956:1;966:113;980:6;977:1;974:13;966:113;;;1056:11;;;1050:18;1037:11;;;1030:39;1002:2;995:10;966:113;;;-1:-1:-1;;1113:1:242;1095:16;;1088:27;871:250::o;1126:534::-;1180:5;1233:3;1226:4;1218:6;1214:17;1210:27;1200:55;;1251:1;1248;1241:12;1200:55;1278:13;;-1:-1:-1;;;;;1303:30:242;;1300:56;;;1336:18;;:::i;:::-;1380:59;1427:2;1404:17;;-1:-1:-1;;1400:31:242;1433:4;1396:42;1380:59;:::i;:::-;1464:6;1455:7;1448:23;1518:3;1511:4;1502:6;1494;1490:19;1486:30;1483:39;1480:59;;;1535:1;1532;1525:12;1480:59;1548:81;1622:6;1615:4;1606:7;1602:18;1595:4;1587:6;1583:17;1548:81;:::i;:::-;1647:7;1126:534;-1:-1:-1;;;;1126:534:242:o;1665:177::-;1744:13;;-1:-1:-1;;;;;1786:31:242;;1776:42;;1766:70;;1832:1;1829;1822:12;1766:70;1665:177;;;:::o;1847:1349::-;1923:5;1976:3;1969:4;1961:6;1957:17;1953:27;1943:55;;1994:1;1991;1984:12;1943:55;2027:6;2021:13;2054:63;2070:46;2109:6;2070:46;:::i;:::-;2054:63;:::i;:::-;2141:3;2165:6;2160:3;2153:19;2197:4;2192:3;2188:14;2181:21;;2258:4;2248:6;2245:1;2241:14;2233:6;2229:27;2225:38;2211:52;;2286:3;2278:6;2275:15;2272:35;;;2303:1;2300;2293:12;2272:35;2339:4;2331:6;2327:17;2353:812;2369:6;2364:3;2361:15;2353:812;;;2444:10;;-1:-1:-1;;;;;2470:35:242;;2467:55;;;2518:1;2515;2508:12;2467:55;2545:24;;2617:4;2593:12;;;-1:-1:-1;;2589:26:242;2585:37;2582:57;;;2635:1;2632;2625:12;2582:57;2665:22;;:::i;:::-;2714:44;2752:4;2748:2;2744:13;2714:44;:::i;:::-;2700:59;;2802:2;2794:11;;2788:18;-1:-1:-1;;;;;2822:32:242;;2819:52;;;2867:1;2864;2857:12;2819:52;2909:63;2968:3;2961:4;2950:8;2946:2;2942:17;2938:28;2909:63;:::i;:::-;2902:4;2891:16;;;2884:89;;;;3040:4;3032:13;;;;3026:20;3077:2;3066:14;;3059:31;-1:-1:-1;3103:18:242;;3141:14;;;;2386;2353:812;;;-1:-1:-1;3183:7:242;1847:1349;-1:-1:-1;;;;;1847:1349:242:o;3201:1547::-;3388:6;3396;3404;3412;3465:3;3453:9;3444:7;3440:23;3436:33;3433:53;;;3482:1;3479;3472:12;3433:53;3509:16;;-1:-1:-1;;;;;3537:30:242;;3534:50;;;3580:1;3577;3570:12;3534:50;3603:22;;3656:4;3648:13;;3644:27;-1:-1:-1;3634:55:242;;3685:1;3682;3675:12;3634:55;3718:2;3712:9;3741:63;3757:46;3796:6;3757:46;:::i;3741:63::-;3826:3;3850:6;3845:3;3838:19;3882:4;3877:3;3873:14;3866:21;;3939:4;3929:6;3926:1;3922:14;3918:2;3914:23;3910:34;3896:48;;3967:7;3959:6;3956:19;3953:39;;;3988:1;3985;3978:12;3953:39;4020:4;4016:2;4012:13;4034:309;4050:6;4045:3;4042:15;4034:309;;;4125:10;;-1:-1:-1;;;;;4151:35:242;;4148:55;;;4199:1;4196;4189:12;4148:55;4228:70;4290:7;4283:4;4269:11;4265:2;4261:20;4257:31;4228:70;:::i;:::-;4216:83;;-1:-1:-1;4328:4:242;4319:14;;;;4067;4034:309;;;-1:-1:-1;4413:4:242;4398:20;;4392:27;4362:5;;-1:-1:-1;4392:27:242;-1:-1:-1;;;;;;;;4431:32:242;;4428:52;;;4476:1;4473;4466:12;4428:52;4499:85;4576:7;4565:8;4554:9;4550:24;4499:85;:::i;:::-;4489:95;;;4603:49;4648:2;4637:9;4633:18;4603:49;:::i;:::-;4714:2;4699:18;;;;4693:25;3201:1547;;;;-1:-1:-1;;;3201:1547:242:o;4753:127::-;4814:10;4809:3;4805:20;4802:1;4795:31;4845:4;4842:1;4835:15;4869:4;4866:1;4859:15;4885:289;5016:3;5054:6;5048:13;5070:66;5129:6;5124:3;5117:4;5109:6;5105:17;5070:66;:::i;:::-;5152:16;;;;;4885:289;-1:-1:-1;;4885:289:242:o;5179:380::-;5258:1;5254:12;;;;5301;;;5322:61;;5376:4;5368:6;5364:17;5354:27;;5322:61;5429:2;5421:6;5418:14;5398:18;5395:38;5392:161;;5475:10;5470:3;5466:20;5463:1;5456:31;5510:4;5507:1;5500:15;5538:4;5535:1;5528:15;5392:161;;5179:380;;;:::o;5690:518::-;5792:2;5787:3;5784:11;5781:421;;;5828:5;5825:1;5818:16;5872:4;5869:1;5859:18;5942:2;5930:10;5926:19;5923:1;5919:27;5913:4;5909:38;5978:4;5966:10;5963:20;5960:47;;;-1:-1:-1;6001:4:242;5960:47;6056:2;6051:3;6047:12;6044:1;6040:20;6034:4;6030:31;6020:41;;6111:81;6129:2;6122:5;6119:13;6111:81;;;6188:1;6174:16;;6155:1;6144:13;6111:81;;;6115:3;;5781:421;5690:518;;;:::o;6384:1299::-;6504:10;;-1:-1:-1;;;;;6526:30:242;;6523:56;;;6559:18;;:::i;:::-;6588:97;6678:6;6638:38;6670:4;6664:11;6638:38;:::i;:::-;6632:4;6588:97;:::i;:::-;6734:4;6765:2;6754:14;;6782:1;6777:649;;;;7470:1;7487:6;7484:89;;;-1:-1:-1;7539:19:242;;;7533:26;7484:89;-1:-1:-1;;6341:1:242;6337:11;;;6333:24;6329:29;6319:40;6365:1;6361:11;;;6316:57;7586:81;;6747:930;;6777:649;5637:1;5630:14;;;5674:4;5661:18;;-1:-1:-1;;6813:20:242;;;6931:222;6945:7;6942:1;6939:14;6931:222;;;7027:19;;;7021:26;7006:42;;7134:4;7119:20;;;;7087:1;7075:14;;;;6961:12;6931:222;;;6935:3;7181:6;7172:7;7169:19;7166:201;;;7242:19;;;7236:26;-1:-1:-1;;7325:1:242;7321:14;;;7337:3;7317:24;7313:37;7309:42;7294:58;7279:74;;7166:201;-1:-1:-1;;;;7413:1:242;7397:14;;;7393:22;7380:36;;-1:-1:-1;6384:1299:242:o;:::-;738:3932:184;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "738:3932:184:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;791:41;;;;;;;;160:25:242;;;148:2;133:18;791:41:184;;;;;;;;946:52;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;1004:29;;;;;;;;-1:-1:-1;;;;;2058:32:242;;;2040:51;;2028:2;2013:18;1004:29:184;1878:219:242;2477:172:184;;;;;;:::i;:::-;;:::i;879:61::-;;;;;;:::i;:::-;;:::i;:::-;;;;;;;;;:::i;2052:419::-;;;;;;:::i;:::-;;:::i;:::-;;1753:293;;;;;;:::i;:::-;;:::i;2733:472::-;;;;;;:::i;:::-;;:::i;2477:172::-;2532:7;2551:20;2589:6;-1:-1:-1;;;;;2574:29:184;;:31;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;2574:31:184;;;;;;;;;;;;:::i;:::-;2551:54;;2622:20;2635:6;2622:12;:20::i;:::-;2615:27;2477:172;-1:-1:-1;;;2477:172:184:o;879:61::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;879:61:184;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;2052:419::-;2160:5;-1:-1:-1;;;;;2160:18:184;;2179:10;2191:5;-1:-1:-1;;;;;2191:21:184;;:23;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2160:55;;-1:-1:-1;;;;;;2160:55:184;;;;;;;-1:-1:-1;;;;;6366:32:242;;;2160:55:184;;;6348:51:242;6415:18;;;6408:34;6321:18;;2160:55:184;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2155:125;;2238:31;;-1:-1:-1;;;2238:31:184;;;;;;;;;;;2155:125;2293:18;;-1:-1:-1;;;;;2293:32:184;2289:102;;2348:32;;-1:-1:-1;;;2348:32:184;;;;;;;;;;;2289:102;2418:6;2400:7;2408:6;2400:15;;;;;;:::i;:::-;;;;;;;;;;;;;;;:24;;;;-1:-1:-1;;;;;;2400:24:184;-1:-1:-1;;;;;2400:24:184;;;;;;;;;;-1:-1:-1;2400:24:184;;;;;;;:::i;:::-;-1:-1:-1;2400:24:184;;;;;;;;;;2439:25;;;;;2449:6;;2457;;2439:25;:::i;:::-;;;;;;;;2052:419;;:::o;1753:293::-;1834:5;-1:-1:-1;;;;;1834:18:184;;1853:10;1865:5;-1:-1:-1;;;;;1865:21:184;;:23;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1834:55;;-1:-1:-1;;;;;;1834:55:184;;;;;;;-1:-1:-1;;;;;6366:32:242;;;1834:55:184;;;6348:51:242;6415:18;;;6408:34;6321:18;;1834:55:184;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1829:125;;1912:31;;-1:-1:-1;;;1912:31:184;;;;;;;;;;;1829:125;1992:3;1963:18;1982:6;1963:26;;;;;;:::i;:::-;;;;;;;;;;;;;:32;;;;2010:29;2027:6;2035:3;2010:29;;;;;;;:::i;2733:472::-;2809:7;2931:20;2984:6;-1:-1:-1;;;;;2969:33:184;;:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;2954:58:184;;:60;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;2954:60:184;;;;;;;;;;;;:::i;:::-;2931:83;;3024:41;3068:7;3076:6;3068:15;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;3024:59;;;;;;;;-1:-1:-1;;;;;3024:59:184;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3093:16;3112:20;3125:6;3112:12;:20::i;:::-;3093:39;;3172:6;:25;;;3167:2;:30;;;;:::i;:::-;3160:38;;:2;:38;:::i;:::-;3149:49;;:8;:49;:::i;:::-;3142:56;2733:472;-1:-1:-1;;;;;2733:472:184:o;3211:504::-;3278:7;3297:41;3341:7;3349:6;3341:15;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;3297:59;;;;;;;;-1:-1:-1;;;;;3297:59:184;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3367:17;3386:20;3410:31;3426:6;3434;3410:15;:31::i;:::-;3366:75;;-1:-1:-1;3366:75:184;-1:-1:-1;3451:13:184;3486:17;3366:75;3486:2;:17;:::i;:::-;3479:25;;:2;:25;:::i;:::-;3467:37;;:9;:37;:::i;:::-;3577:23;;-1:-1:-1;;;3577:23:184;;;12451:18:242;3451:53:184;;-1:-1:-1;12485:11:242;;3577:23:184;;;;;;;;;;;;3567:34;;;;;;3546:6;:15;;;3529:33;;;;;;;;:::i;:::-;;;;;;;;;;;;;3519:44;;;;;;:82;3515:171;;3667:8;3634:29;3647:6;:15;;;3634:12;:29::i;:::-;3626:37;;:5;:37;:::i;:::-;3625:50;;;;:::i;:::-;3617:58;3703:5;-1:-1:-1;;;;;;3211:504:184:o;3721:734::-;3894:18;;3858:7;;;;-1:-1:-1;;;;;3894:32:184;3890:65;;3928:27;;-1:-1:-1;;;3928:27:184;;12931:2:242;3928:27:184;;;12913:21:242;12970:2;12950:18;;;12943:30;-1:-1:-1;;;12989:18:242;;;12982:47;13046:18;;3928:27:184;;;;;;;3890:65;3966:20;4005:6;:18;;;3966:58;;4073:12;4088:17;4110:4;-1:-1:-1;;;;;4110:20:184;;:22;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;4070:62;;;;;;;4158:1;4150:5;:9;4142:51;;;;-1:-1:-1;;;4142:51:184;;;;;;;;;;;;4273:21;4287:6;4273:13;:21::i;:::-;4243:27;4261:9;4243:15;:27;:::i;:::-;:51;4235:91;;;;-1:-1:-1;;;4235:91:184;;;;;;;;;;;;4337:16;4356:4;-1:-1:-1;;;;;4356:13:184;;:15;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;4406:5;;-1:-1:-1;;;4337:34:184;;;-1:-1:-1;;3721:734:184;;;;;:::o;4461:207::-;4529:7;4548:19;4570:18;4589:6;4570:26;;;;;;:::i;:::-;;;;;;;;;;;;;;4548:48;;4627:1;4613:11;:15;:48;;4645:16;4613:48;;;4631:11;4606:55;-1:-1:-1;;4461:207:184:o;196:127:242:-;257:10;252:3;248:20;245:1;238:31;288:4;285:1;278:15;312:4;309:1;302:15;328:253;400:2;394:9;442:4;430:17;;477:18;462:34;;498:22;;;459:62;456:88;;;524:18;;:::i;:::-;560:2;553:22;328:253;:::o;586:275::-;657:2;651:9;722:2;703:13;;-1:-1:-1;;699:27:242;687:40;;757:18;742:34;;778:22;;;739:62;736:88;;;804:18;;:::i;:::-;840:2;833:22;586:275;;-1:-1:-1;586:275:242:o;866:187::-;915:4;948:18;940:6;937:30;934:56;;;970:18;;:::i;:::-;-1:-1:-1;1036:2:242;1015:15;-1:-1:-1;;1011:29:242;1042:4;1007:40;;866:187::o;1058:488::-;1101:5;1154:3;1147:4;1139:6;1135:17;1131:27;1121:55;;1172:1;1169;1162:12;1121:55;1212:6;1199:20;1243:53;1259:36;1288:6;1259:36;:::i;:::-;1243:53;:::i;:::-;1321:6;1312:7;1305:23;1375:3;1368:4;1359:6;1351;1347:19;1343:30;1340:39;1337:59;;;1392:1;1389;1382:12;1337:59;1457:6;1450:4;1442:6;1438:17;1431:4;1422:7;1418:18;1405:59;1513:1;1484:20;;;1506:4;1480:31;1473:42;;;;1488:7;1058:488;-1:-1:-1;;;1058:488:242:o;1551:322::-;1620:6;1673:2;1661:9;1652:7;1648:23;1644:32;1641:52;;;1689:1;1686;1679:12;1641:52;1729:9;1716:23;1762:18;1754:6;1751:30;1748:50;;;1794:1;1791;1784:12;1748:50;1817;1859:7;1850:6;1839:9;1835:22;1817:50;:::i;:::-;1807:60;1551:322;-1:-1:-1;;;;1551:322:242:o;2102:131::-;-1:-1:-1;;;;;2177:31:242;;2167:42;;2157:70;;2223:1;2220;2213:12;2157:70;2102:131;:::o;2238:247::-;2297:6;2350:2;2338:9;2329:7;2325:23;2321:32;2318:52;;;2366:1;2363;2356:12;2318:52;2405:9;2392:23;2424:31;2449:5;2424:31;:::i;2490:250::-;2575:1;2585:113;2599:6;2596:1;2593:13;2585:113;;;2675:11;;;2669:18;2656:11;;;2649:39;2621:2;2614:10;2585:113;;;-1:-1:-1;;2732:1:242;2714:16;;2707:27;2490:250::o;2745:271::-;2787:3;2825:5;2819:12;2852:6;2847:3;2840:19;2868:76;2937:6;2930:4;2925:3;2921:14;2914:4;2907:5;2903:16;2868:76;:::i;:::-;2998:2;2977:15;-1:-1:-1;;2973:29:242;2964:39;;;;3005:4;2960:50;;2745:271;-1:-1:-1;;2745:271:242:o;3021:388::-;-1:-1:-1;;;;;3226:32:242;;3208:51;;3295:2;3290;3275:18;;3268:30;;;-1:-1:-1;;3315:45:242;;3341:18;;3333:6;3315:45;:::i;:::-;3307:53;;3396:6;3391:2;3380:9;3376:18;3369:34;3021:388;;;;;;:::o;3414:1066::-;3522:6;3530;3583:2;3571:9;3562:7;3558:23;3554:32;3551:52;;;3599:1;3596;3589:12;3551:52;3639:9;3626:23;3672:18;3664:6;3661:30;3658:50;;;3704:1;3701;3694:12;3658:50;3727;3769:7;3760:6;3749:9;3745:22;3727:50;:::i;:::-;3717:60;;;3830:2;3819:9;3815:18;3802:32;3859:18;3849:8;3846:32;3843:52;;;3891:1;3888;3881:12;3843:52;3914:24;;3972:4;3954:16;;;3950:27;3947:47;;;3990:1;3987;3980:12;3947:47;4016:22;;:::i;:::-;4075:2;4062:16;4087:33;4112:7;4087:33;:::i;:::-;4129:22;;4197:2;4189:11;;4176:25;4226:18;4213:32;;4210:52;;;4258:1;4255;4248:12;4210:52;4294:45;4331:7;4320:8;4316:2;4312:17;4294:45;:::i;:::-;4289:2;4278:14;;4271:69;-1:-1:-1;4406:2:242;4398:11;;;4385:25;4426:14;;;4419:31;;;;3414:1066;;4282:5;;-1:-1:-1;3414:1066:242;;-1:-1:-1;;3414:1066:242:o;4485:436::-;4563:6;4571;4624:2;4612:9;4603:7;4599:23;4595:32;4592:52;;;4640:1;4637;4630:12;4592:52;4680:9;4667:23;4713:18;4705:6;4702:30;4699:50;;;4745:1;4742;4735:12;4699:50;4768;4810:7;4801:6;4790:9;4786:22;4768:50;:::i;:::-;4758:60;4887:2;4872:18;;;;4859:32;;-1:-1:-1;;;;4485:436:242:o;4926:669::-;5006:6;5059:2;5047:9;5038:7;5034:23;5030:32;5027:52;;;5075:1;5072;5065:12;5027:52;5108:9;5102:16;5141:18;5133:6;5130:30;5127:50;;;5173:1;5170;5163:12;5127:50;5196:22;;5249:4;5241:13;;5237:27;-1:-1:-1;5227:55:242;;5278:1;5275;5268:12;5227:55;5311:2;5305:9;5336:53;5352:36;5381:6;5352:36;:::i;5336:53::-;5412:6;5405:5;5398:21;5460:7;5455:2;5446:6;5442:2;5438:15;5434:24;5431:37;5428:57;;;5481:1;5478;5471:12;5428:57;5494:71;5558:6;5553:2;5546:5;5542:14;5537:2;5533;5529:11;5494:71;:::i;5600:380::-;5679:1;5675:12;;;;5722;;;5743:61;;5797:4;5789:6;5785:17;5775:27;;5743:61;5850:2;5842:6;5839:14;5819:18;5816:38;5813:161;;5896:10;5891:3;5887:20;5884:1;5877:31;5931:4;5928:1;5921:15;5959:4;5956:1;5949:15;5813:161;;5600:380;;;:::o;5985:184::-;6055:6;6108:2;6096:9;6087:7;6083:23;6079:32;6076:52;;;6124:1;6121;6114:12;6076:52;-1:-1:-1;6147:16:242;;5985:184;-1:-1:-1;5985:184:242:o;6453:277::-;6520:6;6573:2;6561:9;6552:7;6548:23;6544:32;6541:52;;;6589:1;6586;6579:12;6541:52;6621:9;6615:16;6674:5;6667:13;6660:21;6653:5;6650:32;6640:60;;6696:1;6693;6686:12;6735:289;6866:3;6904:6;6898:13;6920:66;6979:6;6974:3;6967:4;6959:6;6955:17;6920:66;:::i;:::-;7002:16;;;;;6735:289;-1:-1:-1;;6735:289:242:o;7155:518::-;7257:2;7252:3;7249:11;7246:421;;;7293:5;7290:1;7283:16;7337:4;7334:1;7324:18;7407:2;7395:10;7391:19;7388:1;7384:27;7378:4;7374:38;7443:4;7431:10;7428:20;7425:47;;;-1:-1:-1;7466:4:242;7425:47;7521:2;7516:3;7512:12;7509:1;7505:20;7499:4;7495:31;7485:41;;7576:81;7594:2;7587:5;7584:13;7576:81;;;7653:1;7639:16;;7620:1;7609:13;7576:81;;;7580:3;;7246:421;7155:518;;;:::o;7849:1299::-;7975:3;7969:10;8002:18;7994:6;7991:30;7988:56;;;8024:18;;:::i;:::-;8053:97;8143:6;8103:38;8135:4;8129:11;8103:38;:::i;:::-;8097:4;8053:97;:::i;:::-;8199:4;8230:2;8219:14;;8247:1;8242:649;;;;8935:1;8952:6;8949:89;;;-1:-1:-1;9004:19:242;;;8998:26;8949:89;-1:-1:-1;;7806:1:242;7802:11;;;7798:24;7794:29;7784:40;7830:1;7826:11;;;7781:57;9051:81;;8212:930;;8242:649;7102:1;7095:14;;;7139:4;7126:18;;-1:-1:-1;;8278:20:242;;;8396:222;8410:7;8407:1;8404:14;8396:222;;;8492:19;;;8486:26;8471:42;;8599:4;8584:20;;;;8552:1;8540:14;;;;8426:12;8396:222;;;8400:3;8646:6;8637:7;8634:19;8631:201;;;8707:19;;;8701:26;-1:-1:-1;;8790:1:242;8786:14;;;8802:3;8782:24;8778:37;8774:42;8759:58;8744:74;;8631:201;-1:-1:-1;;;;8878:1:242;8862:14;;;8858:22;8845:36;;-1:-1:-1;7849:1299:242:o;9153:678::-;9390:2;9379:9;9372:21;9353:4;9416:45;9457:2;9446:9;9442:18;9434:6;9416:45;:::i;:::-;9509:9;9501:6;9497:22;9492:2;9481:9;9477:18;9470:50;9580:1;9576;9571:3;9567:11;9563:19;9554:6;9548:13;9544:39;9536:6;9529:55;9631:2;9623:6;9619:15;9613:22;9668:4;9663:2;9655:6;9651:15;9644:29;9696:50;9740:4;9732:6;9728:17;9714:12;9696:50;:::i;:::-;9682:64;;9797:2;9789:6;9785:15;9779:22;9774:2;9766:6;9762:15;9755:47;9819:6;9811:14;;;;9153:678;;;;;:::o;9836:291::-;10013:2;10002:9;9995:21;9976:4;10033:45;10074:2;10063:9;10059:18;10051:6;10033:45;:::i;:::-;10025:53;;10114:6;10109:2;10098:9;10094:18;10087:34;9836:291;;;;;:::o;10132:251::-;10202:6;10255:2;10243:9;10234:7;10230:23;10226:32;10223:52;;;10271:1;10268;10261:12;10223:52;10303:9;10297:16;10322:31;10347:5;10322:31;:::i;10388:127::-;10449:10;10444:3;10440:20;10437:1;10430:31;10480:4;10477:1;10470:15;10504:4;10501:1;10494:15;10520:128;10587:9;;;10608:11;;;10605:37;;;10622:18;;:::i;10653:375::-;10741:1;10759:5;10773:249;10794:1;10784:8;10781:15;10773:249;;;10844:4;10839:3;10835:14;10829:4;10826:24;10823:50;;;10853:18;;:::i;:::-;10903:1;10893:8;10889:16;10886:49;;;10917:16;;;;10886:49;11000:1;10996:16;;;;;10956:15;;10773:249;;;10653:375;;;;;;:::o;11033:902::-;11082:5;11112:8;11102:80;;-1:-1:-1;11153:1:242;11167:5;;11102:80;11201:4;11191:76;;-1:-1:-1;11238:1:242;11252:5;;11191:76;11283:4;11301:1;11296:59;;;;11369:1;11364:174;;;;11276:262;;11296:59;11326:1;11317:10;;11340:5;;;11364:174;11401:3;11391:8;11388:17;11385:43;;;11408:18;;:::i;:::-;-1:-1:-1;;11464:1:242;11450:16;;11523:5;;11276:262;;11622:2;11612:8;11609:16;11603:3;11597:4;11594:13;11590:36;11584:2;11574:8;11571:16;11566:2;11560:4;11557:12;11553:35;11550:77;11547:203;;;-1:-1:-1;11659:19:242;;;11735:5;;11547:203;11782:42;-1:-1:-1;;11807:8:242;11801:4;11782:42;:::i;:::-;11860:6;11856:1;11852:6;11848:19;11839:7;11836:32;11833:58;;;11871:18;;:::i;:::-;11909:20;;11033:902;-1:-1:-1;;;11033:902:242:o;11940:131::-;12000:5;12029:36;12056:8;12050:4;12029:36;:::i;12076:168::-;12149:9;;;12180;;12197:15;;;12191:22;;12177:37;12167:71;;12218:18;;:::i;12507:217::-;12547:1;12573;12563:132;;12617:10;12612:3;12608:20;12605:1;12598:31;12652:4;12649:1;12642:15;12680:4;12677:1;12670:15;12563:132;-1:-1:-1;12709:9:242;;12507:217::o;13075:179::-;13153:13;;13206:22;13195:34;;13185:45;;13175:73;;13244:1;13241;13234:12;13175:73;13075:179;;;:::o;13259:571::-;13362:6;13370;13378;13386;13394;13447:3;13435:9;13426:7;13422:23;13418:33;13415:53;;;13464:1;13461;13454:12;13415:53;13487:39;13516:9;13487:39;:::i;:::-;13566:2;13551:18;;13545:25;13632:2;13617:18;;13611:25;13726:2;13711:18;;13705:25;13477:49;;-1:-1:-1;13545:25:242;;-1:-1:-1;13611:25:242;-1:-1:-1;13705:25:242;-1:-1:-1;13775:49:242;13819:3;13804:19;;13775:49;:::i;:::-;13765:59;;13259:571;;;;;;;;:::o;13835:273::-;13903:6;13956:2;13944:9;13935:7;13931:23;13927:32;13924:52;;;13972:1;13969;13962:12;13924:52;14004:9;13998:16;14054:4;14047:5;14043:16;14036:5;14033:27;14023:55;;14074:1;14071;14064:12", "linkReferences": {}, "immutableReferences": {"84170": [{"start": 146, "length": 32}, {"start": 2979, "length": 32}], "84182": [{"start": 247, "length": 32}, {"start": 734, "length": 32}, {"start": 782, "length": 32}, {"start": 1242, "length": 32}, {"start": 1290, "length": 32}]}}, "methodIdentifiers": {"STALENESS_PERIOD()": "24c9477a", "configs(string)": "5ef7fbad", "getPrice(address)": "41976e09", "getUnderlyingPrice(address)": "fc57d4df", "roles()": "392f5f64", "setConfig(string,(address,string,uint256))": "909d0481", "setStaleness(string,uint256)": "c86e8685", "stalenessPerSymbol(string)": "2ab77c57"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"string[]\",\"name\":\"symbols_\",\"type\":\"string[]\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"defaultFeed\",\"type\":\"address\"},{\"internalType\":\"string\",\"name\":\"toSymbol\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"underlyingDecimals\",\"type\":\"uint256\"}],\"internalType\":\"struct IDefaultAdapter.PriceConfig[]\",\"name\":\"configs_\",\"type\":\"tuple[]\"},{\"internalType\":\"address\",\"name\":\"roles_\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"stalenessPeriod_\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"MixedPriceOracle_InvalidConfig\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"MixedPriceOracle_InvalidPrice\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"MixedPriceOracle_InvalidRound\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"MixedPriceOracle_StalePrice\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"MixedPriceOracle_Unauthorized\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"defaultFeed\",\"type\":\"address\"},{\"internalType\":\"string\",\"name\":\"toSymbol\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"underlyingDecimals\",\"type\":\"uint256\"}],\"indexed\":false,\"internalType\":\"struct IDefaultAdapter.PriceConfig\",\"name\":\"config\",\"type\":\"tuple\"}],\"name\":\"ConfigSet\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"val\",\"type\":\"uint256\"}],\"name\":\"StalenessUpdated\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"STALENESS_PERIOD\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"configs\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"defaultFeed\",\"type\":\"address\"},{\"internalType\":\"string\",\"name\":\"toSymbol\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"underlyingDecimals\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"}],\"name\":\"getPrice\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"}],\"name\":\"getUnderlyingPrice\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"roles\",\"outputs\":[{\"internalType\":\"contract IRoles\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"defaultFeed\",\"type\":\"address\"},{\"internalType\":\"string\",\"name\":\"toSymbol\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"underlyingDecimals\",\"type\":\"uint256\"}],\"internalType\":\"struct IDefaultAdapter.PriceConfig\",\"name\":\"config\",\"type\":\"tuple\"}],\"name\":\"setConfig\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"val\",\"type\":\"uint256\"}],\"name\":\"setStaleness\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"stalenessPerSymbol\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{\"getPrice(address)\":{\"params\":{\"mToken\":\"The mToken to get the price of\"},\"returns\":{\"_0\":\"The underlying asset price mantissa (scaled by 1e18).  Zero means the price is unavailable.\"}},\"getUnderlyingPrice(address)\":{\"params\":{\"mToken\":\"The mToken to get the underlying price of\"},\"returns\":{\"_0\":\"The underlying asset price mantissa (scaled by 1e18).  Zero means the price is unavailable.\"}}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"getPrice(address)\":{\"notice\":\"Get the price of a mToken asset\"},\"getUnderlyingPrice(address)\":{\"notice\":\"Get the underlying price of a mToken asset\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/oracles/MixedPriceOracleV3.sol\":\"MixedPriceOracleV3\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"src/interfaces/IDefaultAdapter.sol\":{\"keccak256\":\"0xbf7e882eeb81776c7be55110bb171c65d166bafeb71d828c085b139bed5735c8\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://7e139fb3ddd0623189493679e73fd42c4e505531502d55e1e699789fa3c1451a\",\"dweb:/ipfs/Qma3XsUVPffiGXZ7epTqMyNJKuh87xrFhqCTwQXznEccU6\"]},\"src/interfaces/IOracleOperator.sol\":{\"keccak256\":\"0xcc2848c75dd5e1531c2b958746cdd76a3b2b329be98fdca4311219a12a6b409e\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://337bdabf03ac279d95ba96aca60105932c7af5c15fdb4b21ab1c369509310fbc\",\"dweb:/ipfs/QmNYrNYNUwWWSrPwHdarhxt1ShdH1dy8B94M49TZ7XvmK1\"]},\"src/interfaces/IRoles.sol\":{\"keccak256\":\"0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c\",\"dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj\"]},\"src/interfaces/ImToken.sol\":{\"keccak256\":\"0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36\",\"dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL\"]},\"src/oracles/MixedPriceOracleV3.sol\":{\"keccak256\":\"0x2a12b509ba518e116b6c1136575e04a2c2bfdd5402730f6746cfabbbe5e6549e\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://2417f80a6dfc814e73df5362226cd89db15ea2314effd80dd16bcb87595ea755\",\"dweb:/ipfs/QmcRPazSFBvLKUqo3SCYhNkzuDuQZHWcWjwTzxV88E6own\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "string[]", "name": "symbols_", "type": "string[]"}, {"internalType": "struct IDefaultAdapter.PriceConfig[]", "name": "configs_", "type": "tuple[]", "components": [{"internalType": "address", "name": "defaultFeed", "type": "address"}, {"internalType": "string", "name": "toSymbol", "type": "string"}, {"internalType": "uint256", "name": "underlyingDecimals", "type": "uint256"}]}, {"internalType": "address", "name": "roles_", "type": "address"}, {"internalType": "uint256", "name": "stalenessPeriod_", "type": "uint256"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "type": "error", "name": "MixedPriceOracle_InvalidConfig"}, {"inputs": [], "type": "error", "name": "MixedPriceOracle_InvalidPrice"}, {"inputs": [], "type": "error", "name": "MixedPriceOracle_InvalidRound"}, {"inputs": [], "type": "error", "name": "MixedPriceOracle_StalePrice"}, {"inputs": [], "type": "error", "name": "MixedPriceOracle_Unauthorized"}, {"inputs": [{"internalType": "string", "name": "symbol", "type": "string", "indexed": false}, {"internalType": "struct IDefaultAdapter.PriceConfig", "name": "config", "type": "tuple", "components": [{"internalType": "address", "name": "defaultFeed", "type": "address"}, {"internalType": "string", "name": "toSymbol", "type": "string"}, {"internalType": "uint256", "name": "underlyingDecimals", "type": "uint256"}], "indexed": false}], "type": "event", "name": "ConfigSet", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "symbol", "type": "string", "indexed": false}, {"internalType": "uint256", "name": "val", "type": "uint256", "indexed": false}], "type": "event", "name": "StalenessUpdated", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "STALENESS_PERIOD", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function", "name": "configs", "outputs": [{"internalType": "address", "name": "defaultFeed", "type": "address"}, {"internalType": "string", "name": "toSymbol", "type": "string"}, {"internalType": "uint256", "name": "underlyingDecimals", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getPrice", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getUnderlyingPrice", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "roles", "outputs": [{"internalType": "contract IRoles", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "struct IDefaultAdapter.PriceConfig", "name": "config", "type": "tuple", "components": [{"internalType": "address", "name": "defaultFeed", "type": "address"}, {"internalType": "string", "name": "toSymbol", "type": "string"}, {"internalType": "uint256", "name": "underlyingDecimals", "type": "uint256"}]}], "stateMutability": "nonpayable", "type": "function", "name": "setConfig"}, {"inputs": [{"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint256", "name": "val", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "setStaleness"}, {"inputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function", "name": "stalenessPerSymbol", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}], "devdoc": {"kind": "dev", "methods": {"getPrice(address)": {"params": {"mToken": "The mToken to get the price of"}, "returns": {"_0": "The underlying asset price mantissa (scaled by 1e18).  Zero means the price is unavailable."}}, "getUnderlyingPrice(address)": {"params": {"mToken": "The mToken to get the underlying price of"}, "returns": {"_0": "The underlying asset price mantissa (scaled by 1e18).  Zero means the price is unavailable."}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"getPrice(address)": {"notice": "Get the price of a mToken asset"}, "getUnderlyingPrice(address)": {"notice": "Get the underlying price of a mToken asset"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/oracles/MixedPriceOracleV3.sol": "MixedPriceOracleV3"}, "evmVersion": "london", "libraries": {}}, "sources": {"src/interfaces/IDefaultAdapter.sol": {"keccak256": "0xbf7e882eeb81776c7be55110bb171c65d166bafeb71d828c085b139bed5735c8", "urls": ["bzz-raw://7e139fb3ddd0623189493679e73fd42c4e505531502d55e1e699789fa3c1451a", "dweb:/ipfs/Qma3XsUVPffiGXZ7epTqMyNJKuh87xrFhqCTwQXznEccU6"], "license": "BSL-1.1"}, "src/interfaces/IOracleOperator.sol": {"keccak256": "0xcc2848c75dd5e1531c2b958746cdd76a3b2b329be98fdca4311219a12a6b409e", "urls": ["bzz-raw://337bdabf03ac279d95ba96aca60105932c7af5c15fdb4b21ab1c369509310fbc", "dweb:/ipfs/QmNYrNYNUwWWSrPwHdarhxt1ShdH1dy8B94M49TZ7XvmK1"], "license": "BSL-1.1"}, "src/interfaces/IRoles.sol": {"keccak256": "0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365", "urls": ["bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c", "dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj"], "license": "BSL-1.1"}, "src/interfaces/ImToken.sol": {"keccak256": "0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f", "urls": ["bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36", "dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL"], "license": "BSL-1.1"}, "src/oracles/MixedPriceOracleV3.sol": {"keccak256": "0x2a12b509ba518e116b6c1136575e04a2c2bfdd5402730f6746cfabbbe5e6549e", "urls": ["bzz-raw://2417f80a6dfc814e73df5362226cd89db15ea2314effd80dd16bcb87595ea755", "dweb:/ipfs/QmcRPazSFBvLKUqo3SCYhNkzuDuQZHWcWjwTzxV88E6own"], "license": "BSL-1.1"}}, "version": 1}, "id": 184}