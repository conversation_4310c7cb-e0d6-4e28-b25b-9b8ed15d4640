{"abi": [{"type": "constructor", "inputs": [{"name": "_token", "type": "address", "internalType": "address"}, {"name": "_verifier", "type": "address", "internalType": "address"}, {"name": "_owner", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "balanceOf", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "borrow", "inputs": [{"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "journalData", "type": "bytes", "internalType": "bytes"}, {"name": "seal", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "borrowBalanceOf", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "borrowImageId", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "deposit", "inputs": [{"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "to", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "repay", "inputs": [{"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setBorrowImageId", "inputs": [{"name": "_imageId", "type": "bytes32", "internalType": "bytes32"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setVerifier", "inputs": [{"name": "_verifier", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setWithdrawImageId", "inputs": [{"name": "_imageId", "type": "bytes32", "internalType": "bytes32"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "token", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "verifier", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IRiscZeroVerifier"}], "stateMutability": "view"}, {"type": "function", "name": "withdraw", "inputs": [{"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "journalData", "type": "bytes", "internalType": "bytes"}, {"name": "seal", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "withdrawImageId", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "AddressEmptyCode", "inputs": [{"name": "target", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "AddressInsufficientBalance", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "FailedInnerCall", "inputs": []}, {"type": "error", "name": "LendingProtocolMock_InsufficientBalance", "inputs": []}, {"type": "error", "name": "LendingProtocolMock_InsufficientLiquidity", "inputs": []}, {"type": "error", "name": "LendingProtocolMock_InvalidCommitment", "inputs": [{"name": "id", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "LendingProtocolMock_JournalNotValid", "inputs": []}, {"type": "error", "name": "OwnableInvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "SafeERC20FailedOperation", "inputs": [{"name": "token", "type": "address", "internalType": "address"}]}], "bytecode": {"object": "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", "sourceMap": "651:3067:205:-:0;;;1233:159;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1304:6;-1:-1:-1;;;;;1273:26:27;;1269:95;;1322:31;;-1:-1:-1;;;1322:31:27;;1350:1;1322:31;;;725:51:242;698:18;;1322:31:27;;;;;;;1269:95;1373:32;1392:12;1373:18;:32::i;:::-;-1:-1:-1;;1322:8:205::1;:39:::0;;-1:-1:-1;;;;;1322:39:205;;::::1;-1:-1:-1::0;;;;;;1322:39:205;;::::1;;::::0;;;;1371:14;;;;;::::1;::::0;::::1;::::0;;;::::1;::::0;;651:3067;;2912:187:27;2985:16;3004:6;;-1:-1:-1;;;;;3020:17:27;;;-1:-1:-1;;;;;;3020:17:27;;;;;;3052:40;;3004:6;;;;;;;3052:40;;2985:16;3052:40;2975:124;2912:187;:::o;14:177:242:-;93:13;;-1:-1:-1;;;;;135:31:242;;125:42;;115:70;;181:1;178;171:12;115:70;14:177;;;:::o;196:378::-;284:6;292;300;353:2;341:9;332:7;328:23;324:32;321:52;;;369:1;366;359:12;321:52;392:40;422:9;392:40;:::i;:::-;382:50;;451:49;496:2;485:9;481:18;451:49;:::i;:::-;441:59;;519:49;564:2;553:9;549:18;519:49;:::i;:::-;509:59;;196:378;;;;;:::o;579:203::-;651:3067:205;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "651:3067:205:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;797:33;;;;;-1:-1:-1;;;;;797:33:205;;;;;;-1:-1:-1;;;;;205:32:242;;;187:51;;175:2;160:18;797:33:205;;;;;;;;2576:268;;;;;;:::i;:::-;;:::i;:::-;;957:50;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;1014:25:242;;;1002:2;987:18;957:50:205;868:177:242;1560:104:205;;;;;;:::i;:::-;;:::i;1439:115::-;;;;;;:::i;:::-;;:::i;1832:169::-;;;;;;:::i;:::-;;:::i;907:44::-;;;;;;:::i;:::-;;;;;;;;;;;;;;2293:101:27;;;:::i;1638:85::-;1684:7;1710:6;-1:-1:-1;;;;;1710:6:27;1638:85;;2850:536:205;;;;;;:::i;:::-;;:::i;836:28::-;;;;;;1670:108;;;;;;:::i;:::-;;:::i;2007:563::-;;;;;;:::i;:::-;;:::i;870:30::-;;;;;;2543:215:27;;;;;;:::i;:::-;;:::i;771:20:205:-;;;;;-1:-1:-1;;;;;771:20:205;;;2576:268;2650:10;2634:27;;;;:15;:27;;;;;;:37;-1:-1:-1;2634:37:205;2626:89;;;;-1:-1:-1;;;2626:89:205;;;;;;;;;;;;2741:10;2725:27;;;;:15;:27;;;;;:37;;2756:6;;2725:27;:37;;2756:6;;2725:37;:::i;:::-;;;;-1:-1:-1;;2779:5:205;;2772:65;;-1:-1:-1;;;;;2779:5:205;2803:10;2823:4;2830:6;2772:30;:65::i;:::-;2576:268;:::o;1560:104::-;1531:13:27;:11;:13::i;:::-;1633::205::1;:24:::0;1560:104::o;1439:115::-;1531:13:27;:11;:13::i;:::-;1508:8:205::1;:39:::0;;-1:-1:-1;;;;;;1508:39:205::1;-1:-1:-1::0;;;;;1508:39:205;;;::::1;::::0;;;::::1;::::0;;1439:115::o;1832:169::-;1903:5;;1896:65;;-1:-1:-1;;;;;1903:5:205;1927:10;1947:4;1954:6;1896:30;:65::i;:::-;-1:-1:-1;;;;;1971:13:205;;;;;;:9;:13;;;;;:23;;1988:6;;1971:13;:23;;1988:6;;1971:23;:::i;:::-;;;;-1:-1:-1;;;;1832:169:205:o;2293:101:27:-;1531:13;:11;:13::i;:::-;2357:30:::1;2384:1;2357:18;:30::i;:::-;2293:101::o:0;2850:536:205:-;2952:48;2965:11;;2978:4;;2984:15;;2952:12;:48::i;:::-;3042:16;;3087;:11;3099:2;3087:11;;:16;:::i;:::-;3076:48;;;;;;;:::i;:::-;3041:83;;;;3155:6;3143:8;:18;;3135:72;;;;-1:-1:-1;;;3135:72:205;;;;;;;;;;;;-1:-1:-1;;;;;3225:15:205;;;;;;:9;:15;;;;;;:25;-1:-1:-1;3225:25:205;3217:77;;;;-1:-1:-1;;;3217:77:205;;;;;;;;;;;;-1:-1:-1;;;;;3304:15:205;;;;;;:9;:15;;;;;:25;;3323:6;;3304:15;:25;;3323:6;;3304:25;:::i;:::-;;;;-1:-1:-1;;3346:5:205;;3339:40;;-1:-1:-1;;;;;3346:5:205;3366:4;3372:6;3339:26;:40::i;:::-;2942:444;;2850:536;;;;;:::o;1670:108::-;1531:13:27;:11;:13::i;:::-;1745:15:205::1;:26:::0;1670:108::o;2007:563::-;2107:46;2120:11;;2133:4;;2139:13;;2107:12;:46::i;:::-;2195:17;;2241:16;:11;2253:2;2241:11;;:16;:::i;:::-;2230:48;;;;;;;:::i;:::-;2194:84;;;;2310:6;2297:9;:19;;2289:73;;;;-1:-1:-1;;;2289:73:205;;;;;;;;;;;;2387:5;;2380:38;;-1:-1:-1;;;2380:38:205;;2412:4;2380:38;;;187:51:242;2422:6:205;;-1:-1:-1;;;;;2387:5:205;;2380:23;;160:18:242;;2380:38:205;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:48;;2372:100;;;;-1:-1:-1;;;2372:100:205;;;;;;;;;;;;-1:-1:-1;;;;;2482:21:205;;;;;;:15;:21;;;;;:31;;2507:6;;2482:21;:31;;2507:6;;2482:31;:::i;2543:215:27:-;1531:13;:11;:13::i;:::-;-1:-1:-1;;;;;2627:22:27;::::1;2623:91;;2672:31;::::0;-1:-1:-1;;;2672:31:27;;2700:1:::1;2672:31;::::0;::::1;187:51:242::0;160:18;;2672:31:27::1;;;;;;;;2623:91;2723:28;2742:8;2723:18;:28::i;1702:188:43:-:0;1829:53;;-1:-1:-1;;;;;4700:32:242;;;1829:53:43;;;4682:51:242;4769:32;;;4749:18;;;4742:60;4818:18;;;4811:34;;;1802:81:43;;1822:5;;1844:18;;;;;4655::242;;1829:53:43;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;1829:53:43;;;;;;;;;;;1802:19;:81::i;:::-;1702:188;;;;:::o;1796:162:27:-;1684:7;1710:6;-1:-1:-1;;;;;1710:6:27;735:10:47;1855:23:27;1851:101;;1901:40;;-1:-1:-1;;;1901:40:27;;735:10:47;1901:40:27;;;187:51:242;160:18;;1901:40:27;14:230:242;2912:187:27;2985:16;3004:6;;-1:-1:-1;;;;;3020:17:27;;;-1:-1:-1;;;;;;3020:17:27;;;;;;3052:40;;3004:6;;;;;;;3052:40;;2985:16;3052:40;2975:124;2912:187;:::o;3441:275:205:-;3581:2;3560:23;;3552:71;;;;-1:-1:-1;;;3552:71:205;;;;;;;;;;;;3658:8;;;3689:19;;-1:-1:-1;;;;;3658:8:205;;;;:15;;3674:4;;;;3680:7;;3658:8;3689:19;;3696:11;;;;3689:19;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;3658:51;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3441:275;;;;;:::o;1303:160:43:-;1412:43;;-1:-1:-1;;;;;6053:32:242;;;1412:43:43;;;6035:51:242;6102:18;;;6095:34;;;1385:71:43;;1405:5;;1427:14;;;;;6008:18:242;;1412:43:43;5861:274:242;1385:71:43;1303:160;;;:::o;4059:629::-;4478:23;4504:33;-1:-1:-1;;;;;4504:27:43;;4532:4;4504:27;:33::i;:::-;4478:59;;4551:10;:17;4572:1;4551:22;;:57;;;;;4589:10;4578:30;;;;;;;;;;;;:::i;:::-;4577:31;4551:57;4547:135;;;4631:40;;-1:-1:-1;;;4631:40:43;;-1:-1:-1;;;;;205:32:242;;4631:40:43;;;187:51:242;160:18;;4631:40:43;14:230:242;2705:151:46;2780:12;2811:38;2833:6;2841:4;2847:1;2811:21;:38::i;:::-;2804:45;;2705:151;;;;;:::o;3180:392::-;3279:12;3331:5;3307:21;:29;3303:108;;;3359:41;;-1:-1:-1;;;3359:41:46;;3394:4;3359:41;;;187:51:242;160:18;;3359:41:46;14:230:242;3303:108:46;3421:12;3435:23;3462:6;-1:-1:-1;;;;;3462:11:46;3481:5;3488:4;3462:31;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3420:73;;;;3510:55;3537:6;3545:7;3554:10;3510:26;:55::i;:::-;3503:62;;;;3180:392;;;;;;:::o;4625:582::-;4769:12;4798:7;4793:408;;4821:19;4829:10;4821:7;:19::i;:::-;4793:408;;;5045:17;;:22;:49;;;;-1:-1:-1;;;;;;5071:18:46;;;:23;5045:49;5041:119;;;5121:24;;-1:-1:-1;;;5121:24:46;;-1:-1:-1;;;;;205:32:242;;5121:24:46;;;187:51:242;160:18;;5121:24:46;14:230:242;5041:119:46;-1:-1:-1;5180:10:46;5173:17;;5743:516;5874:17;;:21;5870:383;;6102:10;6096:17;6158:15;6145:10;6141:2;6137:19;6130:44;5870:383;6225:17;;-1:-1:-1;;;6225:17:46;;;;;;;;;;;249:226:242;308:6;361:2;349:9;340:7;336:23;332:32;329:52;;;377:1;374;367:12;329:52;-1:-1:-1;422:23:242;;249:226;-1:-1:-1;249:226:242:o;480:131::-;-1:-1:-1;;;;;555:31:242;;545:42;;535:70;;601:1;598;591:12;616:247;675:6;728:2;716:9;707:7;703:23;699:32;696:52;;;744:1;741;734:12;696:52;783:9;770:23;802:31;827:5;802:31;:::i;1235:367::-;1303:6;1311;1364:2;1352:9;1343:7;1339:23;1335:32;1332:52;;;1380:1;1377;1370:12;1332:52;1425:23;;;-1:-1:-1;1524:2:242;1509:18;;1496:32;1537:33;1496:32;1537:33;:::i;:::-;1589:7;1579:17;;;1235:367;;;;;:::o;1815:347::-;1866:8;1876:6;1930:3;1923:4;1915:6;1911:17;1907:27;1897:55;;1948:1;1945;1938:12;1897:55;-1:-1:-1;1971:20:242;;2014:18;2003:30;;2000:50;;;2046:1;2043;2036:12;2000:50;2083:4;2075:6;2071:17;2059:29;;2135:3;2128:4;2119:6;2111;2107:19;2103:30;2100:39;2097:59;;;2152:1;2149;2142:12;2097:59;1815:347;;;;;:::o;2167:826::-;2266:6;2274;2282;2290;2298;2351:2;2339:9;2330:7;2326:23;2322:32;2319:52;;;2367:1;2364;2357:12;2319:52;2412:23;;;-1:-1:-1;2510:2:242;2495:18;;2482:32;2537:18;2526:30;;2523:50;;;2569:1;2566;2559:12;2523:50;2608:58;2658:7;2649:6;2638:9;2634:22;2608:58;:::i;:::-;2685:8;;-1:-1:-1;2582:84:242;-1:-1:-1;;2773:2:242;2758:18;;2745:32;2802:18;2789:32;;2786:52;;;2834:1;2831;2824:12;2786:52;2873:60;2925:7;2914:8;2903:9;2899:24;2873:60;:::i;:::-;2167:826;;;;-1:-1:-1;2167:826:242;;-1:-1:-1;2952:8:242;;2847:86;2167:826;-1:-1:-1;;;2167:826:242:o;3180:127::-;3241:10;3236:3;3232:20;3229:1;3222:31;3272:4;3269:1;3262:15;3296:4;3293:1;3286:15;3312:128;3379:9;;;3400:11;;;3397:37;;;3414:18;;:::i;3445:125::-;3510:9;;;3531:10;;;3528:36;;;3544:18;;:::i;3575:331::-;3680:9;3691;3733:8;3721:10;3718:24;3715:44;;;3755:1;3752;3745:12;3715:44;3784:6;3774:8;3771:20;3768:40;;;3804:1;3801;3794:12;3768:40;-1:-1:-1;;3830:23:242;;;3875:25;;;;;-1:-1:-1;3575:331:242:o;4291:184::-;4361:6;4414:2;4402:9;4393:7;4389:23;4385:32;4382:52;;;4430:1;4427;4420:12;4382:52;-1:-1:-1;4453:16:242;;4291:184;-1:-1:-1;4291:184:242:o;4856:271::-;5039:6;5031;5026:3;5013:33;4995:3;5065:16;;5090:13;;;5065:16;4856:271;-1:-1:-1;4856:271:242:o;5321:535::-;5534:2;5523:9;5516:21;5573:6;5568:2;5557:9;5553:18;5546:34;5631:6;5623;5617:3;5606:9;5602:19;5589:49;5688:1;5682:3;5673:6;5662:9;5658:22;5654:32;5647:43;5497:4;5758:3;5751:2;5747:7;5742:2;5734:6;5730:15;5726:29;5715:9;5711:45;5707:55;5699:63;;5800:6;5793:4;5782:9;5778:20;5771:36;5843:6;5838:2;5827:9;5823:18;5816:34;5321:535;;;;;;;:::o;6140:277::-;6207:6;6260:2;6248:9;6239:7;6235:23;6231:32;6228:52;;;6276:1;6273;6266:12;6228:52;6308:9;6302:16;6361:5;6354:13;6347:21;6340:5;6337:32;6327:60;;6383:1;6380;6373:12;6422:412;6551:3;6589:6;6583:13;6614:1;6624:129;6638:6;6635:1;6632:13;6624:129;;;6736:4;6720:14;;;6716:25;;6710:32;6697:11;;;6690:53;6653:12;6624:129;;;-1:-1:-1;6808:1:242;6772:16;;6797:13;;;-1:-1:-1;6772:16:242;6422:412;-1:-1:-1;6422:412:242:o", "linkReferences": {}}, "methodIdentifiers": {"balanceOf(address)": "70a08231", "borrow(uint256,bytes,bytes)": "cbf1f6b5", "borrowBalanceOf(address)": "374c49b4", "borrowImageId()": "9e0bd6fa", "deposit(uint256,address)": "6e553f65", "owner()": "8da5cb5b", "renounceOwnership()": "715018a6", "repay(uint256)": "371fd8e6", "setBorrowImageId(bytes32)": "4eb56a6a", "setVerifier(address)": "5437988d", "setWithdrawImageId(bytes32)": "ad867ba0", "token()": "fc0c546a", "transferOwnership(address)": "f2fde38b", "verifier()": "2b7ac3f3", "withdraw(uint256,bytes,bytes)": "99807b9b", "withdrawImageId()": "dbe34d42"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_token\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_verifier\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_owner\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"target\",\"type\":\"address\"}],\"name\":\"AddressEmptyCode\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"AddressInsufficientBalance\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"FailedInnerCall\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"LendingProtocolMock_InsufficientBalance\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"LendingProtocolMock_InsufficientLiquidity\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"id\",\"type\":\"uint256\"}],\"name\":\"LendingProtocolMock_InvalidCommitment\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"LendingProtocolMock_JournalNotValid\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"OwnableInvalidOwner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"OwnableUnauthorizedAccount\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"SafeERC20FailedOperation\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"journalData\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"seal\",\"type\":\"bytes\"}],\"name\":\"borrow\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"borrowBalanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"borrowImageId\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"}],\"name\":\"deposit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"repay\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_imageId\",\"type\":\"bytes32\"}],\"name\":\"setBorrowImageId\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_verifier\",\"type\":\"address\"}],\"name\":\"setVerifier\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_imageId\",\"type\":\"bytes32\"}],\"name\":\"setWithdrawImageId\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"token\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"verifier\",\"outputs\":[{\"internalType\":\"contract IRiscZeroVerifier\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"journalData\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"seal\",\"type\":\"bytes\"}],\"name\":\"withdraw\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"withdrawImageId\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"AddressEmptyCode(address)\":[{\"details\":\"There's no code at `target` (it is not a contract).\"}],\"AddressInsufficientBalance(address)\":[{\"details\":\"The ETH balance of the account is not enough to perform the operation.\"}],\"FailedInnerCall()\":[{\"details\":\"A call to an address target failed. The target may have reverted.\"}],\"OwnableInvalidOwner(address)\":[{\"details\":\"The owner is not a valid owner account. (eg. `address(0)`)\"}],\"OwnableUnauthorizedAccount(address)\":[{\"details\":\"The caller account is not authorized to perform an operation.\"}],\"SafeERC20FailedOperation(address)\":[{\"details\":\"An operation with an ERC20 token failed.\"}]},\"kind\":\"dev\",\"methods\":{\"owner()\":{\"details\":\"Returns the address of the current owner.\"},\"renounceOwnership()\":{\"details\":\"Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner.\"},\"transferOwnership(address)\":{\"details\":\"Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"test/mocks/LendingProtocolMock.sol\":\"LendingProtocolMock\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"lib/openzeppelin-contracts/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol\":{\"keccak256\":\"0x60c65f701957fdd6faea1acb0bb45825791d473693ed9ecb34726fdfaa849dd7\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ea290300e0efc4d901244949dc4d877fd46e6c5e43dc2b26620e8efab3ab803f\",\"dweb:/ipfs/QmcLLJppxKeJWqHxE2CUkcfhuRTgHSn8J4kijcLa5MYhSt\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol\":{\"keccak256\":\"0xc3e1fa9d1987f8d349dfb4d6fe93bf2ca014b52ba335cfac30bfe71e357e6f80\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c5703ccdeb7b1d685e375ed719117e9edf2ab4bc544f24f23b0d50ec82257229\",\"dweb:/ipfs/QmTdwkbQq7owpCiyuzE7eh5LrD2ddrBCZ5WHVsWPi1RrTS\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xc6a8ff0ea489379b61faa647490411b80102578440ab9d84e9a957cc12164e70\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0ea104e577e63faea3b69c415637e99e755dcbf64c5833d7140c35a714d6d90c\",\"dweb:/ipfs/Qmau6x4Ns9XdyynRCNNp3RhLqijJjFm7z5fyZazfYFGYdq\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xaa761817f6cd7892fcf158b3c776b34551cde36f48ff9703d53898bc45a94ea2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0ad7c8d4d08938c8dfc43d75a148863fb324b80cf53e0a36f7e5a4ac29008850\",\"dweb:/ipfs/QmcrhfPgVNf5mkdhQvy1pMv51TFokD3Y4Wa5WZhFqVh8UV\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol\":{\"keccak256\":\"0x6008dabfe393240d73d7dd7688033f72740d570aa422254d29a7dce8568f3aff\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f5196ec75139918c6c7bb4251b36395e668f1fa6d206beba7e7520e74913940d\",\"dweb:/ipfs/QmSyqjksXxmm2mCG6qRd1yuwLykypkSVBbnBnGqJRcuJMi\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol\":{\"keccak256\":\"0x37bb49513c49c87c4642a891b13b63571bc87013dde806617aa1efb54605f386\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b3036b3a83b7c48f96641f2a9002b9f2dcb6a5958dd670894ada21ae8229b3d0\",\"dweb:/ipfs/QmUNfSBdoVtjhETaUJCYcaC7pTMgbhht926tJ2uXJbiVd3\"]},\"lib/openzeppelin-contracts/contracts/utils/Address.sol\":{\"keccak256\":\"0xaf28a975a78550e45f65e559a3ad6a5ad43b9b8a37366999abd1b7084eb70721\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b7bd24e224f67f65bfadf85dc2929fa965456bb2415478bd0125471b5ce35245\",\"dweb:/ipfs/QmRaydGr8BTHs1kvaZfsNU69pKzUAGFrvABn1KiRSbE51y\"]},\"lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"lib/risc0-ethereum/contracts/src/IRiscZeroVerifier.sol\":{\"keccak256\":\"0xae73e91e4d2829b0a88176584d876382b4b7540ff07c62cb947cdf9ea43d5cf3\",\"license\":\"Apache-2.0\",\"urls\":[\"bzz-raw://f2dd7da39a6f9b100fc1dcde565d799462cdc7d27722d2265317c3a01619c818\",\"dweb:/ipfs/Qmd3k3kcvrtfkLqVza4jHodbFH8wvq5vmfPtwekBKtPjmz\"]},\"lib/risc0-ethereum/contracts/src/Util.sol\":{\"keccak256\":\"0x230a9a58bb2b0c68d4921ecc3672783f941890a2fa3a238dbe5188cfb7658b82\",\"license\":\"Apache-2.0\",\"urls\":[\"bzz-raw://586cb5296c564761609ca8aaae5b1fbbdb19ab1e174f7197f602572d9e82b70c\",\"dweb:/ipfs/QmWxDmucSfg3Wgdk1rVcheDHwkziTsXWvKpKSCnxtpQBqg\"]},\"lib/risc0-ethereum/contracts/src/steel/Steel.sol\":{\"keccak256\":\"0x20811a302f2bc3e36fb612026319c83cf79c16aa56b822d40bcbe8173d1e9544\",\"license\":\"Apache-2.0\",\"urls\":[\"bzz-raw://ed9f7a15c3be0bdc79217feae9e773d280fcfb44aa450d37c6c891f0b505210f\",\"dweb:/ipfs/QmXHtzvLT2AHHHojJ42epUSBF4HYTaVohSmyZft6KZoVqE\"]},\"test/mocks/LendingProtocolMock.sol\":{\"keccak256\":\"0x17c66a2230bb88f25582a4caeb82f519943db43ed33c1597ba879dae9c927825\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://fc9d1ab5c5cdabfc2b0dd2645c5ff46933e19ce62ffcac5f6a38f211538cefd5\",\"dweb:/ipfs/QmVXsHuv4yed3MpD6QMYHtqugjUx72P4PS6oeeGLYjdsor\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "_token", "type": "address"}, {"internalType": "address", "name": "_verifier", "type": "address"}, {"internalType": "address", "name": "_owner", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "target", "type": "address"}], "type": "error", "name": "AddressEmptyCode"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "type": "error", "name": "AddressInsufficientBalance"}, {"inputs": [], "type": "error", "name": "FailedInnerCall"}, {"inputs": [], "type": "error", "name": "LendingProtocolMock_InsufficientBalance"}, {"inputs": [], "type": "error", "name": "LendingProtocolMock_InsufficientLiquidity"}, {"inputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}], "type": "error", "name": "LendingProtocolMock_InvalidCommitment"}, {"inputs": [], "type": "error", "name": "LendingProtocolMock_JournalNotValid"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "type": "error", "name": "OwnableInvalidOwner"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "type": "error", "name": "OwnableUnauthorizedAccount"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "type": "error", "name": "SafeERC20FailedOperation"}, {"inputs": [{"internalType": "address", "name": "previousOwner", "type": "address", "indexed": true}, {"internalType": "address", "name": "new<PERSON>wner", "type": "address", "indexed": true}], "type": "event", "name": "OwnershipTransferred", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "bytes", "name": "journalData", "type": "bytes"}, {"internalType": "bytes", "name": "seal", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "borrow"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "borrowBalanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "borrowImageId", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "address", "name": "to", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "deposit"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "renounceOwnership"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "repay"}, {"inputs": [{"internalType": "bytes32", "name": "_imageId", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function", "name": "setBorrowImageId"}, {"inputs": [{"internalType": "address", "name": "_verifier", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setVerifier"}, {"inputs": [{"internalType": "bytes32", "name": "_imageId", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function", "name": "setWithdrawImageId"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "token", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "transferOwnership"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "verifier", "outputs": [{"internalType": "contract IRiscZeroVerifier", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "bytes", "name": "journalData", "type": "bytes"}, {"internalType": "bytes", "name": "seal", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "withdraw"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "withdrawImageId", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}], "devdoc": {"kind": "dev", "methods": {"owner()": {"details": "Returns the address of the current owner."}, "renounceOwnership()": {"details": "Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner."}, "transferOwnership(address)": {"details": "Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"test/mocks/LendingProtocolMock.sol": "LendingProtocolMock"}, "evmVersion": "london", "libraries": {}}, "sources": {"lib/openzeppelin-contracts/contracts/access/Ownable.sol": {"keccak256": "0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb", "urls": ["bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6", "dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol": {"keccak256": "0x60c65f701957fdd6faea1acb0bb45825791d473693ed9ecb34726fdfaa849dd7", "urls": ["bzz-raw://ea290300e0efc4d901244949dc4d877fd46e6c5e43dc2b26620e8efab3ab803f", "dweb:/ipfs/QmcLLJppxKeJWqHxE2CUkcfhuRTgHSn8J4kijcLa5MYhSt"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol": {"keccak256": "0xc3e1fa9d1987f8d349dfb4d6fe93bf2ca014b52ba335cfac30bfe71e357e6f80", "urls": ["bzz-raw://c5703ccdeb7b1d685e375ed719117e9edf2ab4bc544f24f23b0d50ec82257229", "dweb:/ipfs/QmTdwkbQq7owpCiyuzE7eh5LrD2ddrBCZ5WHVsWPi1RrTS"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0xc6a8ff0ea489379b61faa647490411b80102578440ab9d84e9a957cc12164e70", "urls": ["bzz-raw://0ea104e577e63faea3b69c415637e99e755dcbf64c5833d7140c35a714d6d90c", "dweb:/ipfs/Qmau6x4Ns9XdyynRCNNp3RhLqijJjFm7z5fyZazfYFGYdq"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0xaa761817f6cd7892fcf158b3c776b34551cde36f48ff9703d53898bc45a94ea2", "urls": ["bzz-raw://0ad7c8d4d08938c8dfc43d75a148863fb324b80cf53e0a36f7e5a4ac29008850", "dweb:/ipfs/QmcrhfPgVNf5mkdhQvy1pMv51TFokD3Y4Wa5WZhFqVh8UV"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol": {"keccak256": "0x6008dabfe393240d73d7dd7688033f72740d570aa422254d29a7dce8568f3aff", "urls": ["bzz-raw://f5196ec75139918c6c7bb4251b36395e668f1fa6d206beba7e7520e74913940d", "dweb:/ipfs/QmSyqjksXxmm2mCG6qRd1yuwLykypkSVBbnBnGqJRcuJMi"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol": {"keccak256": "0x37bb49513c49c87c4642a891b13b63571bc87013dde806617aa1efb54605f386", "urls": ["bzz-raw://b3036b3a83b7c48f96641f2a9002b9f2dcb6a5958dd670894ada21ae8229b3d0", "dweb:/ipfs/QmUNfSBdoVtjhETaUJCYcaC7pTMgbhht926tJ2uXJbiVd3"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Address.sol": {"keccak256": "0xaf28a975a78550e45f65e559a3ad6a5ad43b9b8a37366999abd1b7084eb70721", "urls": ["bzz-raw://b7bd24e224f67f65bfadf85dc2929fa965456bb2415478bd0125471b5ce35245", "dweb:/ipfs/QmRaydGr8BTHs1kvaZfsNU69pKzUAGFrvABn1KiRSbE51y"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "lib/risc0-ethereum/contracts/src/IRiscZeroVerifier.sol": {"keccak256": "0xae73e91e4d2829b0a88176584d876382b4b7540ff07c62cb947cdf9ea43d5cf3", "urls": ["bzz-raw://f2dd7da39a6f9b100fc1dcde565d799462cdc7d27722d2265317c3a01619c818", "dweb:/ipfs/Qmd3k3kcvrtfkLqVza4jHodbFH8wvq5vmfPtwekBKtPjmz"], "license": "Apache-2.0"}, "lib/risc0-ethereum/contracts/src/Util.sol": {"keccak256": "0x230a9a58bb2b0c68d4921ecc3672783f941890a2fa3a238dbe5188cfb7658b82", "urls": ["bzz-raw://586cb5296c564761609ca8aaae5b1fbbdb19ab1e174f7197f602572d9e82b70c", "dweb:/ipfs/QmWxDmucSfg3Wgdk1rVcheDHwkziTsXWvKpKSCnxtpQBqg"], "license": "Apache-2.0"}, "lib/risc0-ethereum/contracts/src/steel/Steel.sol": {"keccak256": "0x20811a302f2bc3e36fb612026319c83cf79c16aa56b822d40bcbe8173d1e9544", "urls": ["bzz-raw://ed9f7a15c3be0bdc79217feae9e773d280fcfb44aa450d37c6c891f0b505210f", "dweb:/ipfs/QmXHtzvLT2AHHHojJ42epUSBF4HYTaVohSmyZft6KZoVqE"], "license": "Apache-2.0"}, "test/mocks/LendingProtocolMock.sol": {"keccak256": "0x17c66a2230bb88f25582a4caeb82f519943db43ed33c1597ba879dae9c927825", "urls": ["bzz-raw://fc9d1ab5c5cdabfc2b0dd2645c5ff46933e19ce62ffcac5f6a38f211538cefd5", "dweb:/ipfs/QmVXsHuv4yed3MpD6QMYHtqugjUx72P4PS6oeeGLYjdsor"], "license": "BSL-1.1"}}, "version": 1}, "id": 205}