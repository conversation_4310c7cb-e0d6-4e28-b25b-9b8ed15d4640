{"abi": [{"type": "constructor", "inputs": [{"name": "symbols_", "type": "string[]", "internalType": "string[]"}, {"name": "feeds_", "type": "address[]", "internalType": "contract IAggregatorV3[]"}, {"name": "baseUnits_", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "DECIMALS", "inputs": [], "outputs": [{"name": "", "type": "uint8", "internalType": "uint8"}], "stateMutability": "view"}, {"type": "function", "name": "baseUnits", "inputs": [{"name": "", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getPrice", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getUnderlyingPrice", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "priceFeeds", "inputs": [{"name": "", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "address", "internalType": "contract IAggregatorV3"}], "stateMutability": "view"}, {"type": "error", "name": "ChainlinkOracle_NoPriceFeed", "inputs": []}, {"type": "error", "name": "ChainlinkOracle_ZeroPrice", "inputs": []}], "bytecode": {"object": "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", "sourceMap": "841:2076:183:-:0;;;1160:328;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1273:9;1268:214;1292:8;:15;1288:1;:19;1268:214;;;1350:6;1357:1;1350:9;;;;;;;;:::i;:::-;;;;;;;1324:10;1335:8;1344:1;1335:11;;;;;;;;:::i;:::-;;;;;;;1324:23;;;;;;:::i;:::-;;;;;;;;;;;;;;:35;;;;;-1:-1:-1;;;;;1324:35:183;;;;;-1:-1:-1;;;;;1324:35:183;;;;;;1398:10;1409:1;1398:13;;;;;;;;:::i;:::-;;;;;;;1373:9;1383:8;1392:1;1383:11;;;;;;;;:::i;:::-;;;;;;;1373:22;;;;;;:::i;:::-;;;;;;;;;;;;;;:38;1454:3;;1268:214;;;;1160:328;;;841:2076;;14:127:242;75:10;70:3;66:20;63:1;56:31;106:4;103:1;96:15;130:4;127:1;120:15;146:275;217:2;211:9;282:2;263:13;;-1:-1:-1;;259:27:242;247:40;;-1:-1:-1;;;;;302:34:242;;338:22;;;299:62;296:88;;;364:18;;:::i;:::-;400:2;393:22;146:275;;-1:-1:-1;146:275:242:o;426:182::-;485:4;-1:-1:-1;;;;;507:30:242;;504:56;;;540:18;;:::i;:::-;-1:-1:-1;585:1:242;581:14;597:4;577:25;;426:182::o;613:250::-;698:1;708:113;722:6;719:1;716:13;708:113;;;798:11;;;792:18;779:11;;;772:39;744:2;737:10;708:113;;;-1:-1:-1;;855:1:242;837:16;;830:27;613:250::o;868:794::-;948:5;1001:3;994:4;986:6;982:17;978:27;968:55;;1019:1;1016;1009:12;968:55;1052:6;1046:13;1079:63;1095:46;1134:6;1095:46;:::i;:::-;1079:63;:::i;:::-;1166:3;1190:6;1185:3;1178:19;1222:4;1217:3;1213:14;1206:21;;1283:4;1273:6;1270:1;1266:14;1258:6;1254:27;1250:38;1236:52;;1311:3;1303:6;1300:15;1297:35;;;1328:1;1325;1318:12;1297:35;1364:4;1356:6;1352:17;1378:253;1394:6;1389:3;1386:15;1378:253;;;1463:10;;-1:-1:-1;;;;;1506:31:242;;1496:42;;1486:70;;1552:1;1549;1542:12;1486:70;1569:18;;1616:4;1607:14;;;;1411;1378:253;;;-1:-1:-1;1649:7:242;868:794;-1:-1:-1;;;;;868:794:242:o;1667:665::-;1732:5;1785:3;1778:4;1770:6;1766:17;1762:27;1752:55;;1803:1;1800;1793:12;1752:55;1836:6;1830:13;1863:63;1879:46;1918:6;1879:46;:::i;1863:63::-;1950:3;1974:6;1969:3;1962:19;2006:4;2001:3;1997:14;1990:21;;2067:4;2057:6;2054:1;2050:14;2042:6;2038:27;2034:38;2020:52;;2095:3;2087:6;2084:15;2081:35;;;2112:1;2109;2102:12;2081:35;2148:4;2140:6;2136:17;2162:139;2178:6;2173:3;2170:15;2162:139;;;2246:10;;2234:23;;2286:4;2277:14;;;;2195;2162:139;;2337:2065;2533:6;2541;2549;2602:2;2590:9;2581:7;2577:23;2573:32;2570:52;;;2618:1;2615;2608:12;2570:52;2645:16;;-1:-1:-1;;;;;2673:30:242;;2670:50;;;2716:1;2713;2706:12;2670:50;2739:22;;2792:4;2784:13;;2780:27;-1:-1:-1;2770:55:242;;2821:1;2818;2811:12;2770:55;2854:2;2848:9;2877:63;2893:46;2932:6;2893:46;:::i;2877:63::-;2962:3;2986:6;2981:3;2974:19;3018:4;3013:3;3009:14;3002:21;;3075:4;3065:6;3062:1;3058:14;3054:2;3050:23;3046:34;3032:48;;3103:7;3095:6;3092:19;3089:39;;;3124:1;3121;3114:12;3089:39;3156:4;3152:2;3148:13;3170:777;3186:6;3181:3;3178:15;3170:777;;;3261:10;;-1:-1:-1;;;;;3287:35:242;;3284:55;;;3335:1;3332;3325:12;3284:55;3362:20;;3417:2;3409:11;;3405:25;-1:-1:-1;3395:53:242;;3444:1;3441;3434:12;3395:53;3491:4;3483:13;;3477:20;-1:-1:-1;;;;;3513:32:242;;3510:58;;;3548:18;;:::i;:::-;3596:61;3645:2;3620:19;;-1:-1:-1;;3616:33:242;3651:4;3612:44;3596:61;:::i;:::-;3670:25;;;3714:39;3722:17;;;3714:39;3711:52;-1:-1:-1;3708:72:242;;;3776:1;3773;3766:12;3708:72;3793:77;3861:8;3854:4;3845:7;3841:18;3836:2;3832;3828:11;3793:77;:::i;:::-;3883:20;;-1:-1:-1;;3932:4:242;3923:14;;;;3203;3170:777;;;-1:-1:-1;4017:4:242;4002:20;;3996:27;3966:5;;-1:-1:-1;3996:27:242;-1:-1:-1;;;;;;;;4035:32:242;;4032:52;;;4080:1;4077;4070:12;4032:52;4103:89;4184:7;4173:8;4162:9;4158:24;4103:89;:::i;:::-;4238:2;4223:18;;4217:25;4093:99;;-1:-1:-1;4217:25:242;-1:-1:-1;;;;;;4254:32:242;;4251:52;;;4299:1;4296;4289:12;4251:52;4322:74;4388:7;4377:8;4366:9;4362:24;4322:74;:::i;:::-;4312:84;;;2337:2065;;;;;:::o;4407:127::-;4468:10;4463:3;4459:20;4456:1;4449:31;4499:4;4496:1;4489:15;4523:4;4520:1;4513:15;4539:289;4670:3;4708:6;4702:13;4724:66;4783:6;4778:3;4771:4;4763:6;4759:17;4724:66;:::i;:::-;4806:16;;;;;4539:289;-1:-1:-1;;4539:289:242:o;:::-;841:2076:183;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "841:2076:183:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1037:35;;1070:2;1037:35;;;;;186:4:242;174:17;;;156:36;;144:2;129:18;1037:35:183;;;;;;;;1584:312;;;;;;:::i;:::-;;:::i;:::-;;;737:25:242;;;725:2;710:18;1584:312:183;591:177:242;987:43:183;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;931:50;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;931:50:183;;;;;;-1:-1:-1;;;;;2266:32:242;;;2248:51;;2236:2;2221:18;931:50:183;2079:226:242;1953:374:183;;;;;;:::i;:::-;;:::i;1584:312::-;1650:7;1669:20;1707:6;-1:-1:-1;;;;;1692:29:183;;:31;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;1692:31:183;;;;;;;;;;;;:::i;:::-;1669:54;;1733:20;1756:10;1767:6;1756:18;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;-1:-1:-1;;;1756:29:183;;;;-1:-1:-1;;;;;1756:18:183;;;;:27;;:29;;;;;;;;;;:18;:29;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1733:52;;;;1797:13;1815:23;1831:6;1815:15;:23::i;:::-;-1:-1:-1;1796:42:183;-1:-1:-1;1871:17:183;1876:12;1871:2;:17;:::i;:::-;1864:25;;:2;:25;:::i;:::-;1856:33;;:5;:33;:::i;:::-;1849:40;1584:312;-1:-1:-1;;;;;1584:312:183:o;1953:374::-;2029:7;2048:20;2101:6;-1:-1:-1;;;;;2086:33:183;;:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;2071:58:183;;:60;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;2071:60:183;;;;;;;;;;;;:::i;:::-;2048:83;;2141:20;2164:10;2175:6;2164:18;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;-1:-1:-1;;;2164:29:183;;;;-1:-1:-1;;;;;2164:18:183;;;;:27;;:29;;;;;;;;;;:18;:29;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2141:52;;;;2205:13;2223:23;2239:6;2223:15;:23::i;:::-;2204:42;;;2303:9;2313:6;2303:17;;;;;;:::i;:::-;;;;;;;;;;;;;;;2280;2285:12;2280:2;:17;:::i;:::-;2273:25;;:2;:25;:::i;:::-;2264:35;;:5;:35;:::i;:::-;2263:57;;;;:::i;2373:542::-;2443:7;2452;2518:1;-1:-1:-1;;;;;2479:41:183;2487:10;2498:6;2487:18;;;;;;:::i;:::-;;;;;;;;;;;;;;;-1:-1:-1;;;;;2487:18:183;2479:41;2471:81;;;;-1:-1:-1;;;2471:81:183;;;;;;;;;;;;2620:12;2680:17;2736:10;2747:6;2736:18;;;;;;:::i;:::-;;;;;;;;;;;;;;;;-1:-1:-1;;;2736:36:183;;;;-1:-1:-1;;;;;2736:18:183;;;;:34;;:36;;;;;;;;;;;;;;:18;:36;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2563:209;;;;;;;2799:1;2791:5;:9;2783:47;;;;-1:-1:-1;;;2783:47:183;;;;;;;;;;;;2865:5;;2898:9;;-1:-1:-1;2373:542:183;-1:-1:-1;;2373:542:183:o;203:131:242:-;-1:-1:-1;;;;;278:31:242;;268:42;;258:70;;324:1;321;314:12;258:70;203:131;:::o;339:247::-;398:6;451:2;439:9;430:7;426:23;422:32;419:52;;;467:1;464;457:12;419:52;506:9;493:23;525:31;550:5;525:31;:::i;:::-;575:5;339:247;-1:-1:-1;;;339:247:242:o;773:127::-;834:10;829:3;825:20;822:1;815:31;865:4;862:1;855:15;889:4;886:1;879:15;905:275;976:2;970:9;1041:2;1022:13;;-1:-1:-1;;1018:27:242;1006:40;;1076:18;1061:34;;1097:22;;;1058:62;1055:88;;;1123:18;;:::i;:::-;1159:2;1152:22;905:275;;-1:-1:-1;905:275:242:o;1185:187::-;1234:4;1267:18;1259:6;1256:30;1253:56;;;1289:18;;:::i;:::-;-1:-1:-1;1355:2:242;1334:15;-1:-1:-1;;1330:29:242;1361:4;1326:40;;1185:187::o;1377:697::-;1446:6;1499:2;1487:9;1478:7;1474:23;1470:32;1467:52;;;1515:1;1512;1505:12;1467:52;1555:9;1542:23;1588:18;1580:6;1577:30;1574:50;;;1620:1;1617;1610:12;1574:50;1643:22;;1696:4;1688:13;;1684:27;-1:-1:-1;1674:55:242;;1725:1;1722;1715:12;1674:55;1765:2;1752:16;1790:53;1806:36;1835:6;1806:36;:::i;:::-;1790:53;:::i;:::-;1866:6;1859:5;1852:21;1914:7;1909:2;1900:6;1896:2;1892:15;1888:24;1885:37;1882:57;;;1935:1;1932;1925:12;1882:57;1990:6;1985:2;1981;1977:11;1972:2;1965:5;1961:14;1948:49;2042:1;2017:18;;;2037:2;2013:27;2006:38;;;;2021:5;1377:697;-1:-1:-1;;;;1377:697:242:o;2310:250::-;2395:1;2405:113;2419:6;2416:1;2413:13;2405:113;;;2495:11;;;2489:18;2476:11;;;2469:39;2441:2;2434:10;2405:113;;;-1:-1:-1;;2552:1:242;2534:16;;2527:27;2310:250::o;2565:669::-;2645:6;2698:2;2686:9;2677:7;2673:23;2669:32;2666:52;;;2714:1;2711;2704:12;2666:52;2747:9;2741:16;2780:18;2772:6;2769:30;2766:50;;;2812:1;2809;2802:12;2766:50;2835:22;;2888:4;2880:13;;2876:27;-1:-1:-1;2866:55:242;;2917:1;2914;2907:12;2866:55;2950:2;2944:9;2975:53;2991:36;3020:6;2991:36;:::i;2975:53::-;3051:6;3044:5;3037:21;3099:7;3094:2;3085:6;3081:2;3077:15;3073:24;3070:37;3067:57;;;3120:1;3117;3110:12;3067:57;3133:71;3197:6;3192:2;3185:5;3181:14;3176:2;3172;3168:11;3133:71;:::i;3239:289::-;3370:3;3408:6;3402:13;3424:66;3483:6;3478:3;3471:4;3463:6;3459:17;3424:66;:::i;:::-;3506:16;;;;;3239:289;-1:-1:-1;;3239:289:242:o;3533:273::-;3601:6;3654:2;3642:9;3633:7;3629:23;3625:32;3622:52;;;3670:1;3667;3660:12;3622:52;3702:9;3696:16;3752:4;3745:5;3741:16;3734:5;3731:27;3721:55;;3772:1;3769;3762:12;3811:127;3872:10;3867:3;3863:20;3860:1;3853:31;3903:4;3900:1;3893:15;3927:4;3924:1;3917:15;3943:128;4010:9;;;4031:11;;;4028:37;;;4045:18;;:::i;:::-;3943:128;;;;:::o;4076:375::-;4164:1;4182:5;4196:249;4217:1;4207:8;4204:15;4196:249;;;4267:4;4262:3;4258:14;4252:4;4249:24;4246:50;;;4276:18;;:::i;:::-;4326:1;4316:8;4312:16;4309:49;;;4340:16;;;;4309:49;4423:1;4419:16;;;;;4379:15;;4196:249;;;4076:375;;;;;;:::o;4456:902::-;4505:5;4535:8;4525:80;;-1:-1:-1;4576:1:242;4590:5;;4525:80;4624:4;4614:76;;-1:-1:-1;4661:1:242;4675:5;;4614:76;4706:4;4724:1;4719:59;;;;4792:1;4787:174;;;;4699:262;;4719:59;4749:1;4740:10;;4763:5;;;4787:174;4824:3;4814:8;4811:17;4808:43;;;4831:18;;:::i;:::-;-1:-1:-1;;4887:1:242;4873:16;;4946:5;;4699:262;;5045:2;5035:8;5032:16;5026:3;5020:4;5017:13;5013:36;5007:2;4997:8;4994:16;4989:2;4983:4;4980:12;4976:35;4973:77;4970:203;;;-1:-1:-1;5082:19:242;;;5158:5;;4970:203;5205:42;-1:-1:-1;;5230:8:242;5224:4;5205:42;:::i;:::-;5283:6;5279:1;5275:6;5271:19;5262:7;5259:32;5256:58;;;5294:18;;:::i;:::-;5332:20;;4456:902;-1:-1:-1;;;4456:902:242:o;5363:131::-;5423:5;5452:36;5479:8;5473:4;5452:36;:::i;5499:168::-;5572:9;;;5603;;5620:15;;;5614:22;;5600:37;5590:71;;5641:18;;:::i;5672:251::-;5742:6;5795:2;5783:9;5774:7;5770:23;5766:32;5763:52;;;5811:1;5808;5801:12;5763:52;5843:9;5837:16;5862:31;5887:5;5862:31;:::i;5928:217::-;5968:1;5994;5984:132;;6038:10;6033:3;6029:20;6026:1;6019:31;6073:4;6070:1;6063:15;6101:4;6098:1;6091:15;5984:132;-1:-1:-1;6130:9:242;;5928:217::o;6150:179::-;6228:13;;6281:22;6270:34;;6260:45;;6250:73;;6319:1;6316;6309:12;6250:73;6150:179;;;:::o;6334:571::-;6437:6;6445;6453;6461;6469;6522:3;6510:9;6501:7;6497:23;6493:33;6490:53;;;6539:1;6536;6529:12;6490:53;6562:39;6591:9;6562:39;:::i;:::-;6641:2;6626:18;;6620:25;6707:2;6692:18;;6686:25;6801:2;6786:18;;6780:25;6552:49;;-1:-1:-1;6620:25:242;;-1:-1:-1;6686:25:242;-1:-1:-1;6780:25:242;-1:-1:-1;6850:49:242;6894:3;6879:19;;6850:49;:::i;:::-;6840:59;;6334:571;;;;;;;;:::o", "linkReferences": {}}, "methodIdentifiers": {"DECIMALS()": "2e0f2625", "baseUnits(string)": "51003683", "getPrice(address)": "41976e09", "getUnderlyingPrice(address)": "fc57d4df", "priceFeeds(string)": "cb8ae86c"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"string[]\",\"name\":\"symbols_\",\"type\":\"string[]\"},{\"internalType\":\"contract IAggregatorV3[]\",\"name\":\"feeds_\",\"type\":\"address[]\"},{\"internalType\":\"uint256[]\",\"name\":\"baseUnits_\",\"type\":\"uint256[]\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"ChainlinkOracle_NoPriceFeed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ChainlinkOracle_ZeroPrice\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"DECIMALS\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"baseUnits\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"}],\"name\":\"getPrice\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"}],\"name\":\"getUnderlyingPrice\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"priceFeeds\",\"outputs\":[{\"internalType\":\"contract IAggregatorV3\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{\"getPrice(address)\":{\"params\":{\"mToken\":\"The mToken to get the price of\"},\"returns\":{\"_0\":\"The underlying asset price mantissa (scaled by 1e18).  Zero means the price is unavailable.\"}},\"getUnderlyingPrice(address)\":{\"params\":{\"mToken\":\"The mToken to get the underlying price of\"},\"returns\":{\"_0\":\"The underlying asset price mantissa (scaled by 1e18).  Zero means the price is unavailable.\"}}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"getPrice(address)\":{\"notice\":\"Get the price of a mToken asset\"},\"getUnderlyingPrice(address)\":{\"notice\":\"Get the underlying price of a mToken asset\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/oracles/ChainlinkOracle.sol\":\"ChainlinkOracle\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"src/interfaces/IOracleOperator.sol\":{\"keccak256\":\"0xcc2848c75dd5e1531c2b958746cdd76a3b2b329be98fdca4311219a12a6b409e\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://337bdabf03ac279d95ba96aca60105932c7af5c15fdb4b21ab1c369509310fbc\",\"dweb:/ipfs/QmNYrNYNUwWWSrPwHdarhxt1ShdH1dy8B94M49TZ7XvmK1\"]},\"src/interfaces/IRoles.sol\":{\"keccak256\":\"0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c\",\"dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj\"]},\"src/interfaces/ImToken.sol\":{\"keccak256\":\"0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36\",\"dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL\"]},\"src/interfaces/external/chainlink/IAggregatorV3.sol\":{\"keccak256\":\"0x81c779f589dcc473725c26e36908145270c14ed4fffc0a6ec4973e3691f494b3\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://c12152add12799fb2219dd9cec9123017e4d865716f39eeeaa191dbf68bb2772\",\"dweb:/ipfs/QmSzBY7vnEL9M6XYrZvXqC3kZpvJwVEUJuCh77osHzyGu7\"]},\"src/oracles/ChainlinkOracle.sol\":{\"keccak256\":\"0x65242eaa6ee20b57ad0a7ef37c29128ab80371148f2ddff760fe61c1c29dc34e\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://c6b5f992b1497321be899aec0f6a4d3296e5873f746c1d00265b71a8dd790822\",\"dweb:/ipfs/Qmcwrj74gLSC663YAThoE2zQSA2Ge6biu5wgFkKrigPoEL\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "string[]", "name": "symbols_", "type": "string[]"}, {"internalType": "contract IAggregatorV3[]", "name": "feeds_", "type": "address[]"}, {"internalType": "uint256[]", "name": "baseUnits_", "type": "uint256[]"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "type": "error", "name": "ChainlinkOracle_NoPriceFeed"}, {"inputs": [], "type": "error", "name": "ChainlinkOracle_ZeroPrice"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "DECIMALS", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}]}, {"inputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function", "name": "baseUnits", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getPrice", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getUnderlyingPrice", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function", "name": "priceFeeds", "outputs": [{"internalType": "contract IAggregatorV3", "name": "", "type": "address"}]}], "devdoc": {"kind": "dev", "methods": {"getPrice(address)": {"params": {"mToken": "The mToken to get the price of"}, "returns": {"_0": "The underlying asset price mantissa (scaled by 1e18).  Zero means the price is unavailable."}}, "getUnderlyingPrice(address)": {"params": {"mToken": "The mToken to get the underlying price of"}, "returns": {"_0": "The underlying asset price mantissa (scaled by 1e18).  Zero means the price is unavailable."}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"getPrice(address)": {"notice": "Get the price of a mToken asset"}, "getUnderlyingPrice(address)": {"notice": "Get the underlying price of a mToken asset"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/oracles/ChainlinkOracle.sol": "ChainlinkOracle"}, "evmVersion": "london", "libraries": {}}, "sources": {"src/interfaces/IOracleOperator.sol": {"keccak256": "0xcc2848c75dd5e1531c2b958746cdd76a3b2b329be98fdca4311219a12a6b409e", "urls": ["bzz-raw://337bdabf03ac279d95ba96aca60105932c7af5c15fdb4b21ab1c369509310fbc", "dweb:/ipfs/QmNYrNYNUwWWSrPwHdarhxt1ShdH1dy8B94M49TZ7XvmK1"], "license": "BSL-1.1"}, "src/interfaces/IRoles.sol": {"keccak256": "0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365", "urls": ["bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c", "dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj"], "license": "BSL-1.1"}, "src/interfaces/ImToken.sol": {"keccak256": "0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f", "urls": ["bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36", "dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL"], "license": "BSL-1.1"}, "src/interfaces/external/chainlink/IAggregatorV3.sol": {"keccak256": "0x81c779f589dcc473725c26e36908145270c14ed4fffc0a6ec4973e3691f494b3", "urls": ["bzz-raw://c12152add12799fb2219dd9cec9123017e4d865716f39eeeaa191dbf68bb2772", "dweb:/ipfs/QmSzBY7vnEL9M6XYrZvXqC3kZpvJwVEUJuCh77osHzyGu7"], "license": "BSL-1.1"}, "src/oracles/ChainlinkOracle.sol": {"keccak256": "0x65242eaa6ee20b57ad0a7ef37c29128ab80371148f2ddff760fe61c1c29dc34e", "urls": ["bzz-raw://c6b5f992b1497321be899aec0f6a4d3296e5873f746c1d00265b71a8dd790822", "dweb:/ipfs/Qmcwrj74gLSC663YAThoE2zQSA2Ge6biu5wgFkKrigPoEL"], "license": "BSL-1.1"}}, "version": 1}, "id": 183}