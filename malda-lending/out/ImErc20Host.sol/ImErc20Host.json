{"abi": [{"type": "function", "name": "extractForRebalancing", "inputs": [{"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "getProofData", "inputs": [{"name": "user", "type": "address", "internalType": "address"}, {"name": "dstId", "type": "uint32", "internalType": "uint32"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "liquidateExternal", "inputs": [{"name": "journalData", "type": "bytes", "internalType": "bytes"}, {"name": "seal", "type": "bytes", "internalType": "bytes"}, {"name": "userToLiquidate", "type": "address[]", "internalType": "address[]"}, {"name": "liquidateAmount", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "collateral", "type": "address[]", "internalType": "address[]"}, {"name": "receiver", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "mintExternal", "inputs": [{"name": "journalData", "type": "bytes", "internalType": "bytes"}, {"name": "seal", "type": "bytes", "internalType": "bytes"}, {"name": "mintAmount", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "minAmountsOut", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "receiver", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "mintOrBorrowMigration", "inputs": [{"name": "mint", "type": "bool", "internalType": "bool"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "receiver", "type": "address", "internalType": "address"}, {"name": "borrower", "type": "address", "internalType": "address"}, {"name": "minAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "performExtensionCall", "inputs": [{"name": "actionType", "type": "uint256", "internalType": "uint256"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "dst<PERSON>hainId", "type": "uint32", "internalType": "uint32"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "repayExternal", "inputs": [{"name": "journalData", "type": "bytes", "internalType": "bytes"}, {"name": "seal", "type": "bytes", "internalType": "bytes"}, {"name": "repayAmount", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "receiver", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "updateAllowedCallerStatus", "inputs": [{"name": "caller", "type": "address", "internalType": "address"}, {"name": "status", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "AllowedCallerUpdated", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "caller", "type": "address", "indexed": true, "internalType": "address"}, {"name": "status", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "mErc20Host_BorrowExternal", "inputs": [{"name": "msgSender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "srcSender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "chainId", "type": "uint32", "indexed": true, "internalType": "uint32"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "mErc20Host_BorrowMigration", "inputs": [{"name": "borrower", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "mErc20Host_BorrowOnExtensionChain", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "dst<PERSON>hainId", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "mErc20Host_ChainStatusUpdated", "inputs": [{"name": "chainId", "type": "uint32", "indexed": true, "internalType": "uint32"}, {"name": "status", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "mErc20Host_GasFeeUpdated", "inputs": [{"name": "dst<PERSON>hainId", "type": "uint32", "indexed": true, "internalType": "uint32"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "mErc20Host_LiquidateExternal", "inputs": [{"name": "msgSender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "srcSender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "userToLiquidate", "type": "address", "indexed": false, "internalType": "address"}, {"name": "receiver", "type": "address", "indexed": false, "internalType": "address"}, {"name": "collateral", "type": "address", "indexed": true, "internalType": "address"}, {"name": "srcChainId", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "mErc20Host_MintExternal", "inputs": [{"name": "msgSender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "srcSender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "receiver", "type": "address", "indexed": true, "internalType": "address"}, {"name": "chainId", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "mErc20Host_MintMigration", "inputs": [{"name": "receiver", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "mErc20Host_RepayExternal", "inputs": [{"name": "msgSender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "srcSender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "position", "type": "address", "indexed": true, "internalType": "address"}, {"name": "chainId", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "mErc20Host_WithdrawExternal", "inputs": [{"name": "msgSender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "srcSender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "chainId", "type": "uint32", "indexed": true, "internalType": "uint32"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "mErc20Host_WithdrawOnExtensionChain", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "dst<PERSON>hainId", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "error", "name": "mErc20Host_ActionNotAvailable", "inputs": []}, {"type": "error", "name": "mErc20Host_AddressNotValid", "inputs": []}, {"type": "error", "name": "mErc20Host_AmountNotValid", "inputs": []}, {"type": "error", "name": "mErc20Host_AmountTooBig", "inputs": []}, {"type": "error", "name": "mErc20Host_CallerNotAllowed", "inputs": []}, {"type": "error", "name": "mErc20Host_ChainNotValid", "inputs": []}, {"type": "error", "name": "mErc20Host_DstChainNotValid", "inputs": []}, {"type": "error", "name": "mErc20Host_JournalNotValid", "inputs": []}, {"type": "error", "name": "mErc20Host_L1InclusionRequired", "inputs": []}, {"type": "error", "name": "mErc20Host_LengthMismatch", "inputs": []}, {"type": "error", "name": "mErc20Host_NotEnoughGasFee", "inputs": []}, {"type": "error", "name": "mErc20Host_NotRebalancer", "inputs": []}, {"type": "error", "name": "mErc20Host_ProofGenerationInputNotValid", "inputs": []}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"extractForRebalancing(uint256)": "ffcaadfe", "getProofData(address,uint32)": "07d923e9", "liquidateExternal(bytes,bytes,address[],uint256[],address[],address)": "a4777a7a", "mintExternal(bytes,bytes,uint256[],uint256[],address)": "05dbe8a7", "mintOrBorrowMigration(bool,uint256,address,address,uint256)": "5bf36c5a", "performExtensionCall(uint256,uint256,uint32)": "2e1483ae", "repayExternal(bytes,bytes,uint256[],address)": "08fee263", "updateAllowedCallerStatus(address,bool)": "4f2be4ce"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"mErc20Host_ActionNotAvailable\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mErc20Host_AddressNotValid\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mErc20Host_AmountNotValid\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mErc20Host_AmountTooBig\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mErc20Host_CallerNotAllowed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mErc20Host_ChainNotValid\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mErc20Host_DstChainNotValid\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mErc20Host_JournalNotValid\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mErc20Host_L1InclusionRequired\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mErc20Host_LengthMismatch\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mErc20Host_NotEnoughGasFee\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mErc20Host_NotRebalancer\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mErc20Host_ProofGenerationInputNotValid\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"caller\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"status\",\"type\":\"bool\"}],\"name\":\"AllowedCallerUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"msgSender\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"srcSender\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint32\",\"name\":\"chainId\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"mErc20Host_BorrowExternal\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"mErc20Host_BorrowMigration\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"dstChainId\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"mErc20Host_BorrowOnExtensionChain\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint32\",\"name\":\"chainId\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"status\",\"type\":\"bool\"}],\"name\":\"mErc20Host_ChainStatusUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint32\",\"name\":\"dstChainId\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"mErc20Host_GasFeeUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"msgSender\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"srcSender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"userToLiquidate\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"collateral\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"srcChainId\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"mErc20Host_LiquidateExternal\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"msgSender\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"srcSender\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"chainId\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"mErc20Host_MintExternal\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"mErc20Host_MintMigration\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"msgSender\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"srcSender\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"position\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"chainId\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"mErc20Host_RepayExternal\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"msgSender\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"srcSender\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint32\",\"name\":\"chainId\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"mErc20Host_WithdrawExternal\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"dstChainId\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"mErc20Host_WithdrawOnExtensionChain\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"extractForRebalancing\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"dstId\",\"type\":\"uint32\"}],\"name\":\"getProofData\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"journalData\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"seal\",\"type\":\"bytes\"},{\"internalType\":\"address[]\",\"name\":\"userToLiquidate\",\"type\":\"address[]\"},{\"internalType\":\"uint256[]\",\"name\":\"liquidateAmount\",\"type\":\"uint256[]\"},{\"internalType\":\"address[]\",\"name\":\"collateral\",\"type\":\"address[]\"},{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"}],\"name\":\"liquidateExternal\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"journalData\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"seal\",\"type\":\"bytes\"},{\"internalType\":\"uint256[]\",\"name\":\"mintAmount\",\"type\":\"uint256[]\"},{\"internalType\":\"uint256[]\",\"name\":\"minAmountsOut\",\"type\":\"uint256[]\"},{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"}],\"name\":\"mintExternal\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bool\",\"name\":\"mint\",\"type\":\"bool\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"minAmount\",\"type\":\"uint256\"}],\"name\":\"mintOrBorrowMigration\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"actionType\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"internalType\":\"uint32\",\"name\":\"dstChainId\",\"type\":\"uint32\"}],\"name\":\"performExtensionCall\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"journalData\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"seal\",\"type\":\"bytes\"},{\"internalType\":\"uint256[]\",\"name\":\"repayAmount\",\"type\":\"uint256[]\"},{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"}],\"name\":\"repayExternal\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"caller\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"status\",\"type\":\"bool\"}],\"name\":\"updateAllowedCallerStatus\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{\"extractForRebalancing(uint256)\":{\"params\":{\"amount\":\"The amount to rebalance\"}},\"liquidateExternal(bytes,bytes,address[],uint256[],address[],address)\":{\"params\":{\"collateral\":\"Array of collaterals to seize\",\"journalData\":\"The journal data for minting (array of encoded journals)\",\"liquidateAmount\":\"Array of amounts to liquidate\",\"receiver\":\"The collateral receiver\",\"seal\":\"The Zk proof seal\",\"userToLiquidate\":\"Array of positions to liquidate\"}},\"mintExternal(bytes,bytes,uint256[],uint256[],address)\":{\"params\":{\"journalData\":\"The journal data for minting (array of encoded journals)\",\"minAmountsOut\":\"Array of min amounts accepted\",\"mintAmount\":\"Array of amounts to mint\",\"receiver\":\"The tokens receiver\",\"seal\":\"The Zk proof seal\"}},\"mintOrBorrowMigration(bool,uint256,address,address,uint256)\":{\"params\":{\"amount\":\"The amount of underlying to be accounted for\",\"borrower\":\"The address that borrow is executed for\",\"minAmount\":\"The min amount of underlying to be accounted for\",\"mint\":\"Mint or borrow\",\"receiver\":\"The address that will receive the mTokens or the underlying in case of borrowing\"}},\"performExtensionCall(uint256,uint256,uint32)\":{\"params\":{\"actionType\":\"The actionType param (1 - withdraw, 2 - borrow)\",\"amount\":\"The amount to withdraw\",\"dstChainId\":\"The destination chain to recieve funds\"}},\"repayExternal(bytes,bytes,uint256[],address)\":{\"params\":{\"journalData\":\"The journal data for repayment (array of encoded journals)\",\"receiver\":\"The position to repay for\",\"repayAmount\":\"Array of amounts to repay\",\"seal\":\"The Zk proof seal\"}},\"updateAllowedCallerStatus(address,bool)\":{\"params\":{\"caller\":\"The caller address\",\"status\":\"The status to set for `caller`\"}}},\"version\":1},\"userdoc\":{\"errors\":{\"mErc20Host_ActionNotAvailable()\":[{\"notice\":\"Thrown when extension action is not valid\"}],\"mErc20Host_AddressNotValid()\":[{\"notice\":\"Thrown when the address is not valid\"}],\"mErc20Host_AmountNotValid()\":[{\"notice\":\"Thrown when the amount specified is invalid (e.g., zero)\"}],\"mErc20Host_AmountTooBig()\":[{\"notice\":\"Thrown when the amount provided is bigger than the available amount`\"}],\"mErc20Host_CallerNotAllowed()\":[{\"notice\":\"Thrown when caller is not allowed\"}],\"mErc20Host_ChainNotValid()\":[{\"notice\":\"Thrown when the chain id is not LINEA\"}],\"mErc20Host_DstChainNotValid()\":[{\"notice\":\"Thrown when the dst chain id is not current chain\"}],\"mErc20Host_JournalNotValid()\":[{\"notice\":\"Thrown when the journal data provided is invalid or corrupted\"}],\"mErc20Host_L1InclusionRequired()\":[{\"notice\":\"Thrown when L1 inclusion is required\"}],\"mErc20Host_LengthMismatch()\":[{\"notice\":\"Thrown when length of array is not valid\"}],\"mErc20Host_NotEnoughGasFee()\":[{\"notice\":\"Thrown when not enough gas fee was received\"}],\"mErc20Host_NotRebalancer()\":[{\"notice\":\"Thrown when caller is not rebalancer\"}],\"mErc20Host_ProofGenerationInputNotValid()\":[{\"notice\":\"Thrown when the chain id is not LINEA\"}]},\"events\":{\"AllowedCallerUpdated(address,address,bool)\":{\"notice\":\"Emitted when a user updates allowed callers\"},\"mErc20Host_BorrowExternal(address,address,uint32,uint256)\":{\"notice\":\"Emitted when a borrow operation is executed\"},\"mErc20Host_BorrowOnExtensionChain(address,uint32,uint256)\":{\"notice\":\"Emitted when a borrow operation is triggered for an extension chain\"},\"mErc20Host_ChainStatusUpdated(uint32,bool)\":{\"notice\":\"Emitted when a chain id whitelist status is updated\"},\"mErc20Host_GasFeeUpdated(uint32,uint256)\":{\"notice\":\"Emitted when gas fees are updated for a dst chain\"},\"mErc20Host_LiquidateExternal(address,address,address,address,address,uint32,uint256)\":{\"notice\":\"Emitted when a liquidate operation is executed\"},\"mErc20Host_MintExternal(address,address,address,uint32,uint256)\":{\"notice\":\"Emitted when a mint operation is executed\"},\"mErc20Host_RepayExternal(address,address,address,uint32,uint256)\":{\"notice\":\"Emitted when a repay operation is executed\"},\"mErc20Host_WithdrawExternal(address,address,uint32,uint256)\":{\"notice\":\"Emitted when a withdrawal is executed\"},\"mErc20Host_WithdrawOnExtensionChain(address,uint32,uint256)\":{\"notice\":\"Emitted when a withdraw operation is triggered for an extension chain\"}},\"kind\":\"user\",\"methods\":{\"extractForRebalancing(uint256)\":{\"notice\":\"Extract amount to be used for rebalancing operation\"},\"getProofData(address,uint32)\":{\"notice\":\"Returns the proof data journal\"},\"liquidateExternal(bytes,bytes,address[],uint256[],address[],address)\":{\"notice\":\"Mints tokens after external verification\"},\"mintExternal(bytes,bytes,uint256[],uint256[],address)\":{\"notice\":\"Mints tokens after external verification\"},\"mintOrBorrowMigration(bool,uint256,address,address,uint256)\":{\"notice\":\"Mints mTokens during migration without requiring underlying transfer\"},\"performExtensionCall(uint256,uint256,uint32)\":{\"notice\":\"Initiates a withdraw operation\"},\"repayExternal(bytes,bytes,uint256[],address)\":{\"notice\":\"Repays tokens after external verification\"},\"updateAllowedCallerStatus(address,bool)\":{\"notice\":\"Set caller status for `msg.sender`\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/ImErc20Host.sol\":\"ImErc20Host\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"src/interfaces/ImErc20Host.sol\":{\"keccak256\":\"0x90f1ba59e63b0bd8d11deb1154bb885906daf858e81ff3eca579db73281a1577\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://4202334b0825195dc2d50cf29420f4bcebdf1d91ebcadc56155d92243f133c11\",\"dweb:/ipfs/QmVrcpP8YcTHNUCGJQCeBMUZU9VMpvsvUfVwN14pVzkD5o\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "mErc20Host_ActionNotAvailable"}, {"inputs": [], "type": "error", "name": "mErc20Host_AddressNotValid"}, {"inputs": [], "type": "error", "name": "mErc20Host_AmountNotValid"}, {"inputs": [], "type": "error", "name": "mErc20Host_AmountTooBig"}, {"inputs": [], "type": "error", "name": "mErc20Host_CallerNotAllowed"}, {"inputs": [], "type": "error", "name": "mErc20Host_ChainNotValid"}, {"inputs": [], "type": "error", "name": "mErc20Host_DstChainNotValid"}, {"inputs": [], "type": "error", "name": "mErc20Host_JournalNotValid"}, {"inputs": [], "type": "error", "name": "mErc20Host_L1InclusionRequired"}, {"inputs": [], "type": "error", "name": "mErc20Host_LengthMismatch"}, {"inputs": [], "type": "error", "name": "mErc20Host_NotEnoughGasFee"}, {"inputs": [], "type": "error", "name": "mErc20Host_NotRebalancer"}, {"inputs": [], "type": "error", "name": "mErc20Host_ProofGenerationInputNotValid"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address", "indexed": true}, {"internalType": "address", "name": "caller", "type": "address", "indexed": true}, {"internalType": "bool", "name": "status", "type": "bool", "indexed": false}], "type": "event", "name": "AllowedCallerUpdated", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "msgSender", "type": "address", "indexed": true}, {"internalType": "address", "name": "srcSender", "type": "address", "indexed": true}, {"internalType": "uint32", "name": "chainId", "type": "uint32", "indexed": true}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}], "type": "event", "name": "mErc20Host_BorrowExternal", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "borrower", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}], "type": "event", "name": "mErc20Host_BorrowMigration", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address", "indexed": true}, {"internalType": "uint32", "name": "dst<PERSON>hainId", "type": "uint32", "indexed": false}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}], "type": "event", "name": "mErc20Host_BorrowOnExtensionChain", "anonymous": false}, {"inputs": [{"internalType": "uint32", "name": "chainId", "type": "uint32", "indexed": true}, {"internalType": "bool", "name": "status", "type": "bool", "indexed": false}], "type": "event", "name": "mErc20Host_ChainStatusUpdated", "anonymous": false}, {"inputs": [{"internalType": "uint32", "name": "dst<PERSON>hainId", "type": "uint32", "indexed": true}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}], "type": "event", "name": "mErc20Host_GasFeeUpdated", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "msgSender", "type": "address", "indexed": true}, {"internalType": "address", "name": "srcSender", "type": "address", "indexed": true}, {"internalType": "address", "name": "userToLiquidate", "type": "address", "indexed": false}, {"internalType": "address", "name": "receiver", "type": "address", "indexed": false}, {"internalType": "address", "name": "collateral", "type": "address", "indexed": true}, {"internalType": "uint32", "name": "srcChainId", "type": "uint32", "indexed": false}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}], "type": "event", "name": "mErc20Host_LiquidateExternal", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "msgSender", "type": "address", "indexed": true}, {"internalType": "address", "name": "srcSender", "type": "address", "indexed": true}, {"internalType": "address", "name": "receiver", "type": "address", "indexed": true}, {"internalType": "uint32", "name": "chainId", "type": "uint32", "indexed": false}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}], "type": "event", "name": "mErc20Host_MintExternal", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}], "type": "event", "name": "mErc20Host_MintMigration", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "msgSender", "type": "address", "indexed": true}, {"internalType": "address", "name": "srcSender", "type": "address", "indexed": true}, {"internalType": "address", "name": "position", "type": "address", "indexed": true}, {"internalType": "uint32", "name": "chainId", "type": "uint32", "indexed": false}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}], "type": "event", "name": "mErc20Host_RepayExternal", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "msgSender", "type": "address", "indexed": true}, {"internalType": "address", "name": "srcSender", "type": "address", "indexed": true}, {"internalType": "uint32", "name": "chainId", "type": "uint32", "indexed": true}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}], "type": "event", "name": "mErc20Host_WithdrawExternal", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address", "indexed": true}, {"internalType": "uint32", "name": "dst<PERSON>hainId", "type": "uint32", "indexed": false}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}], "type": "event", "name": "mErc20Host_WithdrawOnExtensionChain", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "extractForRebalancing"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint32", "name": "dstId", "type": "uint32"}], "stateMutability": "view", "type": "function", "name": "getProofData", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "bytes", "name": "journalData", "type": "bytes"}, {"internalType": "bytes", "name": "seal", "type": "bytes"}, {"internalType": "address[]", "name": "userToLiquidate", "type": "address[]"}, {"internalType": "uint256[]", "name": "liquidateAmount", "type": "uint256[]"}, {"internalType": "address[]", "name": "collateral", "type": "address[]"}, {"internalType": "address", "name": "receiver", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "liquidateExternal"}, {"inputs": [{"internalType": "bytes", "name": "journalData", "type": "bytes"}, {"internalType": "bytes", "name": "seal", "type": "bytes"}, {"internalType": "uint256[]", "name": "mintAmount", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "minAmountsOut", "type": "uint256[]"}, {"internalType": "address", "name": "receiver", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "mintExternal"}, {"inputs": [{"internalType": "bool", "name": "mint", "type": "bool"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "address", "name": "borrower", "type": "address"}, {"internalType": "uint256", "name": "minAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "mintOrBorrowMigration"}, {"inputs": [{"internalType": "uint256", "name": "actionType", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint32", "name": "dst<PERSON>hainId", "type": "uint32"}], "stateMutability": "payable", "type": "function", "name": "performExtensionCall"}, {"inputs": [{"internalType": "bytes", "name": "journalData", "type": "bytes"}, {"internalType": "bytes", "name": "seal", "type": "bytes"}, {"internalType": "uint256[]", "name": "repayAmount", "type": "uint256[]"}, {"internalType": "address", "name": "receiver", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "repayExternal"}, {"inputs": [{"internalType": "address", "name": "caller", "type": "address"}, {"internalType": "bool", "name": "status", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "updateAllowedCallerStatus"}], "devdoc": {"kind": "dev", "methods": {"extractForRebalancing(uint256)": {"params": {"amount": "The amount to rebalance"}}, "liquidateExternal(bytes,bytes,address[],uint256[],address[],address)": {"params": {"collateral": "Array of collaterals to seize", "journalData": "The journal data for minting (array of encoded journals)", "liquidateAmount": "Array of amounts to liquidate", "receiver": "The collateral receiver", "seal": "The Zk proof seal", "userToLiquidate": "Array of positions to liquidate"}}, "mintExternal(bytes,bytes,uint256[],uint256[],address)": {"params": {"journalData": "The journal data for minting (array of encoded journals)", "minAmountsOut": "Array of min amounts accepted", "mintAmount": "Array of amounts to mint", "receiver": "The tokens receiver", "seal": "The Zk proof seal"}}, "mintOrBorrowMigration(bool,uint256,address,address,uint256)": {"params": {"amount": "The amount of underlying to be accounted for", "borrower": "The address that borrow is executed for", "minAmount": "The min amount of underlying to be accounted for", "mint": "Mint or borrow", "receiver": "The address that will receive the mTokens or the underlying in case of borrowing"}}, "performExtensionCall(uint256,uint256,uint32)": {"params": {"actionType": "The actionType param (1 - withdraw, 2 - borrow)", "amount": "The amount to withdraw", "dstChainId": "The destination chain to recieve funds"}}, "repayExternal(bytes,bytes,uint256[],address)": {"params": {"journalData": "The journal data for repayment (array of encoded journals)", "receiver": "The position to repay for", "repayAmount": "Array of amounts to repay", "seal": "The Zk proof seal"}}, "updateAllowedCallerStatus(address,bool)": {"params": {"caller": "The caller address", "status": "The status to set for `caller`"}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"extractForRebalancing(uint256)": {"notice": "Extract amount to be used for rebalancing operation"}, "getProofData(address,uint32)": {"notice": "Returns the proof data journal"}, "liquidateExternal(bytes,bytes,address[],uint256[],address[],address)": {"notice": "Mints tokens after external verification"}, "mintExternal(bytes,bytes,uint256[],uint256[],address)": {"notice": "Mints tokens after external verification"}, "mintOrBorrowMigration(bool,uint256,address,address,uint256)": {"notice": "Mints mTokens during migration without requiring underlying transfer"}, "performExtensionCall(uint256,uint256,uint32)": {"notice": "Initiates a withdraw operation"}, "repayExternal(bytes,bytes,uint256[],address)": {"notice": "Repays tokens after external verification"}, "updateAllowedCallerStatus(address,bool)": {"notice": "Set caller status for `msg.sender`"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/interfaces/ImErc20Host.sol": "ImErc20Host"}, "evmVersion": "london", "libraries": {}}, "sources": {"src/interfaces/ImErc20Host.sol": {"keccak256": "0x90f1ba59e63b0bd8d11deb1154bb885906daf858e81ff3eca579db73281a1577", "urls": ["bzz-raw://4202334b0825195dc2d50cf29420f4bcebdf1d91ebcadc56155d92243f133c11", "dweb:/ipfs/QmVrcpP8YcTHNUCGJQCeBMUZU9VMpvsvUfVwN14pVzkD5o"], "license": "BSL-1.1"}}, "version": 1}, "id": 145}