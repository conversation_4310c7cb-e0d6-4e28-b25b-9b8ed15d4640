{"abi": [{"type": "function", "name": "handleV3AcrossMessage", "inputs": [{"name": "tokenSent", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "relayer", "type": "address", "internalType": "address"}, {"name": "message", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"handleV3AcrossMessage(address,uint256,address,bytes)": "3a5be8cb"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"tokenSent\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"relayer\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"message\",\"type\":\"bytes\"}],\"name\":\"handleV3AcrossMessage\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/external/across/IAcrossReceiverV3.sol\":\"IAcrossReceiverV3\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"src/interfaces/external/across/IAcrossReceiverV3.sol\":{\"keccak256\":\"0xd0c5c3bd085ae04fff63e7787274ff98a08b2e91ca058253d501de69dd19cc8d\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://bc677bbe6c9b8482574dc15283288828bfa3d84d41369fe25dff9d9ef733c4ea\",\"dweb:/ipfs/QmWFLUCHA3HVqDJvcqmqCdjENtLgfTjnsfoPLCkqjntqwb\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "tokenSent", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "address", "name": "relayer", "type": "address"}, {"internalType": "bytes", "name": "message", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "handleV3AcrossMessage"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/interfaces/external/across/IAcrossReceiverV3.sol": "IAcrossReceiverV3"}, "evmVersion": "london", "libraries": {}}, "sources": {"src/interfaces/external/across/IAcrossReceiverV3.sol": {"keccak256": "0xd0c5c3bd085ae04fff63e7787274ff98a08b2e91ca058253d501de69dd19cc8d", "urls": ["bzz-raw://bc677bbe6c9b8482574dc15283288828bfa3d84d41369fe25dff9d9ef733c4ea", "dweb:/ipfs/QmWFLUCHA3HVqDJvcqmqCdjENtLgfTjnsfoPLCkqjntqwb"], "license": "BSL-1.1"}}, "version": 1}, "id": 148}