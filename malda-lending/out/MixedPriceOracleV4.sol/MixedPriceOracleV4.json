{"abi": [{"type": "constructor", "inputs": [{"name": "symbols_", "type": "string[]", "internalType": "string[]"}, {"name": "configs_", "type": "tuple[]", "internalType": "struct MixedPriceOracleV4.PriceConfig[]", "components": [{"name": "api3Feed", "type": "address", "internalType": "address"}, {"name": "eOracleFeed", "type": "address", "internalType": "address"}, {"name": "toSymbol", "type": "string", "internalType": "string"}, {"name": "underlyingDecimals", "type": "uint256", "internalType": "uint256"}]}, {"name": "roles_", "type": "address", "internalType": "address"}, {"name": "stalenessPeriod_", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "PRICE_DELTA_EXP", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "STALENESS_PERIOD", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "configs", "inputs": [{"name": "", "type": "string", "internalType": "string"}], "outputs": [{"name": "api3Feed", "type": "address", "internalType": "address"}, {"name": "eOracleFeed", "type": "address", "internalType": "address"}, {"name": "toSymbol", "type": "string", "internalType": "string"}, {"name": "underlyingDecimals", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "deltaPerSymbol", "inputs": [{"name": "", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getPrice", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getUnderlyingPrice", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "roles", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IRoles"}], "stateMutability": "view"}, {"type": "function", "name": "setConfig", "inputs": [{"name": "symbol", "type": "string", "internalType": "string"}, {"name": "config", "type": "tuple", "internalType": "struct MixedPriceOracleV4.PriceConfig", "components": [{"name": "api3Feed", "type": "address", "internalType": "address"}, {"name": "eOracleFeed", "type": "address", "internalType": "address"}, {"name": "toSymbol", "type": "string", "internalType": "string"}, {"name": "underlyingDecimals", "type": "uint256", "internalType": "uint256"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setMaxPriceDelta", "inputs": [{"name": "_delta", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setStaleness", "inputs": [{"name": "symbol", "type": "string", "internalType": "string"}, {"name": "val", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setSymbolMaxPriceDelta", "inputs": [{"name": "_delta", "type": "uint256", "internalType": "uint256"}, {"name": "_symbol", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "stalenessPerSymbol", "inputs": [{"name": "", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "event", "name": "ConfigSet", "inputs": [{"name": "symbol", "type": "string", "indexed": false, "internalType": "string"}, {"name": "config", "type": "tuple", "indexed": false, "internalType": "struct MixedPriceOracleV4.PriceConfig", "components": [{"name": "api3Feed", "type": "address", "internalType": "address"}, {"name": "eOracleFeed", "type": "address", "internalType": "address"}, {"name": "toSymbol", "type": "string", "internalType": "string"}, {"name": "underlyingDecimals", "type": "uint256", "internalType": "uint256"}]}], "anonymous": false}, {"type": "event", "name": "PriceDeltaUpdated", "inputs": [{"name": "oldVal", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "newVal", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "PriceSymbolDeltaUpdated", "inputs": [{"name": "oldVal", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "newVal", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "symbol", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "StalenessUpdated", "inputs": [{"name": "symbol", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "error", "name": "MixedPriceOracle_ApiV3StalePrice", "inputs": []}, {"type": "error", "name": "MixedPriceOracle_DeltaTooHigh", "inputs": []}, {"type": "error", "name": "MixedPriceOracle_InvalidConfig", "inputs": []}, {"type": "error", "name": "MixedPriceOracle_InvalidConfigDecimals", "inputs": []}, {"type": "error", "name": "MixedPriceOracle_InvalidPrice", "inputs": []}, {"type": "error", "name": "MixedPriceOracle_InvalidRound", "inputs": []}, {"type": "error", "name": "MixedPriceOracle_MissingFeed", "inputs": []}, {"type": "error", "name": "MixedPriceOracle_Unauthorized", "inputs": []}, {"type": "error", "name": "MixedPriceOracle_eOracleStalePrice", "inputs": []}], "bytecode": {"object": "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", "sourceMap": "738:6293:185:-:0;;;1222:5;1191:36;;1988:309;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;2109:22:185;;;;2146:9;2141:105;2165:8;:15;2161:1;:19;2141:105;;;2224:8;2233:1;2224:11;;;;;;;;:::i;:::-;;;;;;;2201:7;2209:8;2218:1;2209:11;;;;;;;;:::i;:::-;;;;;;;2201:20;;;;;;:::i;:::-;;;;;;;;;;;;;;;;:34;;;;-1:-1:-1;;;;;;2201:34:185;;;-1:-1:-1;;;;;2201:34:185;;;;;;;;;;-1:-1:-1;2201:34:185;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;2201:34:185;;;;;;;;;;;2182:3;;2141:105;;;-1:-1:-1;2255:35:185;;-1:-1:-1;738:6293:185;;-1:-1:-1;;738:6293:185;14:127:242;75:10;70:3;66:20;63:1;56:31;106:4;103:1;96:15;130:4;127:1;120:15;146:253;218:2;212:9;260:4;248:17;;-1:-1:-1;;;;;280:34:242;;316:22;;;277:62;274:88;;;342:18;;:::i;:::-;378:2;371:22;146:253;:::o;404:275::-;475:2;469:9;540:2;521:13;;-1:-1:-1;;517:27:242;505:40;;-1:-1:-1;;;;;560:34:242;;596:22;;;557:62;554:88;;;622:18;;:::i;:::-;658:2;651:22;404:275;;-1:-1:-1;404:275:242:o;684:182::-;743:4;-1:-1:-1;;;;;765:30:242;;762:56;;;798:18;;:::i;:::-;-1:-1:-1;843:1:242;839:14;855:4;835:25;;684:182::o;871:250::-;956:1;966:113;980:6;977:1;974:13;966:113;;;1056:11;;;1050:18;1037:11;;;1030:39;1002:2;995:10;966:113;;;-1:-1:-1;;1113:1:242;1095:16;;1088:27;871:250::o;1126:534::-;1180:5;1233:3;1226:4;1218:6;1214:17;1210:27;1200:55;;1251:1;1248;1241:12;1200:55;1278:13;;-1:-1:-1;;;;;1303:30:242;;1300:56;;;1336:18;;:::i;:::-;1380:59;1427:2;1404:17;;-1:-1:-1;;1400:31:242;1433:4;1396:42;1380:59;:::i;:::-;1464:6;1455:7;1448:23;1518:3;1511:4;1502:6;1494;1490:19;1486:30;1483:39;1480:59;;;1535:1;1532;1525:12;1480:59;1548:81;1622:6;1615:4;1606:7;1602:18;1595:4;1587:6;1583:17;1548:81;:::i;:::-;1647:7;1126:534;-1:-1:-1;;;;1126:534:242:o;1665:177::-;1744:13;;-1:-1:-1;;;;;1786:31:242;;1776:42;;1766:70;;1832:1;1829;1822:12;1766:70;1665:177;;;:::o;1847:1428::-;1923:5;1976:3;1969:4;1961:6;1957:17;1953:27;1943:55;;1994:1;1991;1984:12;1943:55;2027:6;2021:13;2054:63;2070:46;2109:6;2070:46;:::i;:::-;2054:63;:::i;:::-;2141:3;2165:6;2160:3;2153:19;2197:4;2192:3;2188:14;2181:21;;2258:4;2248:6;2245:1;2241:14;2233:6;2229:27;2225:38;2211:52;;2286:3;2278:6;2275:15;2272:35;;;2303:1;2300;2293:12;2272:35;2339:4;2331:6;2327:17;2353:891;2369:6;2364:3;2361:15;2353:891;;;2444:10;;-1:-1:-1;;;;;2470:35:242;;2467:55;;;2518:1;2515;2508:12;2467:55;2545:24;;2617:4;2593:12;;;-1:-1:-1;;2589:26:242;2585:37;2582:57;;;2635:1;2632;2625:12;2582:57;2665:22;;:::i;:::-;2714:44;2752:4;2748:2;2744:13;2714:44;:::i;:::-;2707:5;2700:59;2797:42;2835:2;2831;2827:11;2797:42;:::i;:::-;2790:4;2779:16;;2772:68;2883:2;2875:11;;2869:18;-1:-1:-1;;;;;2903:32:242;;2900:52;;;2948:1;2945;2938:12;2900:52;2988:63;3047:3;3040:4;3029:8;3025:2;3021:17;3017:28;2988:63;:::i;:::-;2983:2;2972:14;;2965:87;-1:-1:-1;3119:4:242;3111:13;;;;3105:20;3156:2;3145:14;;3138:31;3182:18;;-1:-1:-1;3220:14:242;;;;2386;2353:891;;;-1:-1:-1;3262:7:242;1847:1428;-1:-1:-1;;;;;1847:1428:242:o;3280:1547::-;3467:6;3475;3483;3491;3544:3;3532:9;3523:7;3519:23;3515:33;3512:53;;;3561:1;3558;3551:12;3512:53;3588:16;;-1:-1:-1;;;;;3616:30:242;;3613:50;;;3659:1;3656;3649:12;3613:50;3682:22;;3735:4;3727:13;;3723:27;-1:-1:-1;3713:55:242;;3764:1;3761;3754:12;3713:55;3797:2;3791:9;3820:63;3836:46;3875:6;3836:46;:::i;3820:63::-;3905:3;3929:6;3924:3;3917:19;3961:4;3956:3;3952:14;3945:21;;4018:4;4008:6;4005:1;4001:14;3997:2;3993:23;3989:34;3975:48;;4046:7;4038:6;4035:19;4032:39;;;4067:1;4064;4057:12;4032:39;4099:4;4095:2;4091:13;4113:309;4129:6;4124:3;4121:15;4113:309;;;4204:10;;-1:-1:-1;;;;;4230:35:242;;4227:55;;;4278:1;4275;4268:12;4227:55;4307:70;4369:7;4362:4;4348:11;4344:2;4340:20;4336:31;4307:70;:::i;:::-;4295:83;;-1:-1:-1;4407:4:242;4398:14;;;;4146;4113:309;;;-1:-1:-1;4492:4:242;4477:20;;4471:27;4441:5;;-1:-1:-1;4471:27:242;-1:-1:-1;;;;;;;;4510:32:242;;4507:52;;;4555:1;4552;4545:12;4507:52;4578:85;4655:7;4644:8;4633:9;4629:24;4578:85;:::i;:::-;4568:95;;;4682:49;4727:2;4716:9;4712:18;4682:49;:::i;:::-;4793:2;4778:18;;;;4772:25;3280:1547;;;;-1:-1:-1;;;3280:1547:242:o;4832:127::-;4893:10;4888:3;4884:20;4881:1;4874:31;4924:4;4921:1;4914:15;4948:4;4945:1;4938:15;4964:289;5095:3;5133:6;5127:13;5149:66;5208:6;5203:3;5196:4;5188:6;5184:17;5149:66;:::i;:::-;5231:16;;;;;4964:289;-1:-1:-1;;4964:289:242:o;5258:380::-;5337:1;5333:12;;;;5380;;;5401:61;;5455:4;5447:6;5443:17;5433:27;;5401:61;5508:2;5500:6;5497:14;5477:18;5474:38;5471:161;;5554:10;5549:3;5545:20;5542:1;5535:31;5589:4;5586:1;5579:15;5617:4;5614:1;5607:15;5471:161;;5258:380;;;:::o;5769:518::-;5871:2;5866:3;5863:11;5860:421;;;5907:5;5904:1;5897:16;5951:4;5948:1;5938:18;6021:2;6009:10;6005:19;6002:1;5998:27;5992:4;5988:38;6057:4;6045:10;6042:20;6039:47;;;-1:-1:-1;6080:4:242;6039:47;6135:2;6130:3;6126:12;6123:1;6119:20;6113:4;6109:31;6099:41;;6190:81;6208:2;6201:5;6198:13;6190:81;;;6267:1;6253:16;;6234:1;6223:13;6190:81;;;6194:3;;5860:421;5769:518;;;:::o;6463:1299::-;6583:10;;-1:-1:-1;;;;;6605:30:242;;6602:56;;;6638:18;;:::i;:::-;6667:97;6757:6;6717:38;6749:4;6743:11;6717:38;:::i;:::-;6711:4;6667:97;:::i;:::-;6813:4;6844:2;6833:14;;6861:1;6856:649;;;;7549:1;7566:6;7563:89;;;-1:-1:-1;7618:19:242;;;7612:26;7563:89;-1:-1:-1;;6420:1:242;6416:11;;;6412:24;6408:29;6398:40;6444:1;6440:11;;;6395:57;7665:81;;6826:930;;6856:649;5716:1;5709:14;;;5753:4;5740:18;;-1:-1:-1;;6892:20:242;;;7010:222;7024:7;7021:1;7018:14;7010:222;;;7106:19;;;7100:26;7085:42;;7213:4;7198:20;;;;7166:1;7154:14;;;;7040:12;7010:222;;;7014:3;7260:6;7251:7;7248:19;7245:201;;;7321:19;;;7315:26;-1:-1:-1;;7404:1:242;7400:14;;;7416:3;7396:24;7392:37;7388:42;7373:58;7358:74;;7245:201;-1:-1:-1;;;;7492:1:242;7476:14;;;7472:22;7459:36;;-1:-1:-1;6463:1299:242:o;:::-;738:6293:185;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "738:6293:185:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3409:424;;;;;;:::i;:::-;;:::i;:::-;;791:41;;;;;;;;866:25:242;;;854:2;839:18;791:41:185;;;;;;;;1078:52;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;1291:29;;;;;;;;-1:-1:-1;;;;;2764:32:242;;;2746:51;;2734:2;2719:18;1291:29:185;2584:219:242;3839:172:185;;;;;;:::i;:::-;;:::i;1027:45::-;;;;;;:::i;:::-;;:::i;:::-;;;;;;;;;;:::i;1191:36::-;;;;;;3045:358;;;;;;:::i;:::-;;:::i;1240:45::-;;1282:3;1240:45;;2303:293;;;;;;:::i;:::-;;:::i;2602:437::-;;;;;;:::i;:::-;;:::i;4095:456::-;;;;;;:::i;:::-;;:::i;1136:48::-;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;3409:424;3506:5;-1:-1:-1;;;;;3506:18:185;;3525:10;3537:5;-1:-1:-1;;;;;3537:21:185;;:23;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;3506:55;;-1:-1:-1;;;;;;3506:55:185;;;;;;;-1:-1:-1;;;;;6474:32:242;;;3506:55:185;;;6456:51:242;6523:18;;;6516:34;6429:18;;3506:55:185;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;3501:125;;3584:31;;-1:-1:-1;;;3584:31:185;;;;;;;;;;;3501:125;1282:3;3644:6;:25;;3636:67;;;;-1:-1:-1;;;3636:67:185;;;;;;;;;;;;3719:65;3743:14;3758:7;;3743:23;;;;;;;:::i;:::-;;;;;;;;;;;;;;;3719:65;;3768:6;;3776:7;;;;3719:65;:::i;:::-;;;;;;;;3820:6;3794:14;3809:7;;3794:23;;;;;;;:::i;:::-;;;;;;;;;;;;;;:32;-1:-1:-1;;;3409:424:185:o;3839:172::-;3894:7;3913:20;3951:6;-1:-1:-1;;;;;3936:29:185;;:31;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;3936:31:185;;;;;;;;;;;;:::i;:::-;3913:54;;3984:20;3997:6;3984:12;:20::i;:::-;3977:27;3839:172;-1:-1:-1;;;3839:172:185:o;1027:45::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;1027:45:185;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;3045:358::-;3111:5;-1:-1:-1;;;;;3111:18:185;;3130:10;3142:5;-1:-1:-1;;;;;3142:21:185;;:23;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;3111:55;;-1:-1:-1;;;;;;3111:55:185;;;;;;;-1:-1:-1;;;;;6474:32:242;;;3111:55:185;;;6456:51:242;6523:18;;;6516:34;6429:18;;3111:55:185;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;3106:125;;3189:31;;-1:-1:-1;;;3189:31:185;;;;;;;;;;;3106:125;1282:3;3249:6;:25;;3241:67;;;;-1:-1:-1;;;3241:67:185;;;;;;;;;;;;3342:13;;3324:40;;;8894:25:242;;;8950:2;8935:18;;8928:34;;;3324:40:185;;8867:18:242;3324:40:185;;;;;;;3374:13;:22;3045:358::o;2303:293::-;2384:5;-1:-1:-1;;;;;2384:18:185;;2403:10;2415:5;-1:-1:-1;;;;;2415:21:185;;:23;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2384:55;;-1:-1:-1;;;;;;2384:55:185;;;;;;;-1:-1:-1;;;;;6474:32:242;;;2384:55:185;;;6456:51:242;6523:18;;;6516:34;6429:18;;2384:55:185;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2379:125;;2462:31;;-1:-1:-1;;;2462:31:185;;;;;;;;;;;2379:125;2542:3;2513:18;2532:6;2513:26;;;;;;:::i;:::-;;;;;;;;;;;;;:32;;;;2560:29;2577:6;2585:3;2560:29;;;;;;;:::i;:::-;;;;;;;;2303:293;;:::o;2602:437::-;2694:5;-1:-1:-1;;;;;2694:18:185;;2713:10;2725:5;-1:-1:-1;;;;;2725:21:185;;:23;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2694:55;;-1:-1:-1;;;;;;2694:55:185;;;;;;;-1:-1:-1;;;;;6474:32:242;;;2694:55:185;;;6456:51:242;6523:18;;;6516:34;6429:18;;2694:55:185;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2689:125;;2772:31;;-1:-1:-1;;;2772:31:185;;;;;;;;;;;2689:125;2827:15;;-1:-1:-1;;;;;2827:29:185;;;:65;;-1:-1:-1;2860:18:185;;;;-1:-1:-1;;;;;2860:32:185;;2827:65;2823:135;;;2915:32;;-1:-1:-1;;;2915:32:185;;;;;;;;;;;2823:135;2986:6;2968:7;2976:6;2968:15;;;;;;:::i;:::-;;;;;;;;;;;;;;;;:24;;;;-1:-1:-1;;;;;;2968:24:185;;;-1:-1:-1;;;;;2968:24:185;;;;;;;;;;-1:-1:-1;2968:24:185;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;3007:25;3017:6;3025;3007:25;;;;;;;:::i;4095:456::-;4171:7;4293:20;4346:6;-1:-1:-1;;;;;4331:33:185;;:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;4316:58:185;;:60;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;4316:60:185;;;;;;;;;;;;:::i;:::-;4293:83;;4386:25;4414:7;4422:6;4414:15;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;4386:43;;;;;;;-1:-1:-1;;;;;4386:43:185;;;;;;;;;;;;;;;;;;;;;;4414:15;;4386:43;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4439:16;4458:20;4471:6;4458:12;:20::i;:::-;4439:39;;4518:6;:25;;;4513:2;:30;;;;:::i;:::-;4506:38;;:2;:38;:::i;:::-;4495:49;;:8;:49;:::i;:::-;4488:56;4095:456;-1:-1:-1;;;;;4095:456:185:o;4557:488::-;4624:7;4643:25;4671:7;4679:6;4671:15;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;4643:43;;;;;;;-1:-1:-1;;;;;4643:43:185;;;;;;;;;;;;;;;;;;;;;;4671:15;;4643:43;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4697:17;4716:20;4740:31;4756:6;4764;4740:15;:31::i;:::-;4696:75;;-1:-1:-1;4696:75:185;-1:-1:-1;4781:13:185;4816:17;4696:75;4816:2;:17;:::i;:::-;4809:25;;:2;:25;:::i;:::-;4797:37;;:9;:37;:::i;:::-;4907:23;;-1:-1:-1;;;4907:23:185;;;14775:18:242;4781:53:185;;-1:-1:-1;14809:11:242;;4907:23:185;;;;;;;;;;;;4897:34;;;;;;4876:6;:15;;;4859:33;;;;;;;;:::i;:::-;;;;;;;;;;;;;4849:44;;;;;;:82;4845:171;;4997:8;4964:29;4977:6;:15;;;4964:12;:29::i;:::-;4956:37;;:5;:37;:::i;:::-;4955:50;;;;:::i;:::-;4947:58;5033:5;-1:-1:-1;;;;;;4557:488:185:o;5051:1634::-;5208:15;;5172:7;;;;-1:-1:-1;;;;;5208:29:185;;;:65;;-1:-1:-1;5241:18:185;;;;-1:-1:-1;;;;;5241:32:185;;5208:65;5204:108;;;5282:30;;-1:-1:-1;;;5282:30:185;;;;;;;;;;;5204:108;5352:17;5372:22;5415:6;:15;;;-1:-1:-1;;;;;5399:48:185;;:50;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;5349:100;;;;;;;5462:19;5484:24;5529:6;:18;;;-1:-1:-1;;;;;5513:51:185;;:53;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;5459:107;;;;;;;5623:18;5644:21;5658:6;5644:13;:21::i;:::-;5623:42;-1:-1:-1;5675:15:185;5623:42;5693:32;5711:14;5693:15;:32;:::i;:::-;:46;;5675:64;;5773:13;5789:34;5798:10;5810:12;5789:8;:34::i;:::-;5773:50;;5833:16;5903:1;5888:12;:16;:47;;5923:12;5888:47;;;5907:13;5908:12;5907:13;:::i;:::-;5853:23;1282:3;5853:5;:23;:::i;:::-;5852:84;;;;:::i;:::-;5833:103;;5947:19;5969:14;5984:6;5969:22;;;;;;:::i;:::-;;;;;;;;;;;;;;5947:44;;6005:11;6020:1;6005:16;6001:74;;-1:-1:-1;6051:13:185;;6001:74;6085:16;6111:14;6140:10;6139:11;:37;;;;6165:11;6154:8;:22;6139:37;6135:508;;;6237:10;6200:34;6218:16;6200:15;:34;:::i;:::-;:47;6192:94;;;;-1:-1:-1;;;6192:94:185;;;;;;;;;;;;6327:6;:18;;;-1:-1:-1;;;;;6311:44:185;;:46;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;6300:57;;;;6388:12;6371:30;;6135:508;;;6475:10;6440:32;6458:14;6440:15;:32;:::i;:::-;:45;6432:90;;;;-1:-1:-1;;;6432:90:185;;;;;;;;;;;;6563:6;:15;;;-1:-1:-1;;;;;6547:41:185;;:43;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;6536:54;;;;6621:10;6604:28;;6135:508;6661:6;6669:8;;-1:-1:-1;5051:1634:185;-1:-1:-1;;;;;;;;;;;;5051:1634:185:o;6822:207::-;6890:7;6909:19;6931:18;6950:6;6931:26;;;;;;:::i;:::-;;;;;;;;;;;;;;6909:48;;6988:1;6974:11;:15;:48;;7006:16;6974:48;;;6992:11;6967:55;-1:-1:-1;;6822:207:185:o;6691:125::-;6752:7;6791:1;6786;:6;;:22;;6803:5;6807:1;6803;:5;:::i;:::-;6786:22;;;6795:5;6799:1;6795;:5;:::i;14:701:242:-;94:6;102;110;163:2;151:9;142:7;138:23;134:32;131:52;;;179:1;176;169:12;131:52;224:23;;;-1:-1:-1;322:2:242;307:18;;294:32;349:18;338:30;;335:50;;;381:1;378;371:12;335:50;404:22;;457:4;449:13;;445:27;-1:-1:-1;435:55:242;;486:1;483;476:12;435:55;526:2;513:16;552:18;544:6;541:30;538:50;;;584:1;581;574:12;538:50;629:7;624:2;615:6;611:2;607:15;603:24;600:37;597:57;;;650:1;647;640:12;597:57;14:701;;681:2;673:11;;;;;-1:-1:-1;703:6:242;;-1:-1:-1;;;14:701:242:o;902:127::-;963:10;958:3;954:20;951:1;944:31;994:4;991:1;984:15;1018:4;1015:1;1008:15;1034:253;1106:2;1100:9;1148:4;1136:17;;1183:18;1168:34;;1204:22;;;1165:62;1162:88;;;1230:18;;:::i;:::-;1266:2;1259:22;1034:253;:::o;1292:275::-;1363:2;1357:9;1428:2;1409:13;;-1:-1:-1;;1405:27:242;1393:40;;1463:18;1448:34;;1484:22;;;1445:62;1442:88;;;1510:18;;:::i;:::-;1546:2;1539:22;1292:275;;-1:-1:-1;1292:275:242:o;1572:187::-;1621:4;1654:18;1646:6;1643:30;1640:56;;;1676:18;;:::i;:::-;-1:-1:-1;1742:2:242;1721:15;-1:-1:-1;;1717:29:242;1748:4;1713:40;;1572:187::o;1764:488::-;1807:5;1860:3;1853:4;1845:6;1841:17;1837:27;1827:55;;1878:1;1875;1868:12;1827:55;1918:6;1905:20;1949:53;1965:36;1994:6;1965:36;:::i;:::-;1949:53;:::i;:::-;2027:6;2018:7;2011:23;2081:3;2074:4;2065:6;2057;2053:19;2049:30;2046:39;2043:59;;;2098:1;2095;2088:12;2043:59;2163:6;2156:4;2148:6;2144:17;2137:4;2128:7;2124:18;2111:59;2219:1;2190:20;;;2212:4;2186:31;2179:42;;;;2194:7;1764:488;-1:-1:-1;;;1764:488:242:o;2257:322::-;2326:6;2379:2;2367:9;2358:7;2354:23;2350:32;2347:52;;;2395:1;2392;2385:12;2347:52;2435:9;2422:23;2468:18;2460:6;2457:30;2454:50;;;2500:1;2497;2490:12;2454:50;2523;2565:7;2556:6;2545:9;2541:22;2523:50;:::i;:::-;2513:60;2257:322;-1:-1:-1;;;;2257:322:242:o;2808:131::-;-1:-1:-1;;;;;2883:31:242;;2873:42;;2863:70;;2929:1;2926;2919:12;2863:70;2808:131;:::o;2944:247::-;3003:6;3056:2;3044:9;3035:7;3031:23;3027:32;3024:52;;;3072:1;3069;3062:12;3024:52;3111:9;3098:23;3130:31;3155:5;3130:31;:::i;3196:250::-;3281:1;3291:113;3305:6;3302:1;3299:13;3291:113;;;3381:11;;;3375:18;3362:11;;;3355:39;3327:2;3320:10;3291:113;;;-1:-1:-1;;3438:1:242;3420:16;;3413:27;3196:250::o;3451:271::-;3493:3;3531:5;3525:12;3558:6;3553:3;3546:19;3574:76;3643:6;3636:4;3631:3;3627:14;3620:4;3613:5;3609:16;3574:76;:::i;:::-;3704:2;3683:15;-1:-1:-1;;3679:29:242;3670:39;;;;3711:4;3666:50;;3451:271;-1:-1:-1;;3451:271:242:o;3727:487::-;-1:-1:-1;;;;;3960:32:242;;;3942:51;;4029:32;;4024:2;4009:18;;4002:60;4098:3;4093:2;4078:18;;4071:31;;;-1:-1:-1;;4119:46:242;;4145:19;;4137:6;4119:46;:::i;:::-;4111:54;;4201:6;4196:2;4185:9;4181:18;4174:34;3727:487;;;;;;;:::o;4219:226::-;4278:6;4331:2;4319:9;4310:7;4306:23;4302:32;4299:52;;;4347:1;4344;4337:12;4299:52;-1:-1:-1;4392:23:242;;4219:226;-1:-1:-1;4219:226:242:o;4450:436::-;4528:6;4536;4589:2;4577:9;4568:7;4564:23;4560:32;4557:52;;;4605:1;4602;4595:12;4557:52;4645:9;4632:23;4678:18;4670:6;4667:30;4664:50;;;4710:1;4707;4700:12;4664:50;4733;4775:7;4766:6;4755:9;4751:22;4733:50;:::i;:::-;4723:60;4852:2;4837:18;;;;4824:32;;-1:-1:-1;;;;4450:436:242:o;4891:1197::-;4999:6;5007;5060:2;5048:9;5039:7;5035:23;5031:32;5028:52;;;5076:1;5073;5066:12;5028:52;5116:9;5103:23;5149:18;5141:6;5138:30;5135:50;;;5181:1;5178;5171:12;5135:50;5204;5246:7;5237:6;5226:9;5222:22;5204:50;:::i;:::-;5194:60;;;5307:2;5296:9;5292:18;5279:32;5336:18;5326:8;5323:32;5320:52;;;5368:1;5365;5358:12;5320:52;5391:24;;5449:4;5431:16;;;5427:27;5424:47;;;5467:1;5464;5457:12;5424:47;5493:22;;:::i;:::-;5552:2;5539:16;5564:33;5589:7;5564:33;:::i;:::-;5606:22;;5673:2;5665:11;;5652:25;5686:33;5652:25;5686:33;:::i;:::-;5746:2;5735:14;;5728:31;5805:2;5797:11;;5784:25;5834:18;5821:32;;5818:52;;;5866:1;5863;5856:12;5818:52;5902:45;5939:7;5928:8;5924:2;5920:17;5902:45;:::i;:::-;5897:2;5886:14;;5879:69;-1:-1:-1;6014:2:242;6006:11;;;5993:25;6034:14;;;6027:31;;;;4891:1197;;5890:5;;-1:-1:-1;4891:1197:242;;-1:-1:-1;;4891:1197:242:o;6093:184::-;6163:6;6216:2;6204:9;6195:7;6191:23;6187:32;6184:52;;;6232:1;6229;6222:12;6184:52;-1:-1:-1;6255:16:242;;6093:184;-1:-1:-1;6093:184:242:o;6561:277::-;6628:6;6681:2;6669:9;6660:7;6656:23;6652:32;6649:52;;;6697:1;6694;6687:12;6649:52;6729:9;6723:16;6782:5;6775:13;6768:21;6761:5;6758:32;6748:60;;6804:1;6801;6794:12;6843:273;7028:6;7020;7015:3;7002:33;6984:3;7054:16;;7079:13;;;7054:16;6843:273;-1:-1:-1;6843:273:242:o;7121:535::-;7336:6;7325:9;7318:25;7379:6;7374:2;7363:9;7359:18;7352:34;7422:2;7417;7406:9;7402:18;7395:30;7461:6;7456:2;7445:9;7441:18;7434:34;7519:6;7511;7505:3;7494:9;7490:19;7477:49;7576:1;7546:22;;;7570:3;7542:32;;;7535:43;;;;7639:2;7618:15;;;-1:-1:-1;;7614:29:242;7599:45;7595:55;;7121:535;-1:-1:-1;;;7121:535:242:o;7661:669::-;7741:6;7794:2;7782:9;7773:7;7769:23;7765:32;7762:52;;;7810:1;7807;7800:12;7762:52;7843:9;7837:16;7876:18;7868:6;7865:30;7862:50;;;7908:1;7905;7898:12;7862:50;7931:22;;7984:4;7976:13;;7972:27;-1:-1:-1;7962:55:242;;8013:1;8010;8003:12;7962:55;8046:2;8040:9;8071:53;8087:36;8116:6;8087:36;:::i;8071:53::-;8147:6;8140:5;8133:21;8195:7;8190:2;8181:6;8177:2;8173:15;8169:24;8166:37;8163:57;;;8216:1;8213;8206:12;8163:57;8229:71;8293:6;8288:2;8281:5;8277:14;8272:2;8268;8264:11;8229:71;:::i;8335:380::-;8414:1;8410:12;;;;8457;;;8478:61;;8532:4;8524:6;8520:17;8510:27;;8478:61;8585:2;8577:6;8574:14;8554:18;8551:38;8548:161;;8631:10;8626:3;8622:20;8619:1;8612:31;8666:4;8663:1;8656:15;8694:4;8691:1;8684:15;8548:161;;8335:380;;;:::o;8973:289::-;9104:3;9142:6;9136:13;9158:66;9217:6;9212:3;9205:4;9197:6;9193:17;9158:66;:::i;:::-;9240:16;;;;;8973:289;-1:-1:-1;;8973:289:242:o;9267:291::-;9444:2;9433:9;9426:21;9407:4;9464:45;9505:2;9494:9;9490:18;9482:6;9464:45;:::i;:::-;9456:53;;9545:6;9540:2;9529:9;9525:18;9518:34;9267:291;;;;;:::o;9689:518::-;9791:2;9786:3;9783:11;9780:421;;;9827:5;9824:1;9817:16;9871:4;9868:1;9858:18;9941:2;9929:10;9925:19;9922:1;9918:27;9912:4;9908:38;9977:4;9965:10;9962:20;9959:47;;;-1:-1:-1;10000:4:242;9959:47;10055:2;10050:3;10046:12;10043:1;10039:20;10033:4;10029:31;10019:41;;10110:81;10128:2;10121:5;10118:13;10110:81;;;10187:1;10173:16;;10154:1;10143:13;10110:81;;;10114:3;;9780:421;9689:518;;;:::o;10383:1299::-;10509:3;10503:10;10536:18;10528:6;10525:30;10522:56;;;10558:18;;:::i;:::-;10587:97;10677:6;10637:38;10669:4;10663:11;10637:38;:::i;:::-;10631:4;10587:97;:::i;:::-;10733:4;10764:2;10753:14;;10781:1;10776:649;;;;11469:1;11486:6;11483:89;;;-1:-1:-1;11538:19:242;;;11532:26;11483:89;-1:-1:-1;;10340:1:242;10336:11;;;10332:24;10328:29;10318:40;10364:1;10360:11;;;10315:57;11585:81;;10746:930;;10776:649;9636:1;9629:14;;;9673:4;9660:18;;-1:-1:-1;;10812:20:242;;;10930:222;10944:7;10941:1;10938:14;10930:222;;;11026:19;;;11020:26;11005:42;;11133:4;11118:20;;;;11086:1;11074:14;;;;10960:12;10930:222;;;10934:3;11180:6;11171:7;11168:19;11165:201;;;11241:19;;;11235:26;-1:-1:-1;;11324:1:242;11320:14;;;11336:3;11316:24;11312:37;11308:42;11293:58;11278:74;;11165:201;-1:-1:-1;;;;11412:1:242;11396:14;;;11392:22;11379:36;;-1:-1:-1;10383:1299:242:o;11687:764::-;11924:2;11913:9;11906:21;11887:4;11950:45;11991:2;11980:9;11976:18;11968:6;11950:45;:::i;:::-;12043:9;12035:6;12031:22;12026:2;12015:9;12011:18;12004:50;12114:1;12110;12105:3;12101:11;12097:19;12088:6;12082:13;12078:39;12070:6;12063:55;12196:1;12192;12187:3;12183:11;12179:19;12173:2;12165:6;12161:15;12155:22;12151:48;12146:2;12138:6;12134:15;12127:73;12247:2;12239:6;12235:15;12229:22;12284:4;12279:2;12271:6;12267:15;12260:29;12312:50;12356:4;12348:6;12344:17;12330:12;12312:50;:::i;:::-;12298:64;;12415:4;12407:6;12403:17;12397:24;12390:4;12382:6;12378:17;12371:51;12439:6;12431:14;;;;11687:764;;;;;:::o;12456:251::-;12526:6;12579:2;12567:9;12558:7;12554:23;12550:32;12547:52;;;12595:1;12592;12585:12;12547:52;12627:9;12621:16;12646:31;12671:5;12646:31;:::i;12712:127::-;12773:10;12768:3;12764:20;12761:1;12754:31;12804:4;12801:1;12794:15;12828:4;12825:1;12818:15;12844:128;12911:9;;;12932:11;;;12929:37;;;12946:18;;:::i;12977:375::-;13065:1;13083:5;13097:249;13118:1;13108:8;13105:15;13097:249;;;13168:4;13163:3;13159:14;13153:4;13150:24;13147:50;;;13177:18;;:::i;:::-;13227:1;13217:8;13213:16;13210:49;;;13241:16;;;;13210:49;13324:1;13320:16;;;;;13280:15;;13097:249;;;12977:375;;;;;;:::o;13357:902::-;13406:5;13436:8;13426:80;;-1:-1:-1;13477:1:242;13491:5;;13426:80;13525:4;13515:76;;-1:-1:-1;13562:1:242;13576:5;;13515:76;13607:4;13625:1;13620:59;;;;13693:1;13688:174;;;;13600:262;;13620:59;13650:1;13641:10;;13664:5;;;13688:174;13725:3;13715:8;13712:17;13709:43;;;13732:18;;:::i;:::-;-1:-1:-1;;13788:1:242;13774:16;;13847:5;;13600:262;;13946:2;13936:8;13933:16;13927:3;13921:4;13918:13;13914:36;13908:2;13898:8;13895:16;13890:2;13884:4;13881:12;13877:35;13874:77;13871:203;;;-1:-1:-1;13983:19:242;;;14059:5;;13871:203;14106:42;-1:-1:-1;;14131:8:242;14125:4;14106:42;:::i;:::-;14184:6;14180:1;14176:6;14172:19;14163:7;14160:32;14157:58;;;14195:18;;:::i;:::-;14233:20;;13357:902;-1:-1:-1;;;13357:902:242:o;14264:131::-;14324:5;14353:36;14380:8;14374:4;14353:36;:::i;14400:168::-;14473:9;;;14504;;14521:15;;;14515:22;;14501:37;14491:71;;14542:18;;:::i;14831:217::-;14871:1;14897;14887:132;;14941:10;14936:3;14932:20;14929:1;14922:31;14976:4;14973:1;14966:15;15004:4;15001:1;14994:15;14887:132;-1:-1:-1;15033:9:242;;14831:217::o;15053:179::-;15131:13;;15184:22;15173:34;;15163:45;;15153:73;;15222:1;15219;15212:12;15153:73;15053:179;;;:::o;15237:571::-;15340:6;15348;15356;15364;15372;15425:3;15413:9;15404:7;15400:23;15396:33;15393:53;;;15442:1;15439;15432:12;15393:53;15465:39;15494:9;15465:39;:::i;:::-;15544:2;15529:18;;15523:25;15610:2;15595:18;;15589:25;15704:2;15689:18;;15683:25;15455:49;;-1:-1:-1;15523:25:242;;-1:-1:-1;15589:25:242;-1:-1:-1;15683:25:242;-1:-1:-1;15753:49:242;15797:3;15782:19;;15753:49;:::i;:::-;15743:59;;15237:571;;;;;;;;:::o;15813:136::-;15848:3;-1:-1:-1;;;15869:22:242;;15866:48;;15894:18;;:::i;:::-;-1:-1:-1;15934:1:242;15930:13;;15813:136::o;15954:273::-;16022:6;16075:2;16063:9;16054:7;16050:23;16046:32;16043:52;;;16091:1;16088;16081:12;16043:52;16123:9;16117:16;16173:4;16166:5;16162:16;16155:5;16152:27;16142:55;;16193:1;16190;16183:12;16232:200;16298:9;;;16271:4;16326:9;;16354:10;;16366:12;;;16350:29;16389:12;;;16381:21;;16347:56;16344:82;;;16406:18;;:::i;:::-;16344:82;16232:200;;;;:::o", "linkReferences": {}, "immutableReferences": {"84582": [{"start": 238, "length": 32}, {"start": 4517, "length": 32}], "84613": [{"start": 339, "length": 32}, {"start": 591, "length": 32}, {"start": 639, "length": 32}, {"start": 1398, "length": 32}, {"start": 1446, "length": 32}, {"start": 1815, "length": 32}, {"start": 1863, "length": 32}, {"start": 2226, "length": 32}, {"start": 2274, "length": 32}]}}, "methodIdentifiers": {"PRICE_DELTA_EXP()": "a49f06ef", "STALENESS_PERIOD()": "24c9477a", "configs(string)": "5ef7fbad", "deltaPerSymbol(string)": "ffa890c5", "getPrice(address)": "41976e09", "getUnderlyingPrice(address)": "fc57d4df", "maxPriceDelta()": "66ed5444", "roles()": "392f5f64", "setConfig(string,(address,address,string,uint256))": "ec285406", "setMaxPriceDelta(uint256)": "7a2dc784", "setStaleness(string,uint256)": "c86e8685", "setSymbolMaxPriceDelta(uint256,string)": "1a48b358", "stalenessPerSymbol(string)": "2ab77c57"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"string[]\",\"name\":\"symbols_\",\"type\":\"string[]\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"api3Feed\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"eOracleFeed\",\"type\":\"address\"},{\"internalType\":\"string\",\"name\":\"toSymbol\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"underlyingDecimals\",\"type\":\"uint256\"}],\"internalType\":\"struct MixedPriceOracleV4.PriceConfig[]\",\"name\":\"configs_\",\"type\":\"tuple[]\"},{\"internalType\":\"address\",\"name\":\"roles_\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"stalenessPeriod_\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"MixedPriceOracle_ApiV3StalePrice\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"MixedPriceOracle_DeltaTooHigh\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"MixedPriceOracle_InvalidConfig\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"MixedPriceOracle_InvalidConfigDecimals\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"MixedPriceOracle_InvalidPrice\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"MixedPriceOracle_InvalidRound\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"MixedPriceOracle_MissingFeed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"MixedPriceOracle_Unauthorized\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"MixedPriceOracle_eOracleStalePrice\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"api3Feed\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"eOracleFeed\",\"type\":\"address\"},{\"internalType\":\"string\",\"name\":\"toSymbol\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"underlyingDecimals\",\"type\":\"uint256\"}],\"indexed\":false,\"internalType\":\"struct MixedPriceOracleV4.PriceConfig\",\"name\":\"config\",\"type\":\"tuple\"}],\"name\":\"ConfigSet\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"oldVal\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"newVal\",\"type\":\"uint256\"}],\"name\":\"PriceDeltaUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"oldVal\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"newVal\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"}],\"name\":\"PriceSymbolDeltaUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"val\",\"type\":\"uint256\"}],\"name\":\"StalenessUpdated\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"PRICE_DELTA_EXP\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"STALENESS_PERIOD\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"configs\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"api3Feed\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"eOracleFeed\",\"type\":\"address\"},{\"internalType\":\"string\",\"name\":\"toSymbol\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"underlyingDecimals\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"deltaPerSymbol\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"}],\"name\":\"getPrice\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"}],\"name\":\"getUnderlyingPrice\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"maxPriceDelta\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"roles\",\"outputs\":[{\"internalType\":\"contract IRoles\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"api3Feed\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"eOracleFeed\",\"type\":\"address\"},{\"internalType\":\"string\",\"name\":\"toSymbol\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"underlyingDecimals\",\"type\":\"uint256\"}],\"internalType\":\"struct MixedPriceOracleV4.PriceConfig\",\"name\":\"config\",\"type\":\"tuple\"}],\"name\":\"setConfig\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_delta\",\"type\":\"uint256\"}],\"name\":\"setMaxPriceDelta\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"val\",\"type\":\"uint256\"}],\"name\":\"setStaleness\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_delta\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"_symbol\",\"type\":\"string\"}],\"name\":\"setSymbolMaxPriceDelta\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"stalenessPerSymbol\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{\"getPrice(address)\":{\"params\":{\"mToken\":\"The mToken to get the price of\"},\"returns\":{\"_0\":\"The underlying asset price mantissa (scaled by 1e18).  Zero means the price is unavailable.\"}},\"getUnderlyingPrice(address)\":{\"params\":{\"mToken\":\"The mToken to get the underlying price of\"},\"returns\":{\"_0\":\"The underlying asset price mantissa (scaled by 1e18).  Zero means the price is unavailable.\"}}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"getPrice(address)\":{\"notice\":\"Get the price of a mToken asset\"},\"getUnderlyingPrice(address)\":{\"notice\":\"Get the underlying price of a mToken asset\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/oracles/MixedPriceOracleV4.sol\":\"MixedPriceOracleV4\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"src/interfaces/IDefaultAdapter.sol\":{\"keccak256\":\"0xbf7e882eeb81776c7be55110bb171c65d166bafeb71d828c085b139bed5735c8\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://7e139fb3ddd0623189493679e73fd42c4e505531502d55e1e699789fa3c1451a\",\"dweb:/ipfs/Qma3XsUVPffiGXZ7epTqMyNJKuh87xrFhqCTwQXznEccU6\"]},\"src/interfaces/IOracleOperator.sol\":{\"keccak256\":\"0xcc2848c75dd5e1531c2b958746cdd76a3b2b329be98fdca4311219a12a6b409e\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://337bdabf03ac279d95ba96aca60105932c7af5c15fdb4b21ab1c369509310fbc\",\"dweb:/ipfs/QmNYrNYNUwWWSrPwHdarhxt1ShdH1dy8B94M49TZ7XvmK1\"]},\"src/interfaces/IRoles.sol\":{\"keccak256\":\"0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c\",\"dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj\"]},\"src/interfaces/ImToken.sol\":{\"keccak256\":\"0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36\",\"dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL\"]},\"src/oracles/MixedPriceOracleV4.sol\":{\"keccak256\":\"0x5bc6345422528b3c76c4c7b4485bbc16dba36e91995a730f4a8c29e28a3b7ad2\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://e3c5c9006e2d3b6a5b8288a771e23831620e757e463fd54aa0827adc72066a94\",\"dweb:/ipfs/QmPETu2S7ZGWDuvMrF7jWAg2MQKUhDwDSw5KMPh4Jc5DGN\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "string[]", "name": "symbols_", "type": "string[]"}, {"internalType": "struct MixedPriceOracleV4.PriceConfig[]", "name": "configs_", "type": "tuple[]", "components": [{"internalType": "address", "name": "api3Feed", "type": "address"}, {"internalType": "address", "name": "eOracleFeed", "type": "address"}, {"internalType": "string", "name": "toSymbol", "type": "string"}, {"internalType": "uint256", "name": "underlyingDecimals", "type": "uint256"}]}, {"internalType": "address", "name": "roles_", "type": "address"}, {"internalType": "uint256", "name": "stalenessPeriod_", "type": "uint256"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "type": "error", "name": "MixedPriceOracle_ApiV3StalePrice"}, {"inputs": [], "type": "error", "name": "MixedPriceOracle_DeltaTooHigh"}, {"inputs": [], "type": "error", "name": "MixedPriceOracle_InvalidConfig"}, {"inputs": [], "type": "error", "name": "MixedPriceOracle_InvalidConfigDecimals"}, {"inputs": [], "type": "error", "name": "MixedPriceOracle_InvalidPrice"}, {"inputs": [], "type": "error", "name": "MixedPriceOracle_InvalidRound"}, {"inputs": [], "type": "error", "name": "MixedPriceOracle_MissingFeed"}, {"inputs": [], "type": "error", "name": "MixedPriceOracle_Unauthorized"}, {"inputs": [], "type": "error", "name": "MixedPriceOracle_eOracleStalePrice"}, {"inputs": [{"internalType": "string", "name": "symbol", "type": "string", "indexed": false}, {"internalType": "struct MixedPriceOracleV4.PriceConfig", "name": "config", "type": "tuple", "components": [{"internalType": "address", "name": "api3Feed", "type": "address"}, {"internalType": "address", "name": "eOracleFeed", "type": "address"}, {"internalType": "string", "name": "toSymbol", "type": "string"}, {"internalType": "uint256", "name": "underlyingDecimals", "type": "uint256"}], "indexed": false}], "type": "event", "name": "ConfigSet", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "oldVal", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "newVal", "type": "uint256", "indexed": false}], "type": "event", "name": "PriceDeltaUpdated", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "oldVal", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "newVal", "type": "uint256", "indexed": false}, {"internalType": "string", "name": "symbol", "type": "string", "indexed": false}], "type": "event", "name": "PriceSymbolDeltaUpdated", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "symbol", "type": "string", "indexed": false}, {"internalType": "uint256", "name": "val", "type": "uint256", "indexed": false}], "type": "event", "name": "StalenessUpdated", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "PRICE_DELTA_EXP", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "STALENESS_PERIOD", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function", "name": "configs", "outputs": [{"internalType": "address", "name": "api3Feed", "type": "address"}, {"internalType": "address", "name": "eOracleFeed", "type": "address"}, {"internalType": "string", "name": "toSymbol", "type": "string"}, {"internalType": "uint256", "name": "underlyingDecimals", "type": "uint256"}]}, {"inputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function", "name": "deltaPerSymbol", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getPrice", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getUnderlyingPrice", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "roles", "outputs": [{"internalType": "contract IRoles", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "struct MixedPriceOracleV4.PriceConfig", "name": "config", "type": "tuple", "components": [{"internalType": "address", "name": "api3Feed", "type": "address"}, {"internalType": "address", "name": "eOracleFeed", "type": "address"}, {"internalType": "string", "name": "toSymbol", "type": "string"}, {"internalType": "uint256", "name": "underlyingDecimals", "type": "uint256"}]}], "stateMutability": "nonpayable", "type": "function", "name": "setConfig"}, {"inputs": [{"internalType": "uint256", "name": "_delta", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "setMaxPriceDelta"}, {"inputs": [{"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint256", "name": "val", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "setStaleness"}, {"inputs": [{"internalType": "uint256", "name": "_delta", "type": "uint256"}, {"internalType": "string", "name": "_symbol", "type": "string"}], "stateMutability": "nonpayable", "type": "function", "name": "setSymbolMaxPriceDelta"}, {"inputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function", "name": "stalenessPerSymbol", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}], "devdoc": {"kind": "dev", "methods": {"getPrice(address)": {"params": {"mToken": "The mToken to get the price of"}, "returns": {"_0": "The underlying asset price mantissa (scaled by 1e18).  Zero means the price is unavailable."}}, "getUnderlyingPrice(address)": {"params": {"mToken": "The mToken to get the underlying price of"}, "returns": {"_0": "The underlying asset price mantissa (scaled by 1e18).  Zero means the price is unavailable."}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"getPrice(address)": {"notice": "Get the price of a mToken asset"}, "getUnderlyingPrice(address)": {"notice": "Get the underlying price of a mToken asset"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/oracles/MixedPriceOracleV4.sol": "MixedPriceOracleV4"}, "evmVersion": "london", "libraries": {}}, "sources": {"src/interfaces/IDefaultAdapter.sol": {"keccak256": "0xbf7e882eeb81776c7be55110bb171c65d166bafeb71d828c085b139bed5735c8", "urls": ["bzz-raw://7e139fb3ddd0623189493679e73fd42c4e505531502d55e1e699789fa3c1451a", "dweb:/ipfs/Qma3XsUVPffiGXZ7epTqMyNJKuh87xrFhqCTwQXznEccU6"], "license": "BSL-1.1"}, "src/interfaces/IOracleOperator.sol": {"keccak256": "0xcc2848c75dd5e1531c2b958746cdd76a3b2b329be98fdca4311219a12a6b409e", "urls": ["bzz-raw://337bdabf03ac279d95ba96aca60105932c7af5c15fdb4b21ab1c369509310fbc", "dweb:/ipfs/QmNYrNYNUwWWSrPwHdarhxt1ShdH1dy8B94M49TZ7XvmK1"], "license": "BSL-1.1"}, "src/interfaces/IRoles.sol": {"keccak256": "0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365", "urls": ["bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c", "dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj"], "license": "BSL-1.1"}, "src/interfaces/ImToken.sol": {"keccak256": "0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f", "urls": ["bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36", "dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL"], "license": "BSL-1.1"}, "src/oracles/MixedPriceOracleV4.sol": {"keccak256": "0x5bc6345422528b3c76c4c7b4485bbc16dba36e91995a730f4a8c29e28a3b7ad2", "urls": ["bzz-raw://e3c5c9006e2d3b6a5b8288a771e23831620e757e463fd54aa0827adc72066a94", "dweb:/ipfs/QmPETu2S7ZGWDuvMrF7jWAg2MQKUhDwDSw5KMPh4Jc5DGN"], "license": "BSL-1.1"}}, "version": 1}, "id": 185}