{"abi": [{"type": "constructor", "inputs": [{"name": "_admin", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "admin", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "decimals", "inputs": [], "outputs": [{"name": "", "type": "uint8", "internalType": "uint8"}], "stateMutability": "pure"}, {"type": "function", "name": "getPrice", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getUnderlyingPrice", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "latestAnswer", "inputs": [], "outputs": [{"name": "", "type": "int256", "internalType": "int256"}], "stateMutability": "view"}, {"type": "function", "name": "latestRoundData", "inputs": [], "outputs": [{"name": "roundId", "type": "uint80", "internalType": "uint80"}, {"name": "answer", "type": "int256", "internalType": "int256"}, {"name": "startedAt", "type": "uint256", "internalType": "uint256"}, {"name": "updatedAt", "type": "uint256", "internalType": "uint256"}, {"name": "answeredInRound", "type": "uint80", "internalType": "uint80"}], "stateMutability": "view"}, {"type": "function", "name": "latestTimestamp", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "price", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "registeredDecimals", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint8", "internalType": "uint8"}], "stateMutability": "view"}, {"type": "function", "name": "setDecimals", "inputs": [{"name": "token", "type": "address", "internalType": "address"}, {"name": "dec", "type": "uint8", "internalType": "uint8"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setPrice", "inputs": [{"name": "_price", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setUnderlyingPrice", "inputs": [{"name": "_price", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "underlyingPrice", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "error", "name": "OracleMock_NotAuthorized", "inputs": []}], "bytecode": {"object": "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", "sourceMap": "62:1591:207:-:0;;;273:59;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;311:5;:14;;-1:-1:-1;;;;;;311:14:207;-1:-1:-1;;;;;311:14:207;;;;;;;;;;62:1591;;14:290:242;84:6;137:2;125:9;116:7;112:23;108:32;105:52;;;153:1;150;143:12;105:52;179:16;;-1:-1:-1;;;;;224:31:242;;214:42;;204:70;;270:1;267;260:12;204:70;293:5;14:290;-1:-1:-1;;;14:290:242:o;:::-;62:1591:207;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x608060405234801561001057600080fd5b50600436106100cf5760003560e01c806391b7f5ed1161008c578063f2259f9611610066578063f2259f961461017c578063f851a4401461018f578063fc57d4df146101ba578063feaf968c146101cf57600080fd5b806391b7f5ed14610157578063a035b1fe1461016a578063efa32d491461017357600080fd5b8063313ce567146100d457806341976e09146100ee578063420143481461011157806350d25bcd1461013457806357aefad21461013c5780638205bf6a14610151575b600080fd5b60085b60405160ff90911681526020015b60405180910390f35b6101036100fc3660046102d6565b5060005490565b6040519081526020016100e5565b6100d761011f3660046102d6565b60036020526000908152604090205460ff1681565b600054610103565b61014f61014a3660046102f8565b610202565b005b42610103565b61014f6101653660046102f8565b610232565b61010360005481565b61010360015481565b61014f61018a366004610311565b610262565b6002546101a2906001600160a01b031681565b6040516001600160a01b0390911681526020016100e5565b6101036101c83660046102d6565b5060015490565b6000546040805160018082526020820193909352429181018290526060810191909152608081019190915260a0016100e5565b6002546001600160a01b0316331461022d576040516356936a7b60e11b815260040160405180910390fd5b600155565b6002546001600160a01b0316331461025d576040516356936a7b60e11b815260040160405180910390fd5b600055565b6002546001600160a01b0316331461028d576040516356936a7b60e11b815260040160405180910390fd5b6001600160a01b03919091166000908152600360205260409020805460ff191660ff909216919091179055565b80356001600160a01b03811681146102d157600080fd5b919050565b6000602082840312156102e857600080fd5b6102f1826102ba565b9392505050565b60006020828403121561030a57600080fd5b5035919050565b6000806040838503121561032457600080fd5b61032d836102ba565b9150602083013560ff8116811461034357600080fd5b80915050925092905056fea2646970667358221220e8b3022d91b0d7c7625c8667cae81a4a15a7d9e466658439e8f244c5fa86c0bb64736f6c634300081c0033", "sourceMap": "62:1591:207:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;514:75;581:1;514:75;;;186:4:242;174:17;;;156:36;;144:2;129:18;514:75:207;;;;;;;;1449:88;;;;;;:::i;:::-;-1:-1:-1;1499:7:207;1525:5;;1449:88;;;;718:25:242;;;706:2;691:18;1449:88:207;572:177:242;176:51:207;;;;;;:::i;:::-;;;;;;;;;;;;;;;;935:92;982:6;1014:5;935:92;;1283:160;;;;;;:::i;:::-;;:::i;:::-;;1033:98;1109:15;1033:98;;1137:140;;;;;;:::i;:::-;;:::i;88:20::-;;;;;;114:30;;;;;;338:170;;;;;;:::i;:::-;;:::i;150:20::-;;;;;-1:-1:-1;;;;;150:20:207;;;;;;-1:-1:-1;;;;;1631:32:242;;;1613:51;;1601:2;1586:18;150:20:207;1467:203:242;1543:108:207;;;;;;:::i;:::-;-1:-1:-1;1629:15:207;;;1543:108;595:334;669:14;813:5;595:334;;;786:1;1928:54:242;;;2013:2;1998:18;;1991:34;;;;841:15:207;2041:18:242;;;2034:34;;;2099:2;2084:18;;2077:34;;;;2142:3;2127:19;;2120:64;;;;1915:3;1900:19;595:334:207;1675:515:242;1283:160:207;1368:5;;-1:-1:-1;;;;;1368:5:207;1354:10;:19;1346:56;;;;-1:-1:-1;;;1346:56:207;;;;;;;;;;;;1412:15;:24;1283:160::o;1137:140::-;1212:5;;-1:-1:-1;;;;;1212:5:207;1198:10;:19;1190:56;;;;-1:-1:-1;;;1190:56:207;;;;;;;;;;;;1256:5;:14;1137:140::o;338:170::-;426:5;;-1:-1:-1;;;;;426:5:207;412:10;:19;404:56;;;;-1:-1:-1;;;404:56:207;;;;;;;;;;;;-1:-1:-1;;;;;470:25:207;;;;;;;;:18;:25;;;;;:31;;-1:-1:-1;;470:31:207;;;;;;;;;;;338:170::o;203:173:242:-;271:20;;-1:-1:-1;;;;;320:31:242;;310:42;;300:70;;366:1;363;356:12;300:70;203:173;;;:::o;381:186::-;440:6;493:2;481:9;472:7;468:23;464:32;461:52;;;509:1;506;499:12;461:52;532:29;551:9;532:29;:::i;:::-;522:39;381:186;-1:-1:-1;;;381:186:242:o;934:180::-;993:6;1046:2;1034:9;1025:7;1021:23;1017:32;1014:52;;;1062:1;1059;1052:12;1014:52;-1:-1:-1;1085:23:242;;934:180;-1:-1:-1;934:180:242:o;1119:343::-;1185:6;1193;1246:2;1234:9;1225:7;1221:23;1217:32;1214:52;;;1262:1;1259;1252:12;1214:52;1285:29;1304:9;1285:29;:::i;:::-;1275:39;;1364:2;1353:9;1349:18;1336:32;1408:4;1401:5;1397:16;1390:5;1387:27;1377:55;;1428:1;1425;1418:12;1377:55;1451:5;1441:15;;;1119:343;;;;;:::o", "linkReferences": {}}, "methodIdentifiers": {"admin()": "f851a440", "decimals()": "313ce567", "getPrice(address)": "41976e09", "getUnderlyingPrice(address)": "fc57d4df", "latestAnswer()": "50d25bcd", "latestRoundData()": "feaf968c", "latestTimestamp()": "8205bf6a", "price()": "a035b1fe", "registeredDecimals(address)": "42014348", "setDecimals(address,uint8)": "f2259f96", "setPrice(uint256)": "91b7f5ed", "setUnderlyingPrice(uint256)": "57aefad2", "underlyingPrice()": "efa32d49"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_admin\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"OracleMock_NotAuthorized\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"admin\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"decimals\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"\",\"type\":\"uint8\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"getPrice\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"getUnderlyingPrice\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"latestAnswer\",\"outputs\":[{\"internalType\":\"int256\",\"name\":\"\",\"type\":\"int256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"latestRoundData\",\"outputs\":[{\"internalType\":\"uint80\",\"name\":\"roundId\",\"type\":\"uint80\"},{\"internalType\":\"int256\",\"name\":\"answer\",\"type\":\"int256\"},{\"internalType\":\"uint256\",\"name\":\"startedAt\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"updatedAt\",\"type\":\"uint256\"},{\"internalType\":\"uint80\",\"name\":\"answeredInRound\",\"type\":\"uint80\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"latestTimestamp\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"price\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"registeredDecimals\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"uint8\",\"name\":\"dec\",\"type\":\"uint8\"}],\"name\":\"setDecimals\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_price\",\"type\":\"uint256\"}],\"name\":\"setPrice\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_price\",\"type\":\"uint256\"}],\"name\":\"setUnderlyingPrice\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"underlyingPrice\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"test/mocks/OracleMock.sol\":\"OracleMock\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"test/mocks/OracleMock.sol\":{\"keccak256\":\"0xb749ba9d217b9aedc9010690022b31672eeacf0927f43efe9769ae886f4ce855\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://5167ca5dcd31bc0ae6dcc22e43276e8f746b018d7d9c2edc332d1160024f6328\",\"dweb:/ipfs/QmVdgBmaBntj9TMV4z65bga1uuN2YkfUbLNN9Hsmvu4x6f\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "_admin", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "type": "error", "name": "OracleMock_NotAuthorized"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "admin", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "pure", "type": "function", "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}]}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getPrice", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getUnderlyingPrice", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "latestAnswer", "outputs": [{"internalType": "int256", "name": "", "type": "int256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "latestRoundData", "outputs": [{"internalType": "uint80", "name": "roundId", "type": "uint80"}, {"internalType": "int256", "name": "answer", "type": "int256"}, {"internalType": "uint256", "name": "startedAt", "type": "uint256"}, {"internalType": "uint256", "name": "updatedAt", "type": "uint256"}, {"internalType": "uint80", "name": "answeredInRound", "type": "uint80"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "latestTimestamp", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "price", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "registeredDecimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}]}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint8", "name": "dec", "type": "uint8"}], "stateMutability": "nonpayable", "type": "function", "name": "setDecimals"}, {"inputs": [{"internalType": "uint256", "name": "_price", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "setPrice"}, {"inputs": [{"internalType": "uint256", "name": "_price", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "setUnderlyingPrice"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "underlyingPrice", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"test/mocks/OracleMock.sol": "OracleMock"}, "evmVersion": "london", "libraries": {}}, "sources": {"test/mocks/OracleMock.sol": {"keccak256": "0xb749ba9d217b9aedc9010690022b31672eeacf0927f43efe9769ae886f4ce855", "urls": ["bzz-raw://5167ca5dcd31bc0ae6dcc22e43276e8f746b018d7d9c2edc332d1160024f6328", "dweb:/ipfs/QmVdgBmaBntj9TMV4z65bga1uuN2YkfUbLNN9Hsmvu4x6f"], "license": "BSL-1.1"}}, "version": 1}, "id": 207}