{"abi": [{"type": "function", "name": "depositV3", "inputs": [{"name": "depositor", "type": "address", "internalType": "address"}, {"name": "recipient", "type": "address", "internalType": "address"}, {"name": "inputToken", "type": "address", "internalType": "address"}, {"name": "outputToken", "type": "address", "internalType": "address"}, {"name": "inputAmount", "type": "uint256", "internalType": "uint256"}, {"name": "outputAmount", "type": "uint256", "internalType": "uint256"}, {"name": "destinationChainId", "type": "uint256", "internalType": "uint256"}, {"name": "exclusiveRelayer", "type": "address", "internalType": "address"}, {"name": "quoteTimestamp", "type": "uint32", "internalType": "uint32"}, {"name": "fillDeadline", "type": "uint32", "internalType": "uint32"}, {"name": "exclusivityDeadline", "type": "uint32", "internalType": "uint32"}, {"name": "message", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "depositV3Now", "inputs": [{"name": "depositor", "type": "address", "internalType": "address"}, {"name": "recipient", "type": "address", "internalType": "address"}, {"name": "inputToken", "type": "address", "internalType": "address"}, {"name": "outputToken", "type": "address", "internalType": "address"}, {"name": "inputAmount", "type": "uint256", "internalType": "uint256"}, {"name": "outputAmount", "type": "uint256", "internalType": "uint256"}, {"name": "destinationChainId", "type": "uint256", "internalType": "uint256"}, {"name": "exclusiveRelayer", "type": "address", "internalType": "address"}, {"name": "fillDeadlineOffset", "type": "uint32", "internalType": "uint32"}, {"name": "exclusivityDeadline", "type": "uint32", "internalType": "uint32"}, {"name": "message", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "executeV3SlowRelayLeaf", "inputs": [{"name": "slow<PERSON>ill<PERSON><PERSON>f", "type": "tuple", "internalType": "struct IAcrossSpokePoolV3.V3SlowFill", "components": [{"name": "relayData", "type": "tuple", "internalType": "struct IAcrossSpokePoolV3.V3RelayData", "components": [{"name": "depositor", "type": "address", "internalType": "address"}, {"name": "recipient", "type": "address", "internalType": "address"}, {"name": "exclusiveRelayer", "type": "address", "internalType": "address"}, {"name": "inputToken", "type": "address", "internalType": "address"}, {"name": "outputToken", "type": "address", "internalType": "address"}, {"name": "inputAmount", "type": "uint256", "internalType": "uint256"}, {"name": "outputAmount", "type": "uint256", "internalType": "uint256"}, {"name": "originChainId", "type": "uint256", "internalType": "uint256"}, {"name": "depositId", "type": "uint32", "internalType": "uint32"}, {"name": "fillDeadline", "type": "uint32", "internalType": "uint32"}, {"name": "exclusivityDeadline", "type": "uint32", "internalType": "uint32"}, {"name": "message", "type": "bytes", "internalType": "bytes"}]}, {"name": "chainId", "type": "uint256", "internalType": "uint256"}, {"name": "updatedOutputAmount", "type": "uint256", "internalType": "uint256"}]}, {"name": "rootBundleId", "type": "uint32", "internalType": "uint32"}, {"name": "proof", "type": "bytes32[]", "internalType": "bytes32[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "fillV3Relay", "inputs": [{"name": "relayData", "type": "tuple", "internalType": "struct IAcrossSpokePoolV3.V3RelayData", "components": [{"name": "depositor", "type": "address", "internalType": "address"}, {"name": "recipient", "type": "address", "internalType": "address"}, {"name": "exclusiveRelayer", "type": "address", "internalType": "address"}, {"name": "inputToken", "type": "address", "internalType": "address"}, {"name": "outputToken", "type": "address", "internalType": "address"}, {"name": "inputAmount", "type": "uint256", "internalType": "uint256"}, {"name": "outputAmount", "type": "uint256", "internalType": "uint256"}, {"name": "originChainId", "type": "uint256", "internalType": "uint256"}, {"name": "depositId", "type": "uint32", "internalType": "uint32"}, {"name": "fillDeadline", "type": "uint32", "internalType": "uint32"}, {"name": "exclusivityDeadline", "type": "uint32", "internalType": "uint32"}, {"name": "message", "type": "bytes", "internalType": "bytes"}]}, {"name": "repaymentChainId", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "fillV3RelayWithUpdatedDeposit", "inputs": [{"name": "relayData", "type": "tuple", "internalType": "struct IAcrossSpokePoolV3.V3RelayData", "components": [{"name": "depositor", "type": "address", "internalType": "address"}, {"name": "recipient", "type": "address", "internalType": "address"}, {"name": "exclusiveRelayer", "type": "address", "internalType": "address"}, {"name": "inputToken", "type": "address", "internalType": "address"}, {"name": "outputToken", "type": "address", "internalType": "address"}, {"name": "inputAmount", "type": "uint256", "internalType": "uint256"}, {"name": "outputAmount", "type": "uint256", "internalType": "uint256"}, {"name": "originChainId", "type": "uint256", "internalType": "uint256"}, {"name": "depositId", "type": "uint32", "internalType": "uint32"}, {"name": "fillDeadline", "type": "uint32", "internalType": "uint32"}, {"name": "exclusivityDeadline", "type": "uint32", "internalType": "uint32"}, {"name": "message", "type": "bytes", "internalType": "bytes"}]}, {"name": "repaymentChainId", "type": "uint256", "internalType": "uint256"}, {"name": "updatedOutputAmount", "type": "uint256", "internalType": "uint256"}, {"name": "updatedRecipient", "type": "address", "internalType": "address"}, {"name": "updatedMessage", "type": "bytes", "internalType": "bytes"}, {"name": "depositorSignature", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "requestV3SlowFill", "inputs": [{"name": "relayData", "type": "tuple", "internalType": "struct IAcrossSpokePoolV3.V3RelayData", "components": [{"name": "depositor", "type": "address", "internalType": "address"}, {"name": "recipient", "type": "address", "internalType": "address"}, {"name": "exclusiveRelayer", "type": "address", "internalType": "address"}, {"name": "inputToken", "type": "address", "internalType": "address"}, {"name": "outputToken", "type": "address", "internalType": "address"}, {"name": "inputAmount", "type": "uint256", "internalType": "uint256"}, {"name": "outputAmount", "type": "uint256", "internalType": "uint256"}, {"name": "originChainId", "type": "uint256", "internalType": "uint256"}, {"name": "depositId", "type": "uint32", "internalType": "uint32"}, {"name": "fillDeadline", "type": "uint32", "internalType": "uint32"}, {"name": "exclusivityDeadline", "type": "uint32", "internalType": "uint32"}, {"name": "message", "type": "bytes", "internalType": "bytes"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "speedUpV3Deposit", "inputs": [{"name": "depositor", "type": "address", "internalType": "address"}, {"name": "depositId", "type": "uint32", "internalType": "uint32"}, {"name": "updatedOutputAmount", "type": "uint256", "internalType": "uint256"}, {"name": "updatedRecipient", "type": "address", "internalType": "address"}, {"name": "updatedMessage", "type": "bytes", "internalType": "bytes"}, {"name": "depositorSignature", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "FilledV3Relay", "inputs": [{"name": "inputToken", "type": "address", "indexed": false, "internalType": "address"}, {"name": "outputToken", "type": "address", "indexed": false, "internalType": "address"}, {"name": "inputAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "outputAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "repaymentChainId", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "originChainId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "depositId", "type": "uint32", "indexed": true, "internalType": "uint32"}, {"name": "fillDeadline", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "exclusivityDeadline", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "exclusiveRelayer", "type": "address", "indexed": false, "internalType": "address"}, {"name": "relayer", "type": "address", "indexed": true, "internalType": "address"}, {"name": "depositor", "type": "address", "indexed": false, "internalType": "address"}, {"name": "recipient", "type": "address", "indexed": false, "internalType": "address"}, {"name": "message", "type": "bytes", "indexed": false, "internalType": "bytes"}, {"name": "relayExecutionInfo", "type": "tuple", "indexed": false, "internalType": "struct IAcrossSpokePoolV3.V3RelayExecutionEventInfo", "components": [{"name": "updatedRecipient", "type": "address", "internalType": "address"}, {"name": "updatedMessage", "type": "bytes", "internalType": "bytes"}, {"name": "updatedOutputAmount", "type": "uint256", "internalType": "uint256"}, {"name": "fillType", "type": "uint8", "internalType": "enum IAcrossSpokePoolV3.FillType"}]}], "anonymous": false}, {"type": "event", "name": "RequestedSpeedUpV3Deposit", "inputs": [{"name": "updatedOutputAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "depositId", "type": "uint32", "indexed": true, "internalType": "uint32"}, {"name": "depositor", "type": "address", "indexed": true, "internalType": "address"}, {"name": "updatedRecipient", "type": "address", "indexed": false, "internalType": "address"}, {"name": "updatedMessage", "type": "bytes", "indexed": false, "internalType": "bytes"}, {"name": "depositorSignature", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "RequestedV3SlowFill", "inputs": [{"name": "inputToken", "type": "address", "indexed": false, "internalType": "address"}, {"name": "outputToken", "type": "address", "indexed": false, "internalType": "address"}, {"name": "inputAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "outputAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "originChainId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "depositId", "type": "uint32", "indexed": true, "internalType": "uint32"}, {"name": "fillDeadline", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "exclusivityDeadline", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "exclusiveRelayer", "type": "address", "indexed": false, "internalType": "address"}, {"name": "depositor", "type": "address", "indexed": false, "internalType": "address"}, {"name": "recipient", "type": "address", "indexed": false, "internalType": "address"}, {"name": "message", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "V3FundsDeposited", "inputs": [{"name": "inputToken", "type": "address", "indexed": false, "internalType": "address"}, {"name": "outputToken", "type": "address", "indexed": false, "internalType": "address"}, {"name": "inputAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "outputAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "destinationChainId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "depositId", "type": "uint32", "indexed": true, "internalType": "uint32"}, {"name": "quoteTimestamp", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "fillDeadline", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "exclusivityDeadline", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "depositor", "type": "address", "indexed": true, "internalType": "address"}, {"name": "recipient", "type": "address", "indexed": false, "internalType": "address"}, {"name": "exclusiveRelayer", "type": "address", "indexed": false, "internalType": "address"}, {"name": "message", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "error", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": []}, {"type": "error", "name": "DisabledRoute", "inputs": []}, {"type": "error", "name": "ExpiredFillDeadline", "inputs": []}, {"type": "error", "name": "InvalidChainId", "inputs": []}, {"type": "error", "name": "InvalidExclusiveRelayer", "inputs": []}, {"type": "error", "name": "InvalidFillDeadline", "inputs": []}, {"type": "error", "name": "InvalidMerkleLeaf", "inputs": []}, {"type": "error", "name": "InvalidMerkleProof", "inputs": []}, {"type": "error", "name": "InvalidPayoutAdjustmentPct", "inputs": []}, {"type": "error", "name": "InvalidQuoteTimestamp", "inputs": []}, {"type": "error", "name": "InvalidSlowFillRequest", "inputs": []}, {"type": "error", "name": "LowLevelCallFailed", "inputs": [{"name": "data", "type": "bytes", "internalType": "bytes"}]}, {"type": "error", "name": "MsgValueDoesNotMatchInputAmount", "inputs": []}, {"type": "error", "name": "NoSlowFillsInExclusivityWindow", "inputs": []}, {"type": "error", "name": "NotExclusiveRelayer", "inputs": []}, {"type": "error", "name": "RelayFilled", "inputs": []}, {"type": "error", "name": "WrongERC7683OrderId", "inputs": []}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"depositV3(address,address,address,address,uint256,uint256,uint256,address,uint32,uint32,uint32,bytes)": "7b939232", "depositV3Now(address,address,address,address,uint256,uint256,uint256,address,uint32,uint32,bytes)": "7aef642c", "executeV3SlowRelayLeaf(((address,address,address,address,address,uint256,uint256,uint256,uint32,uint32,uint32,bytes),uint256,uint256),uint32,bytes32[])": "bbca7db0", "fillV3Relay((address,address,address,address,address,uint256,uint256,uint256,uint32,uint32,uint32,bytes),uint256)": "2e378115", "fillV3RelayWithUpdatedDeposit((address,address,address,address,address,uint256,uint256,uint256,uint32,uint32,uint32,bytes),uint256,uint256,address,bytes,bytes)": "ccfa5971", "requestV3SlowFill((address,address,address,address,address,uint256,uint256,uint256,uint32,uint32,uint32,bytes))": "9963e5a8", "speedUpV3Deposit(address,uint32,uint256,address,bytes,bytes)": "4e0fb8f5"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"ClaimedMerkleLeaf\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Disabled<PERSON>oute\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ExpiredFillDeadline\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidChainId\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidExclusiveRelayer\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidFillDeadline\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidMerkleLeaf\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidMerkleProof\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidPayoutAdjustmentPct\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidQuoteTimestamp\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidSlowFillRequest\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"LowLevelCallFailed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"MsgValueDoesNotMatchInputAmount\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NoSlowFillsInExclusivityWindow\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotExclusiveRelayer\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"RelayFilled\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"WrongERC7683OrderId\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"inputToken\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"outputToken\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"inputAmount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"outputAmount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"repaymentChainId\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"originChainId\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"uint32\",\"name\":\"depositId\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"fillDeadline\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"exclusivityDeadline\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"exclusiveRelayer\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"relayer\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"depositor\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"recipient\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"message\",\"type\":\"bytes\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"updatedRecipient\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"updatedMessage\",\"type\":\"bytes\"},{\"internalType\":\"uint256\",\"name\":\"updatedOutputAmount\",\"type\":\"uint256\"},{\"internalType\":\"enum IAcrossSpokePoolV3.FillType\",\"name\":\"fillType\",\"type\":\"uint8\"}],\"indexed\":false,\"internalType\":\"struct IAcrossSpokePoolV3.V3RelayExecutionEventInfo\",\"name\":\"relayExecutionInfo\",\"type\":\"tuple\"}],\"name\":\"FilledV3Relay\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"updatedOutputAmount\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"uint32\",\"name\":\"depositId\",\"type\":\"uint32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"depositor\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"updatedRecipient\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"updatedMessage\",\"type\":\"bytes\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"depositorSignature\",\"type\":\"bytes\"}],\"name\":\"RequestedSpeedUpV3Deposit\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"inputToken\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"outputToken\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"inputAmount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"outputAmount\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"originChainId\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"uint32\",\"name\":\"depositId\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"fillDeadline\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"exclusivityDeadline\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"exclusiveRelayer\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"depositor\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"recipient\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"message\",\"type\":\"bytes\"}],\"name\":\"RequestedV3SlowFill\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"inputToken\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"outputToken\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"inputAmount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"outputAmount\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"destinationChainId\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"uint32\",\"name\":\"depositId\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"quoteTimestamp\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"fillDeadline\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"exclusivityDeadline\",\"type\":\"uint32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"depositor\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"recipient\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"exclusiveRelayer\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"message\",\"type\":\"bytes\"}],\"name\":\"V3FundsDeposited\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"depositor\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"recipient\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"inputToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"outputToken\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"inputAmount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"outputAmount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"destinationChainId\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"exclusiveRelayer\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"quoteTimestamp\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"fillDeadline\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"exclusivityDeadline\",\"type\":\"uint32\"},{\"internalType\":\"bytes\",\"name\":\"message\",\"type\":\"bytes\"}],\"name\":\"depositV3\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"depositor\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"recipient\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"inputToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"outputToken\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"inputAmount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"outputAmount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"destinationChainId\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"exclusiveRelayer\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"fillDeadlineOffset\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"exclusivityDeadline\",\"type\":\"uint32\"},{\"internalType\":\"bytes\",\"name\":\"message\",\"type\":\"bytes\"}],\"name\":\"depositV3Now\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"depositor\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"recipient\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"exclusiveRelayer\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"inputToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"outputToken\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"inputAmount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"outputAmount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"originChainId\",\"type\":\"uint256\"},{\"internalType\":\"uint32\",\"name\":\"depositId\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"fillDeadline\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"exclusivityDeadline\",\"type\":\"uint32\"},{\"internalType\":\"bytes\",\"name\":\"message\",\"type\":\"bytes\"}],\"internalType\":\"struct IAcrossSpokePoolV3.V3RelayData\",\"name\":\"relayData\",\"type\":\"tuple\"},{\"internalType\":\"uint256\",\"name\":\"chainId\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"updatedOutputAmount\",\"type\":\"uint256\"}],\"internalType\":\"struct IAcrossSpokePoolV3.V3SlowFill\",\"name\":\"slowFillLeaf\",\"type\":\"tuple\"},{\"internalType\":\"uint32\",\"name\":\"rootBundleId\",\"type\":\"uint32\"},{\"internalType\":\"bytes32[]\",\"name\":\"proof\",\"type\":\"bytes32[]\"}],\"name\":\"executeV3SlowRelayLeaf\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"depositor\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"recipient\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"exclusiveRelayer\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"inputToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"outputToken\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"inputAmount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"outputAmount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"originChainId\",\"type\":\"uint256\"},{\"internalType\":\"uint32\",\"name\":\"depositId\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"fillDeadline\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"exclusivityDeadline\",\"type\":\"uint32\"},{\"internalType\":\"bytes\",\"name\":\"message\",\"type\":\"bytes\"}],\"internalType\":\"struct IAcrossSpokePoolV3.V3RelayData\",\"name\":\"relayData\",\"type\":\"tuple\"},{\"internalType\":\"uint256\",\"name\":\"repaymentChainId\",\"type\":\"uint256\"}],\"name\":\"fillV3Relay\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"depositor\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"recipient\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"exclusiveRelayer\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"inputToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"outputToken\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"inputAmount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"outputAmount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"originChainId\",\"type\":\"uint256\"},{\"internalType\":\"uint32\",\"name\":\"depositId\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"fillDeadline\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"exclusivityDeadline\",\"type\":\"uint32\"},{\"internalType\":\"bytes\",\"name\":\"message\",\"type\":\"bytes\"}],\"internalType\":\"struct IAcrossSpokePoolV3.V3RelayData\",\"name\":\"relayData\",\"type\":\"tuple\"},{\"internalType\":\"uint256\",\"name\":\"repaymentChainId\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"updatedOutputAmount\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"updatedRecipient\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"updatedMessage\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"depositorSignature\",\"type\":\"bytes\"}],\"name\":\"fillV3RelayWithUpdatedDeposit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"depositor\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"recipient\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"exclusiveRelayer\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"inputToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"outputToken\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"inputAmount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"outputAmount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"originChainId\",\"type\":\"uint256\"},{\"internalType\":\"uint32\",\"name\":\"depositId\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"fillDeadline\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"exclusivityDeadline\",\"type\":\"uint32\"},{\"internalType\":\"bytes\",\"name\":\"message\",\"type\":\"bytes\"}],\"internalType\":\"struct IAcrossSpokePoolV3.V3RelayData\",\"name\":\"relayData\",\"type\":\"tuple\"}],\"name\":\"requestV3SlowFill\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"depositor\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"depositId\",\"type\":\"uint32\"},{\"internalType\":\"uint256\",\"name\":\"updatedOutputAmount\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"updatedRecipient\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"updatedMessage\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"depositorSignature\",\"type\":\"bytes\"}],\"name\":\"speedUpV3Deposit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"errors\":{\"DisabledRoute()\":[{\"notice\":\"ERRORS                *\"}]},\"events\":{\"V3FundsDeposited(address,address,uint256,uint256,uint256,uint32,uint32,uint32,uint32,address,address,address,bytes)\":{\"notice\":\"EVENTS                *\"}},\"kind\":\"user\",\"methods\":{\"depositV3(address,address,address,address,uint256,uint256,uint256,address,uint32,uint32,uint32,bytes)\":{\"notice\":\"FUNCTIONS             *\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/external/across/IAcrossSpokePoolV3.sol\":\"IAcrossSpokePoolV3\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"src/interfaces/external/across/IAcrossSpokePoolV3.sol\":{\"keccak256\":\"0x3198e1dbc8997dcd3fdf0aa1a13c451184c66cc1d1af6cbe9293835aab672fee\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://3af367496d6c20c4b45c3ee29365291cc6d22c72182ef3e16e4bf858ae8a816c\",\"dweb:/ipfs/QmXMLSvkhfr7VLuEy3BAik1rrSQeaDg8zS3VSth2Jutt8U\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"inputs": [], "type": "error", "name": "DisabledRoute"}, {"inputs": [], "type": "error", "name": "ExpiredFillDeadline"}, {"inputs": [], "type": "error", "name": "InvalidChainId"}, {"inputs": [], "type": "error", "name": "InvalidExclusiveRelayer"}, {"inputs": [], "type": "error", "name": "InvalidFillDeadline"}, {"inputs": [], "type": "error", "name": "InvalidMerkleLeaf"}, {"inputs": [], "type": "error", "name": "InvalidMerkleProof"}, {"inputs": [], "type": "error", "name": "InvalidPayoutAdjustmentPct"}, {"inputs": [], "type": "error", "name": "InvalidQuoteTimestamp"}, {"inputs": [], "type": "error", "name": "InvalidSlowFillRequest"}, {"inputs": [{"internalType": "bytes", "name": "data", "type": "bytes"}], "type": "error", "name": "LowLevelCallFailed"}, {"inputs": [], "type": "error", "name": "MsgValueDoesNotMatchInputAmount"}, {"inputs": [], "type": "error", "name": "NoSlowFillsInExclusivityWindow"}, {"inputs": [], "type": "error", "name": "NotExclusiveRelayer"}, {"inputs": [], "type": "error", "name": "RelayFilled"}, {"inputs": [], "type": "error", "name": "WrongERC7683OrderId"}, {"inputs": [{"internalType": "address", "name": "inputToken", "type": "address", "indexed": false}, {"internalType": "address", "name": "outputToken", "type": "address", "indexed": false}, {"internalType": "uint256", "name": "inputAmount", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "outputAmount", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "repaymentChainId", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "originChainId", "type": "uint256", "indexed": true}, {"internalType": "uint32", "name": "depositId", "type": "uint32", "indexed": true}, {"internalType": "uint32", "name": "fillDeadline", "type": "uint32", "indexed": false}, {"internalType": "uint32", "name": "exclusivityDeadline", "type": "uint32", "indexed": false}, {"internalType": "address", "name": "exclusiveRelayer", "type": "address", "indexed": false}, {"internalType": "address", "name": "relayer", "type": "address", "indexed": true}, {"internalType": "address", "name": "depositor", "type": "address", "indexed": false}, {"internalType": "address", "name": "recipient", "type": "address", "indexed": false}, {"internalType": "bytes", "name": "message", "type": "bytes", "indexed": false}, {"internalType": "struct IAcrossSpokePoolV3.V3RelayExecutionEventInfo", "name": "relayExecutionInfo", "type": "tuple", "components": [{"internalType": "address", "name": "updatedRecipient", "type": "address"}, {"internalType": "bytes", "name": "updatedMessage", "type": "bytes"}, {"internalType": "uint256", "name": "updatedOutputAmount", "type": "uint256"}, {"internalType": "enum IAcrossSpokePoolV3.FillType", "name": "fillType", "type": "uint8"}], "indexed": false}], "type": "event", "name": "FilledV3Relay", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "updatedOutputAmount", "type": "uint256", "indexed": false}, {"internalType": "uint32", "name": "depositId", "type": "uint32", "indexed": true}, {"internalType": "address", "name": "depositor", "type": "address", "indexed": true}, {"internalType": "address", "name": "updatedRecipient", "type": "address", "indexed": false}, {"internalType": "bytes", "name": "updatedMessage", "type": "bytes", "indexed": false}, {"internalType": "bytes", "name": "depositorSignature", "type": "bytes", "indexed": false}], "type": "event", "name": "RequestedSpeedUpV3Deposit", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "inputToken", "type": "address", "indexed": false}, {"internalType": "address", "name": "outputToken", "type": "address", "indexed": false}, {"internalType": "uint256", "name": "inputAmount", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "outputAmount", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "originChainId", "type": "uint256", "indexed": true}, {"internalType": "uint32", "name": "depositId", "type": "uint32", "indexed": true}, {"internalType": "uint32", "name": "fillDeadline", "type": "uint32", "indexed": false}, {"internalType": "uint32", "name": "exclusivityDeadline", "type": "uint32", "indexed": false}, {"internalType": "address", "name": "exclusiveRelayer", "type": "address", "indexed": false}, {"internalType": "address", "name": "depositor", "type": "address", "indexed": false}, {"internalType": "address", "name": "recipient", "type": "address", "indexed": false}, {"internalType": "bytes", "name": "message", "type": "bytes", "indexed": false}], "type": "event", "name": "RequestedV3SlowFill", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "inputToken", "type": "address", "indexed": false}, {"internalType": "address", "name": "outputToken", "type": "address", "indexed": false}, {"internalType": "uint256", "name": "inputAmount", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "outputAmount", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "destinationChainId", "type": "uint256", "indexed": true}, {"internalType": "uint32", "name": "depositId", "type": "uint32", "indexed": true}, {"internalType": "uint32", "name": "quoteTimestamp", "type": "uint32", "indexed": false}, {"internalType": "uint32", "name": "fillDeadline", "type": "uint32", "indexed": false}, {"internalType": "uint32", "name": "exclusivityDeadline", "type": "uint32", "indexed": false}, {"internalType": "address", "name": "depositor", "type": "address", "indexed": true}, {"internalType": "address", "name": "recipient", "type": "address", "indexed": false}, {"internalType": "address", "name": "exclusiveRelayer", "type": "address", "indexed": false}, {"internalType": "bytes", "name": "message", "type": "bytes", "indexed": false}], "type": "event", "name": "V3FundsDeposited", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "depositor", "type": "address"}, {"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "address", "name": "inputToken", "type": "address"}, {"internalType": "address", "name": "outputToken", "type": "address"}, {"internalType": "uint256", "name": "inputAmount", "type": "uint256"}, {"internalType": "uint256", "name": "outputAmount", "type": "uint256"}, {"internalType": "uint256", "name": "destinationChainId", "type": "uint256"}, {"internalType": "address", "name": "exclusiveRelayer", "type": "address"}, {"internalType": "uint32", "name": "quoteTimestamp", "type": "uint32"}, {"internalType": "uint32", "name": "fillDeadline", "type": "uint32"}, {"internalType": "uint32", "name": "exclusivityDeadline", "type": "uint32"}, {"internalType": "bytes", "name": "message", "type": "bytes"}], "stateMutability": "payable", "type": "function", "name": "depositV3"}, {"inputs": [{"internalType": "address", "name": "depositor", "type": "address"}, {"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "address", "name": "inputToken", "type": "address"}, {"internalType": "address", "name": "outputToken", "type": "address"}, {"internalType": "uint256", "name": "inputAmount", "type": "uint256"}, {"internalType": "uint256", "name": "outputAmount", "type": "uint256"}, {"internalType": "uint256", "name": "destinationChainId", "type": "uint256"}, {"internalType": "address", "name": "exclusiveRelayer", "type": "address"}, {"internalType": "uint32", "name": "fillDeadlineOffset", "type": "uint32"}, {"internalType": "uint32", "name": "exclusivityDeadline", "type": "uint32"}, {"internalType": "bytes", "name": "message", "type": "bytes"}], "stateMutability": "payable", "type": "function", "name": "depositV3Now"}, {"inputs": [{"internalType": "struct IAcrossSpokePoolV3.V3SlowFill", "name": "slow<PERSON>ill<PERSON><PERSON>f", "type": "tuple", "components": [{"internalType": "struct IAcrossSpokePoolV3.V3RelayData", "name": "relayData", "type": "tuple", "components": [{"internalType": "address", "name": "depositor", "type": "address"}, {"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "address", "name": "exclusiveRelayer", "type": "address"}, {"internalType": "address", "name": "inputToken", "type": "address"}, {"internalType": "address", "name": "outputToken", "type": "address"}, {"internalType": "uint256", "name": "inputAmount", "type": "uint256"}, {"internalType": "uint256", "name": "outputAmount", "type": "uint256"}, {"internalType": "uint256", "name": "originChainId", "type": "uint256"}, {"internalType": "uint32", "name": "depositId", "type": "uint32"}, {"internalType": "uint32", "name": "fillDeadline", "type": "uint32"}, {"internalType": "uint32", "name": "exclusivityDeadline", "type": "uint32"}, {"internalType": "bytes", "name": "message", "type": "bytes"}]}, {"internalType": "uint256", "name": "chainId", "type": "uint256"}, {"internalType": "uint256", "name": "updatedOutputAmount", "type": "uint256"}]}, {"internalType": "uint32", "name": "rootBundleId", "type": "uint32"}, {"internalType": "bytes32[]", "name": "proof", "type": "bytes32[]"}], "stateMutability": "nonpayable", "type": "function", "name": "executeV3SlowRelayLeaf"}, {"inputs": [{"internalType": "struct IAcrossSpokePoolV3.V3RelayData", "name": "relayData", "type": "tuple", "components": [{"internalType": "address", "name": "depositor", "type": "address"}, {"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "address", "name": "exclusiveRelayer", "type": "address"}, {"internalType": "address", "name": "inputToken", "type": "address"}, {"internalType": "address", "name": "outputToken", "type": "address"}, {"internalType": "uint256", "name": "inputAmount", "type": "uint256"}, {"internalType": "uint256", "name": "outputAmount", "type": "uint256"}, {"internalType": "uint256", "name": "originChainId", "type": "uint256"}, {"internalType": "uint32", "name": "depositId", "type": "uint32"}, {"internalType": "uint32", "name": "fillDeadline", "type": "uint32"}, {"internalType": "uint32", "name": "exclusivityDeadline", "type": "uint32"}, {"internalType": "bytes", "name": "message", "type": "bytes"}]}, {"internalType": "uint256", "name": "repaymentChainId", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "fillV3Relay"}, {"inputs": [{"internalType": "struct IAcrossSpokePoolV3.V3RelayData", "name": "relayData", "type": "tuple", "components": [{"internalType": "address", "name": "depositor", "type": "address"}, {"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "address", "name": "exclusiveRelayer", "type": "address"}, {"internalType": "address", "name": "inputToken", "type": "address"}, {"internalType": "address", "name": "outputToken", "type": "address"}, {"internalType": "uint256", "name": "inputAmount", "type": "uint256"}, {"internalType": "uint256", "name": "outputAmount", "type": "uint256"}, {"internalType": "uint256", "name": "originChainId", "type": "uint256"}, {"internalType": "uint32", "name": "depositId", "type": "uint32"}, {"internalType": "uint32", "name": "fillDeadline", "type": "uint32"}, {"internalType": "uint32", "name": "exclusivityDeadline", "type": "uint32"}, {"internalType": "bytes", "name": "message", "type": "bytes"}]}, {"internalType": "uint256", "name": "repaymentChainId", "type": "uint256"}, {"internalType": "uint256", "name": "updatedOutputAmount", "type": "uint256"}, {"internalType": "address", "name": "updatedRecipient", "type": "address"}, {"internalType": "bytes", "name": "updatedMessage", "type": "bytes"}, {"internalType": "bytes", "name": "depositorSignature", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "fillV3RelayWithUpdatedDeposit"}, {"inputs": [{"internalType": "struct IAcrossSpokePoolV3.V3RelayData", "name": "relayData", "type": "tuple", "components": [{"internalType": "address", "name": "depositor", "type": "address"}, {"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "address", "name": "exclusiveRelayer", "type": "address"}, {"internalType": "address", "name": "inputToken", "type": "address"}, {"internalType": "address", "name": "outputToken", "type": "address"}, {"internalType": "uint256", "name": "inputAmount", "type": "uint256"}, {"internalType": "uint256", "name": "outputAmount", "type": "uint256"}, {"internalType": "uint256", "name": "originChainId", "type": "uint256"}, {"internalType": "uint32", "name": "depositId", "type": "uint32"}, {"internalType": "uint32", "name": "fillDeadline", "type": "uint32"}, {"internalType": "uint32", "name": "exclusivityDeadline", "type": "uint32"}, {"internalType": "bytes", "name": "message", "type": "bytes"}]}], "stateMutability": "nonpayable", "type": "function", "name": "requestV3SlowFill"}, {"inputs": [{"internalType": "address", "name": "depositor", "type": "address"}, {"internalType": "uint32", "name": "depositId", "type": "uint32"}, {"internalType": "uint256", "name": "updatedOutputAmount", "type": "uint256"}, {"internalType": "address", "name": "updatedRecipient", "type": "address"}, {"internalType": "bytes", "name": "updatedMessage", "type": "bytes"}, {"internalType": "bytes", "name": "depositorSignature", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "speedUpV3Deposit"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {"depositV3(address,address,address,address,uint256,uint256,uint256,address,uint32,uint32,uint32,bytes)": {"notice": "FUNCTIONS             *"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/interfaces/external/across/IAcrossSpokePoolV3.sol": "IAcrossSpokePoolV3"}, "evmVersion": "london", "libraries": {}}, "sources": {"src/interfaces/external/across/IAcrossSpokePoolV3.sol": {"keccak256": "0x3198e1dbc8997dcd3fdf0aa1a13c451184c66cc1d1af6cbe9293835aab672fee", "urls": ["bzz-raw://3af367496d6c20c4b45c3ee29365291cc6d22c72182ef3e16e4bf858ae8a816c", "dweb:/ipfs/QmXMLSvkhfr7VLuEy3BAik1rrSQeaDg8zS3VSth2Jutt8U"], "license": "BUSL-1.1"}}, "version": 1}, "id": 149}