{"abi": [{"type": "function", "name": "baseRatePerBlock", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "blocksPerYear", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getBorrowRate", "inputs": [{"name": "cash", "type": "uint256", "internalType": "uint256"}, {"name": "borrows", "type": "uint256", "internalType": "uint256"}, {"name": "reserves", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getSupplyRate", "inputs": [{"name": "cash", "type": "uint256", "internalType": "uint256"}, {"name": "borrows", "type": "uint256", "internalType": "uint256"}, {"name": "reserves", "type": "uint256", "internalType": "uint256"}, {"name": "reserveFactorMantissa", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "isInterestRateModel", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "jumpMultiplierPerBlock", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "kink", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "multiplierPerBlock", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "name", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "utilizationRate", "inputs": [{"name": "cash", "type": "uint256", "internalType": "uint256"}, {"name": "borrows", "type": "uint256", "internalType": "uint256"}, {"name": "reserves", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "pure"}, {"type": "event", "name": "NewInterestParams", "inputs": [{"name": "baseRatePerBlock", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "multiplierPerBlock", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "jumpMultiplierPerBlock", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "kink", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"baseRatePerBlock()": "f14039de", "blocksPerYear()": "a385fb96", "getBorrowRate(uint256,uint256,uint256)": "15f24053", "getSupplyRate(uint256,uint256,uint256,uint256)": "b8168816", "isInterestRateModel()": "2191f92a", "jumpMultiplierPerBlock()": "b9f9850a", "kink()": "fd2da339", "multiplierPerBlock()": "8726bb89", "name()": "06fdde03", "utilizationRate(uint256,uint256,uint256)": "6e71e2d8"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"baseRatePerBlock\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"multiplierPerBlock\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"jumpMultiplierPerBlock\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"kink\",\"type\":\"uint256\"}],\"name\":\"NewInterestParams\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"baseRatePerBlock\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"blocksPerYear\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"cash\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"borrows\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"reserves\",\"type\":\"uint256\"}],\"name\":\"getBorrowRate\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"cash\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"borrows\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"reserves\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"reserveFactorMantissa\",\"type\":\"uint256\"}],\"name\":\"getSupplyRate\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"isInterestRateModel\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"jumpMultiplierPerBlock\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"kink\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"multiplierPerBlock\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"name\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"cash\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"borrows\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"reserves\",\"type\":\"uint256\"}],\"name\":\"utilizationRate\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"pure\",\"type\":\"function\"}],\"devdoc\":{\"events\":{\"NewInterestParams(uint256,uint256,uint256,uint256)\":{\"params\":{\"baseRatePerBlock\":\"The base rate per block\",\"jumpMultiplierPerBlock\":\"The multiplier after hitting the kink\",\"kink\":\"The utilization point where the jump multiplier is applied\",\"multiplierPerBlock\":\"The multiplier per block for the interest rate slope\"}}},\"kind\":\"dev\",\"methods\":{\"baseRatePerBlock()\":{\"returns\":{\"_0\":\"The base rate per block\"}},\"blocksPerYear()\":{\"returns\":{\"_0\":\"The number of blocks per year\"}},\"getBorrowRate(uint256,uint256,uint256)\":{\"params\":{\"borrows\":\"The total borrows in the market\",\"cash\":\"The total cash in the market\",\"reserves\":\"The total reserves in the market\"},\"returns\":{\"_0\":\"The current borrow rate per block, scaled by 1e18\"}},\"getSupplyRate(uint256,uint256,uint256,uint256)\":{\"params\":{\"borrows\":\"The total borrows in the market\",\"cash\":\"The total cash in the market\",\"reserveFactorMantissa\":\"The current reserve factor for the market\",\"reserves\":\"The total reserves in the market\"},\"returns\":{\"_0\":\"The current supply rate per block, scaled by 1e18\"}},\"jumpMultiplierPerBlock()\":{\"returns\":{\"_0\":\"The jump multiplier per block\"}},\"kink()\":{\"returns\":{\"_0\":\"The utilization point (kink)\"}},\"multiplierPerBlock()\":{\"returns\":{\"_0\":\"The multiplier per block\"}},\"name()\":{\"returns\":{\"_0\":\"The name of the interest rate model\"}},\"utilizationRate(uint256,uint256,uint256)\":{\"params\":{\"borrows\":\"The total borrows in the market\",\"cash\":\"The total cash in the market\",\"reserves\":\"The total reserves in the market\"},\"returns\":{\"_0\":\"The utilization rate as a mantissa between [0, 1e18]\"}}},\"title\":\"IInterestRateModel\",\"version\":1},\"userdoc\":{\"events\":{\"NewInterestParams(uint256,uint256,uint256,uint256)\":{\"notice\":\"Emitted when interest rate parameters are updated\"}},\"kind\":\"user\",\"methods\":{\"baseRatePerBlock()\":{\"notice\":\"The base interest rate which is the y-intercept when utilization rate is 0\"},\"blocksPerYear()\":{\"notice\":\"The approximate number of blocks per year that is assumed by the interest rate model\"},\"getBorrowRate(uint256,uint256,uint256)\":{\"notice\":\"Returns the current borrow rate per block for the market\"},\"getSupplyRate(uint256,uint256,uint256,uint256)\":{\"notice\":\"Returns the current supply rate per block for the market\"},\"isInterestRateModel()\":{\"notice\":\"Should return true\"},\"jumpMultiplierPerBlock()\":{\"notice\":\"The multiplierPerBlock after hitting a specified utilization point\"},\"kink()\":{\"notice\":\"The utilization point at which the jump multiplier is applied\"},\"multiplierPerBlock()\":{\"notice\":\"The multiplier of utilization rate that gives the slope of the interest rate\"},\"name()\":{\"notice\":\"A name for user-friendliness, e.g. WBTC\"},\"utilizationRate(uint256,uint256,uint256)\":{\"notice\":\"Calculates the utilization rate of the market\"}},\"notice\":\"Interface for the interest rate contracts\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/IInterestRateModel.sol\":\"IInterestRateModel\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"src/interfaces/IInterestRateModel.sol\":{\"keccak256\":\"0xdc8386be37a06d93fb0967be32e465f3b1f941ec9ce2f90984a5c70488cddb1e\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://de2bf96a7d0945f58b00e9fc867ac00fc4a09480e9e27c1a800604295320b769\",\"dweb:/ipfs/Qmf5sAgA58GjVxeipJNhP4euxwsZ9FS9CgsWTJ319UmCUE\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "uint256", "name": "baseRatePerBlock", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "multiplierPerBlock", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "jumpMultiplierPerBlock", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "kink", "type": "uint256", "indexed": false}], "type": "event", "name": "NewInterestParams", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "baseRatePerBlock", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "blocksPerYear", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "cash", "type": "uint256"}, {"internalType": "uint256", "name": "borrows", "type": "uint256"}, {"internalType": "uint256", "name": "reserves", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getBorrowRate", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "cash", "type": "uint256"}, {"internalType": "uint256", "name": "borrows", "type": "uint256"}, {"internalType": "uint256", "name": "reserves", "type": "uint256"}, {"internalType": "uint256", "name": "reserveFactorMantissa", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getSupplyRate", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "isInterestRateModel", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "jumpMultiplierPerBlock", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "kink", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "multiplierPerBlock", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [{"internalType": "uint256", "name": "cash", "type": "uint256"}, {"internalType": "uint256", "name": "borrows", "type": "uint256"}, {"internalType": "uint256", "name": "reserves", "type": "uint256"}], "stateMutability": "pure", "type": "function", "name": "utilizationRate", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}], "devdoc": {"kind": "dev", "methods": {"baseRatePerBlock()": {"returns": {"_0": "The base rate per block"}}, "blocksPerYear()": {"returns": {"_0": "The number of blocks per year"}}, "getBorrowRate(uint256,uint256,uint256)": {"params": {"borrows": "The total borrows in the market", "cash": "The total cash in the market", "reserves": "The total reserves in the market"}, "returns": {"_0": "The current borrow rate per block, scaled by 1e18"}}, "getSupplyRate(uint256,uint256,uint256,uint256)": {"params": {"borrows": "The total borrows in the market", "cash": "The total cash in the market", "reserveFactorMantissa": "The current reserve factor for the market", "reserves": "The total reserves in the market"}, "returns": {"_0": "The current supply rate per block, scaled by 1e18"}}, "jumpMultiplierPerBlock()": {"returns": {"_0": "The jump multiplier per block"}}, "kink()": {"returns": {"_0": "The utilization point (kink)"}}, "multiplierPerBlock()": {"returns": {"_0": "The multiplier per block"}}, "name()": {"returns": {"_0": "The name of the interest rate model"}}, "utilizationRate(uint256,uint256,uint256)": {"params": {"borrows": "The total borrows in the market", "cash": "The total cash in the market", "reserves": "The total reserves in the market"}, "returns": {"_0": "The utilization rate as a mantissa between [0, 1e18]"}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"baseRatePerBlock()": {"notice": "The base interest rate which is the y-intercept when utilization rate is 0"}, "blocksPerYear()": {"notice": "The approximate number of blocks per year that is assumed by the interest rate model"}, "getBorrowRate(uint256,uint256,uint256)": {"notice": "Returns the current borrow rate per block for the market"}, "getSupplyRate(uint256,uint256,uint256,uint256)": {"notice": "Returns the current supply rate per block for the market"}, "isInterestRateModel()": {"notice": "Should return true"}, "jumpMultiplierPerBlock()": {"notice": "The multiplierPerBlock after hitting a specified utilization point"}, "kink()": {"notice": "The utilization point at which the jump multiplier is applied"}, "multiplierPerBlock()": {"notice": "The multiplier of utilization rate that gives the slope of the interest rate"}, "name()": {"notice": "A name for user-friendliness, e.g. WBTC"}, "utilizationRate(uint256,uint256,uint256)": {"notice": "Calculates the utilization rate of the market"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/interfaces/IInterestRateModel.sol": "IInterestRateModel"}, "evmVersion": "london", "libraries": {}}, "sources": {"src/interfaces/IInterestRateModel.sol": {"keccak256": "0xdc8386be37a06d93fb0967be32e465f3b1f941ec9ce2f90984a5c70488cddb1e", "urls": ["bzz-raw://de2bf96a7d0945f58b00e9fc867ac00fc4a09480e9e27c1a800604295320b769", "dweb:/ipfs/Qmf5sAgA58GjVxeipJNhP4euxwsZ9FS9CgsWTJ319UmCUE"], "license": "BSL-1.1"}}, "version": 1}, "id": 136}