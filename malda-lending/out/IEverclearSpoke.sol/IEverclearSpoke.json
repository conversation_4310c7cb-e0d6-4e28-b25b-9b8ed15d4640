{"abi": [{"type": "function", "name": "newIntent", "inputs": [{"name": "_destinations", "type": "uint32[]", "internalType": "uint32[]"}, {"name": "_receiver", "type": "address", "internalType": "address"}, {"name": "_inputAsset", "type": "address", "internalType": "address"}, {"name": "_outputAsset", "type": "address", "internalType": "address"}, {"name": "_amount", "type": "uint256", "internalType": "uint256"}, {"name": "_maxFee", "type": "uint24", "internalType": "uint24"}, {"name": "_ttl", "type": "uint48", "internalType": "uint48"}, {"name": "_data", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "_intentId", "type": "bytes32", "internalType": "bytes32"}, {"name": "_intent", "type": "tuple", "internalType": "struct IEverclearSpoke.Intent", "components": [{"name": "val", "type": "uint256", "internalType": "uint256"}]}], "stateMutability": "nonpayable"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"newIntent(uint32[],address,address,address,uint256,uint24,uint48,bytes)": "4a943d21"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"uint32[]\",\"name\":\"_destinations\",\"type\":\"uint32[]\"},{\"internalType\":\"address\",\"name\":\"_receiver\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_inputAsset\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_outputAsset\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_amount\",\"type\":\"uint256\"},{\"internalType\":\"uint24\",\"name\":\"_maxFee\",\"type\":\"uint24\"},{\"internalType\":\"uint48\",\"name\":\"_ttl\",\"type\":\"uint48\"},{\"internalType\":\"bytes\",\"name\":\"_data\",\"type\":\"bytes\"}],\"name\":\"newIntent\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"_intentId\",\"type\":\"bytes32\"},{\"components\":[{\"internalType\":\"uint256\",\"name\":\"val\",\"type\":\"uint256\"}],\"internalType\":\"struct IEverclearSpoke.Intent\",\"name\":\"_intent\",\"type\":\"tuple\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{\"newIntent(uint32[],address,address,address,uint256,uint24,uint48,bytes)\":{\"params\":{\"_amount\":\"The amount of the asset\",\"_data\":\"The data of the intent\",\"_destinations\":\"The possible destination chains of the intent\",\"_inputAsset\":\"The asset address on origin\",\"_maxFee\":\"The maximum fee that can be taken by solvers\",\"_outputAsset\":\"The asset address on destination\",\"_receiver\":\"The destinantion address of the intent\",\"_ttl\":\"The time to live of the intent\"},\"returns\":{\"_intent\":\"The intent object\",\"_intentId\":\"The ID of the intent\"}}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"newIntent(uint32[],address,address,address,uint256,uint24,uint48,bytes)\":{\"notice\":\"Creates a new intent\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/external/everclear/IEverclearSpoke.sol\":\"IEverclearSpoke\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"src/interfaces/external/everclear/IEverclearSpoke.sol\":{\"keccak256\":\"0x0e094623cbc0a4f080a5dc76ca59b2cf68cdfa1475dbb924d4ca0be7557d17c1\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://6f30643fe9665bb3266d5a60009a3491912e4b813b9f9dd38e12a94b338cee14\",\"dweb:/ipfs/QmV9KWrA2pNmXqtDdtYUMET6tVtNMHYeaRkpbmAPRBzkhC\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "uint32[]", "name": "_destinations", "type": "uint32[]"}, {"internalType": "address", "name": "_receiver", "type": "address"}, {"internalType": "address", "name": "_inputAsset", "type": "address"}, {"internalType": "address", "name": "_outputAsset", "type": "address"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}, {"internalType": "uint24", "name": "_maxFee", "type": "uint24"}, {"internalType": "uint48", "name": "_ttl", "type": "uint48"}, {"internalType": "bytes", "name": "_data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "newIntent", "outputs": [{"internalType": "bytes32", "name": "_intentId", "type": "bytes32"}, {"internalType": "struct IEverclearSpoke.Intent", "name": "_intent", "type": "tuple", "components": [{"internalType": "uint256", "name": "val", "type": "uint256"}]}]}], "devdoc": {"kind": "dev", "methods": {"newIntent(uint32[],address,address,address,uint256,uint24,uint48,bytes)": {"params": {"_amount": "The amount of the asset", "_data": "The data of the intent", "_destinations": "The possible destination chains of the intent", "_inputAsset": "The asset address on origin", "_maxFee": "The maximum fee that can be taken by solvers", "_outputAsset": "The asset address on destination", "_receiver": "The destinantion address of the intent", "_ttl": "The time to live of the intent"}, "returns": {"_intent": "The intent object", "_intentId": "The ID of the intent"}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"newIntent(uint32[],address,address,address,uint256,uint24,uint48,bytes)": {"notice": "Creates a new intent"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/interfaces/external/everclear/IEverclearSpoke.sol": "IEverclearSpoke"}, "evmVersion": "london", "libraries": {}}, "sources": {"src/interfaces/external/everclear/IEverclearSpoke.sol": {"keccak256": "0x0e094623cbc0a4f080a5dc76ca59b2cf68cdfa1475dbb924d4ca0be7557d17c1", "urls": ["bzz-raw://6f30643fe9665bb3266d5a60009a3491912e4b813b9f9dd38e12a94b338cee14", "dweb:/ipfs/QmV9KWrA2pNmXqtDdtYUMET6tVtNMHYeaRkpbmAPRBzkhC"], "license": "BSL-1.1"}}, "version": 1}, "id": 152}