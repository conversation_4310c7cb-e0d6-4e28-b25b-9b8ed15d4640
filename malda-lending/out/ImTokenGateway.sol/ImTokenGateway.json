{"abi": [{"type": "function", "name": "accAmountIn", "inputs": [{"name": "user", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "accAmountOut", "inputs": [{"name": "user", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "blacklistOperator", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IBlacklister"}], "stateMutability": "view"}, {"type": "function", "name": "extractForRebalancing", "inputs": [{"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "getProofData", "inputs": [{"name": "user", "type": "address", "internalType": "address"}, {"name": "dstId", "type": "uint32", "internalType": "uint32"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "isPaused", "inputs": [{"name": "_type", "type": "uint8", "internalType": "enum ImTokenOperationTypes.OperationType"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "outHere", "inputs": [{"name": "journalData", "type": "bytes", "internalType": "bytes"}, {"name": "seal", "type": "bytes", "internalType": "bytes"}, {"name": "amounts", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "receiver", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "rolesOperator", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IRoles"}], "stateMutability": "view"}, {"type": "function", "name": "setPaused", "inputs": [{"name": "_type", "type": "uint8", "internalType": "enum ImTokenOperationTypes.OperationType"}, {"name": "state", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "supplyOnHost", "inputs": [{"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "receiver", "type": "address", "internalType": "address"}, {"name": "lineaSelector", "type": "bytes4", "internalType": "bytes4"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "underlying", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "updateAllowedCallerStatus", "inputs": [{"name": "caller", "type": "address", "internalType": "address"}, {"name": "status", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "AllowedCallerUpdated", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "caller", "type": "address", "indexed": true, "internalType": "address"}, {"name": "status", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "ZkVerifierUpdated", "inputs": [{"name": "oldVerifier", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newVerifier", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "mTokenGateway_Extracted", "inputs": [{"name": "msgSender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "srcSender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "receiver", "type": "address", "indexed": true, "internalType": "address"}, {"name": "accAmountIn", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "accAmountOut", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "srcChainId", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "dst<PERSON>hainId", "type": "uint32", "indexed": false, "internalType": "uint32"}], "anonymous": false}, {"type": "event", "name": "mTokenGateway_GasFeeUpdated", "inputs": [{"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "mTokenGateway_PausedState", "inputs": [{"name": "_type", "type": "uint8", "indexed": true, "internalType": "enum ImTokenOperationTypes.OperationType"}, {"name": "_status", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "mTokenG<PERSON><PERSON>_Skipped", "inputs": [{"name": "msgSender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "srcSender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "receiver", "type": "address", "indexed": true, "internalType": "address"}, {"name": "accAmountIn", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "accAmountOut", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "srcChainId", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "dst<PERSON>hainId", "type": "uint32", "indexed": false, "internalType": "uint32"}], "anonymous": false}, {"type": "event", "name": "mTokenGateway_Supplied", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "receiver", "type": "address", "indexed": true, "internalType": "address"}, {"name": "accAmountIn", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "accAmountOut", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "srcChainId", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "dst<PERSON>hainId", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "lineaMethodSelector", "type": "bytes4", "indexed": false, "internalType": "bytes4"}], "anonymous": false}, {"type": "event", "name": "mTokenGateway_UserWhitelisted", "inputs": [{"name": "user", "type": "address", "indexed": true, "internalType": "address"}, {"name": "status", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "mTokenGateway_WhitelistDisabled", "inputs": [], "anonymous": false}, {"type": "event", "name": "mTokenGateway_WhitelistEnabled", "inputs": [], "anonymous": false}, {"type": "error", "name": "mTokenGateway_AddressNotValid", "inputs": []}, {"type": "error", "name": "mTokenGateway_AmountNotValid", "inputs": []}, {"type": "error", "name": "mTokenGateway_AmountTooBig", "inputs": []}, {"type": "error", "name": "mTokenGateway_CallerNotAllowed", "inputs": []}, {"type": "error", "name": "mTokenGateway_ChainNotValid", "inputs": []}, {"type": "error", "name": "mTokenGateway_JournalNotValid", "inputs": []}, {"type": "error", "name": "mTokenGateway_L1InclusionRequired", "inputs": []}, {"type": "error", "name": "mTokenGateway_LengthNotValid", "inputs": []}, {"type": "error", "name": "mTokenGateway_NonTransferable", "inputs": []}, {"type": "error", "name": "mTokenGateway_NotEnoughGasFee", "inputs": []}, {"type": "error", "name": "mTokenGateway_NotRebalancer", "inputs": []}, {"type": "error", "name": "mTokenGateway_Paused", "inputs": [{"name": "_type", "type": "uint8", "internalType": "enum ImTokenOperationTypes.OperationType"}]}, {"type": "error", "name": "mTokenGateway_ReleaseCashNotAvailable", "inputs": []}, {"type": "error", "name": "mTokenGateway_UserBlacklisted", "inputs": []}, {"type": "error", "name": "mTokenGateway_UserNotWhitelisted", "inputs": []}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"accAmountIn(address)": "966718fd", "accAmountOut(address)": "85eccf6c", "blacklistOperator()": "2d57d487", "extractForRebalancing(uint256)": "ffcaadfe", "getProofData(address,uint32)": "07d923e9", "isPaused(uint8)": "bc61e733", "outHere(bytes,bytes,uint256[],address)": "b511d3b1", "rolesOperator()": "4fecab70", "setPaused(uint8,bool)": "6dc59d80", "supplyOnHost(uint256,address,bytes4)": "68252fa7", "underlying()": "6f307dc3", "updateAllowedCallerStatus(address,bool)": "4f2be4ce"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"mTokenGateway_AddressNotValid\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mTokenGateway_AmountNotValid\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mTokenGateway_AmountTooBig\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mTokenGateway_CallerNotAllowed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mTokenGateway_ChainNotValid\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mTokenGateway_JournalNotValid\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mTokenGateway_L1InclusionRequired\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mTokenGateway_LengthNotValid\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mTokenGateway_NonTransferable\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mTokenGateway_NotEnoughGasFee\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mTokenGateway_NotRebalancer\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"enum ImTokenOperationTypes.OperationType\",\"name\":\"_type\",\"type\":\"uint8\"}],\"name\":\"mTokenGateway_Paused\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mTokenGateway_ReleaseCashNotAvailable\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mTokenGateway_UserBlacklisted\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mTokenGateway_UserNotWhitelisted\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"caller\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"status\",\"type\":\"bool\"}],\"name\":\"AllowedCallerUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"oldVerifier\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newVerifier\",\"type\":\"address\"}],\"name\":\"ZkVerifierUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"msgSender\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"srcSender\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"accAmountIn\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"accAmountOut\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"srcChainId\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"dstChainId\",\"type\":\"uint32\"}],\"name\":\"mTokenGateway_Extracted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"mTokenGateway_GasFeeUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"enum ImTokenOperationTypes.OperationType\",\"name\":\"_type\",\"type\":\"uint8\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"_status\",\"type\":\"bool\"}],\"name\":\"mTokenGateway_PausedState\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"msgSender\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"srcSender\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"accAmountIn\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"accAmountOut\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"srcChainId\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"dstChainId\",\"type\":\"uint32\"}],\"name\":\"mTokenGateway_Skipped\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"accAmountIn\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"accAmountOut\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"srcChainId\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"dstChainId\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"bytes4\",\"name\":\"lineaMethodSelector\",\"type\":\"bytes4\"}],\"name\":\"mTokenGateway_Supplied\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"status\",\"type\":\"bool\"}],\"name\":\"mTokenGateway_UserWhitelisted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[],\"name\":\"mTokenGateway_WhitelistDisabled\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[],\"name\":\"mTokenGateway_WhitelistEnabled\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"accAmountIn\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"accAmountOut\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"blacklistOperator\",\"outputs\":[{\"internalType\":\"contract IBlacklister\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"extractForRebalancing\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"dstId\",\"type\":\"uint32\"}],\"name\":\"getProofData\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"enum ImTokenOperationTypes.OperationType\",\"name\":\"_type\",\"type\":\"uint8\"}],\"name\":\"isPaused\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"journalData\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"seal\",\"type\":\"bytes\"},{\"internalType\":\"uint256[]\",\"name\":\"amounts\",\"type\":\"uint256[]\"},{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"}],\"name\":\"outHere\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"rolesOperator\",\"outputs\":[{\"internalType\":\"contract IRoles\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"enum ImTokenOperationTypes.OperationType\",\"name\":\"_type\",\"type\":\"uint8\"},{\"internalType\":\"bool\",\"name\":\"state\",\"type\":\"bool\"}],\"name\":\"setPaused\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"internalType\":\"bytes4\",\"name\":\"lineaSelector\",\"type\":\"bytes4\"}],\"name\":\"supplyOnHost\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"underlying\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"caller\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"status\",\"type\":\"bool\"}],\"name\":\"updateAllowedCallerStatus\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{\"extractForRebalancing(uint256)\":{\"params\":{\"amount\":\"The amount to rebalance\"}},\"isPaused(uint8)\":{\"params\":{\"_type\":\"the operation type\"}},\"outHere(bytes,bytes,uint256[],address)\":{\"params\":{\"amounts\":\"The amounts to withdraw for each journal\",\"journalData\":\"The supplied journal\",\"receiver\":\"The receiver address\",\"seal\":\"The seal address\"}},\"setPaused(uint8,bool)\":{\"params\":{\"_type\":\"The pause operation type\",\"state\":\"The pause operation status\"}},\"supplyOnHost(uint256,address,bytes4)\":{\"params\":{\"amount\":\"The supplied amount\",\"lineaSelector\":\"The method selector to be called on Linea by our relayer. If empty, user has to submit it\",\"receiver\":\"The receiver address\"}},\"underlying()\":{\"returns\":{\"_0\":\"The address of the underlying token\"}},\"updateAllowedCallerStatus(address,bool)\":{\"params\":{\"caller\":\"The caller address\",\"status\":\"The status to set for `caller`\"}}},\"version\":1},\"userdoc\":{\"errors\":{\"mTokenGateway_AddressNotValid()\":[{\"notice\":\"Thrown when the address is not valid\"}],\"mTokenGateway_AmountNotValid()\":[{\"notice\":\"Thrown when the amount specified is invalid (e.g., zero)\"}],\"mTokenGateway_AmountTooBig()\":[{\"notice\":\"Thrown when there is insufficient cash to release the specified amount\"}],\"mTokenGateway_CallerNotAllowed()\":[{\"notice\":\"Thrown when caller is not allowed\"}],\"mTokenGateway_ChainNotValid()\":[{\"notice\":\"Thrown when the chain id is not LINEA\"}],\"mTokenGateway_JournalNotValid()\":[{\"notice\":\"Thrown when the journal data provided is invalid\"}],\"mTokenGateway_L1InclusionRequired()\":[{\"notice\":\"Thrown when L1 inclusion is required\"}],\"mTokenGateway_LengthNotValid()\":[{\"notice\":\"Thrown when length is not valid\"}],\"mTokenGateway_NonTransferable()\":[{\"notice\":\"Thrown when token is tranferred\"}],\"mTokenGateway_NotEnoughGasFee()\":[{\"notice\":\"Thrown when not enough gas fee was received\"}],\"mTokenGateway_NotRebalancer()\":[{\"notice\":\"Thrown when caller is not rebalancer\"}],\"mTokenGateway_Paused(uint8)\":[{\"notice\":\"Thrown when market is paused for operation type\"}],\"mTokenGateway_ReleaseCashNotAvailable()\":[{\"notice\":\"Thrown when there is insufficient cash to release the specified amount\"}],\"mTokenGateway_UserBlacklisted()\":[{\"notice\":\"Thrown when user is blacklisted\"}],\"mTokenGateway_UserNotWhitelisted()\":[{\"notice\":\"Thrown when user is not whitelisted\"}]},\"events\":{\"AllowedCallerUpdated(address,address,bool)\":{\"notice\":\"Emitted when a user updates allowed callers\"},\"mTokenGateway_Extracted(address,address,address,uint256,uint256,uint256,uint32,uint32)\":{\"notice\":\"Emitted when an extract was finalized\"},\"mTokenGateway_GasFeeUpdated(uint256)\":{\"notice\":\"Emitted when the gas fee is updated\"},\"mTokenGateway_Skipped(address,address,address,uint256,uint256,uint256,uint32,uint32)\":{\"notice\":\"Emitted when a proof was skipped\"},\"mTokenGateway_Supplied(address,address,uint256,uint256,uint256,uint32,uint32,bytes4)\":{\"notice\":\"Emitted when a supply operation is initiated\"}},\"kind\":\"user\",\"methods\":{\"accAmountIn(address)\":{\"notice\":\"Returns accumulated amount in per user\"},\"accAmountOut(address)\":{\"notice\":\"Returns accumulated amount out per user\"},\"blacklistOperator()\":{\"notice\":\"Blacklist\"},\"extractForRebalancing(uint256)\":{\"notice\":\"Extract amount to be used for rebalancing operation\"},\"getProofData(address,uint32)\":{\"notice\":\"Returns the proof data journal\"},\"isPaused(uint8)\":{\"notice\":\"returns pause state for operation\"},\"outHere(bytes,bytes,uint256[],address)\":{\"notice\":\"Extract tokens\"},\"rolesOperator()\":{\"notice\":\"Roles\"},\"setPaused(uint8,bool)\":{\"notice\":\"Set pause for a specific operation\"},\"supplyOnHost(uint256,address,bytes4)\":{\"notice\":\"Supply underlying to the contract\"},\"underlying()\":{\"notice\":\"Returns the address of the underlying token\"},\"updateAllowedCallerStatus(address,bool)\":{\"notice\":\"Set caller status for `msg.sender`\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/ImTokenGateway.sol\":\"ImTokenGateway\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"src/interfaces/IBlacklister.sol\":{\"keccak256\":\"0x47ed20135507a9d16473027cbee18de5d952846bada761c6248b91c75cd88be1\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://91a886b95dd578156f9b6cec3236e4751aae8a38e5d15e0d6045217c6fbd3d63\",\"dweb:/ipfs/QmRNjz7sGTuMkQNZhwwcg5DNN2eeqTZSAdZmxWxbx66CHU\"]},\"src/interfaces/IRoles.sol\":{\"keccak256\":\"0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c\",\"dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj\"]},\"src/interfaces/ImToken.sol\":{\"keccak256\":\"0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36\",\"dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL\"]},\"src/interfaces/ImTokenGateway.sol\":{\"keccak256\":\"0x7b47f5229cd07de60af49eb473eddc27e429abd846df81001ef05af2c3d47634\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://04d8c52462eeb19a4c28980ac5c81a901fd35982dc47c0ab95a4ee6466b150c1\",\"dweb:/ipfs/QmfTyZrLWMnWLU6zo85RVwjv5W2qXVQPkLWzru5zcBarYm\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "mTokenGateway_AddressNotValid"}, {"inputs": [], "type": "error", "name": "mTokenGateway_AmountNotValid"}, {"inputs": [], "type": "error", "name": "mTokenGateway_AmountTooBig"}, {"inputs": [], "type": "error", "name": "mTokenGateway_CallerNotAllowed"}, {"inputs": [], "type": "error", "name": "mTokenGateway_ChainNotValid"}, {"inputs": [], "type": "error", "name": "mTokenGateway_JournalNotValid"}, {"inputs": [], "type": "error", "name": "mTokenGateway_L1InclusionRequired"}, {"inputs": [], "type": "error", "name": "mTokenGateway_LengthNotValid"}, {"inputs": [], "type": "error", "name": "mTokenGateway_NonTransferable"}, {"inputs": [], "type": "error", "name": "mTokenGateway_NotEnoughGasFee"}, {"inputs": [], "type": "error", "name": "mTokenGateway_NotRebalancer"}, {"inputs": [{"internalType": "enum ImTokenOperationTypes.OperationType", "name": "_type", "type": "uint8"}], "type": "error", "name": "mTokenGateway_Paused"}, {"inputs": [], "type": "error", "name": "mTokenGateway_ReleaseCashNotAvailable"}, {"inputs": [], "type": "error", "name": "mTokenGateway_UserBlacklisted"}, {"inputs": [], "type": "error", "name": "mTokenGateway_UserNotWhitelisted"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address", "indexed": true}, {"internalType": "address", "name": "caller", "type": "address", "indexed": true}, {"internalType": "bool", "name": "status", "type": "bool", "indexed": false}], "type": "event", "name": "AllowedCallerUpdated", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "oldVerifier", "type": "address", "indexed": true}, {"internalType": "address", "name": "newVerifier", "type": "address", "indexed": true}], "type": "event", "name": "ZkVerifierUpdated", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "msgSender", "type": "address", "indexed": true}, {"internalType": "address", "name": "srcSender", "type": "address", "indexed": true}, {"internalType": "address", "name": "receiver", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "accAmountIn", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "accAmountOut", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}, {"internalType": "uint32", "name": "srcChainId", "type": "uint32", "indexed": false}, {"internalType": "uint32", "name": "dst<PERSON>hainId", "type": "uint32", "indexed": false}], "type": "event", "name": "mTokenGateway_Extracted", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}], "type": "event", "name": "mTokenGateway_GasFeeUpdated", "anonymous": false}, {"inputs": [{"internalType": "enum ImTokenOperationTypes.OperationType", "name": "_type", "type": "uint8", "indexed": true}, {"internalType": "bool", "name": "_status", "type": "bool", "indexed": false}], "type": "event", "name": "mTokenGateway_PausedState", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "msgSender", "type": "address", "indexed": true}, {"internalType": "address", "name": "srcSender", "type": "address", "indexed": true}, {"internalType": "address", "name": "receiver", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "accAmountIn", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "accAmountOut", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}, {"internalType": "uint32", "name": "srcChainId", "type": "uint32", "indexed": false}, {"internalType": "uint32", "name": "dst<PERSON>hainId", "type": "uint32", "indexed": false}], "type": "event", "name": "mTokenG<PERSON><PERSON>_Skipped", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "from", "type": "address", "indexed": true}, {"internalType": "address", "name": "receiver", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "accAmountIn", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "accAmountOut", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}, {"internalType": "uint32", "name": "srcChainId", "type": "uint32", "indexed": false}, {"internalType": "uint32", "name": "dst<PERSON>hainId", "type": "uint32", "indexed": false}, {"internalType": "bytes4", "name": "lineaMethodSelector", "type": "bytes4", "indexed": false}], "type": "event", "name": "mTokenGateway_Supplied", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "user", "type": "address", "indexed": true}, {"internalType": "bool", "name": "status", "type": "bool", "indexed": false}], "type": "event", "name": "mTokenGateway_UserWhitelisted", "anonymous": false}, {"inputs": [], "type": "event", "name": "mTokenGateway_WhitelistDisabled", "anonymous": false}, {"inputs": [], "type": "event", "name": "mTokenGateway_WhitelistEnabled", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "stateMutability": "view", "type": "function", "name": "accAmountIn", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "stateMutability": "view", "type": "function", "name": "accAmountOut", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "blacklistOperator", "outputs": [{"internalType": "contract IBlacklister", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "extractForRebalancing"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint32", "name": "dstId", "type": "uint32"}], "stateMutability": "view", "type": "function", "name": "getProofData", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "enum ImTokenOperationTypes.OperationType", "name": "_type", "type": "uint8"}], "stateMutability": "view", "type": "function", "name": "isPaused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "bytes", "name": "journalData", "type": "bytes"}, {"internalType": "bytes", "name": "seal", "type": "bytes"}, {"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}, {"internalType": "address", "name": "receiver", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "outHere"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "rolesOperator", "outputs": [{"internalType": "contract IRoles", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "enum ImTokenOperationTypes.OperationType", "name": "_type", "type": "uint8"}, {"internalType": "bool", "name": "state", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "setPaused"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "bytes4", "name": "lineaSelector", "type": "bytes4"}], "stateMutability": "payable", "type": "function", "name": "supplyOnHost"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "underlying", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "caller", "type": "address"}, {"internalType": "bool", "name": "status", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "updateAllowedCallerStatus"}], "devdoc": {"kind": "dev", "methods": {"extractForRebalancing(uint256)": {"params": {"amount": "The amount to rebalance"}}, "isPaused(uint8)": {"params": {"_type": "the operation type"}}, "outHere(bytes,bytes,uint256[],address)": {"params": {"amounts": "The amounts to withdraw for each journal", "journalData": "The supplied journal", "receiver": "The receiver address", "seal": "The seal address"}}, "setPaused(uint8,bool)": {"params": {"_type": "The pause operation type", "state": "The pause operation status"}}, "supplyOnHost(uint256,address,bytes4)": {"params": {"amount": "The supplied amount", "lineaSelector": "The method selector to be called on Linea by our relayer. If empty, user has to submit it", "receiver": "The receiver address"}}, "underlying()": {"returns": {"_0": "The address of the underlying token"}}, "updateAllowedCallerStatus(address,bool)": {"params": {"caller": "The caller address", "status": "The status to set for `caller`"}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"accAmountIn(address)": {"notice": "Returns accumulated amount in per user"}, "accAmountOut(address)": {"notice": "Returns accumulated amount out per user"}, "blacklistOperator()": {"notice": "Blacklist"}, "extractForRebalancing(uint256)": {"notice": "Extract amount to be used for rebalancing operation"}, "getProofData(address,uint32)": {"notice": "Returns the proof data journal"}, "isPaused(uint8)": {"notice": "returns pause state for operation"}, "outHere(bytes,bytes,uint256[],address)": {"notice": "Extract tokens"}, "rolesOperator()": {"notice": "Roles"}, "setPaused(uint8,bool)": {"notice": "Set pause for a specific operation"}, "supplyOnHost(uint256,address,bytes4)": {"notice": "Supply underlying to the contract"}, "underlying()": {"notice": "Returns the address of the underlying token"}, "updateAllowedCallerStatus(address,bool)": {"notice": "Set caller status for `msg.sender`"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/interfaces/ImTokenGateway.sol": "ImTokenGateway"}, "evmVersion": "london", "libraries": {}}, "sources": {"src/interfaces/IBlacklister.sol": {"keccak256": "0x47ed20135507a9d16473027cbee18de5d952846bada761c6248b91c75cd88be1", "urls": ["bzz-raw://91a886b95dd578156f9b6cec3236e4751aae8a38e5d15e0d6045217c6fbd3d63", "dweb:/ipfs/QmRNjz7sGTuMkQNZhwwcg5DNN2eeqTZSAdZmxWxbx66CHU"], "license": "BSL-1.1"}, "src/interfaces/IRoles.sol": {"keccak256": "0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365", "urls": ["bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c", "dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj"], "license": "BSL-1.1"}, "src/interfaces/ImToken.sol": {"keccak256": "0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f", "urls": ["bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36", "dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL"], "license": "BSL-1.1"}, "src/interfaces/ImTokenGateway.sol": {"keccak256": "0x7b47f5229cd07de60af49eb473eddc27e429abd846df81001ef05af2c3d47634", "urls": ["bzz-raw://04d8c52462eeb19a4c28980ac5c81a901fd35982dc47c0ab95a4ee6466b150c1", "dweb:/ipfs/QmfTyZrLWMnWLU6zo85RVwjv5W2qXVQPkLWzru5zcBarYm"], "license": "BSL-1.1"}}, "version": 1}, "id": 147}