{"abi": [{"type": "function", "name": "ENTRY_SIZE", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "error", "name": "mTokenProofDecoderLib_ChainNotFound", "inputs": []}, {"type": "error", "name": "mTokenProofDecoderLib_InvalidInclusion", "inputs": []}, {"type": "error", "name": "mTokenProofDecoderLib_InvalidLength", "inputs": []}], "bytecode": {"object": "0x60876037600b82828239805160001a607314602a57634e487b7160e01b600052600060045260246000fd5b30600052607381538281f3fe730000000000000000000000000000000000000000301460806040526004361060335760003560e01c8063ad899d12146038575b600080fd5b603f607181565b60405190815260200160405180910390f3fea26469706673582212204418bdf67d91fa6d93f58864dd6766c0748f426cb86970ded93c1f4ac868569764736f6c634300081c0033", "sourceMap": "882:2310:171:-:0;;;;;;;;;;;;;;;-1:-1:-1;;;882:2310:171;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x730000000000000000000000000000000000000000301460806040526004361060335760003560e01c8063ad899d12146038575b600080fd5b603f607181565b60405190815260200160405180910390f3fea26469706673582212204418bdf67d91fa6d93f58864dd6766c0748f426cb86970ded93c1f4ac868569764736f6c634300081c0033", "sourceMap": "882:2310:171:-:0;;;;;;;;;;;;;;;;;;;;;;;;918:40;;955:3;918:40;;;;;168:25:242;;;156:2;141:18;918:40:171;;;;;;", "linkReferences": {}}, "methodIdentifiers": {"ENTRY_SIZE()": "ad899d12"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"mTokenProofDecoderLib_ChainNotFound\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mTokenProofDecoderLib_InvalidInclusion\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"mTokenProofDecoderLib_InvalidLength\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ENTRY_SIZE\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/libraries/mTokenProofDecoderLib.sol\":\"mTokenProofDecoderLib\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"src/libraries/BytesLib.sol\":{\"keccak256\":\"0xfaf080296887274c71c2f2eb3552c4742c96ff24616c61f8ab98399f30b18ef0\",\"license\":\"Unlicense\",\"urls\":[\"bzz-raw://02dcb01b6c9cf4f158792a15e9b40d046332949cd6b2c1e5e54e474901cf579b\",\"dweb:/ipfs/QmWiBhygucYoY8Uu6WQPao2r64Y25Vmmj3gYyZZ6Wy9wnE\"]},\"src/libraries/mTokenProofDecoderLib.sol\":{\"keccak256\":\"0x00216e7389b2d64450d9d13b648f80e459742e1dd91dec543d415df920f8ce71\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://660deae410969bca243a7b8ad2e6ca6d1cb11d3647af0e227355e5b74f949ea0\",\"dweb:/ipfs/QmUmynqsY1kdEoWHNwqHEVBedepdDBaNHotAP7CCQ7PBzN\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "mTokenProofDecoderLib_ChainNotFound"}, {"inputs": [], "type": "error", "name": "mTokenProofDecoderLib_InvalidInclusion"}, {"inputs": [], "type": "error", "name": "mTokenProofDecoderLib_InvalidLength"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "ENTRY_SIZE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/libraries/mTokenProofDecoderLib.sol": "mTokenProofDecoderLib"}, "evmVersion": "london", "libraries": {}}, "sources": {"src/libraries/BytesLib.sol": {"keccak256": "0xfaf080296887274c71c2f2eb3552c4742c96ff24616c61f8ab98399f30b18ef0", "urls": ["bzz-raw://02dcb01b6c9cf4f158792a15e9b40d046332949cd6b2c1e5e54e474901cf579b", "dweb:/ipfs/QmWiBhygucYoY8Uu6WQPao2r64Y25Vmmj3gYyZZ6Wy9wnE"], "license": "Unlicense"}, "src/libraries/mTokenProofDecoderLib.sol": {"keccak256": "0x00216e7389b2d64450d9d13b648f80e459742e1dd91dec543d415df920f8ce71", "urls": ["bzz-raw://660deae410969bca243a7b8ad2e6ca6d1cb11d3647af0e227355e5b74f949ea0", "dweb:/ipfs/QmUmynqsY1kdEoWHNwqHEVBedepdDBaNHotAP7CCQ7PBzN"], "license": "BSL-1.1"}}, "version": 1}, "id": 171}