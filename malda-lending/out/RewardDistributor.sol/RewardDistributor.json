{"abi": [{"type": "constructor", "inputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "REWARD_INITIAL_INDEX", "inputs": [], "outputs": [{"name": "", "type": "uint224", "internalType": "uint224"}], "stateMutability": "view"}, {"type": "function", "name": "claim", "inputs": [{"name": "holders", "type": "address[]", "internalType": "address[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "getBlockTimestamp", "inputs": [], "outputs": [{"name": "", "type": "uint32", "internalType": "uint32"}], "stateMutability": "view"}, {"type": "function", "name": "getRewardTokens", "inputs": [], "outputs": [{"name": "", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "grant<PERSON>eward", "inputs": [{"name": "token", "type": "address", "internalType": "address"}, {"name": "user", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "initialize", "inputs": [{"name": "_owner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "isRewardToken", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "notifyBorrowIndex", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "notify<PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}, {"name": "borrower", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "notifySupplier", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}, {"name": "supplier", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "notifySupplyIndex", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "operator", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "rewardAccountState", "inputs": [{"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "rewardAccrued", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "rewardMarketState", "inputs": [{"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "supplySpeed", "type": "uint256", "internalType": "uint256"}, {"name": "supplyIndex", "type": "uint224", "internalType": "uint224"}, {"name": "supplyBlock", "type": "uint32", "internalType": "uint32"}, {"name": "borrowSpeed", "type": "uint256", "internalType": "uint256"}, {"name": "borrowIndex", "type": "uint224", "internalType": "uint224"}, {"name": "borrowBlock", "type": "uint32", "internalType": "uint32"}], "stateMutability": "view"}, {"type": "function", "name": "rewardTokens", "inputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "setOperator", "inputs": [{"name": "_operator", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "updateRewardSpeeds", "inputs": [{"name": "rewardToken_", "type": "address", "internalType": "address"}, {"name": "mTokens", "type": "address[]", "internalType": "address[]"}, {"name": "supplySpeeds", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "borrowSpeeds", "type": "uint256[]", "internalType": "uint256[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "whitelistToken", "inputs": [{"name": "rewardToken_", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "BorrowIndexNotified", "inputs": [{"name": "rewardToken", "type": "address", "indexed": true, "internalType": "address"}, {"name": "mToken", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "BorrowSpeedUpdated", "inputs": [{"name": "rewardToken", "type": "address", "indexed": true, "internalType": "address"}, {"name": "mToken", "type": "address", "indexed": true, "internalType": "address"}, {"name": "borrowSpeed", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Initialized", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "OperatorSet", "inputs": [{"name": "oldOperator", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newOperator", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "RewardAccrued", "inputs": [{"name": "rewardToken", "type": "address", "indexed": true, "internalType": "address"}, {"name": "user", "type": "address", "indexed": true, "internalType": "address"}, {"name": "deltaAccrued", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "totalAccrued", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "RewardGranted", "inputs": [{"name": "rewardToken", "type": "address", "indexed": true, "internalType": "address"}, {"name": "user", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "SupplyIndexNotified", "inputs": [{"name": "rewardToken", "type": "address", "indexed": true, "internalType": "address"}, {"name": "mToken", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "SupplySpeedUpdated", "inputs": [{"name": "rewardToken", "type": "address", "indexed": true, "internalType": "address"}, {"name": "mToken", "type": "address", "indexed": true, "internalType": "address"}, {"name": "supplySpeed", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "WhitelistedToken", "inputs": [{"name": "token", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "InvalidInitialization", "inputs": []}, {"type": "error", "name": "NotInitializing", "inputs": []}, {"type": "error", "name": "OwnableInvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ReentrancyGuardReentrantCall", "inputs": []}, {"type": "error", "name": "RewardDistributor_AddressAlreadyRegistered", "inputs": []}, {"type": "error", "name": "RewardDistributor_AddressNotValid", "inputs": []}, {"type": "error", "name": "RewardDistributor_BorrowSpeedArrayLengthMismatch", "inputs": []}, {"type": "error", "name": "RewardDistributor_OnlyOperator", "inputs": []}, {"type": "error", "name": "RewardDistributor_RewardNotValid", "inputs": []}, {"type": "error", "name": "RewardDistributor_SupplySpeedArrayLengthMismatch", "inputs": []}, {"type": "error", "name": "RewardDistributor_TransferFailed", "inputs": []}], "bytecode": {"object": "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", "sourceMap": "1389:12639:193:-:0;;;2902:53;;;;;;;;;-1:-1:-1;2926:22:193;:20;:22::i;:::-;1389:12639;;7711:422:22;8870:21;7900:15;;;;;;;7896:76;;;7938:23;;-1:-1:-1;;;7938:23:22;;;;;;;;;;;7896:76;7985:14;;-1:-1:-1;;;;;7985:14:22;;;:34;7981:146;;8035:33;;-1:-1:-1;;;;;;8035:33:22;-1:-1:-1;;;;;8035:33:22;;;;;8087:29;;158:50:242;;;8087:29:22;;146:2:242;131:18;8087:29:22;;;;;;;7981:146;7760:373;7711:422::o;14:200:242:-;1389:12639:193;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "1389:12639:193:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;5150:208;;;;;;:::i;:::-;;:::i;:::-;;4432:712;;;;;;:::i;:::-;;:::i;3000:249::-;;;;;;:::i;:::-;;:::i;1815:105::-;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;1815:105:193;;;;;-1:-1:-1;;;1815:105:193;;;;;;;;;;;;;;;;;;;;;;;;4392:25:242;;;-1:-1:-1;;;;;4453:32:242;;;4448:2;4433:18;;4426:60;4534:10;4522:23;;;4502:18;;;4495:51;4577:2;4562:18;;4555:34;;;;4626:32;;;4620:3;4605:19;;4598:61;4696:23;4690:3;4675:19;;4668:52;4379:3;4364:19;1815:105:193;;;;;;;;6233:286;;;;;;:::i;:::-;;:::i;1699:23::-;;;;;-1:-1:-1;;;;;1699:23:193;;;;;;-1:-1:-1;;;;;4895:32:242;;;4877:51;;4865:2;4850:18;1699:23:193;4731:203:242;5846:327:193;;;;;;:::i;:::-;;:::i;4049:377::-;;;;;;:::i;:::-;;:::i;3155:101:21:-;;;:::i;3309:197:193:-;;;:::i;:::-;;;5304:10:242;5292:23;;;5274:42;;5262:2;5247:18;3309:197:193;5130:192:242;2178:29:193;;;;;;:::i;:::-;;:::i;1587:51::-;;-1:-1:-1;;;1587:51:193;;;;;-1:-1:-1;;;;;5722:32:242;;;5704:51;;5692:2;5677:18;1587:51:193;5558:203:242;2013:107:193;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;5912:25:242;;;5900:2;5885:18;2013:107:193;5766:177:242;2441:144:21;1313:22;2570:8;-1:-1:-1;;;;;2570:8:21;2441:144;;3821:222:193;;;;;;:::i;:::-;;:::i;2268:45::-;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;6113:14:242;;6106:22;6088:41;;6076:2;6061:18;2268:45:193;5948:187:242;3721:94:193;;;;;;:::i;:::-;;:::i;3566:111::-;;;:::i;:::-;;;;;;;:::i;5459:327::-;;;;;;:::i;:::-;;:::i;6579:286::-;;;;;;:::i;:::-;;:::i;3405:215:21:-;;;;;;:::i;:::-;;:::i;5150:208:193:-;2334:13:21;:11;:13::i;:::-;-1:-1:-1;;;;;5251:20:193;::::1;;::::0;;;:13:::1;:20;::::0;;;;;::::1;;5243:65;;;;-1:-1:-1::0;;;5243:65:193::1;;;;;;;;;;;;5318:33;5331:5;5338:4;5344:6;5318:12;:33::i;:::-;;5150:208:::0;;;:::o;4432:712::-;2334:13:21;:11;:13::i;:::-;-1:-1:-1;;;;;4643:27:193;::::1;;::::0;;;:13:::1;:27;::::0;;;;;::::1;;4635:72;;;;-1:-1:-1::0;;;4635:72:193::1;;;;;;;;;;;;4743:12;:19;4725:7;:14;:37;4717:98;;;;-1:-1:-1::0;;;4717:98:193::1;;;;;;;;;;;;4851:12;:19;4833:7;:14;:37;4825:98;;;;-1:-1:-1::0;;;4825:98:193::1;;;;;;;;;;;;4939:9;4934:204;4958:7;:14;4954:1;:18;4934:204;;;4989:78;5008:12;5022:7;5030:1;5022:10;;;;;;;;:::i;:::-;;;;;;;5034:12;5047:1;5034:15;;;;;;;;:::i;:::-;;;;;;;5051:12;5064:1;5051:15;;;;;;;;:::i;:::-;;;;;;;4989:18;:78::i;:::-;5110:3;;4934:204;;;;4432:712:::0;;;;:::o;3000:249::-;3251:21:24;:19;:21::i;:::-;3085:9:193::1;3080:163;3104:12;:19:::0;3100:23;::::1;3080:163;;;3140:32;3147:12;3160:1;3147:15;;;;;;;;:::i;:::-;;::::0;;;::::1;::::0;;;::::1;::::0;-1:-1:-1;;;;;3147:15:193::1;3164:7:::0;3140:6:::1;:32::i;:::-;3215:3;;3080:163;;;;3293:20:24::0;1805:1;2388:30;3969:23;3716:283;3293:20;3000:249:193;:::o;6233:286::-;2782:8;;-1:-1:-1;;;;;2782:8:193;2768:10;:22;2760:65;;;;-1:-1:-1;;;2760:65:193;;;;;;;;;;;;6337:9:::1;6332:181;6356:12;:19:::0;6352:23;::::1;6332:181;;;6392:50;6408:12;6421:1;6408:15;;;;;;;;:::i;:::-;;::::0;;;::::1;::::0;;;::::1;::::0;-1:-1:-1;;;;;6408:15:193::1;6425:6:::0;6433:8;6392:15:::1;:50::i;:::-;6485:3;;6332:181;;;;6233:286:::0;;:::o;5846:327::-;2782:8;;-1:-1:-1;;;;;2782:8:193;2768:10;:22;2760:65;;;;-1:-1:-1;;;2760:65:193;;;;;;;;;;;;5935:9:::1;5930:237;5954:12;:19:::0;5950:23;::::1;5930:237;;;5990:43;6009:12;6022:1;6009:15;;;;;;;;:::i;:::-;;::::0;;;::::1;::::0;;;::::1;::::0;-1:-1:-1;;;;;6009:15:193::1;6026:6:::0;5990:18:::1;:43::i;:::-;6090:6;-1:-1:-1::0;;;;;6053:44:193::1;6073:12;6086:1;6073:15;;;;;;;;:::i;:::-;;::::0;;;::::1;::::0;;::::1;::::0;6053:44:::1;::::0;-1:-1:-1;;;;;6073:15:193;;::::1;::::0;6053:44:::1;::::0;::::1;6139:3;;5930:237;;;;5846:327:::0;:::o;4049:377::-;2334:13:21;:11;:13::i;:::-;-1:-1:-1;;;;;4130:26:193;::::1;4122:72;;;;-1:-1:-1::0;;;4122:72:193::1;;;;;;;;;;;;-1:-1:-1::0;;;;;4213:27:193;::::1;;::::0;;;:13:::1;:27;::::0;;;;;::::1;;4212:28;4204:83;;;;-1:-1:-1::0;;;4204:83:193::1;;;;;;;;;;;;4298:12;:31:::0;;::::1;::::0;;::::1;::::0;;;;::::1;::::0;;-1:-1:-1;;;;;;4298:31:193::1;-1:-1:-1::0;;;;;4298:31:193;::::1;::::0;;::::1;::::0;;;-1:-1:-1;4339:27:193;;;:13:::1;4298:31;4339:27:::0;;;;;:34;;-1:-1:-1;;4339:34:193::1;::::0;;::::1;::::0;;;4389:30;;4298:31;;4389:30:::1;::::0;::::1;4049:377:::0;:::o;3155:101:21:-;2334:13;:11;:13::i;:::-;3219:30:::1;3246:1;3219:18;:30::i;:::-;3155:101::o:0;3309:197:193:-;3368:6;3441:58;3448:15;3441:58;;;;;;;;;;;;;;;;;:6;:58::i;:::-;3434:65;;3309:197;:::o;2178:29::-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2178:29:193;;-1:-1:-1;2178:29:193;:::o;3821:222::-;2334:13:21;:11;:13::i;:::-;-1:-1:-1;;;;;3898:23:193;::::1;3890:69;;;;-1:-1:-1::0;;;3890:69:193::1;;;;;;;;;;;;3986:8;::::0;;3974:32:::1;::::0;-1:-1:-1;;;;;3974:32:193;;::::1;::::0;3986:8;::::1;::::0;3974:32:::1;::::0;::::1;4016:8;:20:::0;;-1:-1:-1;;;;;;4016:20:193::1;-1:-1:-1::0;;;;;4016:20:193;;;::::1;::::0;;;::::1;::::0;;3821:222::o;3721:94::-;8870:21:22;4302:15;;-1:-1:-1;;;4302:15:22;;;;4301:16;;4348:14;;4158:30;4726:16;;:34;;;;;4746:14;4726:34;4706:54;;4770:17;4790:11;:16;;4805:1;4790:16;:50;;;;-1:-1:-1;4818:4:22;4810:25;:30;4790:50;4770:70;;4856:12;4855:13;:30;;;;;4873:12;4872:13;4855:30;4851:91;;;4908:23;;-1:-1:-1;;;4908:23:22;;;;;;;;;;;4851:91;4951:18;;-1:-1:-1;;4951:18:22;4968:1;4951:18;;;4979:67;;;;5013:22;;-1:-1:-1;;;;5013:22:22;-1:-1:-1;;;5013:22:22;;;4979:67;3786:22:193::1;3801:6;3786:14;:22::i;:::-;5070:14:22::0;5066:101;;;5100:23;;-1:-1:-1;;;;5100:23:22;;;5142:14;;-1:-1:-1;7067:50:242;;5142:14:22;;7055:2:242;7040:18;5142:14:22;;;;;;;5066:101;4092:1081;;;;;3721:94:193;:::o;3566:111::-;3623:16;3658:12;3651:19;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3651:19:193;;;;;;;;;;;;;;;;;;;;;;;3566:111;:::o;5459:327::-;2782:8;;-1:-1:-1;;;;;2782:8:193;2768:10;:22;2760:65;;;;-1:-1:-1;;;2760:65:193;;;;;;;;;;;;5548:9:::1;5543:237;5567:12;:19:::0;5563:23;::::1;5543:237;;;5603:43;5622:12;5635:1;5622:15;;;;;;;;:::i;:::-;;::::0;;;::::1;::::0;;;::::1;::::0;-1:-1:-1;;;;;5622:15:193::1;5639:6:::0;5603:18:::1;:43::i;:::-;5703:6;-1:-1:-1::0;;;;;5666:44:193::1;5686:12;5699:1;5686:15;;;;;;;;:::i;:::-;;::::0;;;::::1;::::0;;::::1;::::0;5666:44:::1;::::0;-1:-1:-1;;;;;5686:15:193;;::::1;::::0;5666:44:::1;::::0;::::1;5752:3;;5543:237;;6579:286:::0;2782:8;;-1:-1:-1;;;;;2782:8:193;2768:10;:22;2760:65;;;;-1:-1:-1;;;2760:65:193;;;;;;;;;;;;6683:9:::1;6678:181;6702:12;:19:::0;6698:23;::::1;6678:181;;;6738:50;6754:12;6767:1;6754:15;;;;;;;;:::i;:::-;;::::0;;;::::1;::::0;;;::::1;::::0;-1:-1:-1;;;;;6754:15:193::1;6771:6:::0;6779:8;6738:15:::1;:50::i;:::-;6831:3;;6678:181;;3405:215:21::0;2334:13;:11;:13::i;:::-;-1:-1:-1;;;;;3489:22:21;::::1;3485:91;;3534:31;::::0;-1:-1:-1;;;3534:31:21;;3562:1:::1;3534:31;::::0;::::1;4877:51:242::0;4850:18;;3534:31:21::1;;;;;;;;3485:91;3585:28;3604:8;3585:18;:28::i;2658:162::-:0;966:10:23;2717:7:21;1313:22;2570:8;-1:-1:-1;;;;;2570:8:21;;2441:144;2717:7;-1:-1:-1;;;;;2717:23:21;;2713:101;;2763:40;;-1:-1:-1;;;2763:40:21;;966:10:23;2763:40:21;;;4877:51:242;4850:18;;2763:40:21;4731:203:242;13567:459:193;13691:39;;-1:-1:-1;;;13691:39:193;;13724:4;13691:39;;;4877:51:242;13652:7:193;;;;-1:-1:-1;;;;;13691:24:193;;;;;4850:18:242;;13691:39:193;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;13671:59;;13753:1;13744:6;:10;:33;;;;;13768:9;13758:6;:19;;13744:33;13740:257;;;13807:37;;-1:-1:-1;;;13807:37:193;;-1:-1:-1;;;;;7509:32:242;;;13807:37:193;;;7491:51:242;7558:18;;;7551:34;;;13793:11:193;;13807:23;;;;;;7464:18:242;;13807:37:193;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;13793:51;;13866:6;13858:51;;;;-1:-1:-1;;;13858:51:193;;;;;;;;;;;;13950:4;-1:-1:-1;;;;;13929:34:193;13943:5;-1:-1:-1;;;;;13929:34:193;;13956:6;13929:34;;;;5912:25:242;;5900:2;5885:18;;5766:177;13929:34:193;;;;;;;;13985:1;13978:8;;;;;;13740:257;14013:6;14006:13;;;13567:459;;;;;;:::o;6911:1097::-;-1:-1:-1;;;;;7111:30:193;;;7048:60;7111:30;;;:17;:30;;;;;;;;:38;;;;;;;;;7164:23;;:38;;7160:416;;7222:23;;;;-1:-1:-1;;;;;7222:23:193;;:28;7218:113;;7270:23;;;:46;;-1:-1:-1;;;;;;7270:46:193;-1:-1:-1;;;7270:46:193;;;7218:113;7345:39;7364:11;7377:6;7345:18;:39::i;:::-;7436:6;-1:-1:-1;;;;;7403:40:193;7423:11;-1:-1:-1;;;;;7403:40:193;;;;;;;;;;;7457:37;;;7513:52;;5912:25:242;;;-1:-1:-1;;;;;7513:52:193;;;;;;;;;;5900:2:242;5885:18;7513:52:193;;;;;;;7160:416;7617:11;7590;:23;;;:38;7586:416;;7648:23;;;;-1:-1:-1;;;;;7648:23:193;;:28;7644:113;;7696:23;;;:46;;-1:-1:-1;;;;;;7696:46:193;-1:-1:-1;;;7696:46:193;;;7644:113;7771:39;7790:11;7803:6;7771:18;:39::i;:::-;7862:6;-1:-1:-1;;;;;7829:40:193;7849:11;-1:-1:-1;;;;;7829:40:193;;;;;;;;;;;7909:11;7883;:23;;:37;;;;7971:6;-1:-1:-1;;;;;7939:52:193;7958:11;-1:-1:-1;;;;;7939:52:193;;7979:11;7939:52;;;;5912:25:242;;5900:2;5885:18;;5766:177;7939:52:193;;;;;;;;7038:970;6911:1097;;;;:::o;3326:384:24:-;2388:30;3526:9;;-1:-1:-1;;3526:20:24;3522:88;;3569:30;;-1:-1:-1;;;3569:30:24;;;;;;;;;;;3522:88;1847:1;3684:19;;3326:384::o;13130:431:193:-;13217:9;13212:343;13236:7;:14;13232:1;:18;13212:343;;;-1:-1:-1;;;;;13332:31:193;;13267:62;13332:31;;;:18;:31;;;;;13364:10;;13267:62;;13364:7;;13372:1;;13364:10;;;;;;:::i;:::-;;;;;;;-1:-1:-1;;;;;13332:43:193;-1:-1:-1;;;;;13332:43:193;;;;;;;;;;;;13267:108;;13419:65;13432:11;13445:7;13453:1;13445:10;;;;;;;;:::i;:::-;;;;;;;13457:12;:26;;;13419:12;:65::i;:::-;13390:26;;;;:94;13527:3;;13212:343;;10140:1298;-1:-1:-1;;;;;10301:30:193;;;10238:60;10301:30;;;:17;:30;;;;;;;;:38;;;;;;;;;;;;10414:31;;;:18;:31;;;;;:41;;;;;;;;;;;10488:23;;;;10545:34;;;;;;;;;;;;-1:-1:-1;;;;;10488:23:193;;;10687:48;;;;10301:38;;10414:41;;10488:23;10750:18;;:57;;;;-1:-1:-1;;;;10772:35:193;;;10750:57;10746:124;;;-1:-1:-1;;;;10746:124:193;10963:24;10990:52;;;;;;;;11008:32;11013:11;11026:13;11008:4;:32::i;:::-;10990:52;;11078:35;;-1:-1:-1;;;11078:35:193;;-1:-1:-1;;;;;4895:32:242;;;11078:35:193;;;4877:51:242;10963:79:193;;-1:-1:-1;11053:22:193;;11078:25;;;;;4850:18:242;;11078:35:193;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;11053:60;;11193:21;11217:32;11222:14;11238:10;11217:4;:32::i;:::-;11193:56;;11289:47;11294:12;:26;;;11322:13;11289:4;:47::i;:::-;11260:26;;;:76;;;11352:79;;;8052:25:242;;;8108:2;8093:18;;8086:34;;;;-1:-1:-1;;;;;11352:79:193;;;;;;;;;;8025:18:242;11352:79:193;;;;;;;10228:1210;;;;;;;10140:1298;;;:::o;9020:1114::-;9103:28;9134:46;;;;;;;;9157:6;-1:-1:-1;;;;;9149:27:193;;:29;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;9134:46;;-1:-1:-1;;;;;9254:30:193;;;9191:60;9254:30;;;:17;:30;;;;;;;;:38;;;;;;;;;;;9103:77;;-1:-1:-1;9327:19:193;:17;:19::i;:::-;9378:23;;;;9303:43;;-1:-1:-1;9378:23:193;-1:-1:-1;;;9378:23:193;;;;;9361:40;;;;9357:771;;;9421:23;;;;:27;9417:646;;9507:23;;;;9468:19;;9490:40;;-1:-1:-1;;;9507:23:193;;;;9490:14;:40;:::i;:::-;9468:62;;;;9548:20;9571:55;9584:6;-1:-1:-1;;;;;9576:28:193;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;9608:17;9571:4;:55::i;:::-;9548:78;;9644:15;9662:42;9667:11;9680;:23;;;9662:4;:42::i;:::-;9644:60;;9722:19;9759:1;9744:12;:16;:74;;9797:21;;;;;;;;9815:1;9797:21;;;9744:74;;;9763:31;9772:7;9781:12;9763:8;:31::i;:::-;9896:43;;;;;;;;;9914:23;;;;-1:-1:-1;;;;;9914:23:193;9896:43;;9722:96;;-1:-1:-1;9862:186:193;;9891:56;;9722:96;9891:4;:56::i;:::-;:65;9862:186;;;;;;;;;;;;;;;;;:7;:186::i;:::-;9836:23;;;:212;;-1:-1:-1;;;;;;9836:212:193;-1:-1:-1;;;;;9836:212:193;;;;;;;;;;-1:-1:-1;;;;9417:646:193;10077:23;;;:40;;;;;-1:-1:-1;;;10077:40:193;-1:-1:-1;;;;;10077:40:193;;;;;;9093:1041;;;9020:1114;;:::o;3774:248:21:-;1313:22;3923:8;;-1:-1:-1;;;;;;3941:19:21;;-1:-1:-1;;;;;3941:19:21;;;;;;;;3975:40;;3923:8;;;;;3975:40;;3847:24;;3975:40;3837:185;;3774:248;:::o;3470:163:195:-;3548:6;3587:12;3578:7;3574:11;;3566:34;;;;-1:-1:-1;;;3566:34:195;;;;;;;;:::i;:::-;;3624:1;3610:16;;3470:163;;;;;:::o;1847:127:21:-;6931:20:22;:18;:20::i;:::-;1929:38:21::1;1954:12;1929:24;:38::i;8014:1000:193:-:0;-1:-1:-1;;;;;8160:30:193;;;8097:60;8160:30;;;:17;:30;;;;;;;;:38;;;;;;;;;;;;8233:19;:17;:19::i;:::-;8284:23;;;;8209:43;;-1:-1:-1;8284:23:193;-1:-1:-1;;;8284:23:193;;;;;8267:40;;;;8263:745;;;8327:23;;:27;8323:620;;8413:23;;;;8374:19;;8396:40;;-1:-1:-1;;;8413:23:193;;;;8396:14;:40;:::i;:::-;8374:62;;;;8454:20;8485:6;-1:-1:-1;;;;;8477:27:193;;:29;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;8454:52;;8524:15;8542:42;8547:11;8560;:23;;;8542:4;:42::i;:::-;8524:60;;8602:19;8639:1;8624:12;:16;:74;;8677:21;;;;;;;;8695:1;8677:21;;;8624:74;;;8643:31;8652:7;8661:12;8643:8;:31::i;:::-;8776:43;;;;;;;;;8794:23;;;;-1:-1:-1;;;;;8794:23:193;8776:43;;8602:96;;-1:-1:-1;8742:186:193;;8771:56;;8602:96;8771:4;:56::i;8742:186::-;8716:23;;;:212;;-1:-1:-1;;;;;;8716:212:193;-1:-1:-1;;;;;8716:212:193;;;;;;;;;;-1:-1:-1;;;;8323:620:193;8957:23;;;:40;;;;;-1:-1:-1;;;8957:40:193;-1:-1:-1;;;;;8957:40:193;;;;;;8087:927;;8014:1000;;:::o;11444:1680::-;11542:28;11573:46;;;;;;;;11596:6;-1:-1:-1;;;;;11588:27:193;;:29;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;11573:46;;-1:-1:-1;;;;;11693:30:193;;;11630:60;11693:30;;;:17;:30;;;;;;;;:38;;;;;;;;;;;;11806:31;;;:18;:31;;;;;:41;;;;;;;;;;;11880:23;;;;11937:34;;;:26;;;:34;;;;;;;;-1:-1:-1;;;;;11880:23:193;;;12080:48;;;;11542:77;;-1:-1:-1;11693:38:193;;11806:41;;12143:18;;:57;;;;-1:-1:-1;;;;12165:35:193;;;12143:57;12139:369;;;-1:-1:-1;;;;12139:369:193;12608:24;12635:52;;;;;;;;12653:32;12658:11;12671:13;12653:4;:32::i;:::-;12635:52;;12728:45;;-1:-1:-1;;;12728:45:193;;-1:-1:-1;;;;;4895:32:242;;;12728:45:193;;;4877:51:242;12608:79:193;;-1:-1:-1;12698:22:193;;12723:70;;12728:35;;;;;;4850:18:242;;12728:45:193;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;12775:17;12723:4;:70::i;:::-;12698:95;;12879:21;12903:32;12908:14;12924:10;12903:4;:32::i;:::-;12879:56;;12975:47;12980:12;:26;;;13008:13;12975:4;:47::i;:::-;12946:26;;;:76;;;13038:79;;;8052:25:242;;;8108:2;8093:18;;8086:34;;;;-1:-1:-1;;;;;13038:79:193;;;;;;;;;;8025:18:242;13038:79:193;;;;;;;11532:1592;;;;;;;;11444:1680;;;:::o;4374:97:195:-;4433:7;4459:5;4463:1;4459;:5;:::i;5238:131::-;5303:7;-1:-1:-1;;;5329:19:195;5334:1;5337;:10;;;5329:4;:19::i;:::-;:33;;;;:::i;3955:97::-;4014:7;4040:5;4044:1;4040;:5;:::i;5786:130::-;5848:7;5874:35;5879:17;5884:1;1224:4;5879;:17::i;:::-;5898:10;;5874:4;:35::i;5375:97::-;5434:7;5460:5;5464:1;5460;:5;:::i;6499:151::-;-1:-1:-1;;;;;;;;;;;;6594:49:195;;;;;;;;6612:29;6617:20;6622:1;-1:-1:-1;;;6617:4:195;:20::i;:::-;6639:1;6612:4;:29::i;:::-;6594:49;;6587:56;6499:151;-1:-1:-1;;;6499:151:195:o;3791:158::-;-1:-1:-1;;;;;;;;;;;;3894:48:195;;;;;;;;3912:28;3917:1;:10;;;3929:1;:10;;;3912:4;:28::i;3297:167::-;3376:7;3417:12;-1:-1:-1;;;3403:12:195;;3395:35;;;;-1:-1:-1;;;3395:35:195;;;;;;;;:::i;7084:141:22:-;8870:21;8560:40;-1:-1:-1;;;8560:40:22;;;;7146:73;;7191:17;;-1:-1:-1;;;7191:17:22;;;;;;;;;;;1980:235:21;6931:20:22;:18;:20::i;6396:97:195:-;6455:7;6481:5;6485:1;6481;:5;:::i;14:173:242:-;82:20;;-1:-1:-1;;;;;131:31:242;;121:42;;111:70;;177:1;174;167:12;111:70;14:173;;;:::o;192:374::-;269:6;277;285;338:2;326:9;317:7;313:23;309:32;306:52;;;354:1;351;344:12;306:52;377:29;396:9;377:29;:::i;:::-;367:39;;425:38;459:2;448:9;444:18;425:38;:::i;:::-;192:374;;415:48;;-1:-1:-1;;;532:2:242;517:18;;;;504:32;;192:374::o;571:127::-;632:10;627:3;623:20;620:1;613:31;663:4;660:1;653:15;687:4;684:1;677:15;703:275;774:2;768:9;839:2;820:13;;-1:-1:-1;;816:27:242;804:40;;874:18;859:34;;895:22;;;856:62;853:88;;;921:18;;:::i;:::-;957:2;950:22;703:275;;-1:-1:-1;703:275:242:o;983:183::-;1043:4;1076:18;1068:6;1065:30;1062:56;;;1098:18;;:::i;:::-;-1:-1:-1;1143:1:242;1139:14;1155:4;1135:25;;983:183::o;1171:675::-;1225:5;1278:3;1271:4;1263:6;1259:17;1255:27;1245:55;;1296:1;1293;1286:12;1245:55;1336:6;1323:20;1363:64;1379:47;1419:6;1379:47;:::i;:::-;1363:64;:::i;:::-;1451:3;1475:6;1470:3;1463:19;1507:4;1502:3;1498:14;1491:21;;1568:4;1558:6;1555:1;1551:14;1543:6;1539:27;1535:38;1521:52;;1596:3;1588:6;1585:15;1582:35;;;1613:1;1610;1603:12;1582:35;1649:4;1641:6;1637:17;1663:152;1679:6;1674:3;1671:15;1663:152;;;1747:23;1766:3;1747:23;:::i;:::-;1735:36;;1800:4;1791:14;;;;1696;1663:152;;;-1:-1:-1;1833:7:242;1171:675;-1:-1:-1;;;;;1171:675:242:o;1851:723::-;1905:5;1958:3;1951:4;1943:6;1939:17;1935:27;1925:55;;1976:1;1973;1966:12;1925:55;2016:6;2003:20;2043:64;2059:47;2099:6;2059:47;:::i;2043:64::-;2131:3;2155:6;2150:3;2143:19;2187:4;2182:3;2178:14;2171:21;;2248:4;2238:6;2235:1;2231:14;2223:6;2219:27;2215:38;2201:52;;2276:3;2268:6;2265:15;2262:35;;;2293:1;2290;2283:12;2262:35;2329:4;2321:6;2317:17;2343:200;2359:6;2354:3;2351:15;2343:200;;;2451:17;;2481:18;;2528:4;2519:14;;;;2376;2343:200;;2579:907;2740:6;2748;2756;2764;2817:3;2805:9;2796:7;2792:23;2788:33;2785:53;;;2834:1;2831;2824:12;2785:53;2857:29;2876:9;2857:29;:::i;:::-;2847:39;;2937:2;2926:9;2922:18;2909:32;2964:18;2956:6;2953:30;2950:50;;;2996:1;2993;2986:12;2950:50;3019:61;3072:7;3063:6;3052:9;3048:22;3019:61;:::i;:::-;3009:71;;;3133:2;3122:9;3118:18;3105:32;3162:18;3152:8;3149:32;3146:52;;;3194:1;3191;3184:12;3146:52;3217:63;3272:7;3261:8;3250:9;3246:24;3217:63;:::i;:::-;3207:73;;;3333:2;3322:9;3318:18;3305:32;3362:18;3352:8;3349:32;3346:52;;;3394:1;3391;3384:12;3346:52;3417:63;3472:7;3461:8;3450:9;3446:24;3417:63;:::i;:::-;3407:73;;;2579:907;;;;;;;:::o;3491:348::-;3575:6;3628:2;3616:9;3607:7;3603:23;3599:32;3596:52;;;3644:1;3641;3634:12;3596:52;3684:9;3671:23;3717:18;3709:6;3706:30;3703:50;;;3749:1;3746;3739:12;3703:50;3772:61;3825:7;3816:6;3805:9;3801:22;3772:61;:::i;:::-;3762:71;3491:348;-1:-1:-1;;;;3491:348:242:o;3844:260::-;3912:6;3920;3973:2;3961:9;3952:7;3948:23;3944:32;3941:52;;;3989:1;3986;3979:12;3941:52;4012:29;4031:9;4012:29;:::i;:::-;4002:39;;4060:38;4094:2;4083:9;4079:18;4060:38;:::i;:::-;4050:48;;3844:260;;;;;:::o;4939:186::-;4998:6;5051:2;5039:9;5030:7;5026:23;5022:32;5019:52;;;5067:1;5064;5057:12;5019:52;5090:29;5109:9;5090:29;:::i;5327:226::-;5386:6;5439:2;5427:9;5418:7;5414:23;5410:32;5407:52;;;5455:1;5452;5445:12;5407:52;-1:-1:-1;5500:23:242;;5327:226;-1:-1:-1;5327:226:242:o;6140:637::-;6330:2;6342:21;;;6412:13;;6315:18;;;6434:22;;;6282:4;;6513:15;;;6487:2;6472:18;;;6282:4;6556:195;6570:6;6567:1;6564:13;6556:195;;;6635:13;;-1:-1:-1;;;;;6631:39:242;6619:52;;6700:2;6726:15;;;;6691:12;;;;6667:1;6585:9;6556:195;;;-1:-1:-1;6768:3:242;;6140:637;-1:-1:-1;;;;;6140:637:242:o;6782:127::-;6843:10;6838:3;6834:20;6831:1;6824:31;6874:4;6871:1;6864:15;6898:4;6895:1;6888:15;7128:184;7198:6;7251:2;7239:9;7230:7;7226:23;7222:32;7219:52;;;7267:1;7264;7257:12;7219:52;-1:-1:-1;7290:16:242;;7128:184;-1:-1:-1;7128:184:242:o;7596:277::-;7663:6;7716:2;7704:9;7695:7;7691:23;7687:32;7684:52;;;7732:1;7729;7722:12;7684:52;7764:9;7758:16;7817:5;7810:13;7803:21;7796:5;7793:32;7783:60;;7839:1;7836;7829:12;8131:127;8192:10;8187:3;8183:20;8180:1;8173:31;8223:4;8220:1;8213:15;8247:4;8244:1;8237:15;8263:170;8360:10;8353:18;;;8333;;;8329:43;;8384:20;;8381:46;;;8407:18;;:::i;8438:527::-;8587:2;8576:9;8569:21;8550:4;8619:6;8613:13;8662:6;8657:2;8646:9;8642:18;8635:34;8687:1;8697:140;8711:6;8708:1;8705:13;8697:140;;;8822:2;8806:14;;;8802:23;;8796:30;8791:2;8772:17;;;8768:26;8761:66;8726:10;8697:140;;;8701:3;8886:1;8881:2;8872:6;8861:9;8857:22;8853:31;8846:42;8956:2;8949;8945:7;8940:2;8932:6;8928:15;8924:29;8913:9;8909:45;8905:54;8897:62;;;8438:527;;;;:::o;8970:128::-;9037:9;;;9058:11;;;9055:37;;;9072:18;;:::i;9103:217::-;9143:1;9169;9159:132;;9213:10;9208:3;9204:20;9201:1;9194:31;9248:4;9245:1;9238:15;9276:4;9273:1;9266:15;9159:132;-1:-1:-1;9305:9:242;;9103:217::o;9325:125::-;9390:9;;;9411:10;;;9408:36;;;9424:18;;:::i;9455:168::-;9528:9;;;9559;;9576:15;;;9570:22;;9556:37;9546:71;;9597:18;;:::i", "linkReferences": {}}, "methodIdentifiers": {"REWARD_INITIAL_INDEX()": "8021a9fc", "claim(address[])": "318d9e5d", "getBlockTimestamp()": "796b89b9", "getRewardTokens()": "c4f59f9b", "grantReward(address,address,uint256)": "02fef78b", "initialize(address)": "c4d66de8", "isRewardToken(address)": "b5fd73f8", "notifyBorrowIndex(address)": "5ce65fe9", "notifyBorrower(address,address)": "eb8f2806", "notifySupplier(address,address)": "45a49d3c", "notifySupplyIndex(address)": "e86b2fbe", "operator()": "570ca735", "owner()": "8da5cb5b", "renounceOwnership()": "715018a6", "rewardAccountState(address,address)": "831e8dec", "rewardMarketState(address,address)": "32894a6d", "rewardTokens(uint256)": "7bb7bed1", "setOperator(address)": "b3ab15fb", "transferOwnership(address)": "f2fde38b", "updateRewardSpeeds(address,address[],uint256[],uint256[])": "13523d99", "whitelistToken(address)": "6247f6f2"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"InvalidInitialization\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotInitializing\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"OwnableInvalidOwner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"OwnableUnauthorizedAccount\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ReentrancyGuardReentrantCall\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"RewardDistributor_AddressAlreadyRegistered\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"RewardDistributor_AddressNotValid\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"RewardDistributor_BorrowSpeedArrayLengthMismatch\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"RewardDistributor_OnlyOperator\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"RewardDistributor_RewardNotValid\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"RewardDistributor_SupplySpeedArrayLengthMismatch\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"RewardDistributor_TransferFailed\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"rewardToken\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"}],\"name\":\"BorrowIndexNotified\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"rewardToken\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"borrowSpeed\",\"type\":\"uint256\"}],\"name\":\"BorrowSpeedUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"version\",\"type\":\"uint64\"}],\"name\":\"Initialized\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"oldOperator\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOperator\",\"type\":\"address\"}],\"name\":\"OperatorSet\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"rewardToken\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"deltaAccrued\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"totalAccrued\",\"type\":\"uint256\"}],\"name\":\"RewardAccrued\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"rewardToken\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"RewardGranted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"rewardToken\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"}],\"name\":\"SupplyIndexNotified\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"rewardToken\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"supplySpeed\",\"type\":\"uint256\"}],\"name\":\"SupplySpeedUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"WhitelistedToken\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"REWARD_INITIAL_INDEX\",\"outputs\":[{\"internalType\":\"uint224\",\"name\":\"\",\"type\":\"uint224\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"holders\",\"type\":\"address[]\"}],\"name\":\"claim\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getBlockTimestamp\",\"outputs\":[{\"internalType\":\"uint32\",\"name\":\"\",\"type\":\"uint32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getRewardTokens\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"grantReward\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_owner\",\"type\":\"address\"}],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"isRewardToken\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"}],\"name\":\"notifyBorrowIndex\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"}],\"name\":\"notifyBorrower\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"supplier\",\"type\":\"address\"}],\"name\":\"notifySupplier\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"}],\"name\":\"notifySupplyIndex\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"operator\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"rewardAccountState\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"rewardAccrued\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"rewardMarketState\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"supplySpeed\",\"type\":\"uint256\"},{\"internalType\":\"uint224\",\"name\":\"supplyIndex\",\"type\":\"uint224\"},{\"internalType\":\"uint32\",\"name\":\"supplyBlock\",\"type\":\"uint32\"},{\"internalType\":\"uint256\",\"name\":\"borrowSpeed\",\"type\":\"uint256\"},{\"internalType\":\"uint224\",\"name\":\"borrowIndex\",\"type\":\"uint224\"},{\"internalType\":\"uint32\",\"name\":\"borrowBlock\",\"type\":\"uint32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"rewardTokens\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_operator\",\"type\":\"address\"}],\"name\":\"setOperator\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"rewardToken_\",\"type\":\"address\"},{\"internalType\":\"address[]\",\"name\":\"mTokens\",\"type\":\"address[]\"},{\"internalType\":\"uint256[]\",\"name\":\"supplySpeeds\",\"type\":\"uint256[]\"},{\"internalType\":\"uint256[]\",\"name\":\"borrowSpeeds\",\"type\":\"uint256[]\"}],\"name\":\"updateRewardSpeeds\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"rewardToken_\",\"type\":\"address\"}],\"name\":\"whitelistToken\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"InvalidInitialization()\":[{\"details\":\"The contract is already initialized.\"}],\"NotInitializing()\":[{\"details\":\"The contract is not initializing.\"}],\"OwnableInvalidOwner(address)\":[{\"details\":\"The owner is not a valid owner account. (eg. `address(0)`)\"}],\"OwnableUnauthorizedAccount(address)\":[{\"details\":\"The caller account is not authorized to perform an operation.\"}],\"ReentrancyGuardReentrantCall()\":[{\"details\":\"Unauthorized reentrant call.\"}]},\"events\":{\"Initialized(uint64)\":{\"details\":\"Triggered when the contract has been initialized or reinitialized.\"}},\"kind\":\"dev\",\"methods\":{\"claim(address[])\":{\"params\":{\"holders\":\"the accounts to claim for\"}},\"constructor\":{\"custom:oz-upgrades-unsafe-allow\":\"constructor\"},\"owner()\":{\"details\":\"Returns the address of the current owner.\"},\"renounceOwnership()\":{\"details\":\"Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner.\"},\"transferOwnership(address)\":{\"details\":\"Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner.\"}},\"stateVariables\":{\"isRewardToken\":{\"params\":{\"_token\":\"the token to check for\"}}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"claim(address[])\":{\"notice\":\"Claim tokens for `holders\"},\"getBlockTimestamp()\":{\"notice\":\"Get block timestamp\"},\"getRewardTokens()\":{\"notice\":\"Added reward tokens\"},\"isRewardToken(address)\":{\"notice\":\"Flag to check if reward token added before\"},\"notifyBorrowIndex(address)\":{\"notice\":\"Notifies borrow index\"},\"notifyBorrower(address,address)\":{\"notice\":\"Notifies borrower\"},\"notifySupplier(address,address)\":{\"notice\":\"Notifies supplier\"},\"notifySupplyIndex(address)\":{\"notice\":\"Notifies supply index\"},\"operator()\":{\"notice\":\"The operator that rewards are distributed to\"},\"rewardAccountState(address,address)\":{\"notice\":\"The Reward state for each reward token for each account\"},\"rewardMarketState(address,address)\":{\"notice\":\"The Reward state for each reward token for each market\"},\"rewardTokens(uint256)\":{\"notice\":\"Added reward tokens\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/rewards/RewardDistributor.sol\":\"RewardDistributor\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol\":{\"keccak256\":\"0xc163fcf9bb10138631a9ba5564df1fa25db9adff73bd9ee868a8ae1858fe093a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9706d43a0124053d9880f6e31a59f31bc0a6a3dc1acd66ce0a16e1111658c5f6\",\"dweb:/ipfs/QmUFmfowzkRwGtDu36cXV9SPTBHJ3n7dG9xQiK5B28jTf2\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol\":{\"keccak256\":\"0x631188737069917d2f909d29ce62c4d48611d326686ba6683e26b72a23bfac0b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7a61054ae84cd6c4d04c0c4450ba1d6de41e27e0a2c4f1bcdf58f796b401c609\",\"dweb:/ipfs/QmUvtdp7X1mRVyC3CsHrtPbgoqWaXHp3S1ZR24tpAQYJWM\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol\":{\"keccak256\":\"0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9\",\"dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/ReentrancyGuardUpgradeable.sol\":{\"keccak256\":\"0xb44e086e941292cdc7f440de51478493894ef0b1aeccb0c4047445919f667f74\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://942dad22fbdc1669f025540ba63aa3ccfad5f8458fc5d4525b31ebf272e7af45\",\"dweb:/ipfs/Qmdo4X2M82aM3AMoW2kf2jhYkSCyC4T1pHNd6obdsDFnAB\"]},\"src/interfaces/IRewardDistributor.sol\":{\"keccak256\":\"0xd39c4db6e2a85e90db82e20685d4b235966d993c3407e1c0fb52add6421b86df\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://e865fe1569ec1ac79cd924a9aa2e2b43754555e510526f6cd030395e9923f01d\",\"dweb:/ipfs/QmXcshgzCxLKBZJYGuQviEamruhmwXWNJ3ZYLxazWBahuc\"]},\"src/interfaces/IRoles.sol\":{\"keccak256\":\"0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c\",\"dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj\"]},\"src/interfaces/ImToken.sol\":{\"keccak256\":\"0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36\",\"dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL\"]},\"src/rewards/RewardDistributor.sol\":{\"keccak256\":\"0x8d5c3e5e5050121d4f1310479a2cadde7dc97ff8a57115021cafb2032aaf50c2\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://e200d18803bb0680b3007c79c23a8a7e31f054ce6e4f36c376f7b43419679322\",\"dweb:/ipfs/QmV7CRA5HSB89fwCVF7VWGArZnWoH2BB1heXu9SaMkbL9H\"]},\"src/utils/ExponentialNoError.sol\":{\"keccak256\":\"0xa97178117d8c32059d55dd2224e5514c54b31f896e565c3834cab3d7502b1bb6\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://1cecc0713709f57a5bb7f64d2fa06b9437a42752fa3fed5d3c0b19d82aa56ef4\",\"dweb:/ipfs/QmQqBCZkzmZxomXLr3b1tHUV55n8EPk3wAUsXgojYSpR41\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "type": "error", "name": "InvalidInitialization"}, {"inputs": [], "type": "error", "name": "NotInitializing"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "type": "error", "name": "OwnableInvalidOwner"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "type": "error", "name": "OwnableUnauthorizedAccount"}, {"inputs": [], "type": "error", "name": "ReentrancyGuardReentrantCall"}, {"inputs": [], "type": "error", "name": "RewardDistributor_AddressAlreadyRegistered"}, {"inputs": [], "type": "error", "name": "RewardDistributor_AddressNotValid"}, {"inputs": [], "type": "error", "name": "RewardDistributor_BorrowSpeedArrayLengthMismatch"}, {"inputs": [], "type": "error", "name": "RewardDistributor_OnlyOperator"}, {"inputs": [], "type": "error", "name": "RewardDistributor_RewardNotValid"}, {"inputs": [], "type": "error", "name": "RewardDistributor_SupplySpeedArrayLengthMismatch"}, {"inputs": [], "type": "error", "name": "RewardDistributor_TransferFailed"}, {"inputs": [{"internalType": "address", "name": "rewardToken", "type": "address", "indexed": true}, {"internalType": "address", "name": "mToken", "type": "address", "indexed": true}], "type": "event", "name": "BorrowIndexNotified", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "rewardToken", "type": "address", "indexed": true}, {"internalType": "address", "name": "mToken", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "borrowSpeed", "type": "uint256", "indexed": false}], "type": "event", "name": "BorrowSpeedUpdated", "anonymous": false}, {"inputs": [{"internalType": "uint64", "name": "version", "type": "uint64", "indexed": false}], "type": "event", "name": "Initialized", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "oldOperator", "type": "address", "indexed": true}, {"internalType": "address", "name": "newOperator", "type": "address", "indexed": true}], "type": "event", "name": "OperatorSet", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "previousOwner", "type": "address", "indexed": true}, {"internalType": "address", "name": "new<PERSON>wner", "type": "address", "indexed": true}], "type": "event", "name": "OwnershipTransferred", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "rewardToken", "type": "address", "indexed": true}, {"internalType": "address", "name": "user", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "deltaAccrued", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "totalAccrued", "type": "uint256", "indexed": false}], "type": "event", "name": "RewardAccrued", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "rewardToken", "type": "address", "indexed": true}, {"internalType": "address", "name": "user", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}], "type": "event", "name": "RewardGranted", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "rewardToken", "type": "address", "indexed": true}, {"internalType": "address", "name": "mToken", "type": "address", "indexed": true}], "type": "event", "name": "SupplyIndexNotified", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "rewardToken", "type": "address", "indexed": true}, {"internalType": "address", "name": "mToken", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "supplySpeed", "type": "uint256", "indexed": false}], "type": "event", "name": "SupplySpeedUpdated", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "token", "type": "address", "indexed": true}], "type": "event", "name": "WhitelistedToken", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "REWARD_INITIAL_INDEX", "outputs": [{"internalType": "uint224", "name": "", "type": "uint224"}]}, {"inputs": [{"internalType": "address[]", "name": "holders", "type": "address[]"}], "stateMutability": "nonpayable", "type": "function", "name": "claim"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getBlockTimestamp", "outputs": [{"internalType": "uint32", "name": "", "type": "uint32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getRewardTokens", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}]}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "grant<PERSON>eward"}, {"inputs": [{"internalType": "address", "name": "_owner", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "initialize"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isRewardToken", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "notifyBorrowIndex"}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}, {"internalType": "address", "name": "borrower", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "notify<PERSON><PERSON><PERSON><PERSON>"}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}, {"internalType": "address", "name": "supplier", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "notifySupplier"}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "notifySupplyIndex"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "operator", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "renounceOwnership"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "rewardAccountState", "outputs": [{"internalType": "uint256", "name": "rewardAccrued", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "rewardMarketState", "outputs": [{"internalType": "uint256", "name": "supplySpeed", "type": "uint256"}, {"internalType": "uint224", "name": "supplyIndex", "type": "uint224"}, {"internalType": "uint32", "name": "supplyBlock", "type": "uint32"}, {"internalType": "uint256", "name": "borrowSpeed", "type": "uint256"}, {"internalType": "uint224", "name": "borrowIndex", "type": "uint224"}, {"internalType": "uint32", "name": "borrowBlock", "type": "uint32"}]}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "rewardTokens", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "_operator", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setOperator"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "transferOwnership"}, {"inputs": [{"internalType": "address", "name": "rewardToken_", "type": "address"}, {"internalType": "address[]", "name": "mTokens", "type": "address[]"}, {"internalType": "uint256[]", "name": "supplySpeeds", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "borrowSpeeds", "type": "uint256[]"}], "stateMutability": "nonpayable", "type": "function", "name": "updateRewardSpeeds"}, {"inputs": [{"internalType": "address", "name": "rewardToken_", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "whitelistToken"}], "devdoc": {"kind": "dev", "methods": {"claim(address[])": {"params": {"holders": "the accounts to claim for"}}, "constructor": {"custom:oz-upgrades-unsafe-allow": "constructor"}, "owner()": {"details": "Returns the address of the current owner."}, "renounceOwnership()": {"details": "Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner."}, "transferOwnership(address)": {"details": "Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"claim(address[])": {"notice": "Claim tokens for `holders"}, "getBlockTimestamp()": {"notice": "Get block timestamp"}, "getRewardTokens()": {"notice": "Added reward tokens"}, "isRewardToken(address)": {"notice": "Flag to check if reward token added before"}, "notifyBorrowIndex(address)": {"notice": "Notifies borrow index"}, "notifyBorrower(address,address)": {"notice": "Notifies borrower"}, "notifySupplier(address,address)": {"notice": "Notifies supplier"}, "notifySupplyIndex(address)": {"notice": "Notifies supply index"}, "operator()": {"notice": "The operator that rewards are distributed to"}, "rewardAccountState(address,address)": {"notice": "The Reward state for each reward token for each account"}, "rewardMarketState(address,address)": {"notice": "The Reward state for each reward token for each market"}, "rewardTokens(uint256)": {"notice": "Added reward tokens"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/rewards/RewardDistributor.sol": "RewardDistributor"}, "evmVersion": "london", "libraries": {}}, "sources": {"lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol": {"keccak256": "0xc163fcf9bb10138631a9ba5564df1fa25db9adff73bd9ee868a8ae1858fe093a", "urls": ["bzz-raw://9706d43a0124053d9880f6e31a59f31bc0a6a3dc1acd66ce0a16e1111658c5f6", "dweb:/ipfs/QmUFmfowzkRwGtDu36cXV9SPTBHJ3n7dG9xQiK5B28jTf2"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol": {"keccak256": "0x631188737069917d2f909d29ce62c4d48611d326686ba6683e26b72a23bfac0b", "urls": ["bzz-raw://7a61054ae84cd6c4d04c0c4450ba1d6de41e27e0a2c4f1bcdf58f796b401c609", "dweb:/ipfs/QmUvtdp7X1mRVyC3CsHrtPbgoqWaXHp3S1ZR24tpAQYJWM"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol": {"keccak256": "0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397", "urls": ["bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9", "dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/ReentrancyGuardUpgradeable.sol": {"keccak256": "0xb44e086e941292cdc7f440de51478493894ef0b1aeccb0c4047445919f667f74", "urls": ["bzz-raw://942dad22fbdc1669f025540ba63aa3ccfad5f8458fc5d4525b31ebf272e7af45", "dweb:/ipfs/Qmdo4X2M82aM3AMoW2kf2jhYkSCyC4T1pHNd6obdsDFnAB"], "license": "MIT"}, "src/interfaces/IRewardDistributor.sol": {"keccak256": "0xd39c4db6e2a85e90db82e20685d4b235966d993c3407e1c0fb52add6421b86df", "urls": ["bzz-raw://e865fe1569ec1ac79cd924a9aa2e2b43754555e510526f6cd030395e9923f01d", "dweb:/ipfs/QmXcshgzCxLKBZJYGuQviEamruhmwXWNJ3ZYLxazWBahuc"], "license": "BSL-1.1"}, "src/interfaces/IRoles.sol": {"keccak256": "0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365", "urls": ["bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c", "dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj"], "license": "BSL-1.1"}, "src/interfaces/ImToken.sol": {"keccak256": "0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f", "urls": ["bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36", "dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL"], "license": "BSL-1.1"}, "src/rewards/RewardDistributor.sol": {"keccak256": "0x8d5c3e5e5050121d4f1310479a2cadde7dc97ff8a57115021cafb2032aaf50c2", "urls": ["bzz-raw://e200d18803bb0680b3007c79c23a8a7e31f054ce6e4f36c376f7b43419679322", "dweb:/ipfs/QmV7CRA5HSB89fwCVF7VWGArZnWoH2BB1heXu9SaMkbL9H"], "license": "BSL-1.1"}, "src/utils/ExponentialNoError.sol": {"keccak256": "0xa97178117d8c32059d55dd2224e5514c54b31f896e565c3834cab3d7502b1bb6", "urls": ["bzz-raw://1cecc0713709f57a5bb7f64d2fa06b9437a42752fa3fed5d3c0b19d82aa56ef4", "dweb:/ipfs/QmQqBCZkzmZxomXLr3b1tHUV55n8EPk3wAUsXgojYSpR41"], "license": "BSL-1.1"}}, "version": 1}, "id": 193}