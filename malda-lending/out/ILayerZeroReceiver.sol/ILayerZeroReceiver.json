{"abi": [{"type": "function", "name": "lzReceive", "inputs": [{"name": "_srcChainId", "type": "uint16", "internalType": "uint16"}, {"name": "_srcAddress", "type": "bytes", "internalType": "bytes"}, {"name": "_nonce", "type": "uint64", "internalType": "uint64"}, {"name": "_payload", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"lzReceive(uint16,bytes,uint64,bytes)": "001d3567"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"uint16\",\"name\":\"_src<PERSON>hainId\",\"type\":\"uint16\"},{\"internalType\":\"bytes\",\"name\":\"_srcAddress\",\"type\":\"bytes\"},{\"internalType\":\"uint64\",\"name\":\"_nonce\",\"type\":\"uint64\"},{\"internalType\":\"bytes\",\"name\":\"_payload\",\"type\":\"bytes\"}],\"name\":\"lzReceive\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"is imported from (https://github.com/LayerZero-Labs/LayerZero/blob/main/contracts/interfaces/ILayerZeroReceiver.sol)\",\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/external/layerzero/ILayerZeroReceiver.sol\":\"ILayerZeroReceiver\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"src/interfaces/external/layerzero/ILayerZeroReceiver.sol\":{\"keccak256\":\"0xaafdd3f57f2ac9caabde03bec39a4bc2006e200018ac15957cd2d0baa2963c0e\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://fb09d29da665f479f4b18d6f7ba07eaceccca630434b8a9afb6d0984168a948c\",\"dweb:/ipfs/QmRqgFkJYwHbTZ35WDhLcB94ZamYHP1LgoeKFnhmLQ73R5\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "uint16", "name": "_srcChainId", "type": "uint16"}, {"internalType": "bytes", "name": "_srcAddress", "type": "bytes"}, {"internalType": "uint64", "name": "_nonce", "type": "uint64"}, {"internalType": "bytes", "name": "_payload", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "lzReceive"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/interfaces/external/layerzero/ILayerZeroReceiver.sol": "ILayerZeroReceiver"}, "evmVersion": "london", "libraries": {}}, "sources": {"src/interfaces/external/layerzero/ILayerZeroReceiver.sol": {"keccak256": "0xaafdd3f57f2ac9caabde03bec39a4bc2006e200018ac15957cd2d0baa2963c0e", "urls": ["bzz-raw://fb09d29da665f479f4b18d6f7ba07eaceccca630434b8a9afb6d0984168a948c", "dweb:/ipfs/QmRqgFkJYwHbTZ35WDhLcB94ZamYHP1LgoeKFnhmLQ73R5"], "license": "BUSL-1.1"}}, "version": 1}, "id": 155}