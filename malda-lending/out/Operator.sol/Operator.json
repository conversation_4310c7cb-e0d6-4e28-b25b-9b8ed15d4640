{"abi": [{"type": "constructor", "inputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "accountAssets", "inputs": [{"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "afterMTokenMint", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "view"}, {"type": "function", "name": "allMarkets", "inputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "beforeMTokenBorrow", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}, {"name": "borrower", "type": "address", "internalType": "address"}, {"name": "borrowAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "beforeMTokenLiquidate", "inputs": [{"name": "mTokenBorrowed", "type": "address", "internalType": "address"}, {"name": "mTokenCollateral", "type": "address", "internalType": "address"}, {"name": "borrower", "type": "address", "internalType": "address"}, {"name": "repayAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "view"}, {"type": "function", "name": "beforeMTokenMint", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}, {"name": "minter", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "beforeMTokenRedeem", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}, {"name": "redeemer", "type": "address", "internalType": "address"}, {"name": "redeemTokens", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "beforeMTokenRepay", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}, {"name": "borrower", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "beforeMTokenSeize", "inputs": [{"name": "mTokenCollateral", "type": "address", "internalType": "address"}, {"name": "mTokenBorrowed", "type": "address", "internalType": "address"}, {"name": "liquidator", "type": "address", "internalType": "address"}, {"name": "borrower", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "beforeMTokenTransfer", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}, {"name": "src", "type": "address", "internalType": "address"}, {"name": "dst", "type": "address", "internalType": "address"}, {"name": "transferTokens", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "beforeRebalancing", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "view"}, {"type": "function", "name": "blacklistOperator", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IBlacklister"}], "stateMutability": "view"}, {"type": "function", "name": "borrowCaps", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "checkMembership", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "mToken", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "checkOutflowVolumeLimit", "inputs": [{"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "claimMalda", "inputs": [{"name": "holder", "type": "address", "internalType": "address"}, {"name": "mTokens", "type": "address[]", "internalType": "address[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "claimMalda", "inputs": [{"name": "holders", "type": "address[]", "internalType": "address[]"}, {"name": "mTokens", "type": "address[]", "internalType": "address[]"}, {"name": "borrowers", "type": "bool", "internalType": "bool"}, {"name": "suppliers", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "claimMalda", "inputs": [{"name": "holder", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "closeFactorMantissa", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "cumulativeOutflowVolume", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "disable<PERSON><PERSON><PERSON><PERSON>", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "enterMarkets", "inputs": [{"name": "_mTokens", "type": "address[]", "internalType": "address[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "enterMarketsWithSender", "inputs": [{"name": "_account", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "exitMarket", "inputs": [{"name": "_mToken", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "getAccountLiquidity", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getAllMarkets", "inputs": [], "outputs": [{"name": "mTokens", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "getAssetsIn", "inputs": [{"name": "_user", "type": "address", "internalType": "address"}], "outputs": [{"name": "mTokens", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "getHypotheticalAccountLiquidity", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "mTokenModify", "type": "address", "internalType": "address"}, {"name": "redeemTokens", "type": "uint256", "internalType": "uint256"}, {"name": "borrowAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getUSDValueForAllMarkets", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "initialize", "inputs": [{"name": "_rolesOperator", "type": "address", "internalType": "address"}, {"name": "_blacklistOperator", "type": "address", "internalType": "address"}, {"name": "_rewardDistributor", "type": "address", "internalType": "address"}, {"name": "_admin", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "isDeprecated", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isMarketListed", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isOperator", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "pure"}, {"type": "function", "name": "isPaused", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}, {"name": "_type", "type": "uint8", "internalType": "enum ImTokenOperationTypes.OperationType"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "lastOutflowResetTimestamp", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "limitPerTimePeriod", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "liquidateCalculateSeizeTokens", "inputs": [{"name": "mTokenBorrowed", "type": "address", "internalType": "address"}, {"name": "mTokenCollateral", "type": "address", "internalType": "address"}, {"name": "actualRepayAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "liquidationIncentiveMantissa", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "markets", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "isListed", "type": "bool", "internalType": "bool"}, {"name": "collateralFactorMantissa", "type": "uint256", "internalType": "uint256"}, {"name": "isMalded", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "oracleOperator", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "outflowResetTimeWindow", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "resetOutflowVolume", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "rewardDistributor", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "rolesOperator", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IRoles"}], "stateMutability": "view"}, {"type": "function", "name": "setCloseFactor", "inputs": [{"name": "newCloseFactorMantissa", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setCollateralFactor", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}, {"name": "newCollateralFactorMantissa", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setLiquidationIncentive", "inputs": [{"name": "market", "type": "address", "internalType": "address"}, {"name": "newLiquidationIncentiveMantissa", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setMarketBorrowCaps", "inputs": [{"name": "mTokens", "type": "address[]", "internalType": "address[]"}, {"name": "newBorrowCaps", "type": "uint256[]", "internalType": "uint256[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setMarketSupplyCaps", "inputs": [{"name": "mTokens", "type": "address[]", "internalType": "address[]"}, {"name": "newSupplyCaps", "type": "uint256[]", "internalType": "uint256[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setOutflowTimeLimitInUSD", "inputs": [{"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setOutflowVolumeTimeWindow", "inputs": [{"name": "newTimeWindow", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setPaused", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}, {"name": "_type", "type": "uint8", "internalType": "enum ImTokenOperationTypes.OperationType"}, {"name": "state", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setPriceO<PERSON>le", "inputs": [{"name": "newOracle", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setRewardDistributor", "inputs": [{"name": "newRewardDistributor", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setRolesOperator", "inputs": [{"name": "_roles", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setWhitelistedUser", "inputs": [{"name": "user", "type": "address", "internalType": "address"}, {"name": "state", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "supplyCaps", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "supportMarket", "inputs": [{"name": "mToken", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "user<PERSON><PERSON><PERSON>sted", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "whitelistEnabled", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "event", "name": "ActionPaused", "inputs": [{"name": "mToken", "type": "address", "indexed": true, "internalType": "address"}, {"name": "_type", "type": "uint8", "indexed": false, "internalType": "enum ImTokenOperationTypes.OperationType"}, {"name": "state", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "Initialized", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "MarketEntered", "inputs": [{"name": "mToken", "type": "address", "indexed": true, "internalType": "address"}, {"name": "account", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "MarketExited", "inputs": [{"name": "mToken", "type": "address", "indexed": true, "internalType": "address"}, {"name": "account", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "MarketListed", "inputs": [{"name": "mToken", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "NewBorrowCap", "inputs": [{"name": "mToken", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newBorrowCap", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "NewCloseFactor", "inputs": [{"name": "oldCloseFactorMantissa", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "newCloseFactorMantissa", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "NewCollateralFactor", "inputs": [{"name": "mToken", "type": "address", "indexed": true, "internalType": "address"}, {"name": "oldCollateralFactorMantissa", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "newCollateralFactorMantissa", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "NewLiquidationIncentive", "inputs": [{"name": "market", "type": "address", "indexed": false, "internalType": "address"}, {"name": "oldLiquidationIncentiveMantissa", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "newLiquidationIncentiveMantissa", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "NewPriceOracle", "inputs": [{"name": "oldPriceOracle", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newPriceOracle", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "NewRewardDistributor", "inputs": [{"name": "oldRewardDistributor", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newRewardDistributor", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "NewRolesOperator", "inputs": [{"name": "oldRoles", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newRoles", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "NewSupplyCap", "inputs": [{"name": "mToken", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newBorrowCap", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "OutflowLimitUpdated", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "oldLimit", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "newLimit", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "OutflowTimeWindowUpdated", "inputs": [{"name": "oldWindow", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "newWindow", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "OutflowVolumeReset", "inputs": [], "anonymous": false}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "<PERSON>r<PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "user", "type": "address", "indexed": true, "internalType": "address"}, {"name": "state", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "WhitelistDisabled", "inputs": [], "anonymous": false}, {"type": "event", "name": "WhitelistEnabled", "inputs": [], "anonymous": false}, {"type": "error", "name": "InvalidInitialization", "inputs": []}, {"type": "error", "name": "NotInitializing", "inputs": []}, {"type": "error", "name": "Operator_AssetNotFound", "inputs": []}, {"type": "error", "name": "Operator_Deactivate_MarketBalanceOwed", "inputs": []}, {"type": "error", "name": "Operator_EmptyPrice", "inputs": []}, {"type": "error", "name": "Operator_InsufficientLiquidity", "inputs": []}, {"type": "error", "name": "Operator_InvalidBlacklistOperator", "inputs": []}, {"type": "error", "name": "Operator_InvalidCollateralFactor", "inputs": []}, {"type": "error", "name": "Operator_InvalidInput", "inputs": []}, {"type": "error", "name": "Operator_InvalidRewardDistributor", "inputs": []}, {"type": "error", "name": "Operator_InvalidRolesOperator", "inputs": []}, {"type": "error", "name": "Operator_MarketAlreadyListed", "inputs": []}, {"type": "error", "name": "Operator_MarketBorrowCapReached", "inputs": []}, {"type": "error", "name": "Operator_MarketNotListed", "inputs": []}, {"type": "error", "name": "Operator_MarketSupplyReached", "inputs": []}, {"type": "error", "name": "Operator_Mismatch", "inputs": []}, {"type": "error", "name": "Operator_OnlyAdmin", "inputs": []}, {"type": "error", "name": "Operator_OnlyAdminOrRole", "inputs": []}, {"type": "error", "name": "Operator_OracleUnderlyingFetchError", "inputs": []}, {"type": "error", "name": "Operator_OutflowVolumeReached", "inputs": []}, {"type": "error", "name": "Operator_Paused", "inputs": []}, {"type": "error", "name": "Operator_PriceFetchFailed", "inputs": []}, {"type": "error", "name": "Operator_RepayAmountNotValid", "inputs": []}, {"type": "error", "name": "Operator_RepayingTooMuch", "inputs": []}, {"type": "error", "name": "Operator_SenderMustBeToken", "inputs": []}, {"type": "error", "name": "Operator_UserBlacklisted", "inputs": []}, {"type": "error", "name": "Operator_UserNotWhitelisted", "inputs": []}, {"type": "error", "name": "Operator_WrongMarket", "inputs": []}, {"type": "error", "name": "OwnableInvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}], "bytecode": {"object": "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", "sourceMap": "1433:32536:127:-:0;;;1572:53;;;;;;;;;-1:-1:-1;1596:22:127;:20;:22::i;:::-;1433:32536;;7711:422:22;8870:21;7900:15;;;;;;;7896:76;;;7938:23;;-1:-1:-1;;;7938:23:22;;;;;;;;;;;7896:76;7985:14;;-1:-1:-1;;;;;7985:14:22;;;:34;7981:146;;8035:33;;-1:-1:-1;;;;;;8035:33:22;-1:-1:-1;;;;;8035:33:22;;;;;8087:29;;158:50:242;;;8087:29:22;;146:2:242;131:18;8087:29:22;;;;;;;7981:146;7760:373;7711:422::o;14:200:242:-;1433:32536:127;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "1433:32536:127:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2357:45:128;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;687:25:242;;;675:2;660:18;2357:45:128;;;;;;;;12968:197:127;;;;;;:::i;:::-;;:::i;:::-;;;1404:14:242;;1397:22;1379:41;;1367:2;1352:18;12968:197:127;1239:187:242;21936:539:127;;;;;;:::i;:::-;;:::i;:::-;;1539:29:128;;;;;-1:-1:-1;;;;;1539:29:128;;;;;;-1:-1:-1;;;;;1595:32:242;;;1577:51;;1565:2;1550:18;1539:29:128;1431:203:242;4339:394:127;;;;;;:::i;:::-;;:::i;20868:546::-;;;;;;:::i;:::-;;:::i;9828:709::-;;;;;;:::i;:::-;;:::i;19263:215::-;;;;;;:::i;:::-;;:::i;22534:347::-;;;;;;:::i;:::-;;:::i;19817:205::-;;;;;;:::i;:::-;;:::i;1450:37:128:-;;;;;-1:-1:-1;;;;;1450:37:128;;;1706:63;;;;;;:::i;:::-;;;;;;;;;;;;;;13940:126:127;;;;;;:::i;:::-;-1:-1:-1;;;;;14035:15:127;14012:4;14035:15;;;:7;:15;;;;;:24;;;;13940:126;12829:88;12906:4;12829:88;;2913:162;;;;;;:::i;:::-;;:::i;2260:45:128:-;;;;;;:::i;:::-;;;;;;;;;;;;;;11678:540:127;;;;;;:::i;:::-;;:::i;14336:310::-;;;;;;:::i;:::-;;:::i;:::-;;;;8613:25:242;;;8669:2;8654:18;;8647:34;;;;8586:18;14336:310:127;8439:248:242;1371:27:128;;;;;-1:-1:-1;;;;;1371:27:128;;;22940:1400:127;;;;;;:::i;:::-;;:::i;2975:28:128:-;;;;;;;;;8369:128:127;;;:::i;2181:27:128:-;;;;;;:::i;:::-;;:::i;3907:228:127:-;;;;;;:::i;:::-;;:::i;14117:168::-;;;;;;:::i;:::-;;:::i;26067:888::-;;;;;;:::i;:::-;;:::i;20655:154::-;;;;;;:::i;:::-;;:::i;2623:38:128:-;;;;;;3155:101:21;;;:::i;8599:846:127:-;;;;;;:::i;:::-;;:::i;2538:33:128:-;;;;;;2441:144:21;;;:::i;2066:55:128:-;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;9807:14:242;;9800:22;9782:41;;9854:2;9839:18;;9832:34;;;;9909:14;9902:22;9882:18;;;9875:50;9770:2;9755:18;2066:55:128;9592:339:242;13405:153:127;;;;;;:::i;:::-;-1:-1:-1;;;;;13509:15:127;;;13486:4;13509:15;;;:7;:15;;;;;;;;:42;;;;;:33;;;;:42;;;;;;13405:153;;;;;13768:121;;;;;;:::i;:::-;;:::i;16684:322::-;;;;;;:::i;:::-;;:::i;6321:424::-;;;;;;:::i;:::-;;:::i;12380:361::-;;;;;;:::i;:::-;;:::i;13216:138::-;;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;2454:32:128:-;;;;;-1:-1:-1;;;;;2454:32:128;;;13609:108:127;;;:::i;24837:1171::-;;;;;;:::i;:::-;;:::i;7768:211::-;;;;;;:::i;:::-;;:::i;5008:1109::-;;;;;;:::i;:::-;;:::i;21473:404::-;;;;;;:::i;:::-;;:::i;16285:348::-;;;;;;:::i;:::-;;:::i;24399:379::-;;;;;;:::i;:::-;;:::i;14697:1498::-;;;;;;:::i;:::-;;:::i;6978:667::-;;;;;;:::i;:::-;;:::i;3134:119::-;;;:::i;10919:708::-;;;;;;:::i;:::-;;:::i;3313:122::-;;;:::i;20093:503::-;;;:::i;1871:50:128:-;;;;;;:::i;:::-;;:::i;2713:40::-;;;;;;18852:199:127;;;;;;:::i;:::-;;:::i;1620:34:128:-;;;;;;2832:37;;;;;;17057:1653:127;;;;;;:::i;:::-;;:::i;3405:215:21:-;;;;;;:::i;:::-;;:::i;3556:237:127:-;;;;;;:::i;:::-;;:::i;1631:779::-;;;;;;:::i;:::-;;:::i;8123:187::-;;;;;;:::i;:::-;;:::i;2921:47:128:-;;;;;;:::i;:::-;;;;;;;;;;;;;;;;12968:197:127;-1:-1:-1;;;;;13136:15:127;;13109:4;13136:15;;;:7;:15;;;;;13109:4;13152:5;13136:22;;;;;;;;:::i;:::-;;;;;;;;;:::i;:::-;;;;;;;;;;;;-1:-1:-1;13136:22:127;;;;;-1:-1:-1;12968:197:127;;;;;:::o;21936:539::-;-1:-1:-1;;;;;22030:18:127;;22010:17;22030:18;;;:10;:18;;;;;;22124:14;;22120:349;;22154:19;22184:6;-1:-1:-1;;;;;22176:27:127;;:29;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;22154:51;;22219:23;22245:53;;;;;;;;22268:6;-1:-1:-1;;;;;22260:34:127;;:36;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;22245:53;;22219:79;-1:-1:-1;22312:19:127;22334:45;22219:79;22367:11;22334:18;:45::i;:::-;22312:67;;22416:9;22401:11;:24;;22393:65;;;;-1:-1:-1;;;22393:65:127;;;;;;;;;;;;22140:329;;;22120:349;22000:475;21936:539;:::o;4339:394::-;2334:13:21;:11;:13::i;:::-;3875:7:128::1;4445:22:127;:51;;:106;;;;;4005:6:128;4500:22:127;:51;;4445:106;4424:174;;;;-1:-1:-1::0;;;4424:174:127::1;;;;;;;;;;;;4628:19;::::0;4613:59:::1;::::0;;8613:25:242;;;8669:2;8654:18;;8647:34;;;4613:59:127::1;::::0;8586:18:242;4613:59:127::1;;;;;;;4682:19;:44:::0;4339:394::o;20868:546::-;2656:17;;:37;;-1:-1:-1;;;2656:37:127;;-1:-1:-1;;;;;1595:32:242;;;2656:37:127;;;1577:51:242;20999:3:127;;2656:17;;:31;;1550:18:242;;2656:37:127;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2655:38;2646:76;;;;-1:-1:-1;;;2646:76:127;;;;;;;;;;;;2656:17:::1;::::0;:37:::1;::::0;-1:-1:-1;;;2656:37:127;;-1:-1:-1;;;;;1595:32:242;;;2656:37:127::1;::::0;::::1;1577:51:242::0;21021:3:127;;2656:17:::1;::::0;:31:::1;::::0;1550:18:242;;2656:37:127::1;;;;;;;;;;;;;;;;;::::0;::::1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2655:38;2646:76;;;;-1:-1:-1::0;;;2646:76:127::1;;;;;;;;;;;;-1:-1:-1::0;;;;;21045:15:127;::::2;;::::0;;;:7:::2;:15;::::0;;;;;;;21061:22:::2;21045:39:::0;;;;;;;;::::2;;21044:40;21036:68;;;;-1:-1:-1::0;;;21036:68:127::2;;;;;;;;;;;;21193:42;21207:6;21215:3;21220:14;21193:13;:42::i;:::-;21282:31;21306:6;21282:23;:31::i;:::-;21323:37;21348:6;21356:3;21323:24;:37::i;:::-;21370;21395:6;21403:3;21370:24;:37::i;:::-;2732:1:::1;20868:546:::0;;;;;:::o;9828:709::-;9973:7;:5;:7::i;:::-;-1:-1:-1;;;;;9959:21:127;:10;-1:-1:-1;;;;;9959:21:127;;:100;;;-1:-1:-1;9984:13:127;;10023:35;;;-1:-1:-1;;;10023:35:127;;;;-1:-1:-1;;;;;9984:13:127;;;;:26;;10011:10;;9984:13;;10023:33;;:35;;;;;;;;;;;;;;9984:13;10023:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;9984:75;;-1:-1:-1;;;;;;9984:75:127;;;;;;;-1:-1:-1;;;;;12783:32:242;;;9984:75:127;;;12765:51:242;12832:18;;;12825:34;12738:18;;9984:75:127;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;9938:171;;;;-1:-1:-1;;;9938:171:127;;;;;;;;;;;;10141:7;10189:13;10228:15;;;;;:46;;;10261:13;10247:10;:27;10228:46;10220:80;;;;-1:-1:-1;;;10220:80:127;;;;;;;;;;;;10316:9;10311:220;10331:10;10327:1;:14;10311:220;;;10383:13;;10397:1;10383:16;;;;;;;:::i;:::-;;;;;;;10358:10;:22;10369:7;;10377:1;10369:10;;;;;;;:::i;:::-;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;10358:22:127;;;;;;;;;;;;-1:-1:-1;10358:22:127;:41;10431:7;;10439:1;10431:10;;;;;;;:::i;:::-;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;10418:42:127;;10443:13;;10457:1;10443:16;;;;;;;:::i;:::-;;;;;;;10418:42;;;;687:25:242;;675:2;660:18;;541:177;10418:42:127;;;;;;;;10503:3;;10311:220;;;;9928:609;;9828:709;;;;:::o;19263:215::-;19380:16;;;19394:1;19380:16;;;;;;;;;19353:24;;19380:16;;;;;;;;;;;-1:-1:-1;19380:16:127;19353:43;;19419:6;19406:7;19414:1;19406:10;;;;;;;;:::i;:::-;;;;;;:19;-1:-1:-1;;;;;19406:19:127;;;-1:-1:-1;;;;;19406:19:127;;;;;19435:36;19442:7;19451;19460:4;19466;19435:6;:36::i;:::-;19343:135;19263:215;;:::o;22534:347::-;2469:16;;22652:8;;2469:16;;2465:108;;;-1:-1:-1;;;;;2509:21:127;;;;;;:15;:21;;;;;;;;2501:61;;;;-1:-1:-1;;;2501:61:127;;;;;;;;;;;;2656:17:::1;::::0;:37:::1;::::0;-1:-1:-1;;;2656:37:127;;-1:-1:-1;;;;;1595:32:242;;;2656:37:127::1;::::0;::::1;1577:51:242::0;22679:8:127;;2656:17:::1;::::0;:31:::1;::::0;1550:18:242;;2656:37:127::1;;;;;;;;;;;;;;;;;::::0;::::1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2655:38;2646:76;;;;-1:-1:-1::0;;;2646:76:127::1;;;;;;;;;;;;22699:45:::2;22713:6;22721:8;22731:12;22699:13;:45::i;:::-;22791:31;22815:6;22791:23;:31::i;:::-;22832:42;22857:6;22865:8;22832:24;:42::i;19817:205::-:0;19969:46;19976:7;19985;19994:9;20005;19969:6;:46::i;:::-;19817:205;;;;:::o;2913:162::-;2334:13:21;:11;:13::i;:::-;-1:-1:-1;;;;;2996:21:127;::::1;;::::0;;;:15:::1;:21;::::0;;;;;;;;:29;;-1:-1:-1;;2996:29:127::1;::::0;::::1;;::::0;;::::1;::::0;;;3040:28;;1379:41:242;;;3040:28:127::1;::::0;1352:18:242;3040:28:127::1;;;;;;;2913:162:::0;;:::o;11678:540::-;11791:5;11787:335;;;11851:7;:5;:7::i;:::-;-1:-1:-1;;;;;11837:21:127;:10;-1:-1:-1;;;;;11837:21:127;;:95;;;-1:-1:-1;11862:13:127;;11901:30;;;-1:-1:-1;;;11901:30:127;;;;-1:-1:-1;;;;;11862:13:127;;;;:26;;11889:10;;11862:13;;11901:28;;:30;;;;;;;;;;;;;;11862:13;11901:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;11862:70;;-1:-1:-1;;;;;;11862:70:127;;;;;;;-1:-1:-1;;;;;12783:32:242;;;11862:70:127;;;12765:51:242;12832:18;;;12825:34;12738:18;;11862:70:127;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;11812:178;;;;-1:-1:-1;;;11812:178:127;;;;;;;;;;;;11787:335;;;12081:7;:5;:7::i;:::-;-1:-1:-1;;;;;12067:21:127;:10;-1:-1:-1;;;;;12067:21:127;;12059:52;;;;-1:-1:-1;;;12059:52:127;;;;;;;;;;;;-1:-1:-1;;;;;12132:15:127;;;;;;:7;:15;;;;;12157:5;;12148;12132:22;;;;;;;;:::i;:::-;;;;;;;;;:::i;:::-;;;;;;;;;;;;;:30;;;;;;;;;;;;;;;;;;12190:6;-1:-1:-1;;;;;12177:34:127;;12198:5;12205;12177:34;;;;;;;:::i;:::-;;;;;;;;11678:540;;;:::o;14336:310::-;14521:7;14530;14556:83;14589:7;14598:12;14612;14626;14556:32;:83::i;:::-;14549:90;;;;14336:310;;;;;;;;:::o;22940:1400::-;2469:16;;23058:8;;2469:16;;2465:108;;;-1:-1:-1;;;;;2509:21:127;;;;;;:15;:21;;;;;;;;2501:61;;;;-1:-1:-1;;;2501:61:127;;;;;;;;;;;;2656:17:::1;::::0;:37:::1;::::0;-1:-1:-1;;;2656:37:127;;-1:-1:-1;;;;;1595:32:242;;;2656:37:127::1;::::0;::::1;1577:51:242::0;23085:8:127;;2656:17:::1;::::0;:31:::1;::::0;1550:18:242;;2656:37:127::1;;;;;;;;;;;;;;;;;::::0;::::1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2655:38;2646:76;;;;-1:-1:-1::0;;;2646:76:127::1;;;;;;;;;;;;-1:-1:-1::0;;;;;23114:15:127;::::2;;::::0;;;:7:::2;:15;::::0;;;;;;;23130:20:::2;23114:37:::0;;;;;;;;::::2;;23113:38;23105:66;;;;-1:-1:-1::0;;;23105:66:127::2;;;;;;;;;;;;-1:-1:-1::0;;;;;23189:15:127;::::2;;::::0;;;:7:::2;:15;::::0;;;;:24;::::2;;23181:61;;;;-1:-1:-1::0;;;23181:61:127::2;;;;;;;;;;;;-1:-1:-1::0;;;;;23258:15:127;;::::2;;::::0;;;:7:::2;:15;::::0;;;;;;;:43;;::::2;::::0;;:33:::2;::::0;;::::2;:43:::0;;;;::::2;;23253:272;;23325:10;-1:-1:-1::0;;;;;23325:20:127;::::2;;23317:59;;;;-1:-1:-1::0;;;23317:59:127::2;;;;;;;;;;;;23391:33;23407:6;23415:8;23391:15;:33::i;:::-;-1:-1:-1::0;;;;;23446:15:127;;::::2;;::::0;;;:7:::2;:15;::::0;;;;;;;:43;;::::2;::::0;;:33:::2;::::0;;::::2;:43:::0;;;;::::2;;23438:76;;;;-1:-1:-1::0;;;23438:76:127::2;;;;;;;;;;;;23559:14;::::0;23543:58:::2;::::0;-1:-1:-1;;;23543:58:127;;-1:-1:-1;;;;;1595:32:242;;;23543:58:127::2;::::0;::::2;1577:51:242::0;23559:14:127;;::::2;::::0;23543:50:::2;::::0;1550:18:242;;23543:58:127::2;;;;;;;;;;;;;;;;;::::0;::::2;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;23605:1;23543:63:::0;23535:95:::2;;;;-1:-1:-1::0;;;23535:95:127::2;;;;;;;;;;;;-1:-1:-1::0;;;;;23661:18:127;::::2;23641:17;23661:18:::0;;;:10:::2;:18;::::0;;;;;23755:14;;23751:257:::2;;23785:20;23816:6;-1:-1:-1::0;;;;;23808:28:127::2;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;::::0;::::2;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;23785:53;;23852:24;23879:32;23884:12;23898;23879:4;:32::i;:::-;23852:59;;23952:9;23933:16;:28;23925:72;;;;-1:-1:-1::0;;;23925:72:127::2;;;;;;;;;;;;23771:237;;23751:257;24048:17;24069:67;24102:8;24112:6;24120:1;24123:12;24069:32;:67::i;:::-;24045:91:::0;-1:-1:-1;;24154:14:127;;24146:57:::2;;;;-1:-1:-1::0;;;24146:57:127::2;;;;;;;;;;;;24250:31;24274:6;24250:23;:31::i;:::-;24291:42;24316:6;24324:8;24291:24;:42::i;8369:128::-:0;2334:13:21;:11;:13::i;:::-;8454:1:127::1;8428:23;:27:::0;;;8470:20:::1;::::0;::::1;::::0;8454:1;8470:20:::1;8369:128::o:0;2181:27:128:-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2181:27:128;;-1:-1:-1;2181:27:128;:::o;3907:228:127:-;2334:13:21;:11;:13::i;:::-;-1:-1:-1;;;;;3987:23:127;::::1;3979:57;;;;-1:-1:-1::0;;;3979:57:127::1;;;;;;;;;;;;4066:14;::::0;4051:41:::1;::::0;-1:-1:-1;;;;;4051:41:127;;::::1;::::0;4066:14:::1;::::0;4051:41:::1;::::0;4066:14:::1;::::0;4051:41:::1;4102:14;:26:::0;;-1:-1:-1;;;;;;4102:26:127::1;-1:-1:-1::0;;;;;4102:26:127;;;::::1;::::0;;;::::1;::::0;;3907:228::o;14117:168::-;14184:7;14193;14219:59;14252:7;14269:1;14273;14276;14219:32;:59::i;:::-;14212:66;;;;14117:168;;;:::o;26067:888::-;2656:17;;:37;;-1:-1:-1;;;2656:37:127;;-1:-1:-1;;;;;1595:32:242;;;2656:37:127;;;1577:51:242;26241:10:127;;2656:17;;:31;;1550:18:242;;2656:37:127;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2655:38;2646:76;;;;-1:-1:-1;;;2646:76:127;;;;;;;;;;;;2656:17:::1;::::0;:37:::1;::::0;-1:-1:-1;;;2656:37:127;;-1:-1:-1;;;;;1595:32:242;;;2656:37:127::1;::::0;::::1;1577:51:242::0;26278:8:127;;2656:17:::1;::::0;:31:::1;::::0;1550:18:242;;2656:37:127::1;;;;;;;;;;;;;;;;;::::0;::::1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2655:38;2646:76;;;;-1:-1:-1::0;;;2646:76:127::1;;;;;;;;;;;;-1:-1:-1::0;;;;;26324:25:127;::::2;;::::0;;;:7:::2;:25;::::0;;;;;;;26350:19:::2;26324:46:::0;;;;;;;;::::2;;26323:47;:96:::0;::::2;;;-1:-1:-1::0;;;;;;26375:23:127;::::2;;::::0;;;:7:::2;:23;::::0;;;;;;;26399:19:::2;26375:44:::0;;;;;;;;::::2;;26374:45;26323:96;26302:158;;;;-1:-1:-1::0;;;26302:158:127::2;;;;;;;;;;;;-1:-1:-1::0;;;;;26478:23:127;::::2;;::::0;;;:7:::2;:23;::::0;;;;:32;::::2;;26470:69;;;;-1:-1:-1::0;;;26470:69:127::2;;;;;;;;;;;;-1:-1:-1::0;;;;;26557:25:127;::::2;;::::0;;;:7:::2;:25;::::0;;;;:34;::::2;;26549:71;;;;-1:-1:-1::0;;;26549:71:127::2;;;;;;;;;;;;26686:14;-1:-1:-1::0;;;;;26678:32:127::2;;:34;;;;;;;;;;;;;;;;;;;;;;;;;;;;::::0;::::2;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1::0;;;;;26638:74:127::2;26646:16;-1:-1:-1::0;;;;;26638:34:127::2;;:36;;;;;;;;;;;;;;;;;;;;;;;;;;;;::::0;::::2;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1::0;;;;;26638:74:127::2;;26630:104;;;;-1:-1:-1::0;;;26630:104:127::2;;;;;;;;;;;;26781:41;26805:16;26781:23;:41::i;:::-;26832:52;26857:16;26875:8;26832:24;:52::i;20655:154::-:0;-1:-1:-1;;;;;20740:15:127;;;;;;:7;:15;;;;;;;;20756:25;20740:42;;;;;;;;;;20739:43;20731:71;;;;-1:-1:-1;;;20731:71:127;;;;;;;;;;;;20655:154;:::o;3155:101:21:-;2334:13;:11;:13::i;:::-;3219:30:::1;3246:1;3219:18;:30::i;:::-;3155:101::o:0;8599:846:127:-;8683:10;8675:19;;;;:7;:19;;;;;:28;;;8667:65;;;;-1:-1:-1;;;8667:65:127;;;;;;;;;;;;8807:18;;:22;8803:636;;8939:22;;8911:25;;:50;;;;:::i;:::-;8893:15;:68;8889:195;;;9007:1;8981:23;:27;9054:15;9026:25;:43;8889:195;9144:19;9166:50;9197:6;9205:10;9166:30;:50::i;:::-;9144:72;;9318:18;;9304:11;9278:23;;:37;;;;:::i;:::-;:58;9274:102;;;9345:31;;-1:-1:-1;;;9345:31:127;;;;;;;;;;;9274:102;9417:11;9390:23;;:38;;;;;;;:::i;:::-;;;;-1:-1:-1;;;8599:846:127;:::o;2441:144:21:-;1313:22;2570:8;-1:-1:-1;;;;;2570:8:21;;2441:144::o;13768:121:127:-;13838:4;13861:21;13875:6;13861:13;:21::i;16684:322::-;2469:16;;16768:8;;2469:16;;2465:108;;;-1:-1:-1;;;;;2509:21:127;;;;;;:15;:21;;;;;;;;2501:61;;;;-1:-1:-1;;;2501:61:127;;;;;;;;;;;;16879:10:::1;16833:35;16871:19:::0;;;:7:::1;:19;::::0;;;;16908:15;;::::1;;16900:52;;;;-1:-1:-1::0;;;16900:52:127::1;;;;;;;;;;;;16962:37;16978:10;16990:8;16962:15;:37::i;6321:424::-:0;2334:13:21;:11;:13::i;:::-;-1:-1:-1;;;;;6533:36:127;::::1;;::::0;;;:28:::1;:36;::::0;;;;;;;;;6501:102;;14156:51:242;;;14223:18;;;14216:34;14266:18;;14259:34;;;6501:102:127::1;::::0;14144:2:242;14129:18;6501:102:127::1;;;;;;;-1:-1:-1::0;;;;;6668:36:127;;::::1;;::::0;;;:28:::1;:36;::::0;;;;:70;6321:424::o;12380:361::-;2334:13:21;:11;:13::i;:::-;12576:17:127::1;::::0;12555:61:::1;::::0;-1:-1:-1;;;;;12555:61:127;;::::1;::::0;12576:17:::1;::::0;12555:61:::1;::::0;12576:17:::1;::::0;12555:61:::1;12694:17;:40:::0;;-1:-1:-1;;;;;;12694:40:127::1;-1:-1:-1::0;;;;;12694:40:127;;;::::1;::::0;;;::::1;::::0;;12380:361::o;13216:138::-;-1:-1:-1;;;;;13327:20:127;;;;;;:13;:20;;;;;;;;;13320:27;;;;;;;;;;;;;;;;;13284:24;;13320:27;;;13327:20;13320:27;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;13320:27:127;;;;;;;;;;;;;;;;;;;;;;;13216:138;;;:::o;13609:108::-;13657:24;13700:10;13693:17;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;13693:17:127;;;;;;;;;;;;;;;;;;;;;;;13609:108;:::o;24837:1171::-;2469:16;;25034:8;;2469:16;;2465:108;;;-1:-1:-1;;;;;2509:21:127;;;;;;:15;:21;;;;;;;;2501:61;;;;-1:-1:-1;;;2501:61:127;;;;;;;;;;;;2656:17:::1;::::0;:37:::1;::::0;-1:-1:-1;;;2656:37:127;;-1:-1:-1;;;;;1595:32:242;;;2656:37:127::1;::::0;::::1;1577:51:242::0;25061:8:127;;2656:17:::1;::::0;:31:::1;::::0;1550:18:242;;2656:37:127::1;;;;;;;;;;;;;;;;;::::0;::::1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2655:38;2646:76;;;;-1:-1:-1::0;;;2646:76:127::1;;;;;;;;;;;;-1:-1:-1::0;;;;;25090:23:127;::::2;;::::0;;;:7:::2;:23;::::0;;;;;;;25114::::2;25090:48:::0;;;;;;;;::::2;;25089:49;25081:77;;;;-1:-1:-1::0;;;25081:77:127::2;;;;;;;;;;;;-1:-1:-1::0;;;;;25176:23:127;::::2;;::::0;;;:7:::2;:23;::::0;;;;:32;::::2;;25168:69;;;;-1:-1:-1::0;;;25168:69:127::2;;;;;;;;;;;;-1:-1:-1::0;;;;;25255:25:127;::::2;;::::0;;;:7:::2;:25;::::0;;;;:34;::::2;;25247:71;;;;-1:-1:-1::0;;;25247:71:127::2;;;;;;;;;;;;25353:53;::::0;-1:-1:-1;;;25353:53:127;;-1:-1:-1;;;;;1595:32:242;;;25353:53:127::2;::::0;::::2;1577:51:242::0;25329:21:127::2;::::0;25353:43;;::::2;::::0;::::2;::::0;1550:18:242;;25353:53:127::2;;;;;;;;;;;;;;;;;::::0;::::2;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;25329:77;;25421:29;25435:14;25421:13;:29::i;:::-;25417:585;;;25491:11;25474:13;:28;;25466:69;;;;-1:-1:-1::0;;;25466:69:127::2;;;;;;;;;;;;25417:585;;;25569:17;25590:60;25623:8;25641:1;25645::::0;25648::::2;25590:32;:60::i;:::-;25566:84;;;25684:1;25672:9;:13;25664:56;;;;-1:-1:-1::0;;;25664:56:127::2;;;;;;;;;;;;25827:16;25846:71;25865:36;;;;;;;;25880:19;;25865:36;;::::0;25903:13:::2;25846:18;:71::i;:::-;25827:90;;25954:8;25939:11;:23;;25931:60;;;;-1:-1:-1::0;;;25931:60:127::2;;;;;;;;;;;;25552:450;;25071:937;2582:1:::1;24837:1171:::0;;;;;:::o;7768:211::-;2334:13:21;:11;:13::i;:::-;7886:22:127::1;::::0;7861:63:::1;::::0;;8613:25:242;;;8669:2;8654:18;;8647:34;;;7861:63:127::1;::::0;8586:18:242;7861:63:127::1;;;;;;;7934:22;:38:::0;7768:211::o;5008:1109::-;2334:13:21;:11;:13::i;:::-;-1:-1:-1;;;;;5192:24:127;::::1;5154:35;5192:24:::0;;;:7:::1;:24;::::0;;;;5234:15;;::::1;;5226:52;;;;-1:-1:-1::0;;;5226:52:127::1;;;;;;;;;;;;5325:44;::::0;;::::1;::::0;;::::1;::::0;;;;;5445:47;;;;::::1;::::0;;;4141:6:128::1;5445:47:127::0;;5325:44;5512:46:::1;5445:47:::0;5325:44;2669:14:195;2653:13;;:30;;2551:139;5512:46:127::1;5511:47;5503:92;;;;-1:-1:-1::0;;;5503:92:127::1;;;;;;;;;;;;5610:32:::0;;;::::1;::::0;:99:::1;;-1:-1:-1::0;5662:14:127::1;::::0;5646:58:::1;::::0;-1:-1:-1;;;5646:58:127;;-1:-1:-1;;;;;1595:32:242;;;5646:58:127::1;::::0;::::1;1577:51:242::0;5662:14:127;;::::1;::::0;5646:50:::1;::::0;1550:18:242;;5646:58:127::1;;;;;;;;;;;;;;;;;::::0;::::1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:63:::0;5610:99:::1;5606:158;;;5732:21;;-1:-1:-1::0;;;5732:21:127::1;;;;;;;;;;;5606:158;5890:31;::::0;::::1;::::0;5862:89:::1;::::0;;8613:25:242;;;8669:2;8654:18;;8647:34;;;-1:-1:-1;;;;;5862:89:127;::::1;::::0;::::1;::::0;8586:18:242;5862:89:127::1;;;;;;;-1:-1:-1::0;;6049:31:127::1;;:61:::0;-1:-1:-1;5008:1109:127:o;21473:404::-;2469:16;;21565:6;;2469:16;;2465:108;;;-1:-1:-1;;;;;2509:21:127;;;;;;:15;:21;;;;;;;;2501:61;;;;-1:-1:-1;;;2501:61:127;;;;;;;;;;;;2656:17:::1;::::0;:37:::1;::::0;-1:-1:-1;;;2656:37:127;;-1:-1:-1;;;;;1595:32:242;;;2656:37:127::1;::::0;::::1;1577:51:242::0;21590:6:127;;2656:17:::1;::::0;:31:::1;::::0;1550:18:242;;2656:37:127::1;;;;;;;;;;;;;;;;;::::0;::::1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2655:38;2646:76;;;;-1:-1:-1::0;;;2646:76:127::1;;;;;;;;;;;;-1:-1:-1::0;;;;;21617:15:127;::::2;;::::0;;;:7:::2;:15;::::0;;;;;;;21633:18:::2;21617:35:::0;;;;;;;;::::2;;21616:36;21608:64;;;;-1:-1:-1::0;;;21608:64:127::2;;;;;;;;;;;;-1:-1:-1::0;;;;;21690:15:127;::::2;;::::0;;;:7:::2;:15;::::0;;;;:24;::::2;;21682:61;;;;-1:-1:-1::0;;;21682:61:127::2;;;;;;;;;;;;21789:31;21813:6;21789:23;:31::i;:::-;21830:40;21855:6;21863;21830:24;:40::i;16285:348::-:0;2469:16;;16370:10;;2469:16;;2465:108;;;-1:-1:-1;;;;;2509:21:127;;;;;;:15;:21;;;;;;;;2501:61;;;;-1:-1:-1;;;2501:61:127;;;;;;;;;;;;16406:8;16392:11:::1;16431:196;16455:3;16451:1;:7;16431:196;;;16475:16;16494:8;;16503:1;16494:11;;;;;;;:::i;:::-;;;;;;;;;;;;;;:::i;:::-;16475:30;;16519:37;16535:8;16545:10;16519:15;:37::i;:::-;-1:-1:-1::0;16599:3:127::1;;16431:196;;24399:379:::0;2469:16;;24485:8;;2469:16;;2465:108;;;-1:-1:-1;;;;;2509:21:127;;;;;;:15;:21;;;;;;;;2501:61;;;;-1:-1:-1;;;2501:61:127;;;;;;;;;;;;-1:-1:-1;;;;;24514:15:127;::::1;;::::0;;;:7:::1;:15;::::0;;;;;;;24530:19:::1;24514:36:::0;;;;;;;;::::1;;24513:37;24505:65;;;;-1:-1:-1::0;;;24505:65:127::1;;;;;;;;;;;;-1:-1:-1::0;;;;;24588:15:127;::::1;;::::0;;;:7:::1;:15;::::0;;;;:24;::::1;;24580:61;;;;-1:-1:-1::0;;;24580:61:127::1;;;;;;;;;;;;24688:31;24712:6;24688:23;:31::i;:::-;24729:42;24754:6;24762:8;24729:24;:42::i;14697:1498::-:0;15000:14;;14984:66;;-1:-1:-1;;;14984:66:127;;-1:-1:-1;;;;;1595:32:242;;;14984:66:127;;;1577:51:242;14860:7:127;;;;15000:14;;;14984:50;;1550:18:242;;14984:66:127;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;15110:14;;15094:68;;-1:-1:-1;;;15094:68:127;;-1:-1:-1;;;;;1595:32:242;;;15094:68:127;;;1577:51:242;14952:98:127;;-1:-1:-1;15060:31:127;;15110:14;;;;15094:50;;1550:18:242;;15094:68:127;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;15060:102;;15204:1;15180:21;:25;:56;;;;;15235:1;15209:23;:27;15180:56;15172:94;;;;-1:-1:-1;;;15172:94:127;;;;;;;;;;;;15652:28;15691:16;-1:-1:-1;;;;;15683:44:127;;:46;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;15652:77;;15740:20;-1:-1:-1;;;;;;;;;;;;;;15740:20:127;-1:-1:-1;;;;;;;;;;;;;;;;;;;;;;;15858:63:127;;;;;;;-1:-1:-1;;;;;15873:46:127;;;;:28;:46;;;;;;;15858:63;;15923:38;;;;;;;;;;;-1:-1:-1;;;15840:131:127;;:4;:131::i;:::-;15828:143;;15995:85;16000:40;;;;;;;;16015:23;16000:40;;;16042:37;;;;;;;;16057:20;16042:37;;;15995:4;:85::i;:::-;15981:99;;16098:28;16103:9;16114:11;16098:4;:28::i;:::-;16090:36;;16144:44;16163:5;16170:17;16144:18;:44::i;:::-;16137:51;14697:1498;-1:-1:-1;;;;;;;;;;14697:1498:127:o;6978:667::-;2334:13:21;:11;:13::i;:::-;-1:-1:-1;;;;;7055:24:127;::::1;;::::0;;;:7:::1;:24;::::0;;;;:33;::::1;;7054:34;7046:75;;;;-1:-1:-1::0;;;7046:75:127::1;;;;;;;;;;;;-1:-1:-1::0;;;;;7232:15:127;::::1;7191:38;7232:15:::0;;;:7:::1;:15;::::0;;;;7257:25;;7278:4:::1;-1:-1:-1::0;;7257:25:127;;::::1;::::0;::::1;::::0;;7292:18:::1;::::0;::::1;:26:::0;;;;::::1;::::0;;;7328:34;::::1;:38:::0;;;7232:15;7377:193:::1;7401:10;:17:::0;7397:21;::::1;7377:193;;;7460:6;-1:-1:-1::0;;;;;7443:23:127::1;:10;7454:1;7443:13;;;;;;;;:::i;:::-;;::::0;;;::::1;::::0;;;::::1;::::0;-1:-1:-1;;;;;7443:13:127::1;:23:::0;7435:64:::1;;;;-1:-1:-1::0;;;7435:64:127::1;;;;;;;;;;;;7542:3;;7377:193;;;-1:-1:-1::0;7579:10:127::1;:23:::0;;::::1;::::0;::::1;::::0;;-1:-1:-1;7579:23:127;;;;;::::1;::::0;;-1:-1:-1;;;;;;7579:23:127::1;-1:-1:-1::0;;;;;7579:23:127;::::1;::::0;;::::1;::::0;;;7618:20:::1;::::0;1577:51:242;;;7618:20:127::1;::::0;1565:2:242;1550:18;7618:20:127::1;;;;;;;7036:609;6978:667:::0;:::o;3134:119::-;2334:13:21;:11;:13::i;:::-;3190:16:127::1;:23:::0;;-1:-1:-1;;3190:23:127::1;3209:4;3190:23;::::0;;3228:18:::1;::::0;::::1;::::0;3190:16:::1;::::0;3228:18:::1;3134:119::o:0;10919:708::-;11064:7;:5;:7::i;:::-;-1:-1:-1;;;;;11050:21:127;:10;-1:-1:-1;;;;;11050:21:127;;:100;;;-1:-1:-1;11075:13:127;;11114:35;;;-1:-1:-1;;;11114:35:127;;;;-1:-1:-1;;;;;11075:13:127;;;;:26;;11102:10;;11075:13;;11114:33;;:35;;;;;;;;;;;;;;11075:13;11114:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;11075:75;;-1:-1:-1;;;;;;11075:75:127;;;;;;;-1:-1:-1;;;;;12783:32:242;;;11075:75:127;;;12765:51:242;12832:18;;;12825:34;12738:18;;11075:75:127;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;11029:171;;;;-1:-1:-1;;;11029:171:127;;;;;;;;;;;;11232:7;11280:13;11318:15;;;;;:46;;;11351:13;11337:10;:27;11318:46;11310:80;;;;-1:-1:-1;;;11310:80:127;;;;;;;;;;;;11406:9;11401:220;11421:10;11417:1;:14;11401:220;;;11473:13;;11487:1;11473:16;;;;;;;:::i;:::-;;;;;;;11448:10;:22;11459:7;;11467:1;11459:10;;;;;;;:::i;:::-;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;11448:22:127;;;;;;;;;;;;-1:-1:-1;11448:22:127;:41;11521:7;;11529:1;11521:10;;;;;;;:::i;:::-;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;11508:42:127;;11533:13;;11547:1;11533:16;;;;;;;:::i;:::-;;;;;;;11508:42;;;;687:25:242;;675:2;660:18;;541:177;11508:42:127;;;;;;;;11593:3;;11401:220;;3313:122;2334:13:21;:11;:13::i;:::-;3370:16:127::1;:24:::0;;-1:-1:-1;;3370:24:127::1;::::0;;3409:19:::1;::::0;::::1;::::0;3389:5:::1;::::0;3409:19:::1;3313:122::o:0;20093:503::-;20152:7;20171:11;20197:9;20192:378;20212:10;:17;20208:21;;20192:378;;;20246:15;20272:10;20283:1;20272:13;;;;;;;;:::i;:::-;;;;;;;;;;;-1:-1:-1;;;;;20272:13:127;;-1:-1:-1;20304:31:127;20272:13;20304;:31::i;:::-;20300:45;;;20337:8;20192:378;;20300:45;20359:25;20387:7;-1:-1:-1;;;;;20387:23:127;;:25;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;20359:53;;20433:67;20464:17;20491:7;20433:30;:67::i;:::-;20426:74;;;;:::i;:::-;;;20542:3;;;;;20232:338;;20192:378;;;-1:-1:-1;20586:3:127;20093:503;-1:-1:-1;20093:503:127:o;1871:50:128:-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;1871:50:128;;-1:-1:-1;1871:50:128;;-1:-1:-1;1871:50:128:o;18852:199:127:-;18943:16;;;18957:1;18943:16;;;;;;;;;18916:24;;18943:16;;;;;;;;;;;-1:-1:-1;18943:16:127;18916:43;;18982:6;18969:7;18977:1;18969:10;;;;;;;;:::i;:::-;;;;;;:19;-1:-1:-1;;;;;18969:19:127;;;-1:-1:-1;;;;;18969:19:127;;;;;19005:39;19012:7;19021:10;19005:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;19005:39:127;;;;;;;;;;;;;;;;;;;;;19033:4;19039;19005:6;:39::i;17057:1653::-;-1:-1:-1;;;;;17166:16:127;;17122:41;17166:16;;;:7;:16;;;;;;;;17295:10;17264:42;;:30;;;:42;;;;;;;;;17259:56;;17308:7;17057:1653;:::o;17259:56::-;17447:47;;-1:-1:-1;;;17447:47:127;;17483:10;17447:47;;;1577:51:242;17404:18:127;;;;-1:-1:-1;;;;;17447:35:127;;;;;1550:18:242;;17447:47:127;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;17403:91;;;;;17513:10;17527:1;17513:15;17505:65;;;;-1:-1:-1;;;17505:65:127;;;;;;;;;;;;17608:46;17622:7;17631:10;17643;17608:13;:46::i;:::-;17756:10;17725:42;;;;:30;;;:42;;;;;;;;17718:49;;-1:-1:-1;;17718:49:127;;;17922:13;:25;;;;;17889:58;;;;;;;;;;;;;;;;;;;17922:25;;17889:58;;;17922:25;17889:58;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;17889:58:127;;;;;;;;;;;;;;;;-1:-1:-1;;17971:20:127;;17889:58;;-1:-1:-1;17971:20:127;;-1:-1:-1;17957:11:127;;-1:-1:-1;;18035:213:127;18055:3;18051:1;:7;18035:213;;;18099:7;-1:-1:-1;;;;;18079:27:127;:13;18093:1;18079:16;;;;;;;;:::i;:::-;;;;;;;-1:-1:-1;;;;;18079:27:127;;18075:103;;18139:1;18126:14;;18158:5;;18075:103;18220:3;;18035:213;;;;18375:3;18362:10;:16;18354:51;;;;-1:-1:-1;;;18354:51:127;;;;;;;;;;;;18549:10;18504:28;18535:25;;;:13;:25;;;;;18606:17;;18535:25;;18606:21;;18626:1;;18606:21;:::i;:::-;18595:33;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;-1:-1:-1;;;;;18595:33:127;18570:10;18581;18570:22;;;;;;;;:::i;:::-;;;;;;;;;:58;;;;;-1:-1:-1;;;;;18570:58:127;;;;;-1:-1:-1;;;;;18570:58:127;;;;;;18638:10;:16;;;;;;;:::i;:::-;;;;;;;;;;-1:-1:-1;;18638:16:127;;;;;-1:-1:-1;;;;;;18638:16:127;;;;;;;;;18670:33;;18692:10;;-1:-1:-1;;;;;18670:33:127;;;;;18638:16;18670:33;17112:1598;;;;;;;17057:1653;:::o;3405:215:21:-;2334:13;:11;:13::i;:::-;-1:-1:-1;;;;;3489:22:21;::::1;3485:91;;3534:31;::::0;-1:-1:-1;;;3534:31:21;;3562:1:::1;3534:31;::::0;::::1;1577:51:242::0;1550:18;;3534:31:21::1;;;;;;;;3485:91;3585:28;3604:8;3585:18;:28::i;3556:237:127:-:0;2334:13:21;:11;:13::i;:::-;-1:-1:-1;;;;;3635:20:127;::::1;3627:54;;;;-1:-1:-1::0;;;3627:54:127::1;;;;;;;;;;;;3722:13;::::0;;3697:48:::1;::::0;-1:-1:-1;;;;;3697:48:127;;::::1;::::0;3722:13;::::1;::::0;3697:48:::1;::::0;::::1;3756:13;:30:::0;;-1:-1:-1;;;;;;3756:30:127::1;-1:-1:-1::0;;;;;3756:30:127;;;::::1;::::0;;;::::1;::::0;;3556:237::o;1631:779::-;8870:21:22;4302:15;;-1:-1:-1;;;4302:15:22;;;;4301:16;;4348:14;;4158:30;4726:16;;:34;;;;;4746:14;4726:34;4706:54;;4770:17;4790:11;:16;;4805:1;4790:16;:50;;;;-1:-1:-1;4818:4:22;4810:25;:30;4790:50;4770:70;;4856:12;4855:13;:30;;;;;4873:12;4872:13;4855:30;4851:91;;;4908:23;;-1:-1:-1;;;4908:23:22;;;;;;;;;;;4851:91;4951:18;;-1:-1:-1;;4951:18:22;4968:1;4951:18;;;4979:67;;;;5013:22;;-1:-1:-1;;;;5013:22:22;-1:-1:-1;;;5013:22:22;;;4979:67;-1:-1:-1;;;;;1784:28:127;::::1;1776:70;;;;-1:-1:-1::0;;;1776:70:127::1;;;;;;;;;;;;-1:-1:-1::0;;;;;1864:32:127;::::1;1856:78;;;;-1:-1:-1::0;;;1856:78:127::1;;;;;;;;;;;;-1:-1:-1::0;;;;;1952:32:127;::::1;1944:78;;;;-1:-1:-1::0;;;1944:78:127::1;;;;;;;;;;;;-1:-1:-1::0;;;;;2040:20:127;::::1;2032:54;;;;-1:-1:-1::0;;;2032:54:127::1;;;;;;;;;;;;2096:22;2111:6;2096:14;:22::i;:::-;2128:13;:38:::0;;-1:-1:-1;;;;;2128:38:127;;::::1;-1:-1:-1::0;;;;;;2128:38:127;;::::1;;::::0;;;2176:52;;;;::::1;::::0;;::::1;;::::0;;2238:17:::1;:38:::0;;;;::::1;::::0;;;::::1;;::::0;;2311:7:::1;2286:22;:32:::0;2356:15:::1;2328:25;:43:::0;2381:18:::1;:22:::0;5066:101:22;;;;5100:23;;-1:-1:-1;;;;5100:23:22;;;5142:14;;-1:-1:-1;15183:50:242;;5142:14:22;;15171:2:242;15156:18;5142:14:22;;;;;;;4092:1081;;;;;1631:779:127;;;;:::o;8123:187::-;2334:13:21;:11;:13::i;:::-;8239:18:127::1;::::0;8207:59:::1;::::0;;8613:25:242;;;8669:2;8654:18;;8647:34;;;8227:10:127::1;::::0;8207:59:::1;::::0;8586:18:242;8207:59:127::1;;;;;;;8276:18;:27:::0;8123:187::o;1941:177:195:-;2022:7;2041:18;2062:15;2067:1;2070:6;2062:4;:15::i;:::-;2041:36;;2094:17;2103:7;2094:8;:17::i;:::-;2087:24;1941:177;-1:-1:-1;;;;1941:177:195:o;2658:162:21:-;966:10:23;2717:7:21;:5;:7::i;:::-;-1:-1:-1;;;;;2717:23:21;;2713:101;;2763:40;;-1:-1:-1;;;2763:40:21;;966:10:23;2763:40:21;;;1577:51:242;1550:18;;2763:40:21;1431:203:242;27869:602:127;-1:-1:-1;;;;;27980:15:127;;;;;;:7;:15;;;;;;;;27996:20;27980:37;;;;;;;;;;27979:38;27971:66;;;;-1:-1:-1;;;27971:66:127;;;;;;;;;;;;-1:-1:-1;;;;;28055:15:127;;;;;;:7;:15;;;;;:24;;;28047:61;;;;-1:-1:-1;;;28047:61:127;;;;;;;;;;;;-1:-1:-1;;;;;28217:15:127;;;;;;;:7;:15;;;;;;;;:43;;;;;:33;;;;:43;;;;;;28212:57;;27869:602;;;:::o;28212:57::-;28309:17;28330:67;28363:8;28373:6;28381:12;28395:1;28330:32;:67::i;:::-;28306:91;-1:-1:-1;;28415:14:127;;28407:57;;;;-1:-1:-1;;;28407:57:127;;;;;;;;;;;31303:137;31389:17;;31370:63;;-1:-1:-1;;;31370:63:127;;-1:-1:-1;;;;;1595:32:242;;;31370:63:127;;;1577:51:242;31389:17:127;;;;31370:55;;1550:18:242;;31370:63:127;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;31951:163;32056:17;;32037:70;;-1:-1:-1;;;32037:70:127;;-1:-1:-1;;;;;15436:32:242;;;32037:70:127;;;15418:51:242;15505:32;;;15485:18;;;15478:60;32056:17:127;;;;32037:52;;15391:18:242;;32037:70:127;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;32609:1112;32741:14;;32727:11;32765:887;32785:3;32781:1;:7;32765:887;;;32805:14;32822:7;32830:1;32822:10;;;;;;;;:::i;:::-;;;;;;;;;;;;-1:-1:-1;;;;;32854:15:127;;;;;;:7;:15;;;;;;;:24;32822:10;;-1:-1:-1;32854:24:127;;32846:61;;;;-1:-1:-1;;;32846:61:127;;;;;;;;;;;;32925:9;32921:324;;;32954:40;32986:6;32954:23;:40::i;:::-;33017:9;33012:219;33036:7;:14;33032:1;:18;33012:219;;;33075:53;33108:6;33117:7;33125:1;33117:10;;;;;;;;:::i;:::-;;;;;;;33075:24;:53::i;:::-;33187:3;;33012:219;;;;32921:324;33262:9;33258:324;;;33291:40;33323:6;33291:23;:40::i;:::-;33354:9;33349:219;33373:7;:14;33369:1;:18;33349:219;;;33412:53;33445:6;33454:7;33462:1;33454:10;;;;;;;;:::i;:::-;;;;;;;33412:24;:53::i;:::-;33524:3;;33349:219;;;;33258:324;-1:-1:-1;33624:3:127;;32765:887;;;-1:-1:-1;33681:17:127;;33662:52;;-1:-1:-1;;;33662:52:127;;-1:-1:-1;;;;;33681:17:127;;;;33662:43;;:52;;33706:7;;33662:52;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;28477:2678;28662:7;28671;28690:37;;:::i;:::-;-1:-1:-1;;;;;28789:22:127;;28775:11;28789:22;;;:13;:22;;;;;:29;;28828:2087;28848:3;28844:1;:7;28828:2087;;;-1:-1:-1;;;;;28885:22:127;;28868:14;28885:22;;;:13;:22;;;;;:25;;28908:1;;28885:25;;;;;;:::i;:::-;;;;;;;;;;;29078:43;;-1:-1:-1;;;29078:43:127;;-1:-1:-1;;;;;1595:32:242;;;29078:43:127;;;1577:51:242;28885:25:127;;;;-1:-1:-1;28885:25:127;;29078:34;;1550:18:242;;29078:43:127;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;29033:25;;;28992:129;;;29013:18;;;28992:129;;;;28993:18;;;;28992:129;;;;29160:57;;-1:-1:-1;29160:57:127;;;;;-1:-1:-1;;;;;29175:15:127;;;-1:-1:-1;29175:15:127;;;:7;:15;;;;;:40;;;29160:57;;29136:21;;;:81;;;;29251:42;;;;;;;29266:25;;29251:42;;29231:17;;;:62;29404:14;;29388:58;;-1:-1:-1;;;29388:58:127;;;;;1577:51:242;;;;29404:14:127;;;29388:50;;1550:18:242;;29388:58:127;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;29361:24;;;:85;;;29496:1;29468:29;29460:77;;;;-1:-1:-1;;;29460:77:127;;;;;;;;;;;;29571:41;;;;;;;;;29586:24;;;;29571:41;;29552:16;;;:60;29751:21;;;;29774:17;;;;29741:70;;29746:46;;:4;:46::i;:::-;29794:4;:16;;;29741:4;:70::i;:::-;29720:18;;;:91;;;29955:18;;;;29975;;29909:85;;29720:91;29955:18;29909:25;:85::i;:::-;29888:106;;30146:16;;;;30164:18;;;;30184:25;;;;30120:90;;30146:16;30164:18;30120:25;:90::i;:::-;30076:25;;;:134;-1:-1:-1;;;;;30295:22:127;;;;;;;30291:554;;30490:86;30516:4;:18;;;30536:12;30550:4;:25;;;30490;:86::i;:::-;30442:25;;;:134;;;30772:16;;;;30746:84;;30790:12;;30746:25;:84::i;:::-;30698:25;;;:132;30291:554;-1:-1:-1;30887:3:127;;28828:2087;;;-1:-1:-1;30950:25:127;;;;30929:18;;:46;30925:224;;;31020:25;;;;30999:18;;:46;;31020:25;30999:46;:::i;:::-;31047:1;30991:58;;;;;;;;30925:224;31119:18;;31091:25;;;;31088:1;;31091:46;;;:::i;:::-;31080:58;;;;;;;;27419:444;-1:-1:-1;;;;;27541:16:127;;27497:41;27541:16;;;:7;:16;;;;;27575:21;;;;27567:58;;;;-1:-1:-1;;;27567:58:127;;;;;;;;;;;;-1:-1:-1;;;;;27641:40:127;;;;;;:30;;;:40;;;;;;;;27636:221;;-1:-1:-1;;;;;27697:40:127;;;;;;;:30;;;:40;;;;;;;;:47;;-1:-1:-1;;27697:47:127;27740:4;27697:47;;;;;;27758:13;:23;;;;;:37;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;;27758:37:127;;;;;;;27814:32;27697:40;;27758:37;27814:32;;27697:40;27814:32;27487:376;27419:444;;:::o;3955:97:195:-;4014:7;4040:5;4044:1;4040;:5;:::i;:::-;4033:12;3955:97;-1:-1:-1;;;3955:97:195:o;31588:137:127:-;31674:17;;31655:63;;-1:-1:-1;;;31655:63:127;;-1:-1:-1;;;;;1595:32:242;;;31655:63:127;;;1577:51:242;31674:17:127;;;;31655:55;;1550:18:242;;31655:63:127;1431:203:242;32440:163:127;32545:17;;32526:70;;-1:-1:-1;;;32526:70:127;;-1:-1:-1;;;;;15436:32:242;;;32526:70:127;;;15418:51:242;15505:32;;;15485:18;;;15478:60;32545:17:127;;;;32526:52;;15391:18:242;;32526:70:127;15244:300:242;3774:248:21;1313:22;3923:8;;-1:-1:-1;;;;;;3941:19:21;;-1:-1:-1;;;;;3941:19:21;;;;;;;;3975:40;;3923:8;;;;;3975:40;;3847:24;;3975:40;3837:185;;3774:248;:::o;27001:412:127:-;27161:14;;27145:58;;-1:-1:-1;;;27145:58:127;;-1:-1:-1;;;;;1595:32:242;;;27145:58:127;;;1577:51:242;27096:7:127;;;;27161:14;;;27145:50;;1550:18:242;;27145:58:127;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;27115:88;;27221:19;27244:1;27221:24;27213:72;;;;-1:-1:-1;;;27213:72:127;;;;;;;;;;;;27321:36;;;;;;;;;;;;27402:4;27374:25;27379:6;27321:36;27374:4;:25::i;:::-;:32;;;;:::i;:::-;27367:39;27001:412;-1:-1:-1;;;;;27001:412:127:o;33727:240::-;-1:-1:-1;;;;;33811:15:127;;33788:4;33811:15;;;:7;:15;;;;;:40;;;:45;:86;;;;-1:-1:-1;;;;;;33860:15:127;;;;;;:7;:15;;;;;;;;33876:20;33860:37;;;;;;;;;;33811:86;:149;;;;;33921:6;-1:-1:-1;;;;;33913:37:127;;:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;33956:4;33913:47;33804:156;33727:240;-1:-1:-1;;33727:240:127:o;4477:157:195:-;-1:-1:-1;;;;;;;;;;;;4571:56:195;;;;;;;;1224:4;4586:28;4591:1;:10;;;4603:1;:10;;;4586:4;:28::i;:::-;:39;;;;:::i;:::-;4571:56;;4564:63;4477:157;-1:-1:-1;;;4477:157:195:o;5478:162::-;-1:-1:-1;;;;;;;;;;;;5572:61:195;;;;;;;;5587:44;5592:26;5597:1;:10;;;1224:4;5592;:26::i;:::-;5620:10;;5587:4;:44::i;1847:127:21:-;6931:20:22;:18;:20::i;:::-;1929:38:21::1;1954:12;1929:24;:38::i;4640:134:195:-:0;-1:-1:-1;;;;;;;;;;;;4731:36:195;;;;;;;;4746:19;4751:1;:10;;;4763:1;4746:4;:19::i;1620:213::-;1803:12;;1677:7;;1803:23;;1224:4;;1803:23;:::i;2258:214::-;2362:7;2381:18;2402:15;2407:1;2410:6;2402:4;:15::i;:::-;2381:36;;2434:31;2439:17;2448:7;2439:8;:17::i;:::-;2458:6;2434:4;:31::i;4780:125::-;4842:7;1224:4;4868:19;4873:1;4876;:10;;;4868:4;:19::i;:::-;:30;;;;:::i;5375:97::-;5434:7;5460:5;5464:1;5460;:5;:::i;6396:97::-;6455:7;6481:5;6485:1;6481;:5;:::i;7084:141:22:-;8870:21;8560:40;-1:-1:-1;;;8560:40:22;;;;7146:73;;7191:17;;-1:-1:-1;;;7191:17:22;;;;;;;;;;;1980:235:21;6931:20:22;:18;:20::i;-1:-1:-1:-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;14:131:242:-;-1:-1:-1;;;;;89:31:242;;79:42;;69:70;;135:1;132;125:12;150:134;218:20;;247:31;218:20;247:31;:::i;:::-;150:134;;;:::o;289:247::-;348:6;401:2;389:9;380:7;376:23;372:32;369:52;;;417:1;414;407:12;369:52;456:9;443:23;475:31;500:5;475:31;:::i;723:155::-;802:20;;851:2;841:13;;831:41;;868:1;865;858:12;883:351;970:6;978;1031:2;1019:9;1010:7;1006:23;1002:32;999:52;;;1047:1;1044;1037:12;999:52;1086:9;1073:23;1105:31;1130:5;1105:31;:::i;:::-;1155:5;-1:-1:-1;1179:49:242;1224:2;1209:18;;1179:49;:::i;:::-;1169:59;;883:351;;;;;:::o;1639:226::-;1698:6;1751:2;1739:9;1730:7;1726:23;1722:32;1719:52;;;1767:1;1764;1757:12;1719:52;-1:-1:-1;1812:23:242;;1639:226;-1:-1:-1;1639:226:242:o;1870:650::-;1956:6;1964;1972;1980;2033:3;2021:9;2012:7;2008:23;2004:33;2001:53;;;2050:1;2047;2040:12;2001:53;2089:9;2076:23;2108:31;2133:5;2108:31;:::i;:::-;2158:5;-1:-1:-1;2215:2:242;2200:18;;2187:32;2228:33;2187:32;2228:33;:::i;:::-;2280:7;-1:-1:-1;2339:2:242;2324:18;;2311:32;2352:33;2311:32;2352:33;:::i;:::-;1870:650;;;;-1:-1:-1;2404:7:242;;2484:2;2469:18;2456:32;;-1:-1:-1;;1870:650:242:o;2525:367::-;2588:8;2598:6;2652:3;2645:4;2637:6;2633:17;2629:27;2619:55;;2670:1;2667;2660:12;2619:55;-1:-1:-1;2693:20:242;;2736:18;2725:30;;2722:50;;;2768:1;2765;2758:12;2722:50;2805:4;2797:6;2793:17;2781:29;;2865:3;2858:4;2848:6;2845:1;2841:14;2833:6;2829:27;2825:38;2822:47;2819:67;;;2882:1;2879;2872:12;2819:67;2525:367;;;;;:::o;2897:768::-;3019:6;3027;3035;3043;3096:2;3084:9;3075:7;3071:23;3067:32;3064:52;;;3112:1;3109;3102:12;3064:52;3152:9;3139:23;3185:18;3177:6;3174:30;3171:50;;;3217:1;3214;3207:12;3171:50;3256:70;3318:7;3309:6;3298:9;3294:22;3256:70;:::i;:::-;3345:8;;-1:-1:-1;3230:96:242;-1:-1:-1;;3433:2:242;3418:18;;3405:32;3462:18;3449:32;;3446:52;;;3494:1;3491;3484:12;3446:52;3533:72;3597:7;3586:8;3575:9;3571:24;3533:72;:::i;:::-;2897:768;;;;-1:-1:-1;3624:8:242;-1:-1:-1;;;;2897:768:242:o;3670:127::-;3731:10;3726:3;3722:20;3719:1;3712:31;3762:4;3759:1;3752:15;3786:4;3783:1;3776:15;3802:906;3856:5;3909:3;3902:4;3894:6;3890:17;3886:27;3876:55;;3927:1;3924;3917:12;3876:55;3967:6;3954:20;3997:18;3989:6;3986:30;3983:56;;;4019:18;;:::i;:::-;4065:6;4062:1;4058:14;4101:2;4095:9;4164:2;4160:7;4155:2;4151;4147:11;4143:25;4135:6;4131:38;4235:6;4223:10;4220:22;4199:18;4187:10;4184:34;4181:62;4178:88;;;4246:18;;:::i;:::-;4282:2;4275:22;4332;;;4382:4;4414:15;;;4410:26;;;4332:22;4370:17;;4448:15;;;4445:35;;;4476:1;4473;4466:12;4445:35;4512:4;4504:6;4500:17;4489:28;;4526:152;4542:6;4537:3;4534:15;4526:152;;;4610:23;4629:3;4610:23;:::i;:::-;4598:36;;4663:4;4559:14;;;;4654;4526:152;;;-1:-1:-1;4696:6:242;3802:906;-1:-1:-1;;;;;3802:906:242:o;4713:483::-;4806:6;4814;4867:2;4855:9;4846:7;4842:23;4838:32;4835:52;;;4883:1;4880;4873:12;4835:52;4922:9;4909:23;4941:31;4966:5;4941:31;:::i;:::-;4991:5;-1:-1:-1;5047:2:242;5032:18;;5019:32;5074:18;5063:30;;5060:50;;;5106:1;5103;5096:12;5060:50;5129:61;5182:7;5173:6;5162:9;5158:22;5129:61;:::i;:::-;5119:71;;;4713:483;;;;;:::o;5201:508::-;5278:6;5286;5294;5347:2;5335:9;5326:7;5322:23;5318:32;5315:52;;;5363:1;5360;5353:12;5315:52;5402:9;5389:23;5421:31;5446:5;5421:31;:::i;:::-;5471:5;-1:-1:-1;5528:2:242;5513:18;;5500:32;5541:33;5500:32;5541:33;:::i;:::-;5201:508;;5593:7;;-1:-1:-1;;;5673:2:242;5658:18;;;;5645:32;;5201:508::o;5714:118::-;5800:5;5793:13;5786:21;5779:5;5776:32;5766:60;;5822:1;5819;5812:12;5837:855;5967:6;5975;5983;5991;6044:3;6032:9;6023:7;6019:23;6015:33;6012:53;;;6061:1;6058;6051:12;6012:53;6101:9;6088:23;6134:18;6126:6;6123:30;6120:50;;;6166:1;6163;6156:12;6120:50;6189:61;6242:7;6233:6;6222:9;6218:22;6189:61;:::i;:::-;6179:71;;;6303:2;6292:9;6288:18;6275:32;6332:18;6322:8;6319:32;6316:52;;;6364:1;6361;6354:12;6316:52;6387:63;6442:7;6431:8;6420:9;6416:24;6387:63;:::i;:::-;6377:73;;;6500:2;6489:9;6485:18;6472:32;6513:28;6535:5;6513:28;:::i;:::-;6560:5;-1:-1:-1;6617:2:242;6602:18;;6589:32;6630:30;6589:32;6630:30;:::i;:::-;5837:855;;;;-1:-1:-1;5837:855:242;;-1:-1:-1;;5837:855:242:o;6927:382::-;6992:6;7000;7053:2;7041:9;7032:7;7028:23;7024:32;7021:52;;;7069:1;7066;7059:12;7021:52;7108:9;7095:23;7127:31;7152:5;7127:31;:::i;:::-;7177:5;-1:-1:-1;7234:2:242;7219:18;;7206:32;7247:30;7206:32;7247:30;:::i;:::-;7296:7;7286:17;;;6927:382;;;;;:::o;7314:486::-;7407:6;7415;7423;7476:2;7464:9;7455:7;7451:23;7447:32;7444:52;;;7492:1;7489;7482:12;7444:52;7531:9;7518:23;7550:31;7575:5;7550:31;:::i;:::-;7600:5;-1:-1:-1;7624:49:242;7669:2;7654:18;;7624:49;:::i;:::-;7614:59;;7725:2;7714:9;7710:18;7697:32;7738:30;7760:7;7738:30;:::i;:::-;7787:7;7777:17;;;7314:486;;;;;:::o;7805:629::-;7891:6;7899;7907;7915;7968:3;7956:9;7947:7;7943:23;7939:33;7936:53;;;7985:1;7982;7975:12;7936:53;8024:9;8011:23;8043:31;8068:5;8043:31;:::i;:::-;8093:5;-1:-1:-1;8150:2:242;8135:18;;8122:32;8163:33;8122:32;8163:33;:::i;:::-;7805:629;;8215:7;;-1:-1:-1;;;;8295:2:242;8280:18;;8267:32;;8398:2;8383:18;8370:32;;7805:629::o;8916:671::-;9002:6;9010;9018;9026;9079:3;9067:9;9058:7;9054:23;9050:33;9047:53;;;9096:1;9093;9086:12;9047:53;9135:9;9122:23;9154:31;9179:5;9154:31;:::i;:::-;9204:5;-1:-1:-1;9261:2:242;9246:18;;9233:32;9274:33;9233:32;9274:33;:::i;:::-;9326:7;-1:-1:-1;9385:2:242;9370:18;;9357:32;9398:33;9357:32;9398:33;:::i;:::-;9450:7;-1:-1:-1;9509:2:242;9494:18;;9481:32;9522:33;9481:32;9522:33;:::i;9936:388::-;10004:6;10012;10065:2;10053:9;10044:7;10040:23;10036:32;10033:52;;;10081:1;10078;10071:12;10033:52;10120:9;10107:23;10139:31;10164:5;10139:31;:::i;:::-;10189:5;-1:-1:-1;10246:2:242;10231:18;;10218:32;10259:33;10218:32;10259:33;:::i;10329:367::-;10397:6;10405;10458:2;10446:9;10437:7;10433:23;10429:32;10426:52;;;10474:1;10471;10464:12;10426:52;10513:9;10500:23;10532:31;10557:5;10532:31;:::i;:::-;10582:5;10660:2;10645:18;;;;10632:32;;-1:-1:-1;;;10329:367:242:o;10701:637::-;10891:2;10903:21;;;10973:13;;10876:18;;;10995:22;;;10843:4;;11074:15;;;11048:2;11033:18;;;10843:4;11117:195;11131:6;11128:1;11125:13;11117:195;;;11196:13;;-1:-1:-1;;;;;11192:39:242;11180:52;;11261:2;11287:15;;;;11252:12;;;;11228:1;11146:9;11117:195;;;-1:-1:-1;11329:3:242;;10701:637;-1:-1:-1;;;;;10701:637:242:o;11343:437::-;11429:6;11437;11490:2;11478:9;11469:7;11465:23;11461:32;11458:52;;;11506:1;11503;11496:12;11458:52;11546:9;11533:23;11579:18;11571:6;11568:30;11565:50;;;11611:1;11608;11601:12;11565:50;11650:70;11712:7;11703:6;11692:9;11688:22;11650:70;:::i;:::-;11739:8;;11624:96;;-1:-1:-1;11343:437:242;-1:-1:-1;;;;11343:437:242:o;11785:127::-;11846:10;11841:3;11837:20;11834:1;11827:31;11877:4;11874:1;11867:15;11901:4;11898:1;11891:15;11917:230;11987:6;12040:2;12028:9;12019:7;12015:23;12011:32;12008:52;;;12056:1;12053;12046:12;12008:52;-1:-1:-1;12101:16:242;;11917:230;-1:-1:-1;11917:230:242:o;12152:245::-;12219:6;12272:2;12260:9;12251:7;12247:23;12243:32;12240:52;;;12288:1;12285;12278:12;12240:52;12320:9;12314:16;12339:28;12361:5;12339:28;:::i;12870:127::-;12931:10;12926:3;12922:20;12919:1;12912:31;12962:4;12959:1;12952:15;12986:4;12983:1;12976:15;13002:429;13175:2;13160:18;;13208:2;13197:14;;13187:145;;13254:10;13249:3;13245:20;13242:1;13235:31;13289:4;13286:1;13279:15;13317:4;13314:1;13307:15;13187:145;13341:25;;;13409:14;;13402:22;13397:2;13382:18;;;13375:50;13002:429;:::o;13436:251::-;13506:6;13559:2;13547:9;13538:7;13534:23;13530:32;13527:52;;;13575:1;13572;13565:12;13527:52;13607:9;13601:16;13626:31;13651:5;13626:31;:::i;13692:127::-;13753:10;13748:3;13744:20;13741:1;13734:31;13784:4;13781:1;13774:15;13808:4;13805:1;13798:15;13824:125;13889:9;;;13910:10;;;13907:36;;;13923:18;;:::i;14304:456::-;14392:6;14400;14408;14461:2;14449:9;14440:7;14436:23;14432:32;14429:52;;;14477:1;14474;14467:12;14429:52;-1:-1:-1;;14522:16:242;;14628:2;14613:18;;14607:25;14724:2;14709:18;;;14703:25;14522:16;;14607:25;;-1:-1:-1;14703:25:242;14304:456;-1:-1:-1;14304:456:242:o;14765:128::-;14832:9;;;14853:11;;;14850:37;;;14867:18;;:::i;14898:127::-;14959:10;14954:3;14950:20;14947:1;14940:31;14990:4;14987:1;14980:15;15014:4;15011:1;15004:15;15549:217;15589:1;15615;15605:132;;15659:10;15654:3;15650:20;15647:1;15640:31;15694:4;15691:1;15684:15;15722:4;15719:1;15712:15;15605:132;-1:-1:-1;15751:9:242;;15549:217::o;15771:168::-;15844:9;;;15875;;15892:15;;;15886:22;;15872:37;15862:71;;15913:18;;:::i", "linkReferences": {}}, "methodIdentifiers": {"accountAssets(address,uint256)": "dce15449", "afterMTokenMint(address)": "0d926fc8", "allMarkets(uint256)": "52d84d1e", "beforeMTokenBorrow(address,address,uint256)": "50795f8a", "beforeMTokenLiquidate(address,address,address,uint256)": "b50ce762", "beforeMTokenMint(address,address)": "c0f1ee09", "beforeMTokenRedeem(address,address,uint256)": "1e32bd9b", "beforeMTokenRepay(address,address)": "c321fbcc", "beforeMTokenSeize(address,address,address,address)": "6765dff9", "beforeMTokenTransfer(address,address,address,uint256)": "17bf120e", "beforeRebalancing(address)": "68f6f4b0", "blacklistOperator()": "2d57d487", "borrowCaps(address)": "4a584432", "checkMembership(address,address)": "929fe9a1", "checkOutflowVolumeLimit(uint256)": "823307f2", "claimMalda(address)": "e44a429a", "claimMalda(address,address[])": "1c7818ac", "claimMalda(address[],address[],bool,bool)": "1fbd27a5", "closeFactorMantissa()": "e8755446", "cumulativeOutflowVolume()": "700e1212", "disableWhitelist()": "d6b0f484", "enableWhitelist()": "cdfb2b4e", "enterMarkets(address[])": "c2998238", "enterMarketsWithSender(address)": "973fd521", "exitMarket(address)": "ede4edd0", "getAccountLiquidity(address)": "5ec88c79", "getAllMarkets()": "b0772d0b", "getAssetsIn(address)": "abfceffc", "getHypotheticalAccountLiquidity(address,address,uint256,uint256)": "4e79238f", "getUSDValueForAllMarkets()": "d99faea5", "initialize(address,address,address,address)": "f8c8765e", "isDeprecated(address)": "94543c15", "isMarketListed(address)": "3d98a1e5", "isOperator()": "4456eda2", "isPaused(address,uint8)": "0d126627", "lastOutflowResetTimestamp()": "ddf46254", "limitPerTimePeriod()": "8728d8a7", "liquidateCalculateSeizeTokens(address,address,uint256)": "c488847b", "liquidationIncentiveMantissa(address)": "2e06d7b1", "markets(address)": "8e8f294b", "oracleOperator()": "11679ef7", "outflowResetTimeWindow()": "e92081b4", "owner()": "8da5cb5b", "renounceOwnership()": "715018a6", "resetOutflowVolume()": "52a2548d", "rewardDistributor()": "acc2166a", "rolesOperator()": "4fecab70", "setCloseFactor(uint256)": "12348e96", "setCollateralFactor(address,uint256)": "c04f31ff", "setLiquidationIncentive(address,uint256)": "9bd8f6e8", "setMarketBorrowCaps(address[],uint256[])": "186db48f", "setMarketSupplyCaps(address[],uint256[])": "d136af44", "setOutflowTimeLimitInUSD(uint256)": "f9f00f89", "setOutflowVolumeTimeWindow(uint256)": "befca684", "setPaused(address,uint8,bool)": "4a675b34", "setPriceOracle(address)": "530e784f", "setRewardDistributor(address)": "a1809b95", "setRolesOperator(address)": "f89416ee", "setWhitelistedUser(address,bool)": "44710fbe", "supplyCaps(address)": "02c3bcbb", "supportMarket(address)": "cab4f84c", "transferOwnership(address)": "f2fde38b", "userWhitelisted(address)": "fc2e0c2f", "whitelistEnabled()": "51fb012d"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"InvalidInitialization\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotInitializing\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_AssetNotFound\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_Deactivate_MarketBalanceOwed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_EmptyPrice\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_InsufficientLiquidity\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_InvalidBlacklistOperator\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_InvalidCollateralFactor\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_InvalidInput\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_InvalidRewardDistributor\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_InvalidRolesOperator\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_MarketAlreadyListed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_MarketBorrowCapReached\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_MarketNotListed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_MarketSupplyReached\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_Mismatch\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_OnlyAdmin\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_OnlyAdminOrRole\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_OracleUnderlyingFetchError\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_OutflowVolumeReached\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_Paused\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_PriceFetchFailed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_RepayAmountNotValid\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_RepayingTooMuch\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_SenderMustBeToken\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_UserBlacklisted\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_UserNotWhitelisted\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Operator_WrongMarket\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"OwnableInvalidOwner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"OwnableUnauthorizedAccount\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"enum ImTokenOperationTypes.OperationType\",\"name\":\"_type\",\"type\":\"uint8\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"state\",\"type\":\"bool\"}],\"name\":\"ActionPaused\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"version\",\"type\":\"uint64\"}],\"name\":\"Initialized\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"MarketEntered\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"MarketExited\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"}],\"name\":\"MarketListed\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"newBorrowCap\",\"type\":\"uint256\"}],\"name\":\"NewBorrowCap\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"oldCloseFactorMantissa\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"newCloseFactorMantissa\",\"type\":\"uint256\"}],\"name\":\"NewCloseFactor\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"oldCollateralFactorMantissa\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"newCollateralFactorMantissa\",\"type\":\"uint256\"}],\"name\":\"NewCollateralFactor\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"market\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"oldLiquidationIncentiveMantissa\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"newLiquidationIncentiveMantissa\",\"type\":\"uint256\"}],\"name\":\"NewLiquidationIncentive\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"oldPriceOracle\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newPriceOracle\",\"type\":\"address\"}],\"name\":\"NewPriceOracle\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"oldRewardDistributor\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newRewardDistributor\",\"type\":\"address\"}],\"name\":\"NewRewardDistributor\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"oldRoles\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newRoles\",\"type\":\"address\"}],\"name\":\"NewRolesOperator\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"newBorrowCap\",\"type\":\"uint256\"}],\"name\":\"NewSupplyCap\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"oldLimit\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"newLimit\",\"type\":\"uint256\"}],\"name\":\"OutflowLimitUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"oldWindow\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"newWindow\",\"type\":\"uint256\"}],\"name\":\"OutflowTimeWindowUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[],\"name\":\"OutflowVolumeReset\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"state\",\"type\":\"bool\"}],\"name\":\"UserWhitelisted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[],\"name\":\"WhitelistDisabled\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[],\"name\":\"WhitelistEnabled\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"accountAssets\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"}],\"name\":\"afterMTokenMint\",\"outputs\":[],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"allMarkets\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"borrowAmount\",\"type\":\"uint256\"}],\"name\":\"beforeMTokenBorrow\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mTokenBorrowed\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"mTokenCollateral\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"repayAmount\",\"type\":\"uint256\"}],\"name\":\"beforeMTokenLiquidate\",\"outputs\":[],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"minter\",\"type\":\"address\"}],\"name\":\"beforeMTokenMint\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"redeemer\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"redeemTokens\",\"type\":\"uint256\"}],\"name\":\"beforeMTokenRedeem\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"}],\"name\":\"beforeMTokenRepay\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mTokenCollateral\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"mTokenBorrowed\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"liquidator\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"}],\"name\":\"beforeMTokenSeize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"src\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"dst\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"transferTokens\",\"type\":\"uint256\"}],\"name\":\"beforeMTokenTransfer\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"}],\"name\":\"beforeRebalancing\",\"outputs\":[],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"blacklistOperator\",\"outputs\":[{\"internalType\":\"contract IBlacklister\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"borrowCaps\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"}],\"name\":\"checkMembership\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"checkOutflowVolumeLimit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"holder\",\"type\":\"address\"},{\"internalType\":\"address[]\",\"name\":\"mTokens\",\"type\":\"address[]\"}],\"name\":\"claimMalda\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"holders\",\"type\":\"address[]\"},{\"internalType\":\"address[]\",\"name\":\"mTokens\",\"type\":\"address[]\"},{\"internalType\":\"bool\",\"name\":\"borrowers\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"suppliers\",\"type\":\"bool\"}],\"name\":\"claimMalda\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"holder\",\"type\":\"address\"}],\"name\":\"claimMalda\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"closeFactorMantissa\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"cumulativeOutflowVolume\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"disableWhitelist\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"enableWhitelist\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"_mTokens\",\"type\":\"address[]\"}],\"name\":\"enterMarkets\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_account\",\"type\":\"address\"}],\"name\":\"enterMarketsWithSender\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_mToken\",\"type\":\"address\"}],\"name\":\"exitMarket\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"getAccountLiquidity\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getAllMarkets\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"mTokens\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_user\",\"type\":\"address\"}],\"name\":\"getAssetsIn\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"mTokens\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"mTokenModify\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"redeemTokens\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"borrowAmount\",\"type\":\"uint256\"}],\"name\":\"getHypotheticalAccountLiquidity\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getUSDValueForAllMarkets\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_rolesOperator\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_blacklistOperator\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_rewardDistributor\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_admin\",\"type\":\"address\"}],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"}],\"name\":\"isDeprecated\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"}],\"name\":\"isMarketListed\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"isOperator\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"internalType\":\"enum ImTokenOperationTypes.OperationType\",\"name\":\"_type\",\"type\":\"uint8\"}],\"name\":\"isPaused\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"lastOutflowResetTimestamp\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"limitPerTimePeriod\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mTokenBorrowed\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"mTokenCollateral\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"actualRepayAmount\",\"type\":\"uint256\"}],\"name\":\"liquidateCalculateSeizeTokens\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"liquidationIncentiveMantissa\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"markets\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"isListed\",\"type\":\"bool\"},{\"internalType\":\"uint256\",\"name\":\"collateralFactorMantissa\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"isMalded\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"oracleOperator\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"outflowResetTimeWindow\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"resetOutflowVolume\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"rewardDistributor\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"rolesOperator\",\"outputs\":[{\"internalType\":\"contract IRoles\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"newCloseFactorMantissa\",\"type\":\"uint256\"}],\"name\":\"setCloseFactor\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"newCollateralFactorMantissa\",\"type\":\"uint256\"}],\"name\":\"setCollateralFactor\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"market\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"newLiquidationIncentiveMantissa\",\"type\":\"uint256\"}],\"name\":\"setLiquidationIncentive\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"mTokens\",\"type\":\"address[]\"},{\"internalType\":\"uint256[]\",\"name\":\"newBorrowCaps\",\"type\":\"uint256[]\"}],\"name\":\"setMarketBorrowCaps\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"mTokens\",\"type\":\"address[]\"},{\"internalType\":\"uint256[]\",\"name\":\"newSupplyCaps\",\"type\":\"uint256[]\"}],\"name\":\"setMarketSupplyCaps\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"setOutflowTimeLimitInUSD\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"newTimeWindow\",\"type\":\"uint256\"}],\"name\":\"setOutflowVolumeTimeWindow\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"},{\"internalType\":\"enum ImTokenOperationTypes.OperationType\",\"name\":\"_type\",\"type\":\"uint8\"},{\"internalType\":\"bool\",\"name\":\"state\",\"type\":\"bool\"}],\"name\":\"setPaused\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOracle\",\"type\":\"address\"}],\"name\":\"setPriceOracle\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newRewardDistributor\",\"type\":\"address\"}],\"name\":\"setRewardDistributor\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_roles\",\"type\":\"address\"}],\"name\":\"setRolesOperator\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"state\",\"type\":\"bool\"}],\"name\":\"setWhitelistedUser\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"supplyCaps\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"mToken\",\"type\":\"address\"}],\"name\":\"supportMarket\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"userWhitelisted\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"whitelistEnabled\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"InvalidInitialization()\":[{\"details\":\"The contract is already initialized.\"}],\"NotInitializing()\":[{\"details\":\"The contract is not initializing.\"}],\"OwnableInvalidOwner(address)\":[{\"details\":\"The owner is not a valid owner account. (eg. `address(0)`)\"}],\"OwnableUnauthorizedAccount(address)\":[{\"details\":\"The caller account is not authorized to perform an operation.\"}]},\"events\":{\"Initialized(uint64)\":{\"details\":\"Triggered when the contract has been initialized or reinitialized.\"}},\"kind\":\"dev\",\"methods\":{\"afterMTokenMint(address)\":{\"params\":{\"mToken\":\"Asset being minted\"}},\"beforeMTokenBorrow(address,address,uint256)\":{\"params\":{\"borrowAmount\":\"The amount of underlying the account would borrow\",\"borrower\":\"The account which would borrow the asset\",\"mToken\":\"The market to verify the borrow against\"}},\"beforeMTokenLiquidate(address,address,address,uint256)\":{\"params\":{\"borrower\":\"The address of the borrower\",\"mTokenBorrowed\":\"Asset which was borrowed by the borrower\",\"mTokenCollateral\":\"Asset which was used as collateral and will be seized\",\"repayAmount\":\"The amount of underlying being repaid\"}},\"beforeMTokenMint(address,address)\":{\"params\":{\"mToken\":\"The market to verify the mint against\",\"minter\":\"The account which would get the minted tokens\"}},\"beforeMTokenRedeem(address,address,uint256)\":{\"params\":{\"mToken\":\"The market to verify the redeem against\",\"redeemTokens\":\"The number of mTokens to exchange for the underlying asset in the market\",\"redeemer\":\"The account which would redeem the tokens\"}},\"beforeMTokenRepay(address,address)\":{\"params\":{\"borrower\":\"The account which would borrowed the asset\",\"mToken\":\"The market to verify the repay against\"}},\"beforeMTokenSeize(address,address,address,address)\":{\"params\":{\"borrower\":\"The address of the borrower\",\"liquidator\":\"The address repaying the borrow and seizing the collateral\",\"mTokenBorrowed\":\"Asset which was borrowed by the borrower\",\"mTokenCollateral\":\"Asset which was used as collateral and will be seized\"}},\"beforeMTokenTransfer(address,address,address,uint256)\":{\"params\":{\"dst\":\"The account which receives the tokens\",\"mToken\":\"The market to verify the transfer against\",\"src\":\"The account which sources the tokens\",\"transferTokens\":\"The number of mTokens to transfer\"}},\"beforeRebalancing(address)\":{\"params\":{\"mToken\":\"The market to verify the transfer against\"}},\"checkMembership(address,address)\":{\"params\":{\"account\":\"The address of the account to check\",\"mToken\":\"The mToken to check\"},\"returns\":{\"_0\":\"True if the account is in the asset, otherwise false.\"}},\"checkOutflowVolumeLimit(uint256)\":{\"params\":{\"amount\":\"The new limit\"}},\"claimMalda(address)\":{\"params\":{\"holder\":\"The address to claim MALDA for\"}},\"claimMalda(address,address[])\":{\"params\":{\"holder\":\"The address to claim MALDA for\",\"mTokens\":\"The list of markets to claim MALDA in\"}},\"claimMalda(address[],address[],bool,bool)\":{\"params\":{\"borrowers\":\"Whether or not to claim MALDA earned by borrowing\",\"holders\":\"The addresses to claim MALDA for\",\"mTokens\":\"The list of markets to claim MALDA in\",\"suppliers\":\"Whether or not to claim MALDA earned by supplying\"}},\"constructor\":{\"custom:oz-upgrades-unsafe-allow\":\"constructor\"},\"enterMarkets(address[])\":{\"params\":{\"_mTokens\":\"The list of addresses of the mToken markets to be enabled\"}},\"enterMarketsWithSender(address)\":{\"params\":{\"_account\":\"The account to add for\"}},\"exitMarket(address)\":{\"details\":\"Sender must not have an outstanding borrow balance in the asset,  or be providing necessary collateral for an outstanding borrow.\",\"params\":{\"_mToken\":\"The address of the asset to be removed\"}},\"getAccountLiquidity(address)\":{\"returns\":{\"_0\":\"account liquidity in excess of collateral requirements,          account shortfall below collateral requirements)\"}},\"getAssetsIn(address)\":{\"params\":{\"_user\":\"The address of the account to pull assets for\"},\"returns\":{\"mTokens\":\"A dynamic list with the assets the account has entered\"}},\"getHypotheticalAccountLiquidity(address,address,uint256,uint256)\":{\"params\":{\"account\":\"The account to determine liquidity for\",\"borrowAmount\":\"The amount of underlying to hypothetically borrow\",\"mTokenModify\":\"The market to hypothetically redeem/borrow in\",\"redeemTokens\":\"The number of tokens to hypothetically redeem\"},\"returns\":{\"_0\":\"hypothetical account liquidity in excess of collateral requirements,         hypothetical account shortfall below collateral requirements)\"}},\"isDeprecated(address)\":{\"details\":\"All borrows in a deprecated mToken market can be immediately liquidated\",\"params\":{\"mToken\":\"The market to check if deprecated\"}},\"isPaused(address,uint8)\":{\"params\":{\"_type\":\"the operation type\",\"mToken\":\"The mToken to check\"}},\"liquidateCalculateSeizeTokens(address,address,uint256)\":{\"details\":\"Used in liquidation (called in mTokenBorrowed.liquidate)\",\"params\":{\"actualRepayAmount\":\"The amount of mTokenBorrowed underlying to convert into mTokenCollateral tokens\",\"mTokenBorrowed\":\"The address of the borrowed mToken\",\"mTokenCollateral\":\"The address of the collateral mToken\"},\"returns\":{\"_0\":\"number of mTokenCollateral tokens to be seized in a liquidation\"}},\"owner()\":{\"details\":\"Returns the address of the current owner.\"},\"renounceOwnership()\":{\"details\":\"Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner.\"},\"setCloseFactor(uint256)\":{\"details\":\"Admin function to set closeFactor\",\"params\":{\"newCloseFactorMantissa\":\"New close factor, scaled by 1e18\"}},\"setCollateralFactor(address,uint256)\":{\"details\":\"Admin function to set per-market collateralFactor\",\"params\":{\"mToken\":\"The market to set the factor on\",\"newCollateralFactorMantissa\":\"The new collateral factor, scaled by 1e18\"}},\"setLiquidationIncentive(address,uint256)\":{\"details\":\"Admin function to set liquidationIncentive\",\"params\":{\"newLiquidationIncentiveMantissa\":\"New liquidationIncentive scaled by 1e18\"}},\"setMarketBorrowCaps(address[],uint256[])\":{\"params\":{\"mTokens\":\"The addresses of the markets (tokens) to change the borrow caps for\",\"newBorrowCaps\":\"The new borrow cap values in underlying to be set. A value of 0 corresponds to unlimited borrowing.\"}},\"setMarketSupplyCaps(address[],uint256[])\":{\"params\":{\"mTokens\":\"The addresses of the markets (tokens) to change the supply caps for\",\"newSupplyCaps\":\"The new supply cap values in underlying to be set. A value of 0 corresponds to unlimited supplying.\"}},\"setOutflowTimeLimitInUSD(uint256)\":{\"details\":\"when 0, it means there's no limit\",\"params\":{\"amount\":\"The new limit\"}},\"setOutflowVolumeTimeWindow(uint256)\":{\"params\":{\"newTimeWindow\":\"The new reset time window\"}},\"setPaused(address,uint8,bool)\":{\"params\":{\"_type\":\"The pause operation type\",\"mToken\":\"The market token address\",\"state\":\"The pause operation status\"}},\"setPriceOracle(address)\":{\"details\":\"Admin function to set a new price oracle\"},\"setRewardDistributor(address)\":{\"params\":{\"newRewardDistributor\":\"The address of the new Reward Distributor\"}},\"setRolesOperator(address)\":{\"details\":\"Admin function to set a new operator\"},\"setWhitelistedUser(address,bool)\":{\"params\":{\"state\":\"The new staate\",\"user\":\"The user address\"}},\"supportMarket(address)\":{\"details\":\"Admin function to set isListed and add support for the market\",\"params\":{\"mToken\":\"The address of the market (token) to list\"}},\"transferOwnership(address)\":{\"details\":\"Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner.\"}},\"version\":1},\"userdoc\":{\"events\":{\"ActionPaused(address,uint8,bool)\":{\"notice\":\"Emitted when pause status is changed\"},\"MarketEntered(address,address)\":{\"notice\":\"Emitted when an account enters a market\"},\"MarketExited(address,address)\":{\"notice\":\"Emitted when an account exits a market\"},\"MarketListed(address)\":{\"notice\":\"Emitted when an admin supports a market\"},\"NewBorrowCap(address,uint256)\":{\"notice\":\"Emitted when borrow cap for a mToken is changed\"},\"NewCloseFactor(uint256,uint256)\":{\"notice\":\"Emitted Emitted when close factor is changed by admin\"},\"NewCollateralFactor(address,uint256,uint256)\":{\"notice\":\"Emitted when a collateral factor is changed by admin\"},\"NewLiquidationIncentive(address,uint256,uint256)\":{\"notice\":\"Emitted when liquidation incentive is changed by admin\"},\"NewPriceOracle(address,address)\":{\"notice\":\"Emitted when price oracle is changed\"},\"NewRewardDistributor(address,address)\":{\"notice\":\"Emitted when reward distributor is changed\"},\"NewRolesOperator(address,address)\":{\"notice\":\"Event emitted when rolesOperator is changed\"},\"NewSupplyCap(address,uint256)\":{\"notice\":\"Emitted when supply cap for a mToken is changed\"},\"OutflowLimitUpdated(address,uint256,uint256)\":{\"notice\":\"Event emitted when outflow limit is updated\"},\"OutflowTimeWindowUpdated(uint256,uint256)\":{\"notice\":\"Event emitted when outflow reset time window is updated\"},\"OutflowVolumeReset()\":{\"notice\":\"Event emitted when outflow volume has been reset\"},\"UserWhitelisted(address,bool)\":{\"notice\":\"Emitted when user whitelist status is changed\"}},\"kind\":\"user\",\"methods\":{\"accountAssets(address,uint256)\":{\"notice\":\"Per-account mapping of \\\"assets you are in\\\", capped by maxAssets\"},\"afterMTokenMint(address)\":{\"notice\":\"Validates mint and reverts on rejection. May emit logs.\"},\"allMarkets(uint256)\":{\"notice\":\"A list of all markets\"},\"beforeMTokenBorrow(address,address,uint256)\":{\"notice\":\"Checks if the account should be allowed to borrow the underlying asset of the given market\"},\"beforeMTokenLiquidate(address,address,address,uint256)\":{\"notice\":\"Checks if the liquidation should be allowed to occur\"},\"beforeMTokenMint(address,address)\":{\"notice\":\"Checks if the account should be allowed to mint tokens in the given market\"},\"beforeMTokenRedeem(address,address,uint256)\":{\"notice\":\"Checks if the account should be allowed to redeem tokens in the given market\"},\"beforeMTokenRepay(address,address)\":{\"notice\":\"Checks if the account should be allowed to repay a borrow in the given market\"},\"beforeMTokenSeize(address,address,address,address)\":{\"notice\":\"Checks if the seizing of assets should be allowed to occur\"},\"beforeMTokenTransfer(address,address,address,uint256)\":{\"notice\":\"Checks if the account should be allowed to transfer tokens in the given market\"},\"beforeRebalancing(address)\":{\"notice\":\"Checks if the account should be allowed to rebalance tokens\"},\"blacklistOperator()\":{\"notice\":\"Blacklist\"},\"borrowCaps(address)\":{\"notice\":\"Borrow caps enforced by borrowAllowed for each mToken address. Defaults to zero which corresponds to unlimited borrowing.\"},\"checkMembership(address,address)\":{\"notice\":\"Returns whether the given account is entered in the given asset\"},\"checkOutflowVolumeLimit(uint256)\":{\"notice\":\"Verifies outflow volule limit\"},\"claimMalda(address)\":{\"notice\":\"Claim all the MALDA accrued by holder in all markets\"},\"claimMalda(address,address[])\":{\"notice\":\"Claim all the MALDA accrued by holder in the specified markets\"},\"claimMalda(address[],address[],bool,bool)\":{\"notice\":\"Claim all MALDA accrued by the holders\"},\"closeFactorMantissa()\":{\"notice\":\"Multiplier used to calculate the maximum repayAmount when liquidating a borrow\"},\"cumulativeOutflowVolume()\":{\"notice\":\"Should return outflow volume\"},\"disableWhitelist()\":{\"notice\":\"Disable user whitelist\"},\"enableWhitelist()\":{\"notice\":\"Enable user whitelist\"},\"enterMarkets(address[])\":{\"notice\":\"Add assets to be included in account liquidity calculation\"},\"enterMarketsWithSender(address)\":{\"notice\":\"Add asset (msg.sender) to be included in account liquidity calculation\"},\"exitMarket(address)\":{\"notice\":\"Removes asset from sender's account liquidity calculation\"},\"getAccountLiquidity(address)\":{\"notice\":\"Determine the current account liquidity wrt collateral requirements\"},\"getAllMarkets()\":{\"notice\":\"A list of all markets\"},\"getAssetsIn(address)\":{\"notice\":\"Returns the assets an account has entered\"},\"getHypotheticalAccountLiquidity(address,address,uint256,uint256)\":{\"notice\":\"Determine what the account liquidity would be if the given amounts were redeemed/borrowed\"},\"getUSDValueForAllMarkets()\":{\"notice\":\"Returns USD value for all markets\"},\"isDeprecated(address)\":{\"notice\":\"Returns true if the given mToken market has been deprecated\"},\"isMarketListed(address)\":{\"notice\":\"Returns true/false\"},\"isOperator()\":{\"notice\":\"Should return true\"},\"isPaused(address,uint8)\":{\"notice\":\"Returns if operation is paused\"},\"lastOutflowResetTimestamp()\":{\"notice\":\"Should return last reset time for outflow check\"},\"limitPerTimePeriod()\":{\"notice\":\"Should return outflow limit\"},\"liquidateCalculateSeizeTokens(address,address,uint256)\":{\"notice\":\"Calculate number of tokens of collateral asset to seize given an underlying amount\"},\"liquidationIncentiveMantissa(address)\":{\"notice\":\"Multiplier representing the discount on collateral that a liquidator receives\"},\"markets(address)\":{\"notice\":\"Official mapping of mTokens -> Market metadata\"},\"oracleOperator()\":{\"notice\":\"Oracle which gives the price of any given asset\"},\"outflowResetTimeWindow()\":{\"notice\":\"Should return the outflow volume time window\"},\"resetOutflowVolume()\":{\"notice\":\"Resets outflow volume\"},\"rewardDistributor()\":{\"notice\":\"Reward Distributor to markets supply and borrow (including protocol token)\"},\"rolesOperator()\":{\"notice\":\"Roles\"},\"setCloseFactor(uint256)\":{\"notice\":\"Sets the closeFactor used when liquidating borrows\"},\"setCollateralFactor(address,uint256)\":{\"notice\":\"Sets the collateralFactor for a market\"},\"setLiquidationIncentive(address,uint256)\":{\"notice\":\"Sets liquidationIncentive\"},\"setMarketBorrowCaps(address[],uint256[])\":{\"notice\":\"Set the given borrow caps for the given mToken markets. Borrowing that brings total borrows to or above borrow cap will revert.\"},\"setMarketSupplyCaps(address[],uint256[])\":{\"notice\":\"Set the given supply caps for the given mToken markets. Supplying that brings total supply to or above supply cap will revert.\"},\"setOutflowTimeLimitInUSD(uint256)\":{\"notice\":\"Sets outflow volume limit\"},\"setOutflowVolumeTimeWindow(uint256)\":{\"notice\":\"Sets outflow volume time window\"},\"setPaused(address,uint8,bool)\":{\"notice\":\"Set pause for a specific operation\"},\"setPriceOracle(address)\":{\"notice\":\"Sets a new price oracle\"},\"setRewardDistributor(address)\":{\"notice\":\"Admin function to change the Reward Distributor\"},\"setRolesOperator(address)\":{\"notice\":\"Sets a new Operator for the market\"},\"setWhitelistedUser(address,bool)\":{\"notice\":\"Sets user whitelist status\"},\"supplyCaps(address)\":{\"notice\":\"Supply caps enforced by supplyAllowed for each mToken address. Defaults to zero which corresponds to unlimited supplying.\"},\"supportMarket(address)\":{\"notice\":\"Add the market to the markets mapping and set it as listed\"},\"userWhitelisted(address)\":{\"notice\":\"Returns true/false for user\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/Operator/Operator.sol\":\"Operator\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":risc0-ethereum/=lib/risc0-ethereum/\",\":risc0/=lib/risc0-ethereum/contracts/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol\":{\"keccak256\":\"0xc163fcf9bb10138631a9ba5564df1fa25db9adff73bd9ee868a8ae1858fe093a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9706d43a0124053d9880f6e31a59f31bc0a6a3dc1acd66ce0a16e1111658c5f6\",\"dweb:/ipfs/QmUFmfowzkRwGtDu36cXV9SPTBHJ3n7dG9xQiK5B28jTf2\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol\":{\"keccak256\":\"0x631188737069917d2f909d29ce62c4d48611d326686ba6683e26b72a23bfac0b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7a61054ae84cd6c4d04c0c4450ba1d6de41e27e0a2c4f1bcdf58f796b401c609\",\"dweb:/ipfs/QmUvtdp7X1mRVyC3CsHrtPbgoqWaXHp3S1ZR24tpAQYJWM\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol\":{\"keccak256\":\"0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9\",\"dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV\"]},\"src/Operator/Operator.sol\":{\"keccak256\":\"0x1486ed6af6f40f0e045403ac9e35bc045ae7031d9ecac7d1f0358f78ff83b469\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://e136048d46ae9b4384f561e16dfd4638e68db987007d2059aaf19011dcfc8f65\",\"dweb:/ipfs/QmcyrVa45W2qYtd9UuJz9C89M3xKzKzACDQqVMAk4hvPeq\"]},\"src/Operator/OperatorStorage.sol\":{\"keccak256\":\"0x3d9e7f2bc64ab9b5de39d4a58f0a72ac0f86c3b95a6318dd03a7b8acde166783\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://dc0d5e026e00f5f5f92cded7704bb602b06f387370ddd7ee0d75111099b01f3a\",\"dweb:/ipfs/QmQfZzADQpBuvTHJpbHz6P4fGhaGWvQgkU8qMkKULCLdjQ\"]},\"src/interfaces/IBlacklister.sol\":{\"keccak256\":\"0x47ed20135507a9d16473027cbee18de5d952846bada761c6248b91c75cd88be1\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://91a886b95dd578156f9b6cec3236e4751aae8a38e5d15e0d6045217c6fbd3d63\",\"dweb:/ipfs/QmRNjz7sGTuMkQNZhwwcg5DNN2eeqTZSAdZmxWxbx66CHU\"]},\"src/interfaces/IOperator.sol\":{\"keccak256\":\"0x0ae3d63eb07518da5a4fd6de7b013b6f55c87defb31900548378cffc70a51f72\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://1d9710c4ac43cb0563449aa519501b4ed18a331c90d0bc7eb79cdc56407bad8a\",\"dweb:/ipfs/Qmafe9Wup2GF9pD83CLWUrRait93vwvReqU1C7EHQhY7WV\"]},\"src/interfaces/IOracleOperator.sol\":{\"keccak256\":\"0xcc2848c75dd5e1531c2b958746cdd76a3b2b329be98fdca4311219a12a6b409e\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://337bdabf03ac279d95ba96aca60105932c7af5c15fdb4b21ab1c369509310fbc\",\"dweb:/ipfs/QmNYrNYNUwWWSrPwHdarhxt1ShdH1dy8B94M49TZ7XvmK1\"]},\"src/interfaces/IRewardDistributor.sol\":{\"keccak256\":\"0xd39c4db6e2a85e90db82e20685d4b235966d993c3407e1c0fb52add6421b86df\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://e865fe1569ec1ac79cd924a9aa2e2b43754555e510526f6cd030395e9923f01d\",\"dweb:/ipfs/QmXcshgzCxLKBZJYGuQviEamruhmwXWNJ3ZYLxazWBahuc\"]},\"src/interfaces/IRoles.sol\":{\"keccak256\":\"0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c\",\"dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj\"]},\"src/interfaces/ImToken.sol\":{\"keccak256\":\"0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36\",\"dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL\"]},\"src/utils/ExponentialNoError.sol\":{\"keccak256\":\"0xa97178117d8c32059d55dd2224e5514c54b31f896e565c3834cab3d7502b1bb6\",\"license\":\"BSL-1.1\",\"urls\":[\"bzz-raw://1cecc0713709f57a5bb7f64d2fa06b9437a42752fa3fed5d3c0b19d82aa56ef4\",\"dweb:/ipfs/QmQqBCZkzmZxomXLr3b1tHUV55n8EPk3wAUsXgojYSpR41\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "type": "error", "name": "InvalidInitialization"}, {"inputs": [], "type": "error", "name": "NotInitializing"}, {"inputs": [], "type": "error", "name": "Operator_AssetNotFound"}, {"inputs": [], "type": "error", "name": "Operator_Deactivate_MarketBalanceOwed"}, {"inputs": [], "type": "error", "name": "Operator_EmptyPrice"}, {"inputs": [], "type": "error", "name": "Operator_InsufficientLiquidity"}, {"inputs": [], "type": "error", "name": "Operator_InvalidBlacklistOperator"}, {"inputs": [], "type": "error", "name": "Operator_InvalidCollateralFactor"}, {"inputs": [], "type": "error", "name": "Operator_InvalidInput"}, {"inputs": [], "type": "error", "name": "Operator_InvalidRewardDistributor"}, {"inputs": [], "type": "error", "name": "Operator_InvalidRolesOperator"}, {"inputs": [], "type": "error", "name": "Operator_MarketAlreadyListed"}, {"inputs": [], "type": "error", "name": "Operator_MarketBorrowCapReached"}, {"inputs": [], "type": "error", "name": "Operator_MarketNotListed"}, {"inputs": [], "type": "error", "name": "Operator_MarketSupplyReached"}, {"inputs": [], "type": "error", "name": "Operator_Mismatch"}, {"inputs": [], "type": "error", "name": "Operator_OnlyAdmin"}, {"inputs": [], "type": "error", "name": "Operator_OnlyAdminOrRole"}, {"inputs": [], "type": "error", "name": "Operator_OracleUnderlyingFetchError"}, {"inputs": [], "type": "error", "name": "Operator_OutflowVolumeReached"}, {"inputs": [], "type": "error", "name": "Operator_Paused"}, {"inputs": [], "type": "error", "name": "Operator_PriceFetchFailed"}, {"inputs": [], "type": "error", "name": "Operator_RepayAmountNotValid"}, {"inputs": [], "type": "error", "name": "Operator_RepayingTooMuch"}, {"inputs": [], "type": "error", "name": "Operator_SenderMustBeToken"}, {"inputs": [], "type": "error", "name": "Operator_UserBlacklisted"}, {"inputs": [], "type": "error", "name": "Operator_UserNotWhitelisted"}, {"inputs": [], "type": "error", "name": "Operator_WrongMarket"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "type": "error", "name": "OwnableInvalidOwner"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "type": "error", "name": "OwnableUnauthorizedAccount"}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address", "indexed": true}, {"internalType": "enum ImTokenOperationTypes.OperationType", "name": "_type", "type": "uint8", "indexed": false}, {"internalType": "bool", "name": "state", "type": "bool", "indexed": false}], "type": "event", "name": "ActionPaused", "anonymous": false}, {"inputs": [{"internalType": "uint64", "name": "version", "type": "uint64", "indexed": false}], "type": "event", "name": "Initialized", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address", "indexed": true}, {"internalType": "address", "name": "account", "type": "address", "indexed": true}], "type": "event", "name": "MarketEntered", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address", "indexed": true}, {"internalType": "address", "name": "account", "type": "address", "indexed": true}], "type": "event", "name": "MarketExited", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address", "indexed": false}], "type": "event", "name": "MarketListed", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "newBorrowCap", "type": "uint256", "indexed": false}], "type": "event", "name": "NewBorrowCap", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "oldCloseFactorMantissa", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "newCloseFactorMantissa", "type": "uint256", "indexed": false}], "type": "event", "name": "NewCloseFactor", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "oldCollateralFactorMantissa", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "newCollateralFactorMantissa", "type": "uint256", "indexed": false}], "type": "event", "name": "NewCollateralFactor", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "market", "type": "address", "indexed": false}, {"internalType": "uint256", "name": "oldLiquidationIncentiveMantissa", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "newLiquidationIncentiveMantissa", "type": "uint256", "indexed": false}], "type": "event", "name": "NewLiquidationIncentive", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "oldPriceOracle", "type": "address", "indexed": true}, {"internalType": "address", "name": "newPriceOracle", "type": "address", "indexed": true}], "type": "event", "name": "NewPriceOracle", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "oldRewardDistributor", "type": "address", "indexed": true}, {"internalType": "address", "name": "newRewardDistributor", "type": "address", "indexed": true}], "type": "event", "name": "NewRewardDistributor", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "oldRoles", "type": "address", "indexed": true}, {"internalType": "address", "name": "newRoles", "type": "address", "indexed": true}], "type": "event", "name": "NewRolesOperator", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "newBorrowCap", "type": "uint256", "indexed": false}], "type": "event", "name": "NewSupplyCap", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "oldLimit", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "newLimit", "type": "uint256", "indexed": false}], "type": "event", "name": "OutflowLimitUpdated", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "oldWindow", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "newWindow", "type": "uint256", "indexed": false}], "type": "event", "name": "OutflowTimeWindowUpdated", "anonymous": false}, {"inputs": [], "type": "event", "name": "OutflowVolumeReset", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "previousOwner", "type": "address", "indexed": true}, {"internalType": "address", "name": "new<PERSON>wner", "type": "address", "indexed": true}], "type": "event", "name": "OwnershipTransferred", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "user", "type": "address", "indexed": true}, {"internalType": "bool", "name": "state", "type": "bool", "indexed": false}], "type": "event", "name": "<PERSON>r<PERSON><PERSON><PERSON><PERSON>", "anonymous": false}, {"inputs": [], "type": "event", "name": "WhitelistDisabled", "anonymous": false}, {"inputs": [], "type": "event", "name": "WhitelistEnabled", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "accountAssets", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}], "stateMutability": "view", "type": "function", "name": "afterMTokenMint"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "allMarkets", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}, {"internalType": "address", "name": "borrower", "type": "address"}, {"internalType": "uint256", "name": "borrowAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "beforeMTokenBorrow"}, {"inputs": [{"internalType": "address", "name": "mTokenBorrowed", "type": "address"}, {"internalType": "address", "name": "mTokenCollateral", "type": "address"}, {"internalType": "address", "name": "borrower", "type": "address"}, {"internalType": "uint256", "name": "repayAmount", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "beforeMTokenLiquidate"}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}, {"internalType": "address", "name": "minter", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "beforeMTokenMint"}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}, {"internalType": "address", "name": "redeemer", "type": "address"}, {"internalType": "uint256", "name": "redeemTokens", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "beforeMTokenRedeem"}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}, {"internalType": "address", "name": "borrower", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "beforeMTokenRepay"}, {"inputs": [{"internalType": "address", "name": "mTokenCollateral", "type": "address"}, {"internalType": "address", "name": "mTokenBorrowed", "type": "address"}, {"internalType": "address", "name": "liquidator", "type": "address"}, {"internalType": "address", "name": "borrower", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "beforeMTokenSeize"}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}, {"internalType": "address", "name": "src", "type": "address"}, {"internalType": "address", "name": "dst", "type": "address"}, {"internalType": "uint256", "name": "transferTokens", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "beforeMTokenTransfer"}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}], "stateMutability": "view", "type": "function", "name": "beforeRebalancing"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "blacklistOperator", "outputs": [{"internalType": "contract IBlacklister", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "borrowCaps", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "address", "name": "mToken", "type": "address"}], "stateMutability": "view", "type": "function", "name": "checkMembership", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "checkOutflowVolumeLimit"}, {"inputs": [{"internalType": "address", "name": "holder", "type": "address"}, {"internalType": "address[]", "name": "mTokens", "type": "address[]"}], "stateMutability": "nonpayable", "type": "function", "name": "claimMalda"}, {"inputs": [{"internalType": "address[]", "name": "holders", "type": "address[]"}, {"internalType": "address[]", "name": "mTokens", "type": "address[]"}, {"internalType": "bool", "name": "borrowers", "type": "bool"}, {"internalType": "bool", "name": "suppliers", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "claimMalda"}, {"inputs": [{"internalType": "address", "name": "holder", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "claimMalda"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "closeFactorMantissa", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "cumulativeOutflowVolume", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "disable<PERSON><PERSON><PERSON><PERSON>"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"inputs": [{"internalType": "address[]", "name": "_mTokens", "type": "address[]"}], "stateMutability": "nonpayable", "type": "function", "name": "enterMarkets"}, {"inputs": [{"internalType": "address", "name": "_account", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "enterMarketsWithSender"}, {"inputs": [{"internalType": "address", "name": "_mToken", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "exitMarket"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getAccountLiquidity", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getAllMarkets", "outputs": [{"internalType": "address[]", "name": "mTokens", "type": "address[]"}]}, {"inputs": [{"internalType": "address", "name": "_user", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getAssetsIn", "outputs": [{"internalType": "address[]", "name": "mTokens", "type": "address[]"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "address", "name": "mTokenModify", "type": "address"}, {"internalType": "uint256", "name": "redeemTokens", "type": "uint256"}, {"internalType": "uint256", "name": "borrowAmount", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getHypotheticalAccountLiquidity", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getUSDValueForAllMarkets", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "_rolesOperator", "type": "address"}, {"internalType": "address", "name": "_blacklistOperator", "type": "address"}, {"internalType": "address", "name": "_rewardDistributor", "type": "address"}, {"internalType": "address", "name": "_admin", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "initialize"}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isDeprecated", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isMarketListed", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "pure", "type": "function", "name": "isOperator", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}, {"internalType": "enum ImTokenOperationTypes.OperationType", "name": "_type", "type": "uint8"}], "stateMutability": "view", "type": "function", "name": "isPaused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "lastOutflowResetTimestamp", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "limitPerTimePeriod", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "mTokenBorrowed", "type": "address"}, {"internalType": "address", "name": "mTokenCollateral", "type": "address"}, {"internalType": "uint256", "name": "actualRepayAmount", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "liquidateCalculateSeizeTokens", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "liquidationIncentiveMantissa", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "markets", "outputs": [{"internalType": "bool", "name": "isListed", "type": "bool"}, {"internalType": "uint256", "name": "collateralFactorMantissa", "type": "uint256"}, {"internalType": "bool", "name": "isMalded", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "oracleOperator", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "outflowResetTimeWindow", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "renounceOwnership"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "resetOutflowVolume"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "rewardDistributor", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "rolesOperator", "outputs": [{"internalType": "contract IRoles", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "uint256", "name": "newCloseFactorMantissa", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "setCloseFactor"}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}, {"internalType": "uint256", "name": "newCollateralFactorMantissa", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "setCollateralFactor"}, {"inputs": [{"internalType": "address", "name": "market", "type": "address"}, {"internalType": "uint256", "name": "newLiquidationIncentiveMantissa", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "setLiquidationIncentive"}, {"inputs": [{"internalType": "address[]", "name": "mTokens", "type": "address[]"}, {"internalType": "uint256[]", "name": "newBorrowCaps", "type": "uint256[]"}], "stateMutability": "nonpayable", "type": "function", "name": "setMarketBorrowCaps"}, {"inputs": [{"internalType": "address[]", "name": "mTokens", "type": "address[]"}, {"internalType": "uint256[]", "name": "newSupplyCaps", "type": "uint256[]"}], "stateMutability": "nonpayable", "type": "function", "name": "setMarketSupplyCaps"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "setOutflowTimeLimitInUSD"}, {"inputs": [{"internalType": "uint256", "name": "newTimeWindow", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "setOutflowVolumeTimeWindow"}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}, {"internalType": "enum ImTokenOperationTypes.OperationType", "name": "_type", "type": "uint8"}, {"internalType": "bool", "name": "state", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "setPaused"}, {"inputs": [{"internalType": "address", "name": "newOracle", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setPriceO<PERSON>le"}, {"inputs": [{"internalType": "address", "name": "newRewardDistributor", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setRewardDistributor"}, {"inputs": [{"internalType": "address", "name": "_roles", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setRolesOperator"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "bool", "name": "state", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "setWhitelistedUser"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "supplyCaps", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "mToken", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "supportMarket"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "transferOwnership"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "user<PERSON><PERSON><PERSON>sted", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "whitelistEnabled", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}], "devdoc": {"kind": "dev", "methods": {"afterMTokenMint(address)": {"params": {"mToken": "Asset being minted"}}, "beforeMTokenBorrow(address,address,uint256)": {"params": {"borrowAmount": "The amount of underlying the account would borrow", "borrower": "The account which would borrow the asset", "mToken": "The market to verify the borrow against"}}, "beforeMTokenLiquidate(address,address,address,uint256)": {"params": {"borrower": "The address of the borrower", "mTokenBorrowed": "Asset which was borrowed by the borrower", "mTokenCollateral": "Asset which was used as collateral and will be seized", "repayAmount": "The amount of underlying being repaid"}}, "beforeMTokenMint(address,address)": {"params": {"mToken": "The market to verify the mint against", "minter": "The account which would get the minted tokens"}}, "beforeMTokenRedeem(address,address,uint256)": {"params": {"mToken": "The market to verify the redeem against", "redeemTokens": "The number of mTokens to exchange for the underlying asset in the market", "redeemer": "The account which would redeem the tokens"}}, "beforeMTokenRepay(address,address)": {"params": {"borrower": "The account which would borrowed the asset", "mToken": "The market to verify the repay against"}}, "beforeMTokenSeize(address,address,address,address)": {"params": {"borrower": "The address of the borrower", "liquidator": "The address repaying the borrow and seizing the collateral", "mTokenBorrowed": "Asset which was borrowed by the borrower", "mTokenCollateral": "Asset which was used as collateral and will be seized"}}, "beforeMTokenTransfer(address,address,address,uint256)": {"params": {"dst": "The account which receives the tokens", "mToken": "The market to verify the transfer against", "src": "The account which sources the tokens", "transferTokens": "The number of mTokens to transfer"}}, "beforeRebalancing(address)": {"params": {"mToken": "The market to verify the transfer against"}}, "checkMembership(address,address)": {"params": {"account": "The address of the account to check", "mToken": "The mToken to check"}, "returns": {"_0": "True if the account is in the asset, otherwise false."}}, "checkOutflowVolumeLimit(uint256)": {"params": {"amount": "The new limit"}}, "claimMalda(address)": {"params": {"holder": "The address to claim MALDA for"}}, "claimMalda(address,address[])": {"params": {"holder": "The address to claim MALDA for", "mTokens": "The list of markets to claim MALDA in"}}, "claimMalda(address[],address[],bool,bool)": {"params": {"borrowers": "Whether or not to claim MALDA earned by borrowing", "holders": "The addresses to claim MALDA for", "mTokens": "The list of markets to claim MALDA in", "suppliers": "Whether or not to claim MALDA earned by supplying"}}, "constructor": {"custom:oz-upgrades-unsafe-allow": "constructor"}, "enterMarkets(address[])": {"params": {"_mTokens": "The list of addresses of the mToken markets to be enabled"}}, "enterMarketsWithSender(address)": {"params": {"_account": "The account to add for"}}, "exitMarket(address)": {"details": "Sender must not have an outstanding borrow balance in the asset,  or be providing necessary collateral for an outstanding borrow.", "params": {"_mToken": "The address of the asset to be removed"}}, "getAccountLiquidity(address)": {"returns": {"_0": "account liquidity in excess of collateral requirements,          account shortfall below collateral requirements)"}}, "getAssetsIn(address)": {"params": {"_user": "The address of the account to pull assets for"}, "returns": {"mTokens": "A dynamic list with the assets the account has entered"}}, "getHypotheticalAccountLiquidity(address,address,uint256,uint256)": {"params": {"account": "The account to determine liquidity for", "borrowAmount": "The amount of underlying to hypothetically borrow", "mTokenModify": "The market to hypothetically redeem/borrow in", "redeemTokens": "The number of tokens to hypothetically redeem"}, "returns": {"_0": "hypothetical account liquidity in excess of collateral requirements,         hypothetical account shortfall below collateral requirements)"}}, "isDeprecated(address)": {"details": "All borrows in a deprecated mToken market can be immediately liquidated", "params": {"mToken": "The market to check if deprecated"}}, "isPaused(address,uint8)": {"params": {"_type": "the operation type", "mToken": "The mToken to check"}}, "liquidateCalculateSeizeTokens(address,address,uint256)": {"details": "Used in liquidation (called in mTokenBorrowed.liquidate)", "params": {"actualRepayAmount": "The amount of mTokenBorrowed underlying to convert into mTokenCollateral tokens", "mTokenBorrowed": "The address of the borrowed mToken", "mTokenCollateral": "The address of the collateral mToken"}, "returns": {"_0": "number of mTokenCollateral tokens to be seized in a liquidation"}}, "owner()": {"details": "Returns the address of the current owner."}, "renounceOwnership()": {"details": "Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner."}, "setCloseFactor(uint256)": {"details": "Admin function to set closeFactor", "params": {"newCloseFactorMantissa": "New close factor, scaled by 1e18"}}, "setCollateralFactor(address,uint256)": {"details": "Admin function to set per-market collateralFactor", "params": {"mToken": "The market to set the factor on", "newCollateralFactorMantissa": "The new collateral factor, scaled by 1e18"}}, "setLiquidationIncentive(address,uint256)": {"details": "Admin function to set liquidationIncentive", "params": {"newLiquidationIncentiveMantissa": "New liquidationIncentive scaled by 1e18"}}, "setMarketBorrowCaps(address[],uint256[])": {"params": {"mTokens": "The addresses of the markets (tokens) to change the borrow caps for", "newBorrowCaps": "The new borrow cap values in underlying to be set. A value of 0 corresponds to unlimited borrowing."}}, "setMarketSupplyCaps(address[],uint256[])": {"params": {"mTokens": "The addresses of the markets (tokens) to change the supply caps for", "newSupplyCaps": "The new supply cap values in underlying to be set. A value of 0 corresponds to unlimited supplying."}}, "setOutflowTimeLimitInUSD(uint256)": {"details": "when 0, it means there's no limit", "params": {"amount": "The new limit"}}, "setOutflowVolumeTimeWindow(uint256)": {"params": {"newTimeWindow": "The new reset time window"}}, "setPaused(address,uint8,bool)": {"params": {"_type": "The pause operation type", "mToken": "The market token address", "state": "The pause operation status"}}, "setPriceOracle(address)": {"details": "Admin function to set a new price oracle"}, "setRewardDistributor(address)": {"params": {"newRewardDistributor": "The address of the new Reward Distributor"}}, "setRolesOperator(address)": {"details": "Admin function to set a new operator"}, "setWhitelistedUser(address,bool)": {"params": {"state": "The new staate", "user": "The user address"}}, "supportMarket(address)": {"details": "Admin function to set isListed and add support for the market", "params": {"mToken": "The address of the market (token) to list"}}, "transferOwnership(address)": {"details": "Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"accountAssets(address,uint256)": {"notice": "Per-account mapping of \"assets you are in\", capped by maxAssets"}, "afterMTokenMint(address)": {"notice": "Validates mint and reverts on rejection. May emit logs."}, "allMarkets(uint256)": {"notice": "A list of all markets"}, "beforeMTokenBorrow(address,address,uint256)": {"notice": "Checks if the account should be allowed to borrow the underlying asset of the given market"}, "beforeMTokenLiquidate(address,address,address,uint256)": {"notice": "Checks if the liquidation should be allowed to occur"}, "beforeMTokenMint(address,address)": {"notice": "Checks if the account should be allowed to mint tokens in the given market"}, "beforeMTokenRedeem(address,address,uint256)": {"notice": "Checks if the account should be allowed to redeem tokens in the given market"}, "beforeMTokenRepay(address,address)": {"notice": "Checks if the account should be allowed to repay a borrow in the given market"}, "beforeMTokenSeize(address,address,address,address)": {"notice": "Checks if the seizing of assets should be allowed to occur"}, "beforeMTokenTransfer(address,address,address,uint256)": {"notice": "Checks if the account should be allowed to transfer tokens in the given market"}, "beforeRebalancing(address)": {"notice": "Checks if the account should be allowed to rebalance tokens"}, "blacklistOperator()": {"notice": "Blacklist"}, "borrowCaps(address)": {"notice": "Borrow caps enforced by borrowAllowed for each mToken address. Defaults to zero which corresponds to unlimited borrowing."}, "checkMembership(address,address)": {"notice": "Returns whether the given account is entered in the given asset"}, "checkOutflowVolumeLimit(uint256)": {"notice": "Verifies outflow volule limit"}, "claimMalda(address)": {"notice": "Claim all the MALDA accrued by holder in all markets"}, "claimMalda(address,address[])": {"notice": "Claim all the MALDA accrued by holder in the specified markets"}, "claimMalda(address[],address[],bool,bool)": {"notice": "Claim all MALDA accrued by the holders"}, "closeFactorMantissa()": {"notice": "Multiplier used to calculate the maximum repayAmount when liquidating a borrow"}, "cumulativeOutflowVolume()": {"notice": "Should return outflow volume"}, "disableWhitelist()": {"notice": "Disable user whitelist"}, "enableWhitelist()": {"notice": "Enable user whitelist"}, "enterMarkets(address[])": {"notice": "Add assets to be included in account liquidity calculation"}, "enterMarketsWithSender(address)": {"notice": "Add asset (msg.sender) to be included in account liquidity calculation"}, "exitMarket(address)": {"notice": "Removes asset from sender's account liquidity calculation"}, "getAccountLiquidity(address)": {"notice": "Determine the current account liquidity wrt collateral requirements"}, "getAllMarkets()": {"notice": "A list of all markets"}, "getAssetsIn(address)": {"notice": "Returns the assets an account has entered"}, "getHypotheticalAccountLiquidity(address,address,uint256,uint256)": {"notice": "Determine what the account liquidity would be if the given amounts were redeemed/borrowed"}, "getUSDValueForAllMarkets()": {"notice": "Returns USD value for all markets"}, "isDeprecated(address)": {"notice": "Returns true if the given mToken market has been deprecated"}, "isMarketListed(address)": {"notice": "Returns true/false"}, "isOperator()": {"notice": "Should return true"}, "isPaused(address,uint8)": {"notice": "Returns if operation is paused"}, "lastOutflowResetTimestamp()": {"notice": "Should return last reset time for outflow check"}, "limitPerTimePeriod()": {"notice": "Should return outflow limit"}, "liquidateCalculateSeizeTokens(address,address,uint256)": {"notice": "Calculate number of tokens of collateral asset to seize given an underlying amount"}, "liquidationIncentiveMantissa(address)": {"notice": "Multiplier representing the discount on collateral that a liquidator receives"}, "markets(address)": {"notice": "Official mapping of mTokens -> Market metadata"}, "oracleOperator()": {"notice": "Oracle which gives the price of any given asset"}, "outflowResetTimeWindow()": {"notice": "Should return the outflow volume time window"}, "resetOutflowVolume()": {"notice": "Resets outflow volume"}, "rewardDistributor()": {"notice": "Reward Distributor to markets supply and borrow (including protocol token)"}, "rolesOperator()": {"notice": "Roles"}, "setCloseFactor(uint256)": {"notice": "Sets the closeFactor used when liquidating borrows"}, "setCollateralFactor(address,uint256)": {"notice": "Sets the collateralFactor for a market"}, "setLiquidationIncentive(address,uint256)": {"notice": "Sets liquidationIncentive"}, "setMarketBorrowCaps(address[],uint256[])": {"notice": "Set the given borrow caps for the given mToken markets. Borrowing that brings total borrows to or above borrow cap will revert."}, "setMarketSupplyCaps(address[],uint256[])": {"notice": "Set the given supply caps for the given mToken markets. Supplying that brings total supply to or above supply cap will revert."}, "setOutflowTimeLimitInUSD(uint256)": {"notice": "Sets outflow volume limit"}, "setOutflowVolumeTimeWindow(uint256)": {"notice": "Sets outflow volume time window"}, "setPaused(address,uint8,bool)": {"notice": "Set pause for a specific operation"}, "setPriceOracle(address)": {"notice": "Sets a new price oracle"}, "setRewardDistributor(address)": {"notice": "Admin function to change the Reward Distributor"}, "setRolesOperator(address)": {"notice": "Sets a new Operator for the market"}, "setWhitelistedUser(address,bool)": {"notice": "Sets user whitelist status"}, "supplyCaps(address)": {"notice": "Supply caps enforced by supplyAllowed for each mToken address. Defaults to zero which corresponds to unlimited supplying."}, "supportMarket(address)": {"notice": "Add the market to the markets mapping and set it as listed"}, "userWhitelisted(address)": {"notice": "Returns true/false for user"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "risc0-ethereum/=lib/risc0-ethereum/", "risc0/=lib/risc0-ethereum/contracts/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/Operator/Operator.sol": "Operator"}, "evmVersion": "london", "libraries": {}}, "sources": {"lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol": {"keccak256": "0xc163fcf9bb10138631a9ba5564df1fa25db9adff73bd9ee868a8ae1858fe093a", "urls": ["bzz-raw://9706d43a0124053d9880f6e31a59f31bc0a6a3dc1acd66ce0a16e1111658c5f6", "dweb:/ipfs/QmUFmfowzkRwGtDu36cXV9SPTBHJ3n7dG9xQiK5B28jTf2"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol": {"keccak256": "0x631188737069917d2f909d29ce62c4d48611d326686ba6683e26b72a23bfac0b", "urls": ["bzz-raw://7a61054ae84cd6c4d04c0c4450ba1d6de41e27e0a2c4f1bcdf58f796b401c609", "dweb:/ipfs/QmUvtdp7X1mRVyC3CsHrtPbgoqWaXHp3S1ZR24tpAQYJWM"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol": {"keccak256": "0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397", "urls": ["bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9", "dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV"], "license": "MIT"}, "src/Operator/Operator.sol": {"keccak256": "0x1486ed6af6f40f0e045403ac9e35bc045ae7031d9ecac7d1f0358f78ff83b469", "urls": ["bzz-raw://e136048d46ae9b4384f561e16dfd4638e68db987007d2059aaf19011dcfc8f65", "dweb:/ipfs/QmcyrVa45W2qYtd9UuJz9C89M3xKzKzACDQqVMAk4hvPeq"], "license": "BSL-1.1"}, "src/Operator/OperatorStorage.sol": {"keccak256": "0x3d9e7f2bc64ab9b5de39d4a58f0a72ac0f86c3b95a6318dd03a7b8acde166783", "urls": ["bzz-raw://dc0d5e026e00f5f5f92cded7704bb602b06f387370ddd7ee0d75111099b01f3a", "dweb:/ipfs/QmQfZzADQpBuvTHJpbHz6P4fGhaGWvQgkU8qMkKULCLdjQ"], "license": "BSL-1.1"}, "src/interfaces/IBlacklister.sol": {"keccak256": "0x47ed20135507a9d16473027cbee18de5d952846bada761c6248b91c75cd88be1", "urls": ["bzz-raw://91a886b95dd578156f9b6cec3236e4751aae8a38e5d15e0d6045217c6fbd3d63", "dweb:/ipfs/QmRNjz7sGTuMkQNZhwwcg5DNN2eeqTZSAdZmxWxbx66CHU"], "license": "BSL-1.1"}, "src/interfaces/IOperator.sol": {"keccak256": "0x0ae3d63eb07518da5a4fd6de7b013b6f55c87defb31900548378cffc70a51f72", "urls": ["bzz-raw://1d9710c4ac43cb0563449aa519501b4ed18a331c90d0bc7eb79cdc56407bad8a", "dweb:/ipfs/Qmafe9Wup2GF9pD83CLWUrRait93vwvReqU1C7EHQhY7WV"], "license": "BSL-1.1"}, "src/interfaces/IOracleOperator.sol": {"keccak256": "0xcc2848c75dd5e1531c2b958746cdd76a3b2b329be98fdca4311219a12a6b409e", "urls": ["bzz-raw://337bdabf03ac279d95ba96aca60105932c7af5c15fdb4b21ab1c369509310fbc", "dweb:/ipfs/QmNYrNYNUwWWSrPwHdarhxt1ShdH1dy8B94M49TZ7XvmK1"], "license": "BSL-1.1"}, "src/interfaces/IRewardDistributor.sol": {"keccak256": "0xd39c4db6e2a85e90db82e20685d4b235966d993c3407e1c0fb52add6421b86df", "urls": ["bzz-raw://e865fe1569ec1ac79cd924a9aa2e2b43754555e510526f6cd030395e9923f01d", "dweb:/ipfs/QmXcshgzCxLKBZJYGuQviEamruhmwXWNJ3ZYLxazWBahuc"], "license": "BSL-1.1"}, "src/interfaces/IRoles.sol": {"keccak256": "0x6d462ea7b6d3e63153839ce6bc79b2cba7ba007f0d9868ef8d0ebe89a6f4e365", "urls": ["bzz-raw://32cea056c3d47a6c80e6273aa1df14bbcb5ee19d239f314511b0f3928dfa3b7c", "dweb:/ipfs/QmX6wfsLkg3JXZQ4q5W35Zr51z9QhRNRtYJ3cS6KnUZrMj"], "license": "BSL-1.1"}, "src/interfaces/ImToken.sol": {"keccak256": "0x975e4a23bf3346b65f9e5fbad7ada05351de836c53ecf5d67d9db730eddc278f", "urls": ["bzz-raw://f84f1663730fd08feb0e2baee42f4a3a91b72cfe161b7bbfe867b0c9f09a7c36", "dweb:/ipfs/QmS9xgwrCVpGBWoAh7RzURefDF62waPRXiDwUkHttefBwL"], "license": "BSL-1.1"}, "src/utils/ExponentialNoError.sol": {"keccak256": "0xa97178117d8c32059d55dd2224e5514c54b31f896e565c3834cab3d7502b1bb6", "urls": ["bzz-raw://1cecc0713709f57a5bb7f64d2fa06b9437a42752fa3fed5d3c0b19d82aa56ef4", "dweb:/ipfs/QmQqBCZkzmZxomXLr3b1tHUV55n8EPk3wAUsXgojYSpR41"], "license": "BSL-1.1"}}, "version": 1}, "id": 127}