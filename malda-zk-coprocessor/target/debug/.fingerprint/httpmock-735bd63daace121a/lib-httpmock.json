{"rustc": 13226066032359371072, "features": "[\"basic-cookies\", \"cookies\", \"default\"]", "declared_features": "[\"basic-cookies\", \"clap\", \"color\", \"colored\", \"cookies\", \"default\", \"env_logger\", \"isahc\", \"remote\", \"serde_yaml\", \"standalone\"]", "target": 608559532794175849, "profile": 2241668132362809309, "path": 6494033261159869397, "deps": [[15171501230327402, "async_std", false, 431581037992499082], [915123552320100963, "similar", false, 3924550892797243432], [3150220818285335163, "url", false, 16175289335430707131], [3580020307953720212, "async_object_pool", false, 240256818140093259], [4468123440088164316, "crossbeam_utils", false, 13175080905204208215], [5026306216841577341, "<PERSON><PERSON><PERSON><PERSON>", false, 14396358448136792960], [5849048857304117613, "assert_json_diff", false, 8254797642593120216], [5986029879202738730, "log", false, 16455061867047553401], [7414427314941361239, "hyper", false, 10241486288387996383], [9451456094439810778, "regex", false, 10268355354006804107], [9538054652646069845, "tokio", false, 7614588010834229709], [9689903380558560274, "serde", false, 9355859758247546253], [10629569228670356391, "futures_util", false, 11328398443797682012], [11946729385090170470, "async_trait", false, 1534620821560132416], [12613081344515805984, "serde_regex", false, 984234793299929632], [14299496324343720937, "form_urlencoded", false, 2168860125054142589], [14830292086846978678, "basic_cookies", false, 6762431533931846379], [15367738274754116744, "serde_json", false, 9370259306695989407], [17917672826516349275, "lazy_static", false, 4970714514417749432], [18066890886671768183, "base64", false, 387693755566662100]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/httpmock-735bd63daace121a/dep-lib-httpmock", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}