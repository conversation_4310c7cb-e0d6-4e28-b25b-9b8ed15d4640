{"rustc": 13226066032359371072, "features": "[\"abigen\", \"ethers-contract-abigen\", \"ethers-contract-derive\", \"ethers-providers\", \"providers\", \"rustls\"]", "declared_features": "[\"abigen\", \"abigen-offline\", \"abigen-online\", \"celo\", \"default\", \"eip712\", \"ethers-contract-abigen\", \"ethers-contract-derive\", \"ethers-providers\", \"legacy\", \"openssl\", \"optimism\", \"providers\", \"rustls\"]", "target": 7173054194013103692, "profile": 2241668132362809309, "path": 11290977974409434104, "deps": [[2141440161325128830, "ethers_contract_abigen", false, 9802755375963550618], [3722963349756955755, "once_cell", false, 8076464963361065281], [5030409040180886167, "ethers_core", false, 1535132183218844081], [6264115378959545688, "pin_project", false, 10729296249656342879], [8008191657135824715, "thiserror", false, 15143648107364063845], [8524202948771681628, "ethers_contract_derive", false, 5884850131739342876], [9689903380558560274, "serde", false, 9355859758247546253], [10629569228670356391, "futures_util", false, 11328398443797682012], [13003293521383684806, "hex", false, 1360419901812355704], [14417566112632113257, "ethers_providers", false, 393465440310474759], [15367738274754116744, "serde_json", false, 9370259306695989407]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/ethers-contract-63ea1a4bf52373d6/dep-lib-ethers_contract", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}