{"rustc": 13226066032359371072, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing\", \"tracing-log\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 11202463608144111571, "path": 14941789077292118028, "deps": [[1009387600818341822, "matchers", false, 1779396719722708901], [1017461770342116999, "sharded_slab", false, 16329184767556372962], [1359731229228270592, "thread_local", false, 4638157394572675739], [3424551429995674438, "tracing_core", false, 10820017051342959744], [3666196340704888985, "smallvec", false, 15264098143193521011], [3722963349756955755, "once_cell", false, 8076464963361065281], [8606274917505247608, "tracing", false, 4076779996727418378], [8614575489689151157, "nu_ansi_term", false, 7138635311078825699], [9451456094439810778, "regex", false, 10268355354006804107], [10806489435541507125, "tracing_log", false, 16434909294660479964]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tracing-subscriber-8f557f39d66e5b9c/dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}