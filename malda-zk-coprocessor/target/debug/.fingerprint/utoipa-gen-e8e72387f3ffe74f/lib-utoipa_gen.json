{"rustc": 13226066032359371072, "features": "[]", "declared_features": "[\"actix_extras\", \"auto_into_responses\", \"axum_extras\", \"chrono\", \"config\", \"debug\", \"decimal\", \"decimal_float\", \"indexmap\", \"jiff_0_2\", \"non_strict_integers\", \"rc_schema\", \"regex\", \"repr\", \"rocket_extras\", \"smallvec\", \"time\", \"ulid\", \"url\", \"uuid\", \"yaml\"]", "target": 7060440504916948280, "profile": 16335202704479430798, "path": 5656675523858986760, "deps": [[3060637413840920116, "proc_macro2", false, 5626709823024730917], [10640660562325816595, "syn", false, 6432019188991438304], [17990358020177143287, "quote", false, 7814507195864576094]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/utoipa-gen-e8e72387f3ffe74f/dep-lib-utoipa_gen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}