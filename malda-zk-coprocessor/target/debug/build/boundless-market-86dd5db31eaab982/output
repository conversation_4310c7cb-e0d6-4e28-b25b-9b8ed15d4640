cargo::rerun-if-changed=build.rs
cargo::rerun-if-env-changed=CARGO_CFG_TARGET_OS
cargo::rerun-if-env-changed=CARGO_MANIFEST_DIR
cargo::rerun-if-changed=/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/boundless-market-0.10.2/src/contracts/artifacts
cargo::rerun-if-env-changed=CARGO_CFG_TARGET_OS
cargo::rerun-if-env-changed=OUT_DIR
cargo::rerun-if-env-changed=CARGO_CFG_TARGET_OS
cargo::rerun-if-env-changed=CARGO_CFG_TARGET_OS
cargo:warning=Skipping contract bytecode generation during cargo publish
